{"name": "umv_client_sale", "version": "0.8", "description": "一套基于vue3、element-plus、typesScript、vite4的后台集成方案。", "author": "", "private": false, "scripts": {"i": "pnpm install", "dev": "vite --mode dev", "sit": "vite --mode sit", "test": "vite --mode test", "uat": "node --max-http-header-size=1000000000000 ./node_modules/vite/bin/vite.js --mode uat", "pro": "vite --mode pro", "ts:check": "vue-tsc --noEmit", "build": "node --max_old_space_size=8000 ./node_modules/vite/bin/vite.js build", "build:pro": "node --max_old_space_size=8000 ./node_modules/vite/bin/vite.js build --mode pro", "build:dev": "node --max_old_space_size=8000 ./node_modules/vite/bin/vite.js build --mode dev", "build:sit": "node --max_old_space_size=8000 ./node_modules/vite/bin/vite.js build --mode sit", "build:uat": "node --max_old_space_size=8000 ./node_modules/vite/bin/vite.js build --mode uat", "serve:pro": "vite preview --mode pro", "serve:dev": "vite preview --mode dev", "serve:test": "vite preview --mode test", "serve:uat": "vite preview --mode uat", "npm:check": "npx npm-check-updates", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "prepare": "husky install", "p": "plop", "analysis": "windicss-analysis"}, "dependencies": {"@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.1.0", "@form-create/element-ui": "^3.1.17", "@iconify/iconify": "^3.1.0", "@vueuse/core": "^9.13.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "@zxcvbn-ts/core": "^2.2.1", "animate.css": "^4.1.1", "axios": "^1.3.5", "bpmn-js": "13.2.1", "bpmn-js-token-simulation": "^0.33.2", "cropperjs": "^1.5.13", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "diagram-js": "^14.1.0", "docx-preview": "^0.3.0", "echarts": "^5.4.2", "echarts-wordcloud": "^2.1.0", "el-table-horizontal-scroll": "^1.2.5", "element-plus": "2.6.0", "es-drager": "^1.1.3", "html2canvas": "1.0.0-alpha.12", "intro.js": "^7.0.1", "js-base64": "^3.7.5", "jsencrypt": "^3.3.2", "jspdf": "^2.5.1", "lodash-es": "^4.17.21", "min-dash": "^4.2.1", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pdfjs-dist": "^4.0.379", "pinia": "2.0.36", "prismjs": "^1.29.0", "qrcode": "^1.5.1", "qs": "^6.11.1", "rollup-plugin-visualizer": "^5.11.0", "sass": "^1.72.0", "socket.io-client": "^2.5.0", "steady-xml": "^0.1.0", "swiper": "^10.0.4", "tui-code-snippet": "^2.3.3", "tui-color-picker": "^2.2.8", "tui-image-editor": "^3.15.3", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.0", "url": "^0.11.0", "uuid": "^11.0.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-top-level-await": "^1.3.1", "vue": "3.4.15", "vue-3d-model": "2.0.0-alpha.4", "vue-clipboard3": "^2.0.0", "vue-i18n": "9.2.2", "vue-router": "^4.1.6", "vue-types": "^5.0.2", "vue-uuid": "^3.0.0", "vue3-colorpicker": "^2.1.4", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@iconify/json": "^2.2.48", "@intlify/unplugin-vue-i18n": "^0.10.0", "@types/intro.js": "^5.1.1", "@types/lodash-es": "^4.17.7", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.0", "@types/qrcode": "^1.5.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-vue": "5.0.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/runtime-core": "^3.3.4", "autoprefixer": "^10.4.14", "babel-plugin-prismjs": "^2.1.0", "consola": "^3.0.1", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-define-config": "^1.17.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.13.0", "husky": "^8.0.3", "less": "^4.1.3", "lint-staged": "^13.2.1", "plop": "^3.1.2", "postcss": "^8.4.21", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.7", "rimraf": "^5.0.5", "rollup": "^3.20.2", "stylelint": "^15.4.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^11.0.0", "stylelint-config-standard": "^32.0.0", "stylelint-order": "^6.0.3", "terser": "^5.31.0", "typescript": "5.0.4", "unplugin-auto-import": "^0.16.4", "unplugin-element-plus": "^0.7.1", "unplugin-vue-components": "^0.25.0", "vite": "5.2.3", "vite-plugin-ejs": "^1.6.4", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "^2.9.6", "vite-plugin-prismjs": "^0.0.8", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.8.10", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^1.2.0", "windicss": "^3.5.6", "windicss-analysis": "^0.3.5"}, "engines": {"node": ">= 14.18.0"}}