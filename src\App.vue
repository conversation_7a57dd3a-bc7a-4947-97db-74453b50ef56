<!--
 * @Author: Ho<PERSON><PERSON>
 * @Date: 2023-06-13 08:59:35
 * @LastEditors: HoJack
 * @LastEditTime: 2023-07-31 14:34:23
 * @Description: 
-->
<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { ConfigGlobal } from '@/layout/components/ConfigGlobal'
import { isDark } from '@/utils/is'
import { useDesign } from '@/hooks/web/useDesign'
import { useCache } from '@/hooks/web/useCache'

import loading from '@/components/loading.vue'
import { useLoadingStore } from '@/store/modules/loading'
const loadingStore = useLoadingStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('app')

const appStore = useAppStore()

const currentSize = computed(() => appStore.getCurrentSize)

const greyMode = computed(() => appStore.getGreyMode)

const { wsCache } = useCache()

const { t } = useI18n()

// 根据浏览器当前主题设置系统主题色
const setDefaultTheme = () => {
  if (wsCache.get('isDark') !== null) {
    appStore.setIsDark(wsCache.get('isDark'))
    return
  }
  //检测浏览器设置
  // isDarkTheme = isDark()
  //强制默认非Dark模式
  let isDarkTheme = false
  appStore.setIsDark(isDarkTheme)
}

setDefaultTheme()

//水印
import envController from '@/controller/envController'
import { useWatermark } from '@/hooks/web/useWatermark'
const { setWatermark } = useWatermark()
onMounted(() => {
  const envName = envController.getEnvironment()
  if (envName !== 'pro') {
    setWatermark(envName.toUpperCase() + '环境')
  }
})
</script>

<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''" />
  </ConfigGlobal>
  <loading :msg="t('sys.app.loading')" v-if="loadingStore.current" />
</template>

<style lang="less">
@prefix-cls: ~'@{namespace}-app';

.size {
  width: 100%;
  height: 100%;
}

html,
body {
  padding: 0 !important;
  margin: 0;
  overflow: hidden;
  .size;

  #app {
    .size;
  }
}

.@{prefix-cls}-grey-mode {
  filter: grayscale(100%);
}
// dialog 头部样式
.el-dialog__header {
  margin-right: 0 !important;
  border-bottom: 1px solid #e9e9e9;
}

.el-dialog__body {
  padding: 20px;
}
</style>
