/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 17:58:02
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-11-21 14:38:40
 * @Description:
 */
import request from '@/config/axios'
const url = '/notice/notice'
// const url = 'http://*************:8009/notice'
// const urls = 'http://*************:8009/noticeTo'
const urls = '/notice/noticeTo'

// 业务待办标记已读
export const noticeReaded = (noticeId): any => {
  return request.post({
    url: '/notice/notice/addNotice',
    data: {
      noticeId,
      readStatus: 2
    }
  })
}

// 查看待办读取状态列表
export const findNoticeReadStatusByList = (data): any => {
  return request.post({
    url: '/notice/notice/findNoticeReadStatusByList',
    data
  })
}

// 新增待办读取状态
export const addNoticeReadStatus = (applyId): any => {
  return request.post({
    url: '/notice/notice/addNoticeReadStatus',
    data: {
      applyId,
      readStatus: 2,
      noticebusiness: 2
    }
  })
}

// 查看列表
export const findNoticeByPage = (data: any): any => {
  return request.postOriginal({ url: url + '/findNoticeByPage', data })
}
// 转办
export const addNoticeTo = (data: any): any => {
  return request.postOriginal({ url: urls + '/addNoticeTo', data })
}
// 根据ID查询
export const findNoticeById = (data: any): any => {
  return request.postOriginal({ url: url + `/findNoticeById/${data}` })
}
// 转办记录列表
export const findNoticeToByPage = (data: any): any => {
  return request.postOriginal({ url: urls + '/findNoticeToByPage', data })
}
// 我的消息
export const findMessageToByPage = (data: any): any => {
  return request.postOriginal({ url: '/notice/messageTo/findMessageToByPage', data })
}
// 日志
export const findNoticeToById = (data: any): any => {
  return request.postOriginal({ url: urls + `/findNoticeToById/${data}` })
}
// 查询部门
export const listallsimple = (): any => {
  return request.getOriginal({ url: 'app/dept/list-all-simple' })
  // https://sit-manage.goldpac.cn/app-api/app/dept/list-all-simple
  // return request.getOriginal({
  //   url: 'https://sit-manage.goldpac.cn/app-api/app/dept/list-all-simple'
  // })
}
// 查询部门员工
export const deptUserPage = (data): any => {
  return request.postOriginal({ url: 'app/user/deptUserPage', data })
  // https://sit-manage.goldpac.cn/app-api/app/user/deptUserPage
  // return request.getOriginal({
  //   url: 'https://sit-manage.goldpac.cn/app-api/app/user/deptUserPage',
  //   data
  // })
}
// 查询多个部门下的员工
export const deptUsersPage = (data): any => {
  return request.postOriginal({ url: 'app/user/deptUsersPage', data })
}

// 判断当前用户是否是客服角色
// export const isCustomer = (): any => {
//   return request.getOriginal({ url: 'app/user/isCustomer' })
// }

// 判断当前用户是否是客服角色
export const isCustomer = (params): any => {
  return request.getOriginal({ url: 'app/user/hasRoleTag', params })
}
