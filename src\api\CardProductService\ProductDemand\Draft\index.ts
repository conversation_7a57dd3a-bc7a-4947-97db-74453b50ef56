import request from '@/config/axios'
import type { verifySchemeType } from './type'

const url = '/makecard/makeCardDraftSchemeBelCl'
const draftUrl = '/makecard/makeCardDsHistory'

// 获取稿样列表
export const getDraftListApi = (data: any): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/list', data })
}

// 回执确认
export const acceptDraftApi = (data: verifySchemeType): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/edit', data })
}

// 获取稿样方案历史文件
export const getDraftHistoryApi = (data): Promise<IResponse> => {
  return request.postOriginal({ url: draftUrl + '/list', data })
}
