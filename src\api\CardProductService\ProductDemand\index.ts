import request from '@/config/axios'

import type {
  makeCardListReqType,
  makeCardListResType,
  makeCardListPageReqType,
  makeCardListAddType,
  makeCardListType,
  makeCardListEditType,
  makeCardListFindType
} from './types'

const url = '/makecard/makeCardRequirement'

// 获取需求列表
export const getMakeCardListApi = (
  data: makeCardListPageReqType
): Promise<IResponse<makeCardListResType>> => {
  return request.postOriginal({
    url: `${url}/list?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  })
}

// 远程搜索需求标题
export const getMakeCardTitleApi = (
  data: makeCardListReqType
): Promise<IResponse<makeCardListType[]>> => {
  return request.postOriginal({ url: url + '/associate', data })
}

// 删除需求
export const delMakeCardApi = (makeCardInfoId: string): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/delete?makeCardInfoId=' + makeCardInfoId })
}

// 新增需求
export const addMakeCardApi = (data: makeCardListAddType): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/save', data })
}

// 编辑需求
export const editMakeCardApi = (data: makeCardListEditType): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/edit', data, timeout: 120000 })
}

// 需求详情
export const findMakeCardApi = (
  data: makeCardListFindType
): Promise<IResponse<makeCardListType>> => {
  return request.postOriginal({ url: url + '/find', data })
}

// 上传文件
export const uploadFileApi = (data, onUploadProgress): Promise<IResponse> => {
  return request.postOriginal({
    url: '/makecard/makeCardFile/uploadfileList',
    data,
    headersType: 'application/x-www-from-urlencoded;charset=UTF-8',
    timeout: 120000,
    onUploadProgress
  })
}

// 下载文件
export const downFileApi = (eosName: string): Promise<IResponse> => {
  return request.getOriginal({ url: `/makecard/makeCardFile/downloadfile?eosName=${eosName}` })
}
// 下载文件-文件流
export const downloadFileApi = (data): any => {
  return request.postDownload({
    url: '/makecard/makeCardFile/downloadFlowFile',
    data,
    headersType: 'application/x-www-from-urlencoded;charset=UTF-8'
  })
}
