export type makeCardListPageReqType = {
  makeCardRequirementInfoTitle: string
  pageNum: number
  pageSize: number
}

export type makeCardListReqType = {
  makeCardRequirementInfoTitle: string
}

export type makeCardListFindType = {
  makeCardRequirementInfoId: string
}

export type makeCardListAddType = {
  makeCardRequirementInfoTitle: string
  makeCardRequirementInfoRemark: string
  makeCardRequirementInfoAttachments: string
  makeCardRequirementInfoAttachmentseos: string
  makeCardRequirementBin: string
  makeCardRequirementRtype: string
}

export type makeCardListEditType = {
  makeCardRequirementInfoId: string
  makeCardRequirementInfoTitle: string
  makeCardRequirementInfoRemark: string
  makeCardRequirementInfoAttachments: string
}

export type makeCardListResType = {
  list: makeCardListType[]
  pageNum: number
  pageSize: number
  pages: number
  total: number
}

export type makeCardListType = {
  makeCardRequirementInfoCname: string
  makeCardRequirementInfoCreateDate: string
  makeCardRequirementInfoId: string
  makeCardRequirementInfoOtherAttachments: string
  makeCardRequirementInfoOtherRemark: string
  makeCardRequirementInfoPhase: number
  makeCardRequirementInfoRank: string
  makeCardRequirementInfoRelevanc: string
  makeCardRequirementInfoSource: number
  makeCardRequirementInfoSpecies: string
  makeCardRequirementInfoTitle: string
  makeCardRequirementInfoWithdraw: string
  makeCardRequirementInfoPhaseText?: string
}
