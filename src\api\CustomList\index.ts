/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-27 17:08:26
 * @LastEditors: HoJack
 * @LastEditTime: 2023-06-29 11:59:28
 * @Description:
 */
import request from '@/config/axios'
const prefix = '/customer/customer'

// 新增
export const addCustomerApi = (data: any) => {
  return request.postOriginal({ url: prefix + '/addCustomer', data })
}
// 列表
export const queryListApi = (data: any, params: any) => {
  return request.post({ url: prefix + '/queryList', data, params })
}
// 未启用客户列表
export const queryUnableListApi = (data: any, params: any) => {
  return request.post({ url: prefix + '/getNotEnabledCustomerPage', data, params })
}
// 启用客户
export const enableCustomerApi = (customerId: string) => {
  return request.put({ url: prefix + `/enable/${customerId}` })
}
// 详情
export const queryOneApi = (params: any) => {
  return request.getOriginal({ url: prefix + '/queryOne', params })
}
// 编辑保存
export const upDataSaveApi = (data: any) => {
  return request.put({ url: prefix + '/updateSave', data })
}
// 启用
export const enableApi = (customerId: any) => {
  return request.post({ url: prefix + '/associate-to-tenant/' + customerId })
}

// // 客户所在地
// export const getAreaListApi = (params: any) => {
//   return request.getOriginal({ url: prefix + '/getAreaList', params })
// }
// 省份
export const getAreatTreeApi = () => {
  return request.get({ url: `/admin-api/system/area/tree` })
}
// 市级
export const getAreatListApi = (params: any) => {
  return request.get({ url: `/admin-api/system/area/list`, params })
}

// 根据id查询城市
export const getAreaApi = (params: { areaId: string }) => {
  return request.getOriginal({ url: prefix + '/getArea', params })
}

// 关联客户名称
export const getRelatedApi = (params: any) => {
  return request.getOriginal({ url: prefix + '/getRelated', params })
}

// 重复名字校验
export const checkNameApi = (params: any) => {
  return request.getOriginal({ url: prefix + '/checkName', params })
}

// 导出
export const exportApi = (data: any) => {
  return request.postDownload({ url: prefix + '/export', data })
}

// 负责人
export const getManagementApi = (params: any): Promise<IResponse> => {
  return request.get({ url: prefix + '/getManagement', params })
}

// 负责人模糊搜索
export const getuserListApi = (params: { nickname: string }): Promise<IResponse> => {
  return request.get({
    url: `/app/user/userList`,
    params
  })
}

// 获取k3用户
export const getK3Customer = () => {
  return request.getOriginal({ url: prefix + '/listK3Customers' })
}

// 客户名称多层
export const getCustomerName = (): any => {
  return request.getOriginal({ url: prefix + '/getNamesNode' })
}

// 获取组织机构列表
export const getListSimpleDepts = (params?): Promise<IResponse> => {
  return request.get({
    url: `/management/resource/permission/listSimpleDepts`,
    params
  })
}

// 获取用户分页列表
export const getResourcePermissionUserPage = (params?): Promise<IResponse> => {
  return request.get({
    url: `/management/resource/permission/getUserPage`,
    params
  })
}

// 获取当前用户的角色列表
export const getListCurrentUserRoles = (params?): Promise<IResponse> => {
  return request.get({
    url: `/management/resource/permission/listCurrentUserRoles`,
    params
  })
}

// 获取租户业务场景树列表
export const getListTenantSceneTrees = (id, rid, params?): Promise<IResponse> => {
  return request.get({
    url: `/management/resource/permission/listTenantSceneTrees/${id}/${rid}`,
    params
  })
}

// 分配或创建子角色资源权限
export const assignOrCreateSubResourcePermissions = (data) => {
  return request.post({
    url: 'management/resource/permission/assignOrCreateSubResourcePermissions',
    data
  })
}

// 业务员（销售）列表
export const getSaleCustomerListApi = () => {
  //return saleUserListTest()//listUserByDeptCodes
  const data = ['sale']
  return request.postOriginal({ url: '/app/user/listUserByDeptCodes', data })
}

// 稿样列表
export const getSampleCustomerListApi = () => {
  //return draftUserListTest()
  //return request.get({ url: prefixC + '/getNames' })
  const data = ['draft']
  return request.postOriginal({ url: '/app/user/listUserByDeptCodes', data })
}
