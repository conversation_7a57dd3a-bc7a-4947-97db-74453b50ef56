/**
 * 客户服务-投诉管理销
 */
import request from '@/config/axios'

// 查看投诉列表
export const findComplaintByPage = (data) => {
  return request.postOriginal({
    url: '/notice/complaint/findComplaintByPage',
    data
  })
}
// 查看投诉列表
export const findComplaintManageByPage = (data) => {
  return request.postOriginal({
    url: '/notice/complaint/findComplaintManageByPage',
    data
  })
}
// 订单编号/投诉产品名称
export const findComplaintDetailByList = (data): Promise<IResponse> => {
  return request.postOriginal({ url: '/notice/complaint/findComplaintDetailByList', data })
}
// 卡号模糊搜索
export const findComplaintByList = (data): Promise<IResponse> => {
  return request.postOriginal({ url: '/notice/complaint/findComplaintByList', data })
}
// 新增投诉
export const addComplaint = (data): Promise<IResponse> => {
  return request.postOriginal({ url: '/notice/complaint/addComplaint', data })
}
// 编辑
export const editComplaint = (data): Promise<IResponse> => {
  return request.postOriginal({ url: '/notice/complaint/editComplaint', data })
}
// 上传附件
export const uploadComplaintAttach = (data): Promise<IResponse> => {
  return request.upload({ url: '/notice/complaint/uploadComplaintAttach', data })
}
// 查看编辑/详情
export const findComplaintById = (complaintId): Promise<IResponse> => {
  return request.postOriginal({ url: `/notice/complaint/findComplaintById/${complaintId}` })
}
// 查询部门
export const listallsimple = (): any => {
  return request.getOriginal({ url: 'app/dept/list-all-simple' })
}
// 查询部门员工
export const deptUserPage = (data): any => {
  return request.postOriginal({ url: 'app/user/deptUserPage', data })
}
// 指派
export const complaintAssign = (data): any => {
  return request.postOriginal({ url: '/notice/complaint/complaintAssign', data })
}
//删除
export const deleteComplaintById = (complaintId): Promise<IResponse> => {
  return request.postOriginal({ url: `/notice/complaint/deleteComplaintById/${complaintId}` })
}
// 查询所有员工
export const userList = (nickname): Promise<IResponse> => {
  return request.getOriginal({ url: `/app/user/userList?nickname=${nickname}` })
}
// 审核流程/处理信息
export const getProcessInstanceDetail = (processId): Promise<IResponse> => {
  return request.getOriginal({
    url: `/management/bpmTask/getProcessInstanceDetail?processInstanceId=${processId}`
  })
}
//销售审核
export const complaintSaleAudit = (data): Promise<IResponse> => {
  return request.postOriginal({ url: `/notice/complaint/complaintSaleAudit`, data })
}
//品控部门审核
export const complaintQualityAudit = (data): Promise<IResponse> => {
  return request.postOriginal({ url: `/notice/complaint/complaintQualityAudit`, data })
}

// 客户名称
export const getcustomername = (params: string): any => {
  return request.getOriginal({ url: `/customer/customer/getNames?customerName=${params}` })
}
// diy订单条件查询
export const selectByOtherClient = (data): Promise<IResponse> => {
  return request.postOriginal({ url: `/order/order/diy/selectDiyOrderByCplt`, data })
}
