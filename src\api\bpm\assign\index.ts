import request from '@/config/axios'

export interface RoleVO {
  id: number
  name: string
  code: string
  sort: number
  status: number
  type: number
  dataScope: number
  dataScopeDeptIds: number[]
  createTime: Date
  isStale: boolean
  applicationCode?: string
  pappId?: string
}

// 查询角色（精简)列表
export const getSimpleRoleList = async (): Promise<RoleVO[]> => {
  return await request.get({ url: '/admin-api/system/role/list-all-simple' })
}

export interface DeptVO {
  id?: number
  name: string
  parentId: number
  status: number
  sort: number
  leaderUserId: number
  phone: string
  email: string
  createTime: Date
}

// 查询部门（精简)列表
export const getSimpleDeptList = async (): Promise<DeptVO[]> => {
  return await request.get({ url: '/admin-api/system/dept/list-all-simple' })
}

export interface PostVO {
  id?: number
  name: string
  code: string
  sort: number
  status: number
  remark: string
  createTime?: Date
}

// 获取岗位精简信息列表
export const getSimplePostList = async (): Promise<PostVO[]> => {
  return await request.get({ url: '/admin-api/system/post/list-all-simple' })
}

export interface UserVO {
  id: number
  username: string
  nickname: string
  deptId: number
  postIds: string[]
  email: string
  mobile: string
  sex: number
  avatar: string
  loginIp: string
  status: number
  remark: string
  loginDate: Date
  createTime: Date
}
// 获取用户精简信息列表
export const getSimpleUserList = (): Promise<UserVO[]> => {
  return request.get({ url: '/admin-api/system/user/list-all-simple' })
}
export type UserGroupVO = {
  id: number
  name: string
  description: string
  memberUserIds: number[]
  status: number
  remark: string
  createTime: string
}

// 获取用户组精简信息列表
export const getSimpleUserGroupList = async (): Promise<UserGroupVO[]> => {
  return await request.get({ url: '/admin-api/bpm/user-group/list-all-simple' })
}
