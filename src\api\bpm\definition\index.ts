import request from '@/config/axios'

// 流程详情获取流程图
export const getProcessDefinitionBpmnXML = async (id: number) => {
  return await request.get({
    // url: '/management/bpmTask/getProcessDefinitionDetail?id=' + id
    url: 'http://10.165.30.166:8050/admin-api/bpm/process-definition/get-bpmn-xml?id=' + id
  })
}

export const getProcessDefinitionPage = async (params) => {
  return await request.get({
    url: 'http://10.165.30.166:8050/admin-api/bpm/process-definition/page',
    params
  })
}

export const getProcessDefinitionList = async (params) => {
  return await request.get({
    url: 'http://10.165.30.166:8050/admin-api/bpm/process-definition/list',
    params
  })
}
