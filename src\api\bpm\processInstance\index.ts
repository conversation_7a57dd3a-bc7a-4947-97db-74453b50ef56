import request from '@/config/axios'

export type Task = {
  id: string
  name: string
}

export type ProcessInstanceVO = {
  id: number
  name: string
  processDefinitionId: string
  category: string
  result: number
  tasks: Task[]
  fields: string[]
  status: number
  remark: string
  businessKey: string
  createTime: string
  endTime: string
}

export const getMyProcessInstancePage = async (params) => {
  return await request.get({
    url: 'http://10.165.30.166:8050/admin-api/bpm/process-instance/my-page',
    params
  })
}

export const cancelProcessInstance = async (id: number, reason: string) => {
  const data = {
    id: id,
    reason: reason
  }
  return await request.delete({
    url: 'http://10.165.30.166:8050/admin-api/bpm/process-instance/cancel',
    data: data
  })
}

export const getProcessInstance = async (id: number) => {
  return await request.get({
    url: '/management/bpmTask/getProcessInstanceDetail?processInstanceId=' + id
    // url: 'http://10.165.30.166:8050/admin-api/bpm/process-instance/get?id=' + id
  })
}

// export const getProcessInstance = async (id: number) => {
//   return await request.get({
//     url: '/management/bpmTask/getProcessInstanceDetail?processInstanceId=' + id
//   })
// }

export const getProcess = async (params) => {
  return await request.get({
    url: 'http://10.165.30.166:8050/admin-api/bpm/process-instance/my-page',
    params
  })
}
