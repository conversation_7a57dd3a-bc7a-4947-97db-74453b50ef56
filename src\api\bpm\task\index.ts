import request from '@/config/axios'

export type TaskVO = {
  id: number
}

//审批人选择弹出框
export enum handleTypeEnum {
  /** 转办*/
  Forward = 'Forward',
  /** 委派*/
  Delegate = 'Delegate',
  /**抄送3 */
  CC = 'CC'
}

// 获取我的已办列表
export const getDoneTaskPage = async (params: any, data: any) => {
  return await request.post({
    url: '/management/bpmTask/doneTasks',
    params,
    data
  })
}

// 获取我的待办列表
export const getTodoTaskPage = async (params: any, data: any) => {
  return await request.post({
    url: '/management/bpmTask/todoTasks',
    // url: 'http://10.165.30.166:8050/admin-api/bpm/task/todo-page',
    params,
    data
  })
}

// 获取我发起的列表
export const getInitiatedTaskPage = async (params: any, data: any) => {
  return await request.post({
    url: '/management/bpmTask/listProcessInstanceOfUser',
    params,
    data
  })
}

export const completeTask = async (data) => {
  return await request.put({ url: 'http://10.165.30.166:8050/admin-api/bpm/task/complete', data })
}

// 审批通过
export const approveTask = async (data) => {
  return await request.post({ url: '/management/bpmTask/approve', data })
}

// 审批驳回
export const rejectTask = async (data) => {
  return await request.post({ url: '/management/bpmTask/reject', data })
}

// 审批转办
export const transfer = async (data) => {
  return await request.post({ url: '/management/bpmTask/transfer', data })
}

// 我发起的-撤回&删除
export const cancelProcessInstance = async (data) => {
  return await request.post({ url: '/management/bpmTask/cancelProcessInstance', data })
}

// 导出任务
export const exportTask = async (params) => {
  return await request.download({
    url: 'http://10.165.30.166:8050/admin-api/bpm/task/export',
    params
  })
}
// 查询部门（精简)列表
export const getSimpleDeptList = async () => {
  return await request.get({
    url: `/admin-api/system/user/list-all-simple`
  })
}

// 待办类型
export const definitionKeyApi = async () => {
  return await request.get({ url: '/management/bpmTask/definitionKey' })
}

// 抄送
export const getCcTaskPage = async (data) => {
  return await request.post({ url: '/management/bpmTask/getListProcessCCOfUser', data })
}

// 委派
export const delegate = async (data) => {
  return await request.post({ url: '/management/bpmTask/delegate', data })
}

// 查询部门列表
export const getListAllSimpleVir = async () => {
  return await request.get({
    url: `/admin-api/system/dept/list-all-simple-vir`
  })
}

// 查询部门列表下人员1
export const getpageVir = async (data) => {
  return await request.get({
    url: `/admin-api/system/user/page-vir`,
    params: data
  })
}
// 查询部门列表下人员2
export const getDeptUserPage = async (deptId) => {
  return await request.post({
    url: `/app/user/deptUserPage`,
    data: {
      deptId
    }
  })
}
// 查询部门列表下人员3
export const getuserListApi = (deptId): Promise<IResponse> => {
  return request.get({
    url: `/app/user/userList`,
    params: {
      deptId
    }
  })
}

//我委派的任务记录列表
export const getDelegateOwnerList = async (data) => {
  return await request.post({
    url: '/management/bpmTask/listProcessInstanceTaskDelegateOfOwner',
    data
  })
}

//委派我的任务记录列表
export const getDelegateAssigneeList = async (data) => {
  return await request.post({
    url: '/management/bpmTask/listProcessInstanceTaskDelegateOfAssignee',
    data
  })
}
