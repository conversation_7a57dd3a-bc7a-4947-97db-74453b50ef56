import request from '@/config/axios'
const DIY = '/diy'

/** 简单查询服务配置 */
export const getListSimple = (data) => {
  return request.upload({ url: DIY + '/belConfig/listSimple', data })
}
/** 项目查询 */
export const postFindProjectList = () => {
  return request.postOriginal({
    url: '/sale/Project/findProjectListByCustomer',
    data: {
      applyServiceName: '智能服务'
    }
  })
}

// 客户名称查询
export const getCustomerListApi = (params: string, isAcc?: number): any => {
  let url = `/customer/customer/getNames?customerName=${params}`
  if (isAcc) {
    url = `${url}&isAcc=${isAcc}`
  }
  return request.getOriginal({
    url
  })
}

// 获取项目列表
export const getProjectListApiByCustomerId = (customerId: string) => {
  return request.postOriginal({
    url: '/sale/Project/findProjectList',
    data: {
      customerId
    }
  })
  //return projectListTest()
}
