/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-28 16:44:17
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-11-14 11:40:44
 * @Description:
 */
import request from '@/config/axios'
const Prefix = '/diy'

/*
 * 模板设计相关接口 开始
 */
// 分页查询设计
export const designList = (data) => {
  return request.upload({ url: Prefix + '/belDesign/list', data })
}

// 查询单个设计
export const designGet = (params) => {
  return request.get({ url: Prefix + '/belDesign/get', params })
}

// 复制设计
export const designCopy = (data) => {
  return request.post({ url: Prefix + '/bclDesign/copy', data })
}

// 设计校验是否有已开启的
export const designHaveEnableDesign = (data) => {
  return request.post({ url: Prefix + '/bclDesign/haveEnableDesign', data })
}

// 启用设计
export const designEnableDesign = (data) => {
  return request.post({ url: Prefix + '/bclDesign/enableDesign', data })
}

// 图片列表查询
export const designListImage = (data) => {
  return request.upload({ url: Prefix + '/belImage/listImage', data })
}

// 热区
export const designBelDictList = (data) => {
  return request.upload({ url: Prefix + '/belGroup/list', data })
}

// 热区设置保存
export const designBelDictSave = (data) => {
  return request.postOriginal({ url: Prefix + '/belGroup/saveWithChild', data })
}

// 热区设置编辑
export const designBelDictEdit = (data) => {
  return request.postOriginal({ url: Prefix + '/belGroup/editWithChild', data })
}

// 热区删除
export const designBelGroupDelete = (data) => {
  return request.postOriginal({ url: Prefix + '/belGroup/delete', data })
}

// 首页设计保存
export const designSave = (data) => {
  return request.post({ url: Prefix + '/bclDesign/save', data })
}

// 首页设计编辑
export const designEdit = (data) => {
  return request.post({ url: Prefix + '/bclDesign/edit', data })
}

// 上传图片
export const designEosImage = (data) => {
  return request.upload({ url: Prefix + '/belImage/eosImage', data })
}

// 简单查询服务配置
export const serverSimple = (data) => {
  return request.upload({ url: Prefix + '/belConfig/listSimple', data })
}

// 产品列表
export const getOutProductList = (data) => {
  return request.postOriginal({
    url: '/product/diyCard/out/v1/list',
    data
  })
}

// 产品分类
export const belThemeList = (data) => {
  return request.upload({ url: Prefix + '/belGroup/list', data })
}

// 获取组件数据
export const getGroupComptInfo = () => {
  return request.get({
    url: Prefix + '/bclComponent/getGroupComptInfo'
  })
}
// 保存设计信息到缓存
export const setDesignPageCache = (data) => {
  return request.post({
    url: Prefix + '/bclDesign/setDesignPageCache',
    data
  })
}
// 获取组件数据
export const getChannelDefault = (configId) => {
  return request.get({
    url: Prefix + '/belChannel/getDefault?configId=' + configId
  })
}

// 查询默认渠道商品信息
export const getDefaultChannelPro = (params) => {
  return request.get({
    url: Prefix + '/bclConfig/listDefaultChannelPro',
    params
  })
}
