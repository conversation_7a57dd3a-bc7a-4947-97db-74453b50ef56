import request from '@/config/axios'
import type { UserType } from './types'
import { useSsoStoreWithOut } from '@/store/modules/sso'
interface RoleParams {
  roleName: string
}

export const loginApi = (data: UserType): Promise<IResponse<UserType>> => {
  return request.postOriginal({ url: '/user/login', data })
}

export const loginOutApi = (): Promise<IResponse> => {
  const ssoStore = useSsoStoreWithOut()
  // // 发起请求
  // return request.postOriginal({
  //   url: `/admin-api/system/auth/logout`
  // })

  // 退出登录 - 独立服务
  return request.postOriginal({
    url: `/admin-api/auth/logout`
  })
}

export const getUserListApi = ({ params }: AxiosConfig) => {
  return request.getOriginal<{
    code: string
    data: {
      list: UserType[]
      total: number
    }
  }>({ url: '/user/list', params })
}

export const getAdminRoleApi = (
  params: RoleParams
): Promise<IResponse<AppCustomRouteRecordRaw[]>> => {
  return request.getOriginal({ url: '/role/list', params })
}

export const getTestRoleApi = (params: RoleParams): Promise<IResponse<string[]>> => {
  return request.getOriginal({ url: '/role/list', params })
}

// 获取用户权限信息
export const getInfo = () => {
  return request.post({
    url: `/admin-api/system/auth/get-login-user-info`
  })
}

// 获取用户权限信息-租户改造新版
export const getUserInfo = () => {
  return request.post({
    url: `/admin-api/system/auth/get-user-info`
  })
}

// 路由-路由菜单+权限按钮-新
export const getRouteAndPermission = () => {
  return request.post({
    url: `/admin-api/system/auth/get-login-user-permission`
  })
}

// 路由-路由菜单+权限按钮-新-租户改造
export const getRouteAndPermissionRenovation = (params?) => {
  return request.post({
    url: `/admin-api/system/auth/get-user-permission`,
    params
  })
}

// 通过邮箱重置用户密码
export const updateMailpassword = (data) => {
  return request.postOriginal({ url: 'system/user/updateMailpassword', data })
}

// 重置用户密码
export const updatepasswordByCode = (data) => {
  return request.putOriginal({
    url: `/admin-api/system/user/updatepasswordByCode`,
    data
  })
}

// 发送验证码
export const sendUserCode = (params) => {
  return request.getOriginal({
    url: `/admin-api/system/email/sendUserCode`,
    params
  })
}
