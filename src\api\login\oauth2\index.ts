/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 13:59:22
 * @LastEditors: Ho<PERSON><PERSON>
 * @LastEditTime: 2023-06-28 10:03:57
 * @Description:
 */
import request from '@/config/axios'

// 通过授权码获取token
interface tokenRequset {
  code: string
  redirect_uri: string
  grant_type: string
}
// export const getToken = (params: tokenRequset) => {
//   // 发起请求
//   return request.getOriginal({
//     url: `/oauth2/token`,
//     params
//   })
// }

// 独立服务改版
export const getToken = (params: tokenRequset) => {
  // 发起请求
  return request.postOriginal({
    url: `/admin-api/auth/business/get-access-token`,
    params
  })
}

// // 发起授权获取重定向url
// export const autoAuthorize = (clientId: string, redirectUri: string, state?: string) => {
//   // 发起请求
//   return request.post({
//     url: '/admin-api/system/oauth2/authorize',
//     headers: {
//       'Content-type': 'application/x-www-form-urlencoded'
//     },
//     params: {
//       auto_approve: false,
//       response_type: 'code',
//       scope: JSON.stringify({ 'user.read': true, 'user.write': true }),
//       client_id: clientId,
//       redirect_uri: redirectUri,
//       state: state
//     }
//   })
// }

// 发起授权获取重定向url - 独立服务
export const autoAuthorize = (clientId: string, redirectUri: string, state?: string) => {
  // 发起请求
  return request.post({
    url: '/admin-api/auth/oauth2/authorize',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded'
    },
    params: {
      auto_approve: false,
      response_type: 'code',
      scope: JSON.stringify({ 'user.read': true, 'user.write': true }),
      client_id: clientId,
      redirect_uri: redirectUri,
      state: state
    }
  })
}
