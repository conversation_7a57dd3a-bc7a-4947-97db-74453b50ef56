/*
 * @Author: Ho<PERSON><PERSON>
 * @Date: 2023-06-13 08:59:34
 * @LastEditors: HoJack
 * @LastEditTime: 2023-10-16 19:42:58
 * @Description:
 */
export type UserLoginType = {
  username: string
  password: string
}

export type UserType = {
  username: string
  password: string
  role: string
  roleId: string
  permissions: string | string[]
}

export type TokenType = {
  id: number // 编号
  accessToken: string // 访问令牌
  refreshToken: string // 刷新令牌
  userId: number // 用户编号
  userType: number //用户类型
  clientId: string //客户端编号
  expiresTime: number //过期时间
}

export type OAuth2OpenAccessTokenRespType = {
  /**
   * 过期时间,单位：秒
   */
  expires_in?: number

  /**
   * 授权范围,如果多个授权范围，使用空格分隔
   */
  scope?: string
  /**
   * 令牌类型
   */
  token_type?: string
  /**
   * 访问令牌
   */
  access_token?: string
  /**
   * 刷新令牌
   */
  refresh_token?: string
  /**
   * 访问令牌
   */
  accessToken?: string
  /**
   * 刷新令牌
   */
  refreshToken?: string
}

/**单点登录url携带参数 */
export interface OauthUrlQuery {
  id?: number // 编号
  accessToken: string // 访问令牌
  access_token: string // 访问令牌
  refreshToken: string // 刷新令牌
  refresh_token: string // 刷新令牌
  userId?: number // 用户编号
  userType?: number //用户类型
  clientId: string //客户端编号
  expiresTime: number | string //过期时间
  expires_in: number | string // 过期时间,单位：秒
  tenantId: string //租户Id
  roleId: string //角色Id
  lang: LocaleType
  goPath?: string //跳转路径
}
