import request from '@/config/axios'
import type { verifySchemeType } from './type'

const url = '/makecard/makeCardDesignScheme'

// 获取设计师列表
export const getDesignListApi = (data: any): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/designSchemeList', data })
}

// 获取分配设计师
export const getDesignApi = (data: any): Promise<IResponse> => {
  return request.postOriginal({ url: '/makecard/makeCardDesignMange/findEx', data })
}

// 回执确认
export const acceptDesignApi = (data: verifySchemeType): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/isDesignScheme', data })
}

// 回执确认 批量
export const acceptDesignListApi = (data: verifySchemeType): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/isDesignSchemeList', data })
}

// 判断是否在聊天组内
export const isImScope = (data): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/isImScope', data })
}
