import request from '@/config/axios'

// 创建订单
export const createOrder = (data: any): any => {
  return request.postOriginal({ url: `/order/order/samplecard/create`, data })
}

// 订单列表
export const getOrderListApi = (data: any): any => {
  return request.postOriginal({ url: `/order/order/samplecard/list`, data })
}

// 订单详情
export const getOrderDetailApi = (data: any): any => {
  return request.getOriginal({ url: `/order/order/samplecard/${data.orderId}` })
}

// 获取用户信息
export const getOrderUserInfoApi = (data: any): any => {
  return request.getOriginal({ url: `/customer/customer/queryOne?customerId=${data.customerId}` })
}

// 获取收获地址
export const getReceivingList = (data?: any): Promise<IResponse> => {
  return request.postOriginal({ url: `/order/order/logistics/ReceivingList`, data })
}

// 新建收获地址
export const createReceiving = (data): Promise<IResponse> => {
  return request.postOriginal({ url: '/order/order/logistics/createReceivingInfo', data })
}

// 编辑收获地址
export const editReceiving = (data): Promise<IResponse> => {
  return request.postOriginal({ url: '/order/order/logistics/receivingInfoUpdate', data })
}

// 获取省市区全名
export const getAreaApi = (data): Promise<IResponse> => {
  return request.getOriginal({ url: `/customer/customer/getArea?areaId=${data}` })
}

// 上传文件
export const uploadFileApi = (data: FormData, onUploadProgress): Promise<IResponse> => {
  return request.postOriginal({
    url: '/order/order/upload',
    data,
    headersType: 'application/x-www-from-urlencoded;charset=UTF-8',
    onUploadProgress
  })
}

// 下载文件-文件流
export const downloadFileApi = (data): any => {
  return request.download({
    url: '/order/order/download',
    params: data,
    timeout: 240000
  })
}

// 获取物流数据
export const logisticsTraces = (data): Promise<IResponse> => {
  return request.postOriginal({
    url: `/order/order/logistics/sample/test/logisticsTraces?orderId=${data}`
  })
}

// diy订单条件查询
export const selectByOtherClient = (data): Promise<IResponse> => {
  return request.postOriginal({ url: `/order/order/diy/selectDiyOrderByCplt`, data })
}

// 获取单个物流信息
export const searchTraceMailNo = (data) => {
  // return request.postOriginal({ url: 'http://localhost:4000/express/search/trace/mailNo', data })
  return request.postOriginal({ url: '/order/express/search/trace/mailNo', data })
}
