/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 18:04:04
 * @LastEditors: HoJack
 * @LastEditTime: 2023-11-01 20:26:50
 * @Description:
 */
import request from '@/config/axios'

// 客户名称
export const getCustomerName = (params: string, isAcc?: number): any => {
  let url = `/customer/customer/getNames?customerName=${params}`
  if (isAcc) {
    url = `${url}&isAcc=${isAcc}`
  }
  return request.getOriginal({
    url
  })
}

// 客户账号
export const getCustomerUser = (params: string): any => {
  return request.getOriginal({
    url: `/customer/customer/${params}/getUsersByTenantId`
  })
}

// 客户详情
export const getCustomerDetailApi = (params: string): any => {
  return request.get({ url: `/customer/customer/queryOne?customerId=${params}` })
}

// 项目列表
export const getProjectList = (data: any): any => {
  return request.postOriginal({ url: `/sale/Project/findProjectListByCustomer`, data })
}

// 获取设计师列表
export const getDesignList = (projectId: string): any => {
  return request.postOriginal({ url: `/sale/Project/findProjectById/${projectId}` })
}
