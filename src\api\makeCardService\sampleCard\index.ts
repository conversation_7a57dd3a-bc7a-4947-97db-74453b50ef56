import request from '@/config/axios'
import type { sampleType, verifySchemeType } from './type'

const url = '/makecard/makeCardSampleCardBelCl'

// 编辑样卡
export const getSampleEditApi = (data: verifySchemeType): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/edit', data })
}

// 获取样卡列表
export const getSampleListApi = (data: any): Promise<IResponse> => {
  return request.postOriginal({ url: url + '/list', data })
}

// 订单详情
export const getOrderDetailApi = (data: any): any => {
  return request.getOriginal({ url: `/order/order/samplecard/${data.orderId}` })
}

// 订单列表
export const getOrderListApi = (data: any): any => {
  return request.postOriginal({ url: `/order/order/samplecard/list`, data })
}

// 下载文件-文件流
export const downloadFileApi = (data): any => {
  return request.download({
    url: '/order/order/download',
    params: data,
    timeout: 240000
  })
}

// 获取物流数据
export const logisticsTraces = (data): Promise<IResponse> => {
  return request.postOriginal({
    url: `/order/order/logistics/sample/test/logisticsTraces?orderId=${data}`
  })
}

// 获取单个物流信息
export const searchTraceMailNo = (data) => {
  // return request.postOriginal({ url: 'http://localhost:4000/express/search/trace/mailNo', data })
  return request.postOriginal({ url: '/order/express/search/trace/mailNo', data })
}
