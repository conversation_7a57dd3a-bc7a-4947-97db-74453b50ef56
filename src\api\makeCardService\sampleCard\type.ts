export type sampleType = {
  diySamplecardInfoOrdernumber: string // 样卡订单号
  diySamplecardInfoOrderremark: string // 样卡备注
  diySamplecardInfoDraftschemeid: string // 稿样ID
  diySamplecardInfoCardnumber: string // 制卡需求ID
  diySamplecardInfoCardtitle: string // 制卡需求标题
  diySamplecardInfoMoneyname: string // 卡款名称
  diySamplecardInfoMoneyno: string // 卡款编号
}

export type verifySchemeType = {
  diySamplecardInfoId?: string
  diySamplecardInfoIdList?: string[]
  diySamplecardInfoReceipt: string
  diySamplecardInfoCardnumber: string
  diySamplecardInfoStatus: number
  diySamplecardInfoReceiptfu: string
  diySamplecardInfoReceiptfueos: string
}
