export type makeCardListPageReqType = {
  makeCardRequirementInfoTitle: string
  pageNum: number
  pageSize: number
}

/**
 * @description 卡产品需求列表查询参数
 * @type {type}
 */
export type makeCardListReqType = {
  /**
   * @description 产品需求标题
   * @type {string}
   */
  makeCardRequirementInfoTitle: string
  /**
   * @description 产品名称
   * @type {string}
   */

  productName: string
  /**
   * @description 需求类型
   * @type {string}
   */
  makeCardRequirementRtype?: string
}

export type makeCardListFindType = {
  makeCardRequirementInfoId: string
}

export type makeCardListAddType = {
  makeCardRequirementInfoSource: number
  makeCardRequirementInfoTitle: string
  makeCardRequirementInfoRemark: string
  makeCardRequirementInfoAttachments: string
  makeCardRequirementInfoAttachmentseos: string
  makeCardRequirementBin: string
  makeCardRequirementRtype: string
  themeName: string
  makeCardRequirementInfoUserId?: string
  makeCardRequirementInfoCId?: string
  makeCardRequirementInfoCname?: string
  makeCardRequirementInfoUserName?: string
  imTenantId?: string
  makeCardRequirementInfoCcode?: string
  makeCardRequirementInfoRelevanc?: string
  makeCardRequirementInfoRelevancName?: string
  makeCardRequirementInfoProjectCode?: string

  /**
   * @description 产品类型
   * @type {productType}
   */
  productType: productType
  /**
   * @description 产品主键Id
   * @type {string}
   */
  productId?: string
  /**
   * @description 产品名称
   * @type {productName}
   */
  productName?: string
}

export type makeCardListEditType = {
  makeCardRequirementInfoId: string
  makeCardRequirementInfoTitle: string
  makeCardRequirementInfoRemark: string
  makeCardRequirementInfoAttachments: string
}

export type makeCardListResType = {
  list: makeCardListType[]
  pageNum: number
  pageSize: number
  pages: number
  total: number
}

export type makeCardListType = {
  makeCardRequirementInfoCname: string
  makeCardRequirementInfoCreateDate: string
  makeCardRequirementInfoId: string
  makeCardRequirementInfoOtherAttachments: string
  makeCardRequirementInfoOtherRemark: string
  makeCardRequirementInfoPhase: number
  makeCardRequirementInfoRank: string
  makeCardRequirementInfoRelevanc: string
  makeCardRequirementInfoSource: number
  makeCardRequirementInfoSpecies: string
  makeCardRequirementInfoTitle: string
  makeCardRequirementInfoWithdraw: string
  makeCardRequirementInfoPhaseText?: string
}

/**
 * @description 产品类型定义
 * @export
 * @enum {number}
 */
export enum productType {
  /**
   * 批卡
   */
  batch = 1,

  /**
   * DIY
   */
  diy = 2
}
