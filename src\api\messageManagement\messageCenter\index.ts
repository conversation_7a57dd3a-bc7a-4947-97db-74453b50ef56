import request from '@/config/axios'

// 根据id查询待办详情
export const findMessageById = (messageId) => {
  return request.post({
    url: `/notice/message/findMessageById/${messageId}`
  })
}

// 读取消息推送
export const readMessageTo = (data) => {
  return request.post({
    url: `/notice/messageTo/readMessageTo`,
    data
  })
}

// 查询未读总数
export const countUnreadMessageTo = () => {
  return request.post({
    url: `/notice/messageTo/countUnreadMessageTo`,
    data: {
      toUserType: 'CLIENT'
    }
  })
}

// 分页查询消息中心列表
export const findMessageToByPage = (data) => {
  return request.post({
    url: `/notice/messageTo/findMessageToByPage`,
    data
  })
}
