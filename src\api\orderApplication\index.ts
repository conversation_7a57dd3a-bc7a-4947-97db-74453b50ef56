import request from '@/config/axios'
const prefix = '/order/order/application'
import IOrderApplication from './types/orderApplication.d'
import { orderApplicationReviewResultEnum } from './types/enum.d'
import {
  IOrderApplicationQueryParams,
  IOrderApplicationPageResult
} from '@/api/orderApplication/types/request.d'
/**
 * @description 查询订单申请数据
 * @export
 * @param {number} pageIndex
 * @param {number} pageSize
 * @param {IOrderApplication} data
 * @return {*}  {Promise<IOrderApplication>}
 */
export function searchApplications(
  pageIndex: number,
  pageSize: number,
  data: IOrderApplicationQueryParams
): Promise<IOrderApplicationPageResult> {
  const params = { pageNo: pageIndex, pageSize: pageSize }
  return request.post<IOrderApplicationPageResult>({ url: prefix + '/page', data, params })
}

/**
 * @description 查询样卡订单申请数据
 * @export
 * @param {number} pageIndex
 * @param {number} pageSize
 * @param {IOrderApplication} data
 * @return {*}  {Promise<IOrderApplication>}
 */
export function searchSampleApplications(
  pageIndex: number,
  pageSize: number,
  data: IOrderApplicationQueryParams
): Promise<IOrderApplicationPageResult> {
  const params = { pageNo: pageIndex, pageSize: pageSize }
  return request.post<IOrderApplicationPageResult>({ url: prefix + '/sample/page', data, params })
}

/**
 * @description 查询备库订单申请数据
 * @export
 * @param {number} pageIndex
 * @param {number} pageSize
 * @param {IOrderApplication} data
 * @return {*}  {Promise<IOrderApplication>}
 */
export function searchPreparationApplications(
  pageIndex: number,
  pageSize: number,
  data: IOrderApplicationQueryParams
): Promise<IOrderApplicationPageResult> {
  const params = { pageNo: pageIndex, pageSize: pageSize }
  return request.post<IOrderApplicationPageResult>({ url: prefix + '/standby/page', data, params })
}

/**
 * @description 获取指定订单申请数据
 * @export
 * @param {string} applyId
 * @return {*}
 */
export function getApplication(applyId: string) {
  return request.get<IOrderApplication>({ url: `${prefix}/${applyId}` })
}

/**
 * @description 保存订单申请数据
 * @export
 * @param {IOrderApplication} data
 * @return {*}
 */
export function saveApplication(data: IOrderApplication) {
  return request.post<boolean>({ url: prefix + '/save', data })
}

/**
 * @description 提交订单申请数据
 * @export
 * @param {IOrderApplication} data
 * @return {*}
 */
export function submitApplication(data: IOrderApplication) {
  return request.post<boolean>({ url: prefix + '/submit', data })
}

/**
 * @description 销售审批
 * @export
 * @param {IOrderApplication} data
 * @return {*}
 */
export function saleAudit(data: IOrderApplication) {
  return request.post<boolean>({ url: prefix + '/saleAudit', data })
}

/**
 * @description 经理审批
 * @export
 * @param {string} applyId
 * @param {orderApplicationReviewResultEnum} result
 * @param {string} remark
 * @param {string} leaderId
 * @return {*}
 */
export function saleManagerAudit(
  applyId: string,
  result: orderApplicationReviewResultEnum,
  remark: string,
  leaderId: string
) {
  const data = { applyId, result, remark, leaderId }
  return request.post<boolean>({ url: prefix + '/saleManagerAudit', data })
}

/**
 * @description 领导审批
 * @export
 * @param {string} applyId
 * @param {orderApplicationReviewResultEnum} result
 * @param {string} remark
 * @return {*}
 */
export function saleLeaderAudit(
  applyId: string,
  result: orderApplicationReviewResultEnum,
  remark: string
) {
  const data = { applyId, result, remark }
  return request.post<boolean>({ url: prefix + '/saleLeaderAudit', data })
}

/**
 * @description 取消
 * @export
 * @param {string} applyId
 * @param {orderApplicationReviewResultEnum} result
 * @param {string} remark
 * @return {*}
 */
export function cancelApplication(applyId: string) {
  const data = { applyId }
  return request.post<boolean>({ url: prefix + '/cancel', data })
}
