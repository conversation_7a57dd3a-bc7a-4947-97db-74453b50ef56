/**
 * @description 申请单枚举类型
 * @export
 * @enum {string}
 */
export enum orderApplicationTypeEnum {
  /**
   * 免费样卡
   */
  FreeSampleCard = 'FreeSampleCard',

  /**
   * 入库样卡
   */
  InboundSampleCard = 'InboundSampleCard',

  /**
   * 备库订单
   */
  StandbyOrder = 'StandbyOrder',

  /**
   * 收费样卡
   */
  ChargeSampleCard = 'ChargeSampleCard'
}

/**
 * @description 申请单状态枚举
 * @export
 * @enum {string}
 */
export enum orderApplicationStatusEnum {
  /**
   * 取消
   */
  CANCEL = 'CANCEL',
  /**
   * 待提交
   */
  WAIT_SUBMIT = 'WAIT_SUBMIT',
  /**
   * 销售审核
   */
  SALE_AUDIT = 'SALE_AUDIT',
  /**
   * 销售经理审核
   */
  MANAGE_AUDIT = 'MANAGE_AUDIT',
  /**
   * 领导审核
   */
  LEADER_AUDIT = 'LEADER_AUDIT',
  /**
   * 已完成
   */
  FINISH = 'FINISH',
  /**
   * 新建
   */
  CREATE = 'CREATE'
}

/**
 * @description 申请单节点的评审结果
 * @export
 * @enum {string}
 */
export enum orderApplicationReviewResultEnum {
  /**
   * 通过
   */
  OK = 'OK',
  /**
   * 不通过
   */
  NT = 'NT'
}

/**
 * @description 产品类型
 * @export
 * @enum {number}
 */
export enum productTypeEnum {
  /**
   * 卡产品
   */
  card = '1',

  /**
   * 非卡产品
   */
  nonCard = '2'
}

export enum standbyTypeEnum {
  /**
   * 成品备库
   */
  finish = 'finish',
  /**
   * 半成品备库
   */
  semiFinish = 'semiFinish'
}
