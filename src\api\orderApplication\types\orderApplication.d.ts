import {
  orderApplicationTypeEnum,
  orderApplicationReviewResultEnum,
  orderApplicationStatusEnum
} from './enum'
import OrderApplicationDemand from './orderApplicationDemand'
import OrderApplicationProduct from './orderApplicationProduct'

/**
 * @description 订单申请
 * @export
 * @interface IOrderApplication
 */
export default interface IOrderApplication {
  /**
   * @description 主键id
   * @type {string}
   * @memberof IOrderApplication
   */
  id: string
  /**
   * @description 申请单编号
   * @type {string}
   * @memberof IOrderApplication
   */
  applyCode: string
  /**
   * @description 申请类型枚举
   * @type {orderApplicationTypeEnum}
   * @memberof IOrderApplication
   */
  type: orderApplicationTypeEnum
  /**
   * @description 客户Id
   * @type {string}
   * @memberof IOrderApplication
   */
  customerId: string
  /**
   * @description 客户编号
   * @type {string}
   * @memberof IOrderApplication
   */
  customerCode: string

  /**
   * @description 客户名称
   * @type {string}
   * @memberof IOrderApplication
   */
  customerName: string

  /**
   * @description 备注
   * @type {string}
   * @memberof IOrderApplication
   */
  remark: string
  /**
   * @description 创建人
   * @type {string}
   * @memberof IOrderApplication
   */
  createBy: string

  /**
   * @description 创建人名称
   * @type {string}
   * @memberof IOrderApplication
   */
  createName: string

  /**
   * @description 创建时间
   * @type {Date}
   * @memberof IOrderApplication
   */
  createDate?: Date

  /**
   * @description 销售Id
   * @type {string}
   * @memberof IOrderApplication
   */
  saleUserId: string

  /**
   * @description 销售人员姓名
   * @type {string}
   * @memberof IOrderApplication
   */
  saleUserName: string

  /**
   * @description 销售审批结果
   * @type {orderApplicationReviewResultEnum}
   * @memberof IOrderApplication
   */
  saleUserResult?: orderApplicationReviewResultEnum

  /**
   * @description 销售审时间
   * @type {Date}
   * @memberof IOrderApplication
   */
  saleUserTime?: Date

  /**
   * @description 销售经理Id
   * @type {string}
   * @memberof IOrderApplication
   */
  managerId: string

  /**
   * @description 销售经理姓名
   * @type {string}
   * @memberof IOrderApplication
   */
  managerName: string

  /**
   * @description 经理审批结果
   * @type {string}
   * @memberof IOrderApplication
   */
  managerResult?: orderApplicationReviewResultEnum

  /**
   * @description 经理审批时间
   * @type {Date}
   * @memberof IOrderApplication
   */
  managerTime?: Date

  /**
   * @description 交付日期
   * @type {Date}
   * @memberof IOrderApplication
   */
  deliveryAt?: Date

  /**
   * @description 交付方式
   * @type {string}
   * @memberof IOrderApplication
   */
  deliveryType: string

  /**
   * @description 是否加急
   * @type {boolean}
   * @memberof IOrderApplication
   */
  urgentSign: boolean

  /**
   * @description 加急原因
   * @type {string}
   * @memberof IOrderApplication
   */
  urgentReason: string

  /**
   * @description 样卡申请状态
   * @type {orderApplicationStatusEnum}
   * @memberof IOrderApplication
   */
  status: orderApplicationStatusEnum

  /**
   * @description 样卡需求申请表信息
   * @type {OrderApplicationDemand}
   * @memberof IOrderApplication
   */
  demand?: OrderApplicationDemand

  /**
   * @description 申请产品信息数据
   * @type {OrderApplicationProduct[]}
   * @memberof IOrderApplication
   */
  productList: OrderApplicationProduct[]

  /**
   * @description 卡产品需求编号
   * @type {string}
   * @memberof IOrderApplication
   */
  requirementCode: string

  /**
   * @description UMV订单ID
   * @type {string}
   * @memberof IOrderApplication
   */
  umvOrderId: string

  /**
   * @description UMV订单编号
   * @type {string}
   * @memberof IOrderApplication
   */
  umvOrderCode: string

  /**
   * @description  领导人ID
   * @type {string}
   * @memberof IOrderApplication
   */
  leaderId: string

  /**
   * @description 领导人名称
   * @type {string}
   * @memberof IOrderApplication
   */
  leaderName: string

  /**
   * @description 领导审批结果
   * @type {orderApplicationReviewResultEnum}
   * @memberof IOrderApplication
   */
  leaderResult?: orderApplicationReviewResultEnum

  /**
   * @description 领导审批时间
   * @type {Date}
   * @memberof IOrderApplication
   */
  leaderTime?: Date
}
