/**
 * @description 订单申请需求要素对象
 * @export
 * @interface IOrderApplicationDemand
 */
export default interface IOrderApplicationDemand {
  /**
   * @description 主键ID
   * @type {string}
   * @memberof OrderApplicationDemand
   */
  id: string
  /**
   * @description 订单申请主键
   * @type {string}
   * @memberof OrderApplicationDemand
   */
  applyId: string

  /**
   * @description 申请的用途
   * @type {IDemandObject}
   * @memberof IOrderApplicationDemand
   */
  purpose: IDemandObject

  /**
   * @description 正面印和刷要素，
   * @type {IDemandObject}
   * @memberof IOrderApplicationDemand
   */
  front: IDemandObject

  /**
   * @description 背面印刷要素
   * @type {IDemandObject}
   * @memberof IOrderApplicationDemand
   */
  back: IDemandObject

  /**
   * @description 其他要素，
   * @type {IDemandObject}
   * @memberof IOrderApplicationDemand
   */
  other: IDemandObject

  /**
   * @description 个人化需求，
   * @type {IDemandObject}
   * @memberof IOrderApplicationDemand
   */
  personal: IDemandObject

  /**
   * @description PM检测要求，
   * @type {IDemandObject}
   * @memberof IOrderApplicationDemand
   */
  pm: IDemandObject

  /**
   * @description 安全处理要求，
   * @type {IDemandObject}
   * @memberof IOrderApplicationDemand
   */
  safety: IDemandObject

  /**
   * @description 备注
   * @type {string}
   * @memberof IOrderApplicationDemand
   */
  remark: string
}

/**
 * @description 需求要素对象
 * @export
 * @interface IDemandList
 */
export interface IDemandObject {
  /**
   * @description 需求项数组
   * @type {IDemandItem[]}
   * @memberof IDemandList
   */
  items: IDemandItem[]
  remark: string
}

/**
 * @description 需求项
 * @export
 * @interface IDemandItem
 */
export interface IDemandItem {
  /**
   * @description 需求项ID
   * @type {string}
   * @memberof IDemandItem
   */
  key: string

  /**
   * @description 需求项名称
   * @type {string}
   * @memberof IDemandItem
   */
  value: string
}
