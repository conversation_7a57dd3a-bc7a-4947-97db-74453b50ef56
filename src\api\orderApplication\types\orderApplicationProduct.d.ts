import { productTypeEnum, standbyTypeEnum } from './enum'

export default interface IOrderApplicationProduct {
  /**
   * @description id
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  id: string
  /**
   * @description 申请表id
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  applyId: string

  /**
   * @description 产品主键Id
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  productId: string
  /**
   * @description 产品名称
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  productName: string

  /**
   * @description 卡基编号
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  cardCode: string
  /**
   * @description 数量
   * @type {number}
   * @memberof OrderApplicationProduct
   */
  amount: number

  /**
   * @description 备注
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  remark: string

  /**
   * @description 方案数
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  plan: string

  /**
   * @description 分行信息
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  branchInfo: string

  /**
   * @description 备库类型
   * @type {standbyTypeEnum}
   * @memberof OrderApplicationProduct
   */
  standbyType: standbyTypeEnum

  /**
   * @description 产品类型
   * @type {productTypeEnum}
   * @memberof OrderApplicationProduct
   */
  productType: productTypeEnum

  /**
   * @description 客户产品代码
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  customerProductCode: string

  /**
   * @description 期望交期
   * @type {Date}
   * @memberof OrderApplicationProduct
   */
  deliveryAt?: Date

  /**
   * @description 表格行号索引
   * @type {string}
   * @memberof OrderApplicationProduct
   */
  index: string
}
