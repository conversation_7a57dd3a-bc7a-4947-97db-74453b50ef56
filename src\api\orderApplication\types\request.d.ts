import {
  orderApplicationTypeEnum,
  orderApplicationReviewResultEnum,
  orderApplicationStatusEnum
} from './enum'
import OrderApplicationDemand from './orderApplicationDemand'
import OrderApplicationProduct from './orderApplicationProduct'
import IOrderApplication from './orderApplication'

/**
 * @description 订单申请
 * @export
 * @interface IOrderApplication
 */
export interface IOrderApplicationQueryParams {
  /**
   * @description 申请单号
   * @type {string}
   * @memberof IOrderApplicationQueryParams
   */
  applyCode: string

  /**
   * @description 申请类型枚举
   * @type {orderApplicationTypeEnum}
   * @memberof IOrderApplication
   */
  type?: orderApplicationTypeEnum
  /**
   * @description 客户编号
   * @type {string}
   * @memberof IOrderApplication
   */
  customerCode: string

  /**
   * @description 客户名称
   * @type {string}
   * @memberof IOrderApplication
   */
  customerName: string

  /**
   * @description 样卡申请状态
   * @type {orderApplicationStatusEnum}
   * @memberof IOrderApplication
   */
  status?: orderApplicationStatusEnum
}

export interface IOrderApplicationPageResult {
  total: number
  list: IOrderApplication[]
}
