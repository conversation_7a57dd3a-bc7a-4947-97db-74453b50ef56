import request from '@/config/axios'
const prefix = '/order/order'
const prefixC = '/customer/customer'
// DIY列表
export const pageListApi = (data: any, params: any) => {
  return request.post({ url: prefix + '/diy/page', data, params })
}
// DIY订单详情
export const orderDetailsApi = (params: string) => {
  return request.get({ url: prefix + `/diy/${params}/orderId` })
}
// DIY图审特批
export const reiewSpecialApi = (data: any) => {
  return request.postOriginal({ url: prefix + '/diy/reviewSpecial', data })
}

// DIY图审加急
export const reiewUrgentApi = (data: any) => {
  return request.postOriginal({ url: prefix + '/diy/reviewUrgent', data })
}

// DIY取消制卡
export const diyCancelApi = (params: string) => {
  return request.getOriginal({ url: prefix + `/diy/${params}/cancel` })
}

// 开票
export const createInvoiceApi = (data: any) => {
  return request.postOriginal({ url: '/order/invoice/apply', data })
}

// 客户名称
export const getNamesApi = () => {
  return request.get({ url: prefixC + '/getNames' })
}

// 批卡订单创建
export const createApi = (data) => {
  return request.postOriginal({ url: prefix + '/batch/create', data })
}

// 批卡订单列表
export const batchPageApi = (data, params) => {
  return request.post({ url: prefix + '/batch/page-manage', data, params })
}

// 批卡订单列表-下单客户字典
export const fastSearchApi = () => {
  return request.get({ url: prefixC + '/fast-search' })
}

// 批卡订单详情
export const batchDetailsApi = (orderId) => {
  return request.get({ url: prefix + `/batch/${orderId}` })
}

// 批卡订单取消
export const cancelApi = (orderId) => {
  return request.getOriginal({ url: prefix + `/batch/${orderId}/cancel` })
}

// 文件上传
export const uploadApi = (data) => {
  return request.upload({ url: prefix + '/upload', data })
}

// 添加商品
export const listApi = (data) => {
  return request.post({
    url: '/product/product/out/v1/list',
    data
  })
}

// 收货地址列表
export const receivingListApi = (data: any) => {
  return request.post({ url: prefix + `/logistics/ReceivingList`, data })
}

// 新增收货地址
export const createReceivingInfoApi = (data: any) => {
  return request.postOriginal({ url: prefix + `/logistics/createReceivingInfo`, data })
}

// 编辑地址
export const receivingInfoUpdateApi = (data: any) => {
  return request.postOriginal({ url: prefix + `/logistics/receivingInfoUpdate`, data })
}

// 退款申请
export const refundApi = (data: any) => {
  return request.postOriginal({ url: prefix + `/diy/refund`, data })
}

// 支付信息
export const payLog = (orderId: string) => {
  return request.get({ url: prefix + `/diy/${orderId}/payLog` })
}

// 物流信息
export const logisticsTraces = (orderId: string) => {
  return request.get({ url: prefix + `/logistics/diy/test/logisticsTraces?orderId=${orderId}` })
}

// 查询商品单价
export const pricesApi = (data) => {
  return request.post({ url: '/product/product/out/v1/prices', data })
}
