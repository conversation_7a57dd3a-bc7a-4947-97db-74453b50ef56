import request from '@/config/axios'

// PLM-分页查询订单签审列表
export const pageReviewCard = (data) => {
  return request.post({
    url: `/order/pmReview/pageReviewCard`,
    data
  })
}

// 用户树接口
export const pmReviewListPmUserTrees = (data?) => {
  return request.get({
    url: `/order/pmReview/listPmUserTrees`,
    data
  })
}

// 转办接口
export const pmReviewListPmTaskTransfer = (data, params?) => {
  return request.post({
    url: `/order/pmReview/taskTransfer`,
    data,
    params
  })
}

// 销售节点查询列表
export const saleReviewTaskList = (data, params?) => {
  return request.post({
    url: `/order/saleReview/task/list`,
    data,
    params
  })
}

// 销售签审卡款详情
export const saleReviewCardList = (data?) => {
  return request.post({
    url: `/order/saleReview/card/list`,
    data
  })
}

// 销售签审操作接口
export const saleReviewOptionResult = (data?) => {
  return request.get({
    url: `/order/saleReview/option/result`,
    data
  })
}

// 销售卡款签审结果接口
export const saleReviewCardResult = (data?) => {
  return request.post({
    url: `/order/saleReview/card/result`,
    data
  })
}

// 获取pm列表详情
export const getPmReviewDetail = (id, data?) => {
  return request.post({
    url: `/order/pmReview/getReviewDetail/${id}`,
    data
  })
}

// 获取pm列表详情按钮情况
export const getPmReviewOperateOption = (data?) => {
  return request.post({
    url: `/order/pmReview/getPmReviewOperateOption`,
    data
  })
}

// 获取pm模块基础数据
export const getListCompleteModuleInfos = (data?) => {
  return request.post({
    url: `/order/pmReview/listCompleteModuleInfos`,
    data
  })
}

// 获取pm模块白名单基础数据
export const getListProjectWhitelistCompleteModuleInfos = (data?) => {
  return request.post({
    url: `/order/pmReview/listProjectWhitelistCompleteModuleInfos`,
    data
  })
}

// 分页查询预测主题
export const getForecastThemePage = (data?) => {
  return request.post({
    url: `/order/pmReview/getForecastThemePage`,
    data
  })
}

// 获取签审卡款算法类型列表
export const getListCardAlgorithmTypes = (data?) => {
  return request.get({
    url: `/order/pmReview/listCardAlgorithmTypes`,
    data
  })
}

// 获取签审卡款芯片外观列表
export const getListChipAppearances = (data?) => {
  return request.get({
    url: `/order/pmReview/listChipAppearances`,
    data
  })
}

// 获取签审卡款芯片颜色列表
export const getListChipColors = (data?) => {
  return request.get({
    url: `/order/pmReview/listChipColors`,
    data
  })
}

// 获取签审卡款IC卡款标准列表
export const getListICCardStandards = (data?) => {
  return request.get({
    url: `/order/pmReview/listICCardStandards`,
    data
  })
}

// 保存签审结果
export const savePmReview = (data?) => {
  return request.post({
    url: `/order/pmReview/save`,
    data
  })
}

// 提交签审结果
export const submitPmReview = (data?) => {
  return request.post({
    url: `/order/pmReview/submit`,
    data
  })
}

// 获取项目下拉选择项 cards[]customerId
export const getListProjectsByCustomerId = (id, data?) => {
  return request.post({
    url: `/order/pmReview/listProjectsByCustomerId/${id}`,
    data
  })
}

// 获取个人化评估意见列表
export const getListPersonalAssessOpinions = (params?) => {
  return request.get({
    url: `/order/pmReview/listPersonalAssessOpinions`,
    params
  })
}

// 获取个人化流程列表
export const getListPersonalProcess = (params?) => {
  return request.get({
    url: `/order/pmReview/listPersonalProcess`,
    params
  })
}

// 获取pdm客户列表
export const getListCustomers = (data?) => {
  return request.post({
    url: `/order/pmReview/listCustomers`,
    data
  })
}

// 获取签审卡基性质
export const getListCardBaseNatures = (params?) => {
  return request.get({
    url: `/order/pmReview/listCardBaseNatures`,
    params
  })
}

// 获取签审卡款性质
export const getListCardNatures = (params?) => {
  return request.get({
    url: `/order/pmReview/listCardNatures`,
    params
  })
}

// 获取一级分行
export const getListCustomerSubBankInfos = (id?, data?) => {
  return request.post({
    url: `/order/pmReview/listCustomerSubBankInfos/${id}`,
    data
  })
}

// 获取稿样文件
export const downloadFinalDraftsFile = (data?) => {
  return request.postOriginal({
    url: `/order/reviewOrder/download/finalDraftsFile`,
    data,
    responseType: 'blob'
  })
}

// 获取订单类型
export const reviewOrderTypeList = (data?) => {
  return request.get({
    url: `/order/reviewOrder/type/list`,
    data
  })
}

// 获取任务节点日志
export const reviewTaskListReviewTaskResult = (data?) => {
  return request.post({
    url: `/order/reviewTask/listReviewTaskResult`,
    data
  })
}

// 获取签审节点
export const pmReviewListTaskNodes = (data?) => {
  return request.get({
    url: `/order/pmReview/listTaskNodes`,
    data
  })
}

// 获取订单标识
export const pmReviewListOrderTypes = (data?) => {
  return request.get({
    url: `/order/pmReview/listOrderTypes`,
    data
  })
}

// 获取应用类型
export const pmReviewListApplicationTypes = (data?) => {
  return request.get({
    url: `/order/pmReview/listApplicationTypes`,
    data
  })
}

// 获取卡组织
export const pmReviewListCardOrganizations = (data?) => {
  return request.get({
    url: `/order/pmReview/listCardOrganizations`,
    data
  })
}

// 获取签审结果
export const pmReviewListOptionResultTypes = (data?) => {
  return request.get({
    url: `/order/pmReview/listOptionResultTypes`,
    data
  })
}

// 获取部门 + 用户树接口
export const getDeptUserTrees = (data?) => {
  return request.get({
    url: `/app/user/getDeptUserTrees`,
    data
  })
}

// 刷新报批芯片接口地址
export const refreshSubmitApprovalChip = (data?) => {
  return request.post({
    url: `/order/pmReview/refreshSubmitApprovalChip`,
    data
  })
}

export interface pmReviewOperateOptionVO {
  code: string
  name: string
  order: number
}

export interface pmReviewOperateVO {
  newCard: pmReviewOperateOptionVO[]
  repeatCard: pmReviewOperateOptionVO[]
  sampleNewCard: pmReviewOperateOptionVO[]
  sampleRepeatCard: pmReviewOperateOptionVO[]
}

export interface saleReviewTaskVO {
  reviewOrderId: string
  reviewTaskId: string
  reviewOrderProductId: string
  customerName: string
  orderType: string
  orderTypeName: string
  taskCode: string
  quantity: string
  productId: string
  productName: string
  processorId: string
  processorCount: string
  processorName: string
  targetCode: string
  targetName: string
  taskStatus: string
  deliveryAt: string
  deliveryType: string
  orderCreateBy: string
  orderCreateName: string
  orderCreateDate: string
  saleNeedReview: string
  saleReviewStatus: string
}

export interface resultListVO<T> {
  list: T[]
  total: number
}

export interface productCodeTaskVO {
  productCode: string
  quantity: string
  orderCode: string
  productName: string
  createDate: string
  reviewNode: string
  completed: boolean
}

export interface productCodeCardsVO {
  primaryFlag: boolean
  orderType: string
  cardName: string
  semifinishedStock: number | null
  finishedStock: number | null
  projectInfo: string | null
  reviewOrderId: string
  cardCode: string

  [key: string]: any
}

export interface productCodeVO {
  task: productCodeTaskVO
  cards: productCodeCardsVO
}

export interface cardModuleListVO {
  chipType: number
  chipCode: string
  chipSupplier: string
  moduleTransferInterfaceType: string
  color: string
  capacity: string
  maskCode: string
  maxCode: string
  shape: string
  oiOrModuleCode: string
  antenna: string
  aerialType: string
  property: string
  stripe: string
  capacitance: string
}

export interface cardNoteVO {
  cardCode: string
  remark: string
  cardNote: string
}

export interface saleCardInfoVO {
  cardName: string
  cardCode: string
  cardFullCode: string
  latestK3Code: string
  saleNeedReview: string
  reviewResultCode: number
  reviewResultRemark: string
  id: string
  batchTime: string
  cardPlanNum: string
  encapType: string
  finishedCardModuleList: cardModuleListVO[]
  semiFinishedCardModuleList: cardModuleListVO[]
  orderRemark: string
  finishedStock: string | number
  semifinishedStock: string | number
  cardOrgUsableFlag: boolean
  primaryFlag: boolean | number
  finalDraftsFileUrl: string
  finalDraftsFileName: string
  cardNoteList: cardNoteVO[]
  saleReviewStatus: string
}
