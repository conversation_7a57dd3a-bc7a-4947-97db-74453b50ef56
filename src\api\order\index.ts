import request from '@/config/axios'
const prefix = '/order/order'
const prefixC = '/customer/customer'
// DIY列表
export const pageListApi = (data: any, params: any) => {
  return request.post({ url: prefix + '/diy/page', data, params })
}
// DIY订单详情
export const orderDetailsApi = (params: string) => {
  return request.get({ url: prefix + `/diy/${params}/orderId` })
}
// DIY图审特批
export const reiewSpecialApi = (data: any) => {
  return request.postOriginal({ url: prefix + '/diy/reviewSpecial', data })
}

// DIY图审加急
export const reiewUrgentApi = (data: any) => {
  return request.postOriginal({ url: prefix + '/diy/reviewUrgent', data })
}

// DIY取消制卡
export const diyCancelApi = (params: string) => {
  return request.getOriginal({ url: prefix + `/diy/${params}/cancel` })
}

// 开票
export const createInvoiceApi = (data: any) => {
  return request.postOriginal({ url: '/order/invoice/apply', data })
}

// 客户名称
export const getNamesApi = () => {
  return request.get({ url: prefixC + '/getNames' })
}

// 批卡订单保存
export const saveApi = (data) => {
  return request.postOriginal({ url: prefix + '/batch/save', data })
}

// 批卡订单创建
export const createApi = (data) => {
  return request.postOriginal({ url: prefix + '/batch/create', data })
}

// 销售确认提交订单
export const saleConfirmOrderApi = (data) => {
  return request.postOriginal({ url: prefix + '/batch/sale/confirm', data })
}

// 批卡订单列表
export const batchPageApi = (data, params) => {
  return request.post({ url: prefix + '/order/page-manage', data, params })
}

// 批卡样卡订单列表
export const batchAndSamplePageApi = (data, params) => {
  //   return request.post({ url: prefix + '/pageByBatchAndSample-manage', data, params })
  return request.post({ url: prefix + '/batch/page', data, params })
}

// 批卡订单列表-下单客户字典
export const fastSearchApi = () => {
  return request.get({ url: prefixC + '/fast-search' })
}

// 批卡订单详情
export const batchDetailsApi = (orderId) => {
  return request.get({ url: prefix + `/batch/${orderId}` })
}

// 批卡订单详情
export const sampleDetailsApi = (orderId) => {
  return request.get({ url: prefix + `/samplecard/${orderId}` })
}

// 批卡订单取消
export const cancelApi = (orderId, orderType) => {
  return request.getOriginal({ url: prefix + `/batch/${orderId}/cancel?orderType=${orderType}` })
}

// 文件上传
export const uploadApi = (data) => {
  return request.upload({ url: prefix + '/upload', data })
}

// 添加商品
export const listApi = (data) => {
  return request.post({
    url: '/product/product/out/v1/list',
    data
  })
}

//客户信息
import { getTenantId } from '@/utils/auth'

export const getCusInfoByTenantId = () => {
  return request.get({ url: prefixC + `/getCusInfoByTenantId?tenantId=${getTenantId()}` })
}

// 收货地址列表
export const receivingListApi = (data: any) => {
  return request.post({ url: prefix + `/logistics/ReceivingList`, data })
}

// 新增收货地址
export const createReceivingInfoApi = (data: any) => {
  return request.postOriginal({ url: prefix + `/logistics/createReceivingInfo`, data })
}

// 编辑地址
export const receivingInfoUpdateApi = (data: any) => {
  return request.postOriginal({ url: prefix + `/logistics/receivingInfoUpdate`, data })
}

// 退款申请
export const refundApi = (data: any) => {
  return request.postOriginal({ url: prefix + `/diy/refund`, data })
}

// 支付信息
export const payLog = (orderId: string) => {
  return request.get({ url: prefix + `/diy/${orderId}/payLog` })
}

// 物流信息
export const logisticsTraces = (orderId: string) => {
  return request.get({ url: prefix + `/logistics/diy/test/logisticsTraces?orderId=${orderId}` })
}

// 查询商品单价
export const pricesApi = (data) => {
  return request.post({ url: '/product/product/out/v1/prices', data })
}

// 下载文件-文件流
export const downloadFileApi = (data): any => {
  return request.download({
    url: '/order/order/download',
    params: data,
    timeout: 240000
  })
}

// 获取单个物流信息
export const searchTraceMailNo = (data) => {
  return request.post({ url: '/order/express/search/trace/mailNo', data })
}

// 获取批量物流信息
export const searchTraceMailNoMap = (data) => {
  return request.post({ url: '/order/express/search/trace/mailNoMap', data })
}

// 客户详情
export const getCustomerDetailApi = (params: string): any => {
  return request.get({ url: `/customer/customer/queryOne?customerId=${params}` })
}

// 省份
export const getAreatTreeApi = () => {
  return request.get({ url: `/admin-api/system/area/tree` })
}
// 市级
export const getAreatListApi = (params: any) => {
  return request.get({ url: `/admin-api/system/area/list`, params })
}

// 根据id查询城市
export const getAreaApi = (params: { areaId: string }) => {
  return request.getOriginal({ url: '/customer/customer/getArea', params })
}

// 业务员（销售）列表
export const getSaleCustomerListApi = () => {
  const data = ['sale']
  return request.postOriginal({ url: '/app/user/listUserByDeptCodes', data })
}

// 稿样列表
export const getSampleCustomerListApi = () => {
  const data = ['draft']
  return request.postOriginal({ url: '/app/user/listUserByDeptCodes', data })
}

export interface LogisticsrouteListVO {
  opTime: string
  opName: string
  remark: string
}
export interface LogisticsDataVO {
  mailNo: string
  routeList: Array<LogisticsrouteListVO>
}

// 获取租户币种类型
export const getCoinTypeConfig = (tenantId: string) => {
  return request.getOriginal({
    url: `/order/order/coin/type`,
    params: { tenantId }
  })
}

//导出订单数据
export const exportList = (data) => {
  return request.postDownload({ url: '/order/order/batch/export', data })
}

/**
 * @description 查询下单产品的卡组织数据
 * @export
 * @param {String} orderId
 * @return {*}
 */
export function getProductOrganizations(orderId: String) {
  const url = `${prefix}/batch/product/org/${orderId}`
  return request.get({ url })
}
