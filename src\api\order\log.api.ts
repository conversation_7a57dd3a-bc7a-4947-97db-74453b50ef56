import request from '@/config/axios'
const prefix = '/order/order/batch'

import { batchOrderProductStatus } from './types/batchOrderProductStatus.d'

/**
 * @description 查询下单产品状态日志记录
 * @export
 * @param {String} orderId 订单ID
 * @return {*}  {Promise<batchOrderProductStatus[]>}
 */
export function getBatchOrderProductStatusLogs(
  orderId: String
): Promise<batchOrderProductStatus[]> {
  const url = `${prefix}/${orderId}/product/logs`
  return request.get({ url })
}
