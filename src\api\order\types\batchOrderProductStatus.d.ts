import { batchOrderProductStatuEnum } from './enum.d'

/**
 * @description 批卡产品状态定义
 * @export
 * @interface batchOrderProductStatus
 */
export interface batchOrderProductStatus {
  /**
   * @description 订单ID
   * @type {string}
   * @memberof batchOrderProductStatus
   */
  orderId: string

  /**
   * @description 下单产品ID
   * @type {string}
   * @memberof batchOrderProductStatus
   */
  orderDetailId: string

  /**
   * @description 产品状态
   * @type {batchOrderProductStatuEnum}
   * @memberof batchOrderProductStatus
   */
  productStatus: batchOrderProductStatuEnum

  /**
   * @description 记录时间
   * @type {Date}
   * @memberof batchOrderProductStatus
   */
  createTime: Date

  /**
   * @description 当前产品数量
   * @type {number}
   * @memberof batchOrderProductStatus
   */
  quantity: number
}
