import axios, { AxiosHeaders, AxiosInstance } from 'axios'
import { getTenantId } from '@/utils/auth'
import request from '@/config/axios'
import { Base64 } from 'js-base64'

const productAxios: AxiosInstance = axios.create()

const GATEWAY_PREFIX_PRODUCT_URL = '/product'
const GATEWAY_PREFIX_CUSTOMER_URL = '/customer'
const GATEWAY_PREFIX_SALE_URL = '/sale'
const GATEWAY_PREFIX_DIY_URL = '/diy'

export enum DiyFileType {
  DESIGN_FILE = 'designFile',
  EXAMPLE_FILE = 'exampleFile',
  THREE_DIM_FILE = 'threeDimFile'
}

// 文件上传
export const uploadFileApi = (diyFileType: DiyFileType | String, formData: FormData) => {
  return request.upload({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/upload/' + diyFileType,
    data: formData
  })
}

export const downloadEosFileApi = (fileUrl) => {
  return new Promise<Blob>((resolve, reject) => {
    const xhr: XMLHttpRequest = new XMLHttpRequest()
    xhr.open('GET', fileUrl)
    xhr.setRequestHeader('Access-Control-Allow-Origin', '*')
    xhr.setRequestHeader('Cache-Control', 'no-cache')
    // xhr.setRequestHeader('If-Modified-Since', '0')
    xhr.responseType = 'blob'
    xhr.onload = () => {
      resolve(xhr.response)
    }
    xhr.onerror = (e) => {
      console.error('could not download eos file')
    }
    xhr.send()
  })
}

// 文件下载（eos）
export const downloadFileApi = (fileUrl) => {
  const headers = new AxiosHeaders()
  headers.set('Access-Control-Allow-Origin', '*')
  return productAxios.get(fileUrl, {
    headers: headers,
    responseType: 'blob'
  })
}

// 文件下载（卡产品平台）
export const downloadProductPlatformFileApi = (data) => {
  return request.postDownload({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/product/platform/file/download',
    data
  })
}

// 获取产品列表数据
export const getProductListApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/list',
    data
  })
}

// 添加、修改产品数据
export const addOrUpdateProductApi = (url, data) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + url,
    data
  })
}

// 产品上架
export const productListedApi = (diyCardId: string) => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/listed/' + diyCardId
  })
}

// 产品下架
export const productUnListedApi = (diyCardId: string) => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/unlisted/' + diyCardId
  })
}

// 删除产品
export const deleteProductApi = (diyCardId: string) => {
  return request.delete({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/delete/' + diyCardId
  })
}

// 产品详情
export const getProductDetailApi = (diyCardId: string) => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/' + diyCardId
  })
}

// 获取产品状态
export const getProductStatusListApi = () => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/status/list'
  })
}

// 获取产品类型
export const getProductTypeListApi = () => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/type/list'
  })
}

// 获取卡款列表
export const getMerchantCardListApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/product/card/simple/list',
    data
  })
}

// 获取相似产品列表
export const getSimilarProductListApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/similar/list',
    data
  })
}

// 获取版型列表
export const getCardStyleListApi = () => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/diyCard/v1/style/list'
  })
}

// 获取定制属性
export const getAttributeListApi = (params: any) => {
  return request.get({
    url: GATEWAY_PREFIX_DIY_URL + '/bclConfig/getCustomizedInfo',
    params
  })
}
// 获取卡款类型，来源字典表
export const getCardTypeListApi = (params = { dictType: 'diy_product_card_type' }) => {
  return request.get({ url: '/admin-api/system/dict-data/list', params })
}

// 获取客户列表
export const getCustomerListApi = (params: any) => {
  return request.get({
    url: GATEWAY_PREFIX_CUSTOMER_URL + '/customer/getNamesNode',
    params
  })
}

// 获取项目列表
export const getProjectListApi = (data: any) => {
  return request.post({
    // url: GATEWAY_PREFIX_SALE_URL + '/Project/listProject',
    url: GATEWAY_PREFIX_SALE_URL + '/Project/findProjectList',
    data
  })
}

// 获取项目秘钥
export const getProjectSecretKeyApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_SALE_URL + '/ProjectAppKeyPo/findAPPId',
    data
  })
}

// 获取DIY授权服务列表
export const getServiceListApi = (formData: FormData) => {
  return request.post({
    url: GATEWAY_PREFIX_DIY_URL + '/belConfig/listSimple',
    data: formData,
    headersType: {
      ContentType: 'multipart/form-data'
    }
  })
}

// 获取服务列表
// export const getServiceListApi = (projectId: string) => {
//   return request.post({
//     url: GATEWAY_PREFIX_SALE_URL + '/Project/findProjectById/' + projectId
//   })
// }

// 获取产品分类列表
export const getCataLogListApi = (data: any) => {
  return request.upload({
    url: GATEWAY_PREFIX_DIY_URL + '/belGroup/list',
    data
  })
}

// 获取定制属性
export const getDiyCustomAttributeApi = (params: any) => {
  return request.get({
    url: GATEWAY_PREFIX_DIY_URL + '/bclConfig/getCustomizedInfo',
    params
  })
}

// 获取城市列表
export const getCityListApi = (params: any) => {
  const headers = new AxiosHeaders()
  headers.set('tenant-id', getTenantId())
  return productAxios.get('/admin-api/system/area/list', {
    headers: headers,
    params: params
  })
}

// 获取供应商列表
export const getSupplierListApi = (data = {}) => {
  return request.post({
    url: GATEWAY_PREFIX_DIY_URL + '/diySupplier/listDownSupplier',
    data: data
  })
}

// 获取图片素材分组（分组信息）
export const getDiyImageTypeListApi = (data: any) => {
  return request.upload({
    url: GATEWAY_PREFIX_DIY_URL + '/belGroup/list',
    data
  })
}

// 获取图片素材列表（分页）
export const getDiyImageListApi = (data: any) => {
  return request.upload({
    url: GATEWAY_PREFIX_DIY_URL + '/belMaterial/list',
    data
  })
}

// 获取卡面贴图组 diyMaterialInfoType = 1
export const getDiyStickImageGroupListApi = (params: any) => {
  return request.get({
    url: GATEWAY_PREFIX_DIY_URL + '/belMaterial/getMaterialInfo',
    params
  })
}

// 获取元素拼接组 diyMaterialInfoType = 2
export const getDiyElementImageGroupListApi = (params: any) => {
  return request.get({
    url: GATEWAY_PREFIX_DIY_URL + '/belMaterial/getMaterialInfo',
    params
  })
}

// 获取图审配置列表
export const getVerifyServiceList = (projectSecret: Object, data: any) => {
  const headers = new AxiosHeaders()
  headers.set('Content-Type', 'application/json')

  const secretKey = Base64.encode(projectSecret.appId + ':' + projectSecret.appKey)
  headers.set('Authorization', 'Basic ' + secretKey)
  return productAxios.post('/app-api/review/external/api/service', data, {
    headers: headers
  })
}

// 获取图审标识列表
export const getVerifyCodeList = (formData: FormData) => {
  return request.post({
    url: GATEWAY_PREFIX_DIY_URL + '/bclConfig/listAuditCodeById',
    data: formData,
    headersType: {
      ContentType: 'multipart/form-data'
    }
  })
}

// 客户名称
export const getCustomerName = (params: string, isAcc?: number): any => {
  let url = `/customer/customer/getNames?customerName=${params}`
  if (isAcc) {
    url = `${url}&isAcc=${isAcc}`
  }
  return request.getOriginal({
    url
  })
}

// 产品列表
export const getOutProductList = (data) => {
  return request.postOriginal({
    url: '/product/diyCard/out/v1/list',
    data
  })
}

// 获取单个物流信息
export const searchTraceMailNo = (data) => {
  // return request.postOriginal({ url: 'http://localhost:4000/express/search/trace/mailNo', data })
  return request.postOriginal({ url: '/order/express/search/trace/mailNo', data })
}
