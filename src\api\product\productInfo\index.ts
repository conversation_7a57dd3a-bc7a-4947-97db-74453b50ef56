import request from '@/config/axios'

const GATEWAY_PREFIX_PRODUCT_URL = '/product'
const GATEWAY_PREFIX_CUSTOMER_URL = '/customer'
const GATEWAY_PREFIX_SALE_URL = '/sale'

// 获取产品列表数据
export const getProductListApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/list',
    data
  })
}

// 获取产品详情
export const getProductDetailApi = (productId: String) => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/' + productId
  })
}

// 获取产品详情
export const updatePriceStageApi = (data) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/updatePriceStage',
    data
  })
}

// 添加、修改产品数据
export const addOrUpdateProductApi = (url: String, formData: FormData) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + url,
    data: formData,
    headersType: 'multipart/form-data'
  })
}

// 产品下架
export const productUnListedApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/updateClientProduct',
    data
  })
}

// 产品上架
export const productListedApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/updateClientProduct',
    data
  })
}

// 产品上下架
export const updateProductStateApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/out/v1/updateClientProduct',
    data,
    headersType: 'multipart/form-data'
  })
}

// 删除产品
export const deleteProductApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/delete',
    data
  })
}

// 获取产品状态
export const getProductStatusListApi = () => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/status/list'
  })
}

// 获取产品类型
export const getProductTypeListApi = () => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/type/list'
  })
}

// 获取卡款列表
export const getMerchantCardListApi = (data: any) => {
  return request.post({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/merchant/card/list',
    data
  })
}

// 获取卡款库存(废弃！！)
export const getCardInventoryByCardCodeApi = (cardCode: any) => {
  return request.get({
    url: GATEWAY_PREFIX_PRODUCT_URL + '/product/v1/merchant/card/inventory/' + cardCode
  })
}

// 获取客户列表
export const getCustomerListApi = (params: any) => {
  return request.get({
    url: GATEWAY_PREFIX_CUSTOMER_URL + '/customer/getNamesNode',
    params
  })
}

// 获取客户信息
export const getCustomerInfoApi = (params: any): any => {
  return request.get({
    url: GATEWAY_PREFIX_CUSTOMER_URL + '/customer/queryOne',
    params
  })
}

// 获取项目列表
export const getProjectListApi = (data: any) => {
  return request.post({
    // url: GATEWAY_PREFIX_SALE_URL + '/Project/listProject',
    url: GATEWAY_PREFIX_SALE_URL + '/Project/findProjectList',
    data
  })
}
