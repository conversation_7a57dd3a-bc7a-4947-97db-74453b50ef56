/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-07-19 11:45:51
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-11-14 09:47:54
 * @Description:
 */
import request from '@/config/axios'

export type DictDataVO = {
  id: number | undefined
  sort: number | undefined
  label: string
  value: string
  dictType: string
  status: number
  colorType: string
  cssClass: string
  remark: string
  createTime: Date
}

// 查询字典数据（精简)列表
export const listSimpleDictData = () => {
  return request.get({ url: '/app/dict-data/list-all-simple' })
}
// app-api/app/dict-data/list-all-simple

// 查询字典数据详情
export const getDictData = (id: number) => {
  return request.get({ url: '/app/dict-data/get?id=' + id })
}
