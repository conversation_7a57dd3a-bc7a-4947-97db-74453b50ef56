/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-07-20 11:24:56
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-26 15:06:55
 * @Description:
 */
import request from '@/config/axios'

// 获取用户信息
export const getLoginUserInfoApi = () => {
  return request.postOriginal({
    url: `/app/user/getUserByToken`
  })
}

// 获取账号信息
export interface AccountType {
  avatar: string
  createTime: string
  deptName: string
  deptId: number
  email: string
  mobile: string
  nickname: string
  username: string
}

export const getUserAccountInfo = (userId: number) => {
  return request.get({
    url: `/customer/customer/userAccountInfo/${userId}`
  })
}

//获取企业信息
export interface CompanyInfoType {
  customerName: string //企业名称
  cusRelated: [] //关联客户
  uscc: string //社会统一信用代码 uscc
}

export const getUserCompanyInfo = (userId: number) => {
  return request.get({
    url: `/customer/customer/userCompanyInfo/${userId}`
  })
}
export const updateAvatar = (data) => {
  return request.uploadPut({
    url: `/admin-api/system/user/profile/update-avatar`,
    data
  })
}
