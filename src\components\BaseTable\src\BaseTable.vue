<!-- /**
 * 基础table
 * <AUTHOR>
 * @data 2023-9-20
 * 调用示例：
 *<BaseTable
    :data="data" //必填，表单数据
    :columns="columns" //必填，表头内容
    :handleList='handleList'//非必填，操作列表
    />
    API
    columns = {
      slotName: string // 插槽的名称
      ... //ELTableColumn组件的参数，完美继承
    }
    handleList = {
      label:string //操作按钮标题
      onClick:(scope)=>void //点击按钮事件
      isShow:(scope)=>boole //是否显示按钮
      ...////ELButton组件的参数，完美继承
    }
 *
 **/
//快速复制粘贴
<template>
  <BaseTable :data="data" :columns="columns" :handleList="handleList">
    <template  #slotName1="scope">
    </template>
  </BaseTable>
 </template> 
 <script lang="ts" setup>
  const data = ref([
    {
      data1: 1,
      data2: 2,
    }])
  const handleList = ref([
    { label: '删除', onClick: (scope) => { console.log({ scope }) },isShow: (scope) => { return scope.data1 === 1}}
  ])
  const columns = ref([
  { label: 'data1表格头', prop: 'data1', showOverflowTooltip: true },
  { label: '自定义模板', slotName: 'slotName1'}
  ])
</script>
-->

<template>
  <el-table
    :data="props.data"
    style="width: 100%; margin-top: 20px"
    header-row-class-name="component-header-row"
    header-cell-class-name="component-header-cell"
    row-class-name="component-row"
    :header-cell-style="{ background: '#f8f8f8' }"
    tooltip-effect="light"
  >
    <el-table-column
      show-overflow-tooltip
      v-for="(item, index) in props.columns"
      :key="item.label ?? '' + index"
      v-bind="item"
    >
      <template #default="scope" v-if="item.slotName">
        <slot :name="item.slotName ?? item.prop" v-bind="scope"></slot>
      </template>
    </el-table-column>
    <el-table-column
      v-if="props.handleList && props.handleList.length > 0"
      :label="t('components.BaseTable.operation')"
      fixed="right"
    >
      <template #default="scope">
        <template v-for="item in props.handleList" :key="item.label">
          <el-button
            :type="item.type ?? 'primary'"
            link
            v-bind="changeClick(item)"
            @click="
              (value) => {
                item.onClick(scope)
              }
            "
            v-if="item.isShow ? item.isShow(scope) : true"
            >{{ item.label }}</el-button
          >
        </template>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import { ElButton, ElTable, ElTableColumn } from 'element-plus'
import { fooProps, handleListType } from './type'
const { t } = useI18n()

const props = defineProps(fooProps)
//将用到的属性进行排除，避免绑定的属性与ElButton事件冲突
function changeClick(item: any) {
  const { onClick, label, isShow, ...data } = item
  return data
}
</script>

<style lang="less">
.component-header-row {
  height: 45px;

  color: #9b9ea3;
  .component-header-cell {
    padding: 0;
  }
}
.component-row {
  height: 65px;
}
</style>
