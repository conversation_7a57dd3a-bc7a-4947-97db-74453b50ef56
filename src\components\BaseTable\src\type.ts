import { ExtractPropType } from 'element-plus/es/utils'

import { PropType } from 'vue'
import { ElTableColumn, ElButton } from 'element-plus'

export type ElTableColumnValue = InstanceType<typeof ElTableColumn>['$props']
type ElButtonType = InstanceType<typeof ElButton>['$props']
export type ElButtonValue = { -readonly [K in keyof ElButtonType]: ElButtonType[K] }

export type columnsType = (ElTableColumnValue & { slotName?: string | number })[]
export type handleListType = ({
  label: string
  onClick: (value: any) => void
  isShow?: (value: any) => boolean
} & ElButtonValue)[]
export const fooProps = {
  data: {
    type: Array,
    default: () => []
  },
  handleList: {
    type: Array as PropType<handleListType>
  },
  columns: {
    type: Array as PropType<columnsType>,
    require: true
  }

  //下面是el-table原始的API，看element文档即可，组件继承
  // size: {
  //   type: String as PropType<'' | 'default' | 'small' | 'large'>,
  //   required: false
  // },
  // width: [String, Number],
  // height: [String, Number],
  // maxHeight: [String, Number],
  // fit: {
  //   type: Boolean,
  //   default: true
  // },
  // stripe: Boolean,
  // border: Boolean,
  // rowKey: [String, Function],
  // showHeader: {
  //   type: Boolean,
  //   default: true
  // },
  // showSummary: Boolean,
  // sumText: String,
  // summaryMethod: Function,
  // rowClassName: [String, Function],
  // rowStyle: [Object, Function],
  // cellClassName: [String, Function],
  // cellStyle: [Object, Function],
  // headerRowClassName: [String, Function],
  // headerRowStyle: [Object, Function],
  // headerCellClassName: [String, Function],
  // headerCellStyle: [Object, Function],
  // highlightCurrentRow: Boolean,
  // currentRowKey: [String, Number],
  // emptyText: String,
  // expandRowKeys: Array,
  // defaultExpandAll: Boolean,
  // defaultSort: Object,
  // tooltipEffect: String,
  // tooltipOptions: Object,
  // spanMethod: Function,
  // selectOnIndeterminate: {
  //   type: Boolean,
  //   default: true
  // },
  // indent: {
  //   type: Number,
  //   default: 16
  // },
  // treeProps: {
  //   type: Object,
  //   default: () => {
  //     return {
  //       hasChildren: 'hasChildren',
  //       children: 'children'
  //     }
  //   }
  // },
  // lazy: Boolean,
  // load: Function,
  // style: {
  //   type: Object,
  //   default: () => ({})
  // },
  // className: {
  //   type: String,
  //   default: ''
  // },
  // tableLayout: {
  //   type: String,
  //   default: 'fixed'
  // },
  // scrollbarAlwaysOn: {
  //   type: Boolean,
  //   default: false
  // },
  // flexible: Boolean
}

export type FooProps = ExtractPropType<typeof fooProps>
