<template>
  <div class="panel-tab__content">
    <el-form :model="flowConditionForm" label-width="90px" size="small">
      <el-form-item
        :label="t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.type')"
      >
        <el-select v-model="flowConditionForm.type" @change="updateFlowType">
          <el-option
            :label="
              t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.normal')
            "
            value="normal"
          />
          <el-option
            :label="
              t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.default')
            "
            value="default"
          />
          <el-option
            :label="
              t(
                'components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.condition'
              )
            "
            value="condition"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="
          t(
            'components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.conditionType'
          )
        "
        v-if="flowConditionForm.type === 'condition'"
        key="condition"
      >
        <el-select v-model="flowConditionForm.conditionType">
          <el-option
            :label="
              t(
                'components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.expression'
              )
            "
            value="expression"
          />
          <el-option
            :label="
              t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.script')
            "
            value="script"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="
          t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.expression')
        "
        v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'expression'"
        key="express"
      >
        <el-input
          v-model="flowConditionForm.body"
          style="width: 192px"
          clearable
          @change="updateFlowCondition"
        />
      </el-form-item>
      <template
        v-if="flowConditionForm.conditionType && flowConditionForm.conditionType === 'script'"
      >
        <el-form-item
          :label="
            t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.language')
          "
          key="language"
        >
          <el-input v-model="flowConditionForm.language" clearable @change="updateFlowCondition" />
        </el-form-item>
        <el-form-item
          :label="
            t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.scriptType')
          "
          key="scriptType"
        >
          <el-select v-model="flowConditionForm.scriptType">
            <el-option
              :label="
                t(
                  'components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.inlineScript'
                )
              "
              value="inlineScript"
            />
            <el-option
              :label="
                t(
                  'components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.externalScript'
                )
              "
              value="externalScript"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="
            t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.script')
          "
          v-if="flowConditionForm.scriptType === 'inlineScript'"
          key="body"
        >
          <el-input
            v-model="flowConditionForm.body"
            type="textarea"
            clearable
            @change="updateFlowCondition"
          />
        </el-form-item>
        <el-form-item
          :label="
            t('components.BpmnProcessDesigner.package.penal.flowCondition.FlowCondition.resource')
          "
          v-if="flowConditionForm.scriptType === 'externalScript'"
          key="resource"
        >
          <el-input v-model="flowConditionForm.resource" clearable @change="updateFlowCondition" />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'FlowCondition'
})

const { t } = useI18n()
const props = defineProps({
  businessObject: Object,
  type: String
})
const flowConditionForm = ref<any>({})
const bpmnElement = ref()
const bpmnElementSource = ref()
const bpmnElementSourceRef = ref()
const flowConditionRef = ref()
const bpmnInstances = () => (window as any)?.bpmnInstances
const resetFlowCondition = () => {
  bpmnElement.value = bpmnInstances().bpmnElement
  bpmnElementSource.value = bpmnElement.value.source
  bpmnElementSourceRef.value = bpmnElement.value.businessObject.sourceRef
  if (
    bpmnElementSourceRef.value &&
    bpmnElementSourceRef.value.default &&
    bpmnElementSourceRef.value.default.id === bpmnElement.value.id &&
    flowConditionForm.value.type == 'default'
  ) {
    // 默认
    flowConditionForm.value = { type: 'default' }
  } else if (!bpmnElement.value.businessObject.conditionExpression) {
    // 普通
    flowConditionForm.value = { type: 'normal' }
  } else {
    // 带条件
    const conditionExpression = bpmnElement.value.businessObject.conditionExpression
    flowConditionForm.value = { ...conditionExpression, type: 'condition' }
    // resource 可直接标识 是否是外部资源脚本
    if (flowConditionForm.value.resource) {
      // this.$set(this.flowConditionForm, "conditionType", "script");
      // this.$set(this.flowConditionForm, "scriptType", "externalScript");
      flowConditionForm.value['conditionType'] = 'script'
      flowConditionForm.value['scriptType'] = 'externalScript'
      return
    }
    if (conditionExpression.language) {
      // this.$set(this.flowConditionForm, "conditionType", "script");
      // this.$set(this.flowConditionForm, "scriptType", "inlineScript");
      flowConditionForm.value['conditionType'] = 'script'
      flowConditionForm.value['scriptType'] = 'inlineScript'

      return
    }
    // this.$set(this.flowConditionForm, "conditionType", "expression");
    flowConditionForm.value['conditionType'] = 'expression'
  }
}
const updateFlowType = (flowType) => {
  // 正常条件类
  if (flowType === 'condition') {
    flowConditionRef.value = bpmnInstances().moddle.create('bpmn:FormalExpression')
    bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
      conditionExpression: flowConditionRef.value
    })
    return
  }
  // 默认路径
  if (flowType === 'default') {
    bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
      conditionExpression: null
    })
    bpmnInstances().modeling.updateProperties(toRaw(bpmnElementSource.value), {
      default: bpmnElement.value
    })
    return
  }
  // 正常路径，如果来源节点的默认路径是当前连线时，清除父元素的默认路径配置
  if (
    bpmnElementSourceRef.value.default &&
    bpmnElementSourceRef.value.default.id === bpmnElement.value.id
  ) {
    bpmnInstances().modeling.updateProperties(toRaw(bpmnElementSource.value), {
      default: null
    })
  }
  bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
    conditionExpression: null
  })
}
const updateFlowCondition = () => {
  let { conditionType, scriptType, body, resource, language } = flowConditionForm.value
  let condition
  if (conditionType === 'expression') {
    condition = bpmnInstances().moddle.create('bpmn:FormalExpression', { body })
  } else {
    if (scriptType === 'inlineScript') {
      condition = bpmnInstances().moddle.create('bpmn:FormalExpression', { body, language })
      // this.$set(this.flowConditionForm, "resource", "");
      flowConditionForm.value['resource'] = ''
    } else {
      // this.$set(this.flowConditionForm, "body", "");
      flowConditionForm.value['body'] = ''
      condition = bpmnInstances().moddle.create('bpmn:FormalExpression', {
        resource,
        language
      })
    }
  }
  bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
    conditionExpression: condition
  })
}

onBeforeUnmount(() => {
  bpmnElement.value = null
  bpmnElementSource.value = null
  bpmnElementSourceRef.value = null
})

watch(
  () => props.businessObject,
  (val) => {
    console.log(val, 'val')
    nextTick(() => {
      resetFlowCondition()
    })
  },
  {
    immediate: true
  }
)
</script>
