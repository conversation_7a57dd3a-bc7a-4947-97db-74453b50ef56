<template>
  <div class="panel-tab__content">
    <el-table :data="elementListenersList" size="small" border>
      <el-table-column
        :label="t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.num')"
        width="50px"
        type="index"
      />
      <el-table-column
        :label="
          t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.eventType')
        "
        min-width="80px"
        show-overflow-tooltip
        :formatter="(row) => listenerEventTypeObject[row.event]"
      />
      <el-table-column
        :label="
          t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.eventId')
        "
        min-width="80px"
        prop="id"
        show-overflow-tooltip
      />
      <el-table-column
        :label="
          t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.listenerType')
        "
        min-width="80px"
        show-overflow-tooltip
        :formatter="(row) => listenerTypeObject[row.listenerType]"
      />
      <el-table-column
        :label="
          t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.operation')
        "
        width="90px"
      >
        <template #default="scope">
          <el-button size="small" link @click="openListenerForm(scope.row, scope.$index)">{{
            t('common.edit')
          }}</el-button>
          <el-divider direction="vertical" />
          <el-button
            size="small"
            link
            style="color: #ff4d4f"
            @click="removeListener(scope.row, scope.$index)"
            >{{
              t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.cutout')
            }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="element-drawer__button">
      <XButton
        size="small"
        type="primary"
        preIcon="ep:plus"
        :title="
          t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.addListener')
        "
        @click="openListenerForm(null)"
      />
    </div>

    <!-- 监听器 编辑/创建 部分 -->
    <el-drawer
      v-model="listenerFormModelVisible"
      :title="
        t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.taskListener')
      "
      :size="`${width}px`"
      append-to-body
      destroy-on-close
    >
      <el-form size="small" :model="listenerForm" label-width="96px" ref="listenerFormRef">
        <el-form-item
          :label="
            t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.eventType')
          "
          prop="event"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-select v-model="listenerForm.event">
            <el-option
              v-for="i in Object.keys(listenerEventTypeObject)"
              :key="i"
              :label="listenerEventTypeObject[i]"
              :value="i"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="
            t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.listener')
          "
          prop="id"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerForm.id" clearable />
        </el-form-item>
        <el-form-item
          :label="
            t(
              'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.listenerType'
            )
          "
          prop="listenerType"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-select v-model="listenerForm.listenerType">
            <el-option
              v-for="i in Object.keys(listenerTypeObject)"
              :key="i"
              :label="listenerTypeObject[i]"
              :value="i"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="listenerForm.listenerType === 'classListener'"
          :label="
            t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.javaClass')
          "
          prop="class"
          key="listener-class"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerForm.class" clearable />
        </el-form-item>
        <el-form-item
          v-if="listenerForm.listenerType === 'expressionListener'"
          :label="
            t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.expression')
          "
          prop="expression"
          key="listener-expression"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerForm.expression" clearable />
        </el-form-item>
        <el-form-item
          v-if="listenerForm.listenerType === 'delegateExpressionListener'"
          :label="
            t(
              'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.delegateExpression'
            )
          "
          prop="delegateExpression"
          key="listener-delegate"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerForm.delegateExpression" clearable />
        </el-form-item>
        <template v-if="listenerForm.listenerType === 'scriptListener'">
          <el-form-item
            :label="
              t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.scriptFormat'
              )
            "
            prop="scriptFormat"
            key="listener-script-format"
            :rules="{
              required: true,
              trigger: ['blur', 'change'],
              message: t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.pleaseEnterScriptFormat'
              )
            }"
          >
            <el-input v-model="listenerForm.scriptFormat" clearable />
          </el-form-item>
          <el-form-item
            :label="
              t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.scriptType'
              )
            "
            prop="scriptType"
            key="listener-script-type"
            :rules="{
              required: true,
              trigger: ['blur', 'change'],
              message: t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.pleaseSelectScriptType'
              )
            }"
          >
            <el-select v-model="listenerForm.scriptType">
              <el-option
                :label="
                  t(
                    'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.inlineScript'
                  )
                "
                value="inlineScript"
              />
              <el-option
                :label="
                  t(
                    'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.externalScript'
                  )
                "
                value="externalScript"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="listenerForm.scriptType === 'inlineScript'"
            :label="
              t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.scriptValue'
              )
            "
            prop="value"
            key="listener-script"
            :rules="{
              required: true,
              trigger: ['blur', 'change'],
              message: t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.pleaseInputScriptValue'
              )
            }"
          >
            <el-input v-model="listenerForm.value" clearable />
          </el-form-item>
          <el-form-item
            v-if="listenerForm.scriptType === 'externalScript'"
            :label="
              t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.resourceUrl'
              )
            "
            prop="resource"
            key="listener-resource"
            :rules="{
              required: true,
              trigger: ['blur', 'change'],
              message: t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.pleaseInputResourceUrl'
              )
            }"
          >
            <el-input v-model="listenerForm.resource" clearable />
          </el-form-item>
        </template>

        <template v-if="listenerForm.event === 'timeout'">
          <el-form-item
            :label="
              t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.eventDefinitionType'
              )
            "
            prop="eventDefinitionType"
            key="eventDefinitionType"
          >
            <el-select v-model="listenerForm.eventDefinitionType">
              <el-option
                :label="
                  t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.date')
                "
                value="date"
              />
              <el-option
                :label="
                  t(
                    'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.duration'
                  )
                "
                value="duration"
              />
              <el-option
                :label="
                  t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.loop')
                "
                value="cycle"
              />
              <el-option
                :label="
                  t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.none')
                "
                value="null"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="!!listenerForm.eventDefinitionType && listenerForm.eventDefinitionType !== 'null'"
            :label="
              t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.timer')
            "
            prop="eventTimeDefinitions"
            key="eventTimeDefinitions"
            :rules="{
              required: true,
              trigger: ['blur', 'change'],
              message: t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.pleaseInputTimer'
              )
            }"
          >
            <el-input v-model="listenerForm.eventTimeDefinitions" clearable />
          </el-form-item>
        </template>
      </el-form>

      <el-divider />
      <p class="listener-filed__title">
        <span
          ><Icon icon="ep:menu" />{{
            t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.injectKey')
          }}</span
        >
        <el-button size="small" type="primary" @click="openListenerFieldForm(null)">{{
          t('components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.addField')
        }}</el-button>
      </p>
      <el-table
        :data="fieldsListOfListener"
        size="small"
        max-height="240"
        border
        fit
        style="flex: none"
      >
        <el-table-column
          :label="t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.num')"
          width="50px"
          type="index"
        />
        <el-table-column
          :label="
            t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.fieldName')
          "
          min-width="100px"
          prop="name"
        />
        <el-table-column
          :label="
            t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.fieldType')
          "
          min-width="80px"
          show-overflow-tooltip
          :formatter="(row) => fieldTypeObject[row.fieldType]"
        />
        <el-table-column
          :label="`${t(
            'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.fieldValue'
          )}/${t(
            'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.expression'
          )}`"
          min-width="100px"
          show-overflow-tooltip
          :formatter="(row) => row.string || row.expression"
        />
        <el-table-column
          :label="
            t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.operation')
          "
          width="100px"
        >
          <template #default="scope">
            <el-button size="small" link @click="openListenerFieldForm(scope.row, scope.$index)">{{
              t('common.edit')
            }}</el-button>
            <el-divider direction="vertical" />
            <el-button
              size="small"
              link
              style="color: #ff4d4f"
              @click="removeListenerField(scope.row, scope.$index)"
              >{{
                t('components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.cutout')
              }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="element-drawer__button">
        <el-button size="small" @click="listenerFormModelVisible = false">{{
          t('common.cancel')
        }}</el-button>
        <el-button size="small" type="primary" @click="saveListenerConfig">{{
          t('components.BpmnProcessDesigner.save')
        }}</el-button>
      </div>
    </el-drawer>

    <!-- 注入西段 编辑/创建 部分 -->
    <el-dialog
      :title="t('components.BpmnProcessDesigner.fieldSetting')"
      v-model="listenerFieldFormModelVisible"
      width="600px"
      append-to-body
      destroy-on-close
    >
      <el-form
        :model="listenerFieldForm"
        size="small"
        label-width="96px"
        ref="listenerFieldFormRef"
        style="height: 136px"
      >
        <el-form-item
          :label="t('components.BpmnProcessDesigner.fieldName')"
          prop="name"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerFieldForm.name" clearable />
        </el-form-item>
        <el-form-item
          :label="t('components.BpmnProcessDesigner.fieldType')"
          prop="fieldType"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-select v-model="listenerFieldForm.fieldType">
            <el-option
              v-for="i in Object.keys(fieldTypeObject)"
              :key="i"
              :label="fieldTypeObject[i]"
              :value="i"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="listenerFieldForm.fieldType === 'string'"
          :label="t('components.BpmnProcessDesigner.fieldValue')"
          prop="string"
          key="field-string"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerFieldForm.string" clearable />
        </el-form-item>
        <el-form-item
          v-if="listenerFieldForm.fieldType === 'expression'"
          :label="t('components.BpmnProcessDesigner.expression')"
          prop="expression"
          key="field-expression"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerFieldForm.expression" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button size="small" @click="listenerFieldFormModelVisible = false">{{
          t('common.cancel')
        }}</el-button>
        <el-button size="small" type="primary" @click="saveListenerFiled">{{
          t('common.ok')
        }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'UserTaskListeners'
})

const { t } = useI18n()
import { ElMessageBox } from 'element-plus'
import { createListenerObject, updateElementExtensions } from '../../utils'
import { initListenerForm, initListenerType, eventType, listenerType, fieldType } from './utilSelf'
const props = defineProps({
  id: String,
  type: String
})
const prefix = inject('prefix')
const width = inject('width')
const elementListenersList = ref<any[]>([])
const listenerEventTypeObject = ref(eventType)
const listenerTypeObject = ref(listenerType)
const listenerFormModelVisible = ref(false)
const listenerForm = ref<any>({})
const fieldTypeObject = ref(fieldType)
const fieldsListOfListener = ref<any[]>([])
const listenerFieldFormModelVisible = ref(false) // 监听器 注入字段表单弹窗 显示状态
const editingListenerIndex = ref(-1) // 监听器所在下标，-1 为新增
const editingListenerFieldIndex = ref(-1) // 字段所在下标，-1 为新增
const listenerFieldForm = ref<any>({}) // 监听器 注入字段 详情表单
const bpmnElement = ref()
const bpmnElementListeners = ref()
const otherExtensionList = ref()
const listenerFormRef = ref()
const listenerFieldFormRef = ref()
const bpmnInstances = () => (window as any)?.bpmnInstances

const resetListenersList = () => {
  console.log(
    bpmnInstances().bpmnElement,
    'window.bpmnInstances.bpmnElementwindow.bpmnInstances.bpmnElementwindow.bpmnInstances.bpmnElementwindow.bpmnInstances.bpmnElementwindow.bpmnInstances.bpmnElementwindow.bpmnInstances.bpmnElement'
  )
  bpmnElement.value = bpmnInstances().bpmnElement
  otherExtensionList.value = []
  bpmnElementListeners.value =
    bpmnElement.value.businessObject?.extensionElements?.values.filter(
      (ex) => ex.$type === `${prefix}:TaskListener`
    ) ?? []
  elementListenersList.value = bpmnElementListeners.value.map((listener) =>
    initListenerType(listener)
  )
}
const openListenerForm = (listener, index?) => {
  if (listener) {
    listenerForm.value = initListenerForm(listener)
    editingListenerIndex.value = index
  } else {
    listenerForm.value = {}
    editingListenerIndex.value = -1 // 标记为新增
  }
  if (listener && listener.fields) {
    fieldsListOfListener.value = listener.fields.map((field) => ({
      ...field,
      fieldType: field.string ? 'string' : 'expression'
    }))
  } else {
    fieldsListOfListener.value = []
    listenerForm.value['fields'] = []
  }
  // 打开侧边栏并清楚验证状态
  listenerFormModelVisible.value = true
  nextTick(() => {
    if (listenerFormRef.value) listenerFormRef.value.clearValidate()
  })
}
// 移除监听器
const removeListener = (listener, index?) => {
  console.log(listener, 'listener')
  ElMessageBox.confirm(
    t('components.BpmnProcessDesigner.confirmCutOutListenerTip'),
    t('components.BpmnProcessDesigner.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel')
    }
  )
    .then(() => {
      bpmnElementListeners.value.splice(index, 1)
      elementListenersList.value.splice(index, 1)
      updateElementExtensions(
        bpmnElement.value,
        otherExtensionList.value.concat(bpmnElementListeners.value)
      )
    })
    .catch(() => console.info('操作取消'))
}
// 保存监听器
const saveListenerConfig = async () => {
  let validateStatus = await listenerFormRef.value.validate()
  if (!validateStatus) return // 验证不通过直接返回
  const listenerObject = createListenerObject(listenerForm.value, true, prefix)
  if (editingListenerIndex.value === -1) {
    bpmnElementListeners.value.push(listenerObject)
    elementListenersList.value.push(listenerForm.value)
  } else {
    bpmnElementListeners.value.splice(editingListenerIndex.value, 1, listenerObject)
    elementListenersList.value.splice(editingListenerIndex.value, 1, listenerForm.value)
  }
  // 保存其他配置
  otherExtensionList.value =
    bpmnElement.value.businessObject?.extensionElements?.values?.filter(
      (ex) => ex.$type !== `${prefix}:TaskListener`
    ) ?? []
  updateElementExtensions(
    bpmnElement.value,
    otherExtensionList.value.concat(bpmnElementListeners.value)
  )
  // 4. 隐藏侧边栏
  listenerFormModelVisible.value = false
  listenerForm.value = {}
}
// 打开监听器字段编辑弹窗
const openListenerFieldForm = (field, index?) => {
  listenerFieldForm.value = field ? JSON.parse(JSON.stringify(field)) : {}
  editingListenerFieldIndex.value = field ? index : -1
  listenerFieldFormModelVisible.value = true
  nextTick(() => {
    if (listenerFieldFormRef.value) listenerFieldFormRef.value.clearValidate()
  })
}
// 保存监听器注入字段
const saveListenerFiled = async () => {
  let validateStatus = await listenerFieldFormRef.value.validate()
  if (!validateStatus) return // 验证不通过直接返回
  if (editingListenerFieldIndex.value === -1) {
    fieldsListOfListener.value.push(listenerFieldForm.value)
    listenerForm.value.fields.push(listenerFieldForm.value)
  } else {
    fieldsListOfListener.value.splice(editingListenerFieldIndex.value, 1, listenerFieldForm.value)
    listenerForm.value.fields.splice(editingListenerFieldIndex.value, 1, listenerFieldForm.value)
  }
  listenerFieldFormModelVisible.value = false
  nextTick(() => {
    listenerFieldForm.value = {}
  })
}
// 移除监听器字段
const removeListenerField = (field, index) => {
  console.log(field, 'field')
  ElMessageBox.confirm(
    t('components.BpmnProcessDesigner.confirmCutOutFieldTip'),
    t('components.BpmnProcessDesigner.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel')
    }
  )
    .then(() => {
      fieldsListOfListener.value.splice(index, 1)
      listenerForm.value.fields.splice(index, 1)
    })
    .catch(() => console.info('操作取消'))
}

watch(
  () => props.id,
  (val) => {
    val &&
      val.length &&
      nextTick(() => {
        resetListenersList()
      })
  },
  { immediate: true }
)
</script>
