export const template = (isTaskListener) => {
  return `
  <div class="panel-tab__content">
    <el-table :data="elementListenersList" size="small" border>
      <el-table-column label="${t(
        'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.num'
      )}" width="50px" type="index" />
      <el-table-column label="${t(
        'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.eventType'
      )}" min-width="100px" prop="event" />
      <el-table-column label="${t(
        'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.listenerType'
      )}" min-width="100px" show-overflow-tooltip :formatter="row => listenerTypeObject[row.listenerType]" />
      <el-table-column label="${t(
        'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.operation'
      )}" width="90px">
        <template #default="scope">
          <el-button size="small" type="primary" link @click="openListenerForm(scope, scope.$index)">${t(
            'common.edit'
          )}</el-button>
          <el-divider direction="vertical" />
          <el-button size="small" type="primary" link style="color: #ff4d4f" @click="removeListener(scope, scope.$index)">${t(
            'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.cutout'
          )}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="element-drawer__button">
      <el-button size="small" type="primary" icon="el-icon-plus" @click="openListenerForm(null)">${t(
        'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.addListener'
      )}</el-button>
    </div>

    <!-- 监听器 编辑/创建 部分 -->
    <el-drawer :visible.sync="listenerFormModelVisible" title="${t(
      'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.executeListener'
    )}" :size="width + 'px'" append-to-body destroy-on-close>
      <el-form size="small" :model="listenerForm" label-width="96px" ref="listenerFormRef" @submit.native.prevent>
        <el-form-item label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.eventType'
        )}" prop="event" :rules="{ required: true, trigger: ['blur', 'change'] }">
          <el-select v-model="listenerForm.event">
            <el-option label="start" value="start" />
            <el-option label="end" value="end" />
          </el-select>
        </el-form-item>
        <el-form-item label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.listenerType'
        )}" prop="listenerType" :rules="{ required: true, trigger: ['blur', 'change'] }">
          <el-select v-model="listenerForm.listenerType">
            <el-option v-for="i in Object.keys(listenerTypeObject)" :key="i" :label="listenerTypeObject[i]" :value="i" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="listenerForm.listenerType === 'classListener'"
          label="${t(
            'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.javaClass'
          )}"
          prop="class"
          key="listener-class"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerForm.class" clearable />
        </el-form-item>
        <el-form-item
          v-if="listenerForm.listenerType === 'expressionListener'"
          label="${t(
            'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.expression'
          )}"
          prop="expression"
          key="listener-expression"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerForm.expression" clearable />
        </el-form-item>
        <el-form-item
          v-if="listenerForm.listenerType === 'delegateExpressionListener'"
          label="${t(
            'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.delegateExpression'
          )}"
          prop="delegateExpression"
          key="listener-delegate"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerForm.delegateExpression" clearable />
        </el-form-item>
        <template v-if="listenerForm.listenerType === 'scriptListener'">
          <el-form-item
            label="${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.scriptFormat'
            )}"
            prop="scriptFormat"
            key="listener-script-format"
            :rules="{ required: true, trigger: ['blur', 'change'], message: ${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.pleaseEnterScriptFormat'
            )} }"
          >
            <el-input v-model="listenerForm.scriptFormat" clearable />
          </el-form-item>
          <el-form-item
            label="${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.scriptType'
            )}"
            prop="scriptType"
            key="listener-script-type"
            :rules="{ required: true, trigger: ['blur', 'change'], message: ${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.pleaseSelectScriptType'
            )} }"
          >
            <el-select v-model="listenerForm.scriptType">
              <el-option label="${t(
                'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.inlineScript'
              )}" value="inlineScript" />
              <el-option label="${t(
                'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.externalScript'
              )}" value="externalScript" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="listenerForm.scriptType === 'inlineScript'"
            label="${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.scriptValue'
            )}"
            prop="value"
            key="listener-script"
            :rules="{ required: true, trigger: ['blur', 'change'], message: ${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.pleaseInputScriptValue'
            )} }"
          >
            <el-input v-model="listenerForm.value" clearable />
          </el-form-item>
          <el-form-item
            v-if="listenerForm.scriptType === 'externalScript'"
            label="${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.resourceUrl'
            )}"
            prop="resource"
            key="listener-resource"
            :rules="{ required: true, trigger: ['blur', 'change'], message: ${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.pleaseInputResourceUrl'
            )} }"
          >
            <el-input v-model="listenerForm.resource" clearable />
          </el-form-item>
        </template>
        ${
          isTaskListener
            ? `<el-form-item label='${t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.eventDefinitionType'
              )}' prop='eventDefinitionType' key='eventDefinitionType'>" +
              "<el-select v-model='listenerForm.eventDefinitionType'>" +
              "<el-option label='${t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.date'
              )}' value='date' />" +
              "<el-option label='${t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.duration'
              )}' value='duration' />" +
              "<el-option label='${t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.loop'
              )}' value='cycle' />" +
              "<el-option label='${t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.none'
              )}' value='' />" +
              '</el-select>' +
              '</el-form-item>' +
              "<el-form-item v-if='!!listenerForm.eventDefinitionType' label='${t(
                'components.BpmnProcessDesigner.package.penal.listeners.UserTaskListeners.timer'
              )}' prop='eventDefinitions' key='eventDefinitions'>" +
              "<el-input v-model='listenerForm.eventDefinitions' clearable />` + '</el-form-item>'
            : ''
        }
      </el-form>
      <el-divider />
      <p class="listener-filed__title">
        <span><i class="el-icon-menu"></i>${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.inputField'
        )}</span>
        <el-button size="small" type="primary" @click="openListenerFieldForm(null)">${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.addField'
        )}</el-button>
      </p>
      <el-table :data="fieldsListOfListener" size="small" max-height="240" border fit style="flex: none">
        <el-table-column label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.num'
        )}" width="50px" type="index" />
        <el-table-column label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.fieldName'
        )}" min-width="100px" prop="name" />
        <el-table-column label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.fieldType'
        )}" min-width="80px" show-overflow-tooltip :formatter="row => fieldTypeObject[row.fieldType]" />
        <el-table-column label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.fieldValue'
        )}/${t(
    'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.expression'
  )}" min-width="100px" show-overflow-tooltip :formatter="row => row.string || row.expression" />
        <el-table-column label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.operation'
        )}" width="100px">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="openListenerFieldForm(scope, scope.$index)">${t(
              'common.edit'
            )}</el-button>
            <el-divider direction="vertical" />
            <el-button size="small" type="primary" link style="color: #ff4d4f" @click="removeListenerField(scope, scope.$index)">${t(
              'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.cutout'
            )}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="element-drawer__button">
        <el-button size="small" @click="listenerFormModelVisible = false">${t(
          'common.cancel'
        )}</el-button>
        <el-button size="small" type="primary" @click="saveListenerConfig">${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.save'
        )}</el-button>
      </div>
    </el-drawer>

    <!-- 注入西段 编辑/创建 部分 -->
    <el-dialog title="${t(
      'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.fieIdSetting'
    )}" :visible.sync="listenerFieldFormModelVisible" width="600px" append-to-body destroy-on-close>
      <el-form :model="listenerFieldForm" size="small" label-width="96px" ref="listenerFieldFormRef" style="height: 136px" @submit.native.prevent>
        <el-form-item label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.fieldName'
        )}：" prop="name" :rules="{ required: true, trigger: ['blur', 'change'] }">
          <el-input v-model="listenerFieldForm.name" clearable />
        </el-form-item>
        <el-form-item label="${t(
          'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.fieldType'
        )}：" prop="fieldType" :rules="{ required: true, trigger: ['blur', 'change'] }">
          <el-select v-model="listenerFieldForm.fieldType">
            <el-option v-for="i in Object.keys(fieldTypeObject)" :key="i" :label="fieldTypeObject[i]" :value="i" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="listenerFieldForm.fieldType === 'string'"
          label="${t(
            'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.fieldValue'
          )}："
          prop="string"
          key="field-string"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerFieldForm.string" clearable />
        </el-form-item>
        <el-form-item
          v-if="listenerFieldForm.fieldType === 'expression'"
          label="${t(
            'components.BpmnProcessDesigner.package.penal.listeners.ElementListeners.expression'
          )}："
          prop="expression"
          key="field-expression"
          :rules="{ required: true, trigger: ['blur', 'change'] }"
        >
          <el-input v-model="listenerFieldForm.expression" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button size="small" @click="listenerFieldFormModelVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveListenerFiled">确 定</el-button>
      </template>
    </el-dialog>
  </div>
  `
}
