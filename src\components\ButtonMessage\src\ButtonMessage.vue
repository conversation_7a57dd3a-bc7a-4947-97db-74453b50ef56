<!-- /**
 * 提示框异步button，点击后可选配弹出提示框，自动loading，结束后弹出message
 * <AUTHOR>
 * @data 2023-7-12
 * 调用示例：
 *<ButtonMessage
    clickPromise="warningTypeList" //必填，function返回一个promise
    loadingText="提交中"//选填，loading中显示的字段
    messageBoxConfirm=['确定删除合同预警吗？', '提示',
      {confirmButtonText: '确定',cancelButtonText: '取消',type: 'warning'}] 选填，ElMessageBox.confirm的3个参数
    ElMessage = {showClose: true,message: '删除成功',duration: 2000,type: 'success'}选填，ElMessages提示框对象
    />
 *
 **/
//快速复制粘贴
 <ButtonMessage
          :messageBoxConfirm="['']"
          type="primary"
          :clickPromise="clickPromise"
          :message="{}"
          >搜索</ButtonMessage
async function clickPromise() {
  const Apromise = new Promise<void>((resolve, reject) => {
    setTimeout(() => {
      resolve()
    }, 3000)
  })
  await Apromise //放置请求 等异步任务
}
-->
<template>
  <el-button :loading="loading" @click="btnClick" v-bind="$attrs">
    <template v-if="loading && props.loadingText">
      {{ props.loadingText }}
    </template>
    <template v-else>
      <slot></slot>
    </template>
  </el-button>
</template>
<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ElMessageBoxOptions, MessageParams } from 'element-plus'
const { t } = useI18n()
type PropsValue = {
  clickPromise: () => Promise<void>
  loadingText?: string
  messageBoxConfirm?: [string?, string?, ElMessageBoxOptions?]
  message?: MessageParams
}
const props = defineProps<PropsValue>()
const loading = ref(false)
async function btnClick() {
  if (props.messageBoxConfirm) {
    const arr = props.messageBoxConfirm
    try {
      await ElMessageBox.confirm(
        arr[0] ?? t('components.ButtonMessage.isConfirm'),
        arr[1] ?? t('components.ButtonMessage.tip'),
        arr[2] ?? {
          confirmButtonText: t('common.ok'),
          cancelButtonText: t('common.cancel'),
          type: 'warning'
        }
      )
    } catch (error) {
      return
    }
  }

  loading.value = true
  try {
    await props.clickPromise()
  } finally {
    loading.value = false
  }
  if (props.message) {
    let obj: MessageParams = {
      showClose: true,
      message: '成功',
      duration: 2000,
      type: 'success'
    }
    let message: any = props.message
    obj = { ...obj, ...message }
    ElMessage(obj)
  }
}
</script>
