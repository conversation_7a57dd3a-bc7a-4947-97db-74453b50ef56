<!-- /**
 * 异步button，点击后自动loading
 * <AUTHOR>
 * @data 2023-7-4
 * 备注：未做全局注册,功能继承el-button，
 * 调用示例：
 *<ButtonPromise
    clickPromise="warningTypeList" //必填，function返回一个promise
    loadingText="提交中"//选填，loading中显示的字段
    />
 *
 **/
//快速复制粘贴
 <ButtonPromise :clickPromise="clickPromise">我是一个按钮</ButtonPromise>
 import ButtonPromise from '@/components/ButtonPromise/src/ButtonPromise.vue'
 async function clickPromise() {
  const Apromise = new Promise<void>((resolve, reject) => {
    setTimeout(() => {
      resolve()
    }, 3000)
  })
  await Apromise //放置请求等异步任务
}
-->
<template>
  <el-button :loading="loading" @click="btnClick" v-bind="$attrs">
    <template v-if="loading && props.loadingText">
      {{ props.loadingText }}
    </template>
    <template v-else>
      <slot></slot>
    </template>
  </el-button>
</template>
<script setup lang="ts">
type PropsValue = {
  clickPromise: (value?: any) => Promise<any>
  loadingText?: string
}
const props = defineProps<PropsValue>()

const loading = ref(false)
async function btnClick() {
  loading.value = true
  try {
    await props.clickPromise()
  } finally {
    loading.value = false
  }
}
</script>
