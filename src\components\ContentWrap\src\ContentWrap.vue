<template>
  <ElCard
    ref="tableRef"
    :body-style="{ height: '100%', paddingBottom: ifTable ? '0px' : 'var(--el-card-padding)' }"
    :class="[prefixCls, props.ifTable ? 'tableClass' : '']"
    shadow="never"
  >
    <template v-if="title" #header>
      <div class="flex items-center">
        <span class="text-16px font-700">{{ title }}</span>
        <ElTooltip v-if="message" effect="dark" placement="right">
          <template #content>
            <div class="max-w-200px">{{ message }}</div>
          </template>
          <Icon class="ml-5px" icon="bi:question-circle-fill" :size="14" />
        </ElTooltip>
      </div>
    </template>
    <div class="!h-full flex flex-col">
      <div v-if="$slots.search" class="!mb-0">
        <div v-show="showSearchForm">
          <slot name="search"> </slot>
        </div>
        <el-divider class="!m-0 !mb-3">
          <el-link
            v-if="showSearchForm"
            :underline="false"
            @click="() => (showSearchForm = false)"
            :icon="CaretTop"
          >
            {{ t('common.shrink') }}
          </el-link>
          <el-link
            v-if="!showSearchForm"
            :underline="false"
            @click="() => (showSearchForm = true)"
            :icon="CaretBottom"
          >
            {{ t('common.expand') }}
          </el-link>
        </el-divider>
      </div>
      <slot></slot>
      <div v-if="$slots.pagination" class="!mt-0">
        <slot name="pagination"> </slot>
      </div>
    </div>
  </ElCard>
</template>

<script setup lang="ts">
import { ElCard, ElTooltip } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
const { t } = useI18n()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('content-wrap')

const props = defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def(''),
  ifTable: {
    type: Boolean,
    default: false
  }
})

let tableRef = ref<any>(null)
onMounted(() => {
  setHeight()
})

const setHeight = () => {
  if (props.ifTable) {
    let tableRefEl = tableRef.value.$el
    const tableTop = tableRefEl.getBoundingClientRect().top
    const clientHeight = document?.querySelector('#app')?.clientHeight as number
    const footerHeight = document?.querySelector('.v-footer')?.clientHeight as number

    tableRefEl.style.height = `${clientHeight - tableTop - footerHeight - 20}px` // 或者使用其他方式来动态设置高度

    //
  }
}
window.onresize = () => {
  setHeight()
}

//是否展示搜索
let showSearchForm = ref(true)
</script>
