<template>
  <div class="custom-upload">
    <ElUpload
      v-bind="$attrs"
      v-model:file-list="fileList"
      :before-upload="beforeUpload"
      :on-remove="onRemove"
      :on-change="fileChange"
      :on-exceed="onExceedHandle"
      :show-file-list="!drag"
      drag
      class="upload"
    >
      <ElButton v-if="!drag">{{ btnText }}</ElButton>
      <slot></slot>
      <span :style="{ marginLeft: '8px' }" v-if="tip">{{ tip }}</span>
    </ElUpload>
    <ElRow class="mt-20px" v-if="drag">
      <div class="file-item" v-for="(item, index) in fileList" :key="index">
        <ElImage class="del-icon" :src="del" @click="delFile(index)" />
        <ElImage class="file-item-img" :src="item.imgBase64" v-if="checkFileShow(item)" />
        <ElImage class="file-item-pdf-img" :src="pdf" v-else />
      </div>
    </ElRow>
  </div>
</template>
<script setup lang="ts">
const { t } = useI18n()
import del from './img/del.png'
import pdf from './img/pdf.png'
const props = defineProps({
  tip: {
    type: String,
    default: ''
  },
  //上传最大字节限制
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024
  },
  btnText: {
    type: String,
    default: t('makeCard.common.uploadFile')
  },
  limitFormat: {
    type: Array,
    default: () => []
  },
  //是否自动上传
  autoUpload: {
    type: Boolean,
    default: false
  },
  // 是否上传多个
  multiple: {
    type: Boolean,
    default: false
  },
  drag: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['success', 'file-change', 'delete-file'])

const { maxSize, limitFormat, autoUpload, btnText } = toRefs(props)

const fileList = ref<any>([])

const $attrs = useAttrs()

// 上传之前钩子
const beforeUpload = (file) => {
  return verifyFile(file)
}
function verifyFile(file) {
  if (props.multiple) {
    const sumSize = fileList.value.reduce((a, b) => {
      return a + b.size
    }, 0)
    if (sumSize > maxSize.value) {
      ElMessage.error(t('components.CustomUpload.sumExtends'))
      return false
    }
  }
  if (file.size > maxSize.value) {
    ElMessage.error(t('components.CustomUpload.extends'))
    return false
  }
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
  let fileSuffix = limitFormat.value.length > 0 ? limitFormat.value.includes(fileType) : true
  if (!fileSuffix && limitFormat.value.length > 0) {
    ElMessage.error(t('components.CustomUpload.reUpload'))
  }
  console.log('fileSuffix', fileSuffix)
  return fileSuffix
}
// 超出上传数量
const onExceedHandle = () => {
  ElMessage.error(t('components.CustomUpload.extendsNum'))
}

function fileChange(_file, fileArr) {
  fileList.value = fileArr
  console.log('fileList.value', fileList.value)
  if (!autoUpload) {
    let canUpload = verifyFile(_file)
    if (!canUpload) {
      fileArr.forEach((v: any, i) => {
        if (v.uid === _file.uid) {
          fileList.value.splice(i, 1)
        }
      })
    }
  }
  emit('file-change', fileList.value)
}

const submitFileBase64 = () => {
  return new Promise(async (resolve, reject) => {
    try {
      if (fileList?.value?.length < 1) {
        return reject()
      }
      await fileList.value.forEach(async (item, index) => {
        fileList.value[index].imgBase64 = await fileToBase64(item.raw)
        if (index === fileList?.value?.length - 1) {
          return resolve(fileList.value)
        }
      })
    } catch (e) {
      ElMessage.error(t('components.CustomUpload.uploadFail'))
      return reject(false)
    }
  })
}

const fileToBase64 = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      const base64 = reader.result
      resolve(base64)
    }
    reader.readAsDataURL(file)
  })
}

const onRemove = (file, fileArr) => {
  // ElMessage.warning('您移除了文件【' + file.name + '】')
  emit('delete-file', fileArr)
}

const delFile = (index: number) => {
  fileList.value.splice(index, 1)
  emit('delete-file', fileList.value)
}

// 检验是否上传完成
const checkComplete = () => {
  return new Promise<boolean>((resolve, reject) => {
    if (fileList.value.length > 0) {
      let arr = fileList.value.map((item) => {
        return item.url
      })
      if (arr.includes(undefined)) {
        ElMessage.warning(t('components.CustomUpload.waitForUpload'))
        reject(false)
      } else {
        resolve(true)
      }
    } else {
      resolve(true)
    }
  })
}

// 校验图片是否显示
const checkFileShow = (_fileObj) => {
  if (_fileObj.raw.type.indexOf('pdf') === -1) {
    return true
  } else {
    return false
  }
}

defineExpose({
  fileList,
  checkComplete,
  submitFileBase64
})
</script>
<style lang="less" scoped>
.custom-upload {
}
.file-item {
  width: 168px;
  height: 168px;
  margin-right: 22px;
  border: #c5c5c8 dashed 1px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 22px;
  .del-icon {
    width: 25px;
    height: 25px;
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 999;
    cursor: pointer;
  }
}
.file-item-img {
  width: 158px;
  height: 158px;
  border-radius: 12px;
}

.file-item-pdf-img {
  width: 98px;
  height: 98px;
}

:deep(.el-upload-dragger) {
  background: #f6f6f6;
}
</style>
