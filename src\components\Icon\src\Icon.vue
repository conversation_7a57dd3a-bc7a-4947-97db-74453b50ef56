<script setup lang="ts">
import { computed, unref, ref, watch, nextTick } from 'vue'
import { ElIcon } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import Iconify from '@iconify/iconify'
import { useDesign } from '@/hooks/web/useDesign'

//离线加载icon包
import epIcon from '@iconify/json/json/ep.json'
import faIcon from '@iconify/json/json/fa.json'
import faSolidIcon from '@iconify/json/json/fa-solid.json'
import ionIcon from '@iconify/json/json/ion.json'
import uilIcon from '@iconify/json/json/uil.json'
import mdiIcon from '@iconify/json/json/mdi.json'
import tablerIcon from '@iconify/json/json/tabler.json'
import zmdiIcon from '@iconify/json/json/zmdi.json'
import antDesignIcon from '@iconify/json/json/ant-design.json'
Iconify.addCollection(epIcon)
Iconify.addCollection(faIcon)
Iconify.addCollection(faSolidIcon)
Iconify.addCollection(ionIcon)
Iconify.addCollection(uilIcon)
Iconify.addCollection(mdiIcon)
Iconify.addCollection(tablerIcon)
Iconify.addCollection(zmdiIcon)
Iconify.addCollection(antDesignIcon)

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('icon')

const props = defineProps({
  // icon name
  icon: propTypes.string,
  // icon color
  color: propTypes.string,
  // icon size
  size: propTypes.number.def(16)
})

const elRef = ref<ElRef>(null)

const isLocal = computed(() => props.icon.startsWith('svg-icon:'))

const symbolId = computed(() => {
  return unref(isLocal) ? `#icon-${props.icon.split('svg-icon:')[1]}` : props.icon
})

const getIconifyStyle = computed(() => {
  const { color, size } = props
  return {
    fontSize: `${size}px`,
    color
  }
})

const updateIcon = async (icon: string) => {
  if (unref(isLocal)) return

  const el = unref(elRef)
  if (!el) return

  await nextTick()

  if (!icon) return

  const svg = Iconify.renderSVG(icon, {})
  if (svg) {
    el.textContent = ''
    el.appendChild(svg)
  } else {
    const span = document.createElement('span')
    span.className = 'iconify'
    span.dataset.icon = icon
    el.textContent = ''
    el.appendChild(span)
  }
}

watch(
  () => props.icon,
  (icon: string) => {
    updateIcon(icon)
  }
)
</script>

<template>
  <ElIcon :class="prefixCls" :size="size" :color="color">
    <svg v-if="isLocal" aria-hidden="true">
      <use :xlink:href="symbolId" />
    </svg>

    <span v-else ref="elRef" :class="$attrs.class" :style="getIconifyStyle">
      <span class="iconify" :data-icon="symbolId"></span>
    </span>
  </ElIcon>
</template>
