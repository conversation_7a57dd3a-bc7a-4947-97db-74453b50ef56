<!-- /**
 * 页面table
 * <AUTHOR>
 * @data 2023-9-20
 * 调用示例：
 *<PageTable
    :data="data" //必填，表单数据
    :columns="columns" //必填，表头内容
    :handleList='handleList'//非必填，操作列表
    :total="total" //列表总数
    v-model:page="queryParams.pageNum"  //页码
    v-model:limit="queryParams.pageSize" //每页显示行数
    @pagination="getList" // 改变分页触发方法
    />
    API
    columns = {
      slotName: string // 插槽的名称
      ... //ELTableColumn组件的参数，完美继承
    }
    handleList = {
      label:string //操作按钮标题
      onClick:(scope)=>void //点击按钮事件
      isShow:(scope)=>boole //是否显示按钮
      ...////ELButton组件的参数，完美继承
    }
 *
 **/
//快速复制粘贴
<template>
  <PageTable 
    :data="data"
    :columns="columns"
    :handleList="handleList" 
    :total="total" 
    v-model:page="queryParams.pageNum"  
    v-model:limit="queryParams.pageSize"
    @pagination="getList">
    <template  #slotName1="scope">
    </template>
  </PageTable>
 </template> 
 <script lang="ts" setup>
  const data = ref([
    {
      data1: 1,
      data2: 2,
    }])
  const handleList = ref([
    { label: '删除', onClick: (scope) => { console.log({ scope }) },isShow: (scope) => { return scope.data1 === 1}}
  ])
  const columns = ref([
  { label: 'data1表格头', prop: 'data1', showOverflowTooltip: true },
  { label: '自定义模板', slotName: 'slotName1'}
  ])
</script>
-->
<template>
  <BaseTable :data="props.data" v-bind="$attrs" :columns="columnsHandle" :handleList="handleList">
    <template v-for="item in slotList" :key="item" #[item]="scope">
      <slot :name="item" v-bind="scope"></slot>
    </template>
  </BaseTable>

  <Pagination
    v-model:page="pageNum"
    v-model:limit="pageSize"
    @pagination="paginationFnc"
    :total="total"
  />
</template>

<script lang="ts" setup>
import { BaseTable } from '../../BaseTable/index'
import { fooProps, columnsType } from './type'
const { t } = useI18n()

const props = defineProps(fooProps)
const columnsHandle = computed<columnsType | undefined>(() => {
  if (props.page && props.limit && props.columns) {
    return [
      {
        type: 'index',
        width: '60',
        label: t('components.BpmnProcessDesigner.num'),
        index: (index) => {
          let num: number = index + 1 + (props.page - 1) * props.limit
          return num
        }
      },
      ...props.columns
    ]
  } else {
    return props.columns
  }
})
const slotList = computed(() => {
  const list = props.columns.filter((item) => item.slotName)
  if (list.length > 0) {
    return list.map((item) => item.slotName)
  } else {
    return []
  }
})
const emit = defineEmits(['update:page', 'update:limit', 'pagination'])
function paginationFnc(obj) {
  emit('pagination', obj)
}
const pageNum = computed({
  get() {
    return props.page
  },
  set(val) {
    // 触发 update:page 事件，更新 limit 属性，从而更新 pageNo
    emit('update:page', val)
  }
})
const pageSize = computed({
  get() {
    return props.limit
  },
  set(val) {
    // 触发 update:limit 事件，更新 limit 属性，从而更新 pageSize
    emit('update:limit', val)
  }
})
</script>

<style lang="less">
.component-header-row {
  height: 45px;

  color: #9b9ea3;
  .component-header-cell {
    padding: 0;
  }
}
.component-row {
  height: 65px;
}
</style>
