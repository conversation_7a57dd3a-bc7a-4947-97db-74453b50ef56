<template>
  <el-select size="large" v-bind="$attrs">
    <el-option
      v-for="(item, index) in props.optionList"
      :key="item[props.setOption?.label || 'label'] as string + index"
      :value="item[props.setOption?.value || 'value']"
      :label="item[props.setOption?.label || 'label']"
    />
  </el-select>
</template>
<script setup lang="ts">
type PropsValue = {
  optionList: optionListValue
  setOption: any
}
type optionListValue = {
  label: string | number
  value: string | number | boolean
}[]

const props = defineProps<PropsValue>()
</script>
