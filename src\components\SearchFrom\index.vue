<!-- /**
 * 搜索框
 * <AUTHOR>
 * @data 2023-7-12
 * 调用示例：
 *<SearchFrom
    v-model="obj" //必填，查询数据
    columns="columns" //必填，搜索框内容
    :downDefault='false'//非必填，boole类型,默认展开还是收起，默认收起
    @search="getList" //点击搜索
    @reset="reset"//点击重置
    :offUnfold='false'//非必填，boole类型,默认有展开
    
    />
    API columns = {
      type:'input' | 'select' | 'datePicker'
      itemProps:'' //element组件的内容，完美继承
      label：搜索标题
      key：搜索key
    }
    //可加自定义插槽，但优先喊yanfansong加你想要的类型，此组件扩展类型十分方便，极大概率会满足你的需求。
 *
 **/
//快速复制粘贴
 <SearchFrom v-model="SearchFrom" :columns="columns" @search="getList" />
 const columns = ref<Columns>([
  {
    type: 'input',
    key: 'contractCode',
    label: '合同编码'
  },
  {
    type: 'select',
    key: 'contractTypeCode',
    label: '合同类型',
    optionList: [
      {
        value: '1',
        label: '第一个'
      }
    ]
  },
  {
    type: 'datePicker',
    key: 'timeRange',
    label: '终止时间'
  }
  ])
-->
<template>
  <el-form ref="queryRef" :model="searchForm" :inline="true" label-width="118px">
    <div class="search-box" ref="searchRef" :style="{ height: unfoldOpen ? '' : '76px' }">
      <el-form-item
        class="form-item"
        :style="{ gridColumnStart: `span ${item.span ?? '5'}` }"
        :label="item.label + ':&nbsp;&nbsp;'"
        :prop="item.key"
        v-for="(item, index) in columnsList"
        :key="index"
      >
        {{ item.slotName }}
        <template v-if="item.type !== 'slot'">
          <component
            :is="tabs[item.type]"
            v-model="searchForm[item.key]"
            v-bind="item.itemProps"
            class="component-width"
          />
        </template>
        <template v-if="item.type === 'slot'">
          <slot :name="item.key"></slot>
        </template>
      </el-form-item>
      <el-form-item
        class="form-item button-list"
        :style="{
          'grid-column-end': '-1',
          'grid-row-end': unfoldOpen ? '' : '-1'
        }"
      >
        <div class="form-item-button">
          <el-button type="primary" size="large" class="SearchFrom-btn" @click="search">{{
            t('common.query')
          }}</el-button>
          <el-button type="warning" size="large" @click="reset" class="SearchFrom-btn">{{
            t('common.reset')
          }}</el-button>
          <div class="unfold-box" @click="unfoldOpen = !unfoldOpen" v-if="hasFoldOpen">
            <span>{{ unfoldOpen ? t('common.shrink') : t('common.expand') }} </span>
            <ArrowDown
              :style="{ transform: unfoldOpen ? 'rotate(180deg)' : '' }"
              style="width: 1.2em; height: 1.2em; color: rgba(96, 98, 102)"
            />
          </div>
        </div>
      </el-form-item>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import select from './components/select.vue'
import { ArrowDown } from '@element-plus/icons-vue'
import { ElSelect, ElInput, ElDatePicker, ElCascader, ElTreeSelect } from 'element-plus'
const { t } = useI18n()
type ElSelectPropsValue = InstanceType<typeof ElSelect>['$props']
type ElDatePickerPropsValue = InstanceType<typeof ElDatePicker>['$props']
type ElCascaderPropsValue = InstanceType<typeof ElCascader>['$props']
type ElTreeSelectPropsValue = InstanceType<typeof ElTreeSelect>['$props']
// 目前input类型会报错，暂时先手写类型
type ElInputPropsValue = Partial<{
  type: string
  label: string
  id: string
  disabled: boolean
  clearable: boolean
  autosize:
    | boolean
    | {
        minRows?: number | undefined
        maxRows?: number | undefined
      }
  autocomplete: string
  readonly: boolean
  showPassword: boolean
  showWordLimit: boolean
  containerRole: string
  tabindex: string | number
  validateEvent: boolean
  // inputStyle: import('vue').StyleValue
  size: '' | 'default' | 'small' | 'large'
  resize: 'none' | 'both' | 'horizontal' | 'vertical'
  formatter: (value: string | number) => string
  parser: (value: string) => string
  placeholder: string
  suffixIcon: string | any
  prefixIcon: string | any
  rows: number
  max: any
  min: any
  step: any
  autofocus: boolean
}>

type PropsValue = {
  modelValue: Record<string, any>
  columns: Columns
  downDefault?: boolean
  offUnfold?: boolean
}
const tabs = {
  input: ElInput,
  select,
  datePicker: ElDatePicker,
  cascader: ElCascader,
  treeSelect: ElTreeSelect
}
type ColumnDefault = {
  type: string
  key: string
  label: string
  span?: string | number
  itemProps?: Record<string, any>
}
type optionListValue = {
  label: string | number
  value: string | number | boolean
}[]
export type Column =
  | ({ type: 'input'; label: string; itemProps?: ElInputPropsValue } & ColumnDefault)
  | ({ type: 'cascader'; label: string; itemProps?: ElCascaderPropsValue } & ColumnDefault)
  | ({ type: 'treeSelect'; label: string; itemProps?: ElTreeSelectPropsValue } & ColumnDefault)
  | ({
      type: 'select'
      itemProps?: ElSelectPropsValue
      optionList: optionListValue
    } & ColumnDefault)
  | ({ type: 'datePicker'; label: string; itemProps?: ElDatePickerPropsValue } & ColumnDefault)
  | { type: 'slot'; label: string; key: string }
export type Columns = Column[]

const props = defineProps<PropsValue>()

const searchForm = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  }
})
type emitValue = {
  (e: 'update:modelValue', value): void
  (e: 'search', value): void
  (e: 'reset', value): void
}
const emit = defineEmits<emitValue>()

function search() {
  emit('search', searchForm.value)
}
const queryRef = ref()
function reset() {
  queryRef.value.resetFields()
  emit('reset', searchForm.value)
}
// 设置默认值与重构数据
const columnsList = computed(() => {
  const Columns = props.columns
  const list = Columns?.map((item) => setDefault(item))
  return list
})
function setDefault(Column: Column) {
  let itemObj: any = {}
  let extraItemProps: any = {}
  switch (Column.type) {
    case 'select':
      extraItemProps = {
        placeholder: t('common.selectText') + Column.label,
        optionList: Column.optionList,
        setOption: Column?.itemProps?.setOption || {}
      }
      break
    case 'input':
      extraItemProps = {
        placeholder: t('common.inputText') + Column.label
      }
      break
    case 'datePicker':
      extraItemProps = {
        type: 'daterange',
        rangeSeparator: '-',
        startPlaceholder: t('components.SearchFrom.startDate'),
        endPlaceholder: t('components.SearchFrom.endDate'),
        shortcuts: shortcuts
      }
      break
    default:
      itemObj = { ...Column }
      return itemObj
  }
  handleDate(extraItemProps)
  function handleDate(extraItemProps) {
    //默认配置
    const defaultItemProps = {
      size: 'large',
      clearable: true
    }
    const ItemProps = { ...defaultItemProps, ...extraItemProps }
    if (Column.type !== 'slot') {
      let itemPropsObj = { ...ItemProps, ...Column.itemProps }
      itemObj = { ...Column, itemProps: itemPropsObj }
    } else {
      itemObj = { ...Column }
    }
  }
  return itemObj
}

//时间段
const shortcuts = [
  {
    text: t('components.SearchFrom.recentlyOneWeek'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: t('components.SearchFrom.recentlyOneMonth'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: t('components.SearchFrom.recentlyThreeMonth'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

/** 展开收起 */
const hasFoldOpen = ref(true)
const unfoldOpen = ref(props.downDefault ?? false)
const searchRef = ref()
function handleHasFold() {
  if (searchRef.value.scrollHeight < 90) {
    hasFoldOpen.value = false
    unfoldOpen.value = true
  } else {
    hasFoldOpen.value = true
  }
}
onMounted(() => {
  if (props.offUnfold) {
    hasFoldOpen.value = false
    unfoldOpen.value = true
  } else {
    handleHasFold()
    window.addEventListener('resize', handleHasFold)
  }
})
onUnmounted(() => {
  window.removeEventListener('resize', handleHasFold)
})
</script>

<style lang="less" scoped>
.search-box {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  overflow: hidden;
  padding: 9px 0;
  .form-item {
    align-items: center;
    // width: 352px;
    grid-column-start: span 5;
    padding-right: 20px;
    margin-bottom: 0px;
    margin-right: 0px;
    height: 58px;
    display: flex;
  }
  .form-item-button {
    align-items: center;
    width: 352px;
    display: flex;
    justify-content: flex-end;
  }
  .SearchFrom-btn {
    width: 100px;
    font-size: 16px;
  }
  .unfold-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 76px;
    font-size: 16px;
    margin-left: 10px;
    color: #606266;
    padding: 10px;
    margin-right: -10px;
  }
}
.component-width {
  width: 100%;
}
</style>
<style lang="less">
.search-box {
  .form-item {
    .el-form-item__label {
      align-items: center;
    }
  }
}
</style>
