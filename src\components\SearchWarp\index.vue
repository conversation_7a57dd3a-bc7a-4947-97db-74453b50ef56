<template>
  <div class="search-warp">
    <ElForm ref="searchFormRef" label-position="left" :rules="rules" :model="formItem">
      <ElRow :gutter="20">
        <!-- <ElCol  v-for="(item, index) in formItem" :key="index"> -->
        <ElFormItem
          :label="item.label"
          :prop="item.name"
          :style="{
            width: '240px',
            marginRight: '20px'
          }"
          v-for="(item, index) in formItem"
          :key="index"
        >
          <component
            :is="item.component"
            :placeholder="item.placeholder"
            v-model="formValue[item.name]"
            v-bind="item.config"
            class="search-item"
          />
        </ElFormItem>
        <!-- </ElCol> -->
        <ElCol :span="8">
          <slot name="searchBtns"> </slot>
          <div v-if="!slots.searchBtns">
            <ElButton class="btn" type="primary" @click="search">{{ t('common.search') }}</ElButton>
            <ElButton class="btn" type="primary" @click="reset">{{ t('common.reset') }}</ElButton>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>
<script setup lang="ts">
import { ref, useSlots, computed } from 'vue'
import { ElCol, ElRow, ElForm, ElButton, ElFormItem } from 'element-plus'
const { t } = useI18n()
const emit = defineEmits(['search'])

export interface SEARCHITEMVO {
  name: string
  component: string
  placeholder: string
  //   rule?: any[]
  label?: string
  config?: any
}

const props = defineProps({
  formItem: {
    type: Array<SEARCHITEMVO>,
    default: () => []
  }
})

const searchFormRef = ref()

const rules = ref({})

const formValue = ref({})
let defaultFormValue = {}

const slots = useSlots()

const initFormData = () => {
  //   let rulesTemp = {}
  if (props.formItem.length === 0) {
    formValue.value = {}
    return formValue.value
  } else {
    props.formItem.forEach((el: SEARCHITEMVO) => {
      formValue.value[el.name] = undefined
      //   rulesTemp[el.name] = el.rule
      defaultFormValue[el.name] = undefined
    })
  }
  //   rules.value = rulesTemp
}

const search = async () => {
  emit('search', formValue.value)
}

const reset = () => {
  formValue.value = { ...defaultFormValue }
  return formValue.value
}

defineExpose({
  formValue,
  search,
  reset
})

onMounted(() => {
  initFormData()
})
</script>
<style lang="less" scoped>
.search-item {
  :deep(.el-input__wrapper) {
    background: #f6f6f6;
    box-shadow: 0 0 0 1px #f6f6f6;
    height: 48px;
    border-radius: 10px;
    padding: 0 20px;
    font-size: 16px;
  }
  :deep(.el-input) {
    border: #f6f6f6;
  }
}
.btn {
  padding: 23px 44px;
  color: #ffffff;
  background: linear-gradient(114deg, #eecd91, #d5a147);
  font-size: 18px;
  border-radius: 10px;
}
</style>
