<template>
  <el-input
    v-model="inputValue"
    :disabled="disabled"
    :readonly="readonly"
    :placeholder="placeholder"
    :maxlength="maxlength"
    :show-word-limit="showWordLimit"
  />
</template>
<script lang="ts" setup>
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    required: false
  },
  readonly: {
    type: Boolean,
    required: false
  },
  clearable: {
    type: Boolean,
    required: false
  },
  maxlength: {
    type: [String, Number],
    required: false
  },
  showWordLimit: {
    type: Boolean,
    required: false
  }
})
const emits = defineEmits(['update:modelValue'])
const inputValue = ref<string>('')
watch(
  () => inputValue.value,
  () => {
    let value = inputValue.value.replace(/\*/g, '%')
    emits('update:modelValue', value)
  }
)
watch(
  () => props['modelValue'],
  () => {
    if (props['modelValue'] === '') inputValue.value = props['modelValue']
  }
)
onMounted(() => {
  inputValue.value = props['modelValue']
})
</script>
