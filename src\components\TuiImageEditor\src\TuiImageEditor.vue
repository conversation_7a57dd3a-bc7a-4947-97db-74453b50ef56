<template>
  <!-- 图片编辑组件 -->
  <div class="components__tui-image-editor">
    <div id="tui-image-editor"></div>
  </div>
</template>

<script setup lang="ts">
import 'tui-image-editor/dist/tui-image-editor.css'
import 'tui-color-picker/dist/tui-color-picker.css'
import ImageEditor from 'tui-image-editor'
const localeCN = {
  ZoomIn: '放大',
  ZoomOut: '缩小',
  Hand: '手掌',
  History: '历史',
  Resize: '调整宽高',
  Crop: '裁剪',
  DeleteAll: '全部删除',
  Delete: '删除',
  Undo: '撤销',
  Redo: '反撤销',
  Reset: '重置',
  Flip: '镜像',
  Rotate: '旋转',
  Draw: '涂鸦',
  Shape: '形状标注',
  Icon: '添加图标',
  Text: '添加文本',
  Mask: '遮罩',
  Filter: '滤镜',
  Bold: '加粗',
  Italic: '斜体',
  Underline: '下划线',
  Left: '左对齐',
  Center: '居中',
  Right: '右对齐',
  Color: '颜色',
  'Text size': '字体大小',
  Custom: '自定义',
  Square: '正方形',
  Apply: '应用',
  Cancel: '取消',
  'Flip X': 'X 轴',
  'Flip Y': 'Y 轴',
  Range: '范围',
  Stroke: '描边',
  Fill: '填充',
  Circle: '圆',
  Triangle: '三角',
  Rectangle: '矩形',
  Free: '任意线条',
  Straight: '直线',
  Arrow: '箭头',
  'Arrow-2': '箭头2',
  'Arrow-3': '箭头3',
  'Star-1': '星星1',
  'Star-2': '星星2',
  Polygon: '多边形',
  Location: '定位',
  Heart: '心形',
  Bubble: '气泡',
  'Custom icon': '自定义图标',
  'Load Mask Image': '加载蒙层图片',
  Grayscale: '灰度',
  Blur: '模糊',
  Sharpen: '锐化',
  Emboss: '浮雕',
  'Remove White': '除去白色',
  Distance: '距离',
  Brightness: '亮度',
  Noise: '噪音',
  'Color Filter': '彩色滤镜',
  Sepia: '棕色',
  Sepia2: '棕色2',
  Invert: '负片',
  Pixelate: '像素化',
  Threshold: '阈值',
  Tint: '色调',
  Multiply: '正片叠底',
  Blend: '混合色',
  Width: '宽度',
  Height: '高度',
  'Lock Aspect Ratio': '锁定宽高比例'
}

const customTheme = {
  'common.bi.image': '', // 左上角logo图片
  'common.bisize.width': '0px',
  'common.bisize.height': '0px',
  //   "common.backgroundImage": "none",
  //   "common.backgroundColor": "#f3f4f6",
  //   "common.border": "1px solid #333",

  // load button
  'loadButton.backgroundColor': '#fff',
  'loadButton.border': '1px solid #ddd',
  'loadButton.color': '#222',
  'loadButton.fontFamily': 'NotoSans, sans-serif',
  'loadButton.fontSize': '12px',
  'loadButton.display': 'none', // 隐藏

  // download button
  'downloadButton.backgroundColor': '#fdba3b',
  'downloadButton.border': '1px solid #fdba3b',
  'downloadButton.color': '#fff',
  'downloadButton.fontFamily': 'NotoSans, sans-serif',
  'downloadButton.fontSize': '12px',
  'downloadButton.display': 'none' // 隐藏

  // 整体背景颜色
  //   'common.backgroundColor': '#fff',
  //   'submenu.backgroundColor': '#1e1e1e'
}

const props = defineProps({
  imgeUrl: {
    type: String,
    required: true
  } // 需要进行编辑的图片
})

let instance: any = null // 图片编辑器的实例对象

/** 初始化图片编辑器 */
const init = () => {
  let windowWidth = window.innerWidth
  let cssMaxWidth = 1920
  let cssMaxHeight = 1080
  console.log(windowWidth)
  if (windowWidth < 1400) {
    cssMaxWidth = 1100
    cssMaxHeight = 1000
  } else if (windowWidth <= 2000) {
    cssMaxWidth = 1200
    cssMaxHeight = 1600
  }

  instance = new ImageEditor(document.querySelector('#tui-image-editor'), {
    includeUI: {
      loadImage: {
        path: props.imgeUrl,
        name: 'sample'
      },
      initMenu: 'shape', // 默认打开的菜单项
      menu: [
        'crop', // 裁切
        'flip', // 翻转
        'rotate', // 旋转
        'draw', // 添加绘画
        'shape', // 添加形状
        'icon', // 添加图标
        'text', // 添加文本
        'mask', // 添加覆盖
        'filter' // 添加滤镜'
      ],
      menuBarPosition: 'left', // 菜单所在的位置 bottom left
      locale: localeCN, // 本地化语言为中文
      theme: customTheme // 自定义样式
    },
    cssMaxWidth: cssMaxWidth, // canvas 最大宽度
    cssMaxHeight: cssMaxHeight // canvas 最大高度
  })
  // 调整图片显示位置
  document.getElementsByClassName('tui-image-editor-main')[0].style.top = '0px'

  //   document.querySelector('[tooltip-content="重置"]').style.display = 'none'
}

// 获取图片base64数据
const getImgDataURL = () => {
  let element: any = document.querySelector('.tui-image-editor')
  element.style.setProperty('transform', `scale(${1})`)
  let boxElement: any = document.querySelector('.tui-image-editor-size-wrap')
  boxElement.style.setProperty('width', `${100}%`)
  boxElement.style.setProperty('height', `${100}%`)
  const base64String = instance.toDataURL() // base64 文件
  return base64String
  //   const data = window.atob(base64String.split(',')[1])
  //   const ia = new Uint8Array(data.length)
  //   for (let i = 0; i < data.length; i++) {
  //     ia[i] = data.charCodeAt(i)
  //   }
  //   const blob = new Blob([ia], { type: 'image/png' }) // blob 文件
  //   const form = new FormData()
  //   form.append('image', blob)
}

onMounted(() => {
  init()
})

defineExpose({
  getImgDataURL
})
</script>

<style lang="less" scoped>
.components__tui-image-editor {
  height: 100%;
}
</style>
<style>
.tui-image-editor-container.left .tui-image-editor-submenu {
  width: 208px !important;
}

/* .tie-btn-hand {
  display: none !important;
} */
</style>
