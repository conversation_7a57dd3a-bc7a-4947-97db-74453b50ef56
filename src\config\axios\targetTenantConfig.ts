import { getTenantId } from '@/utils/auth'

// targetTenantId相关的接口映射配置, key:接口地址 value:{target-tenant-id:目标租户id}
const targetTenantIdApiMap = new Map([
  //   [
  //     '/diyProject/service/apply/type/list',
  //     {
  //       'target-tenant-id': 'goldpac.com'
  //     }
  //   ]
])

const setTargetTenantId = (config) => {
  if (targetTenantIdApiMap.has(config.url)) {
    Object.assign(config.headers, targetTenantIdApiMap.get(config.url)) // 将这个接口额外配置的headers添加到请求头中
  } else {
    Object.assign(config.headers, {
      // 'target-tenant-id': getTenantId()
      'target-tenant-id': 'general'
    })
  }
}

export { setTargetTenantId }
