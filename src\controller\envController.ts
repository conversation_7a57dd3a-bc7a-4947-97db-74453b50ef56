/**
 * 获取当前环境变量封装
 * 引入 import envController from '@/controller/envController'
 * 使用:   envController.getOssUrl()
 */
class EnvironmentManager {
  private envMapOssUrl: Map<string, string>
  private envMapClientUrl: Map<string, string>
  private envMapManageUrl: Map<string, string>
  private envMapServiceUrl: Map<string, string>
  private envMapSaleUrl: Map<string, string>

  constructor() {
    this.envMapOssUrl = new Map<string, string>()
    this.envMapClientUrl = new Map<string, string>()
    this.envMapManageUrl = new Map<string, string>()
    this.envMapServiceUrl = new Map<string, string>()
    this.envMapSaleUrl = new Map<string, string>()

    this.initializeEnvMapOssUrl()
    this.initializeEnvMapClientUrl()
    this.initializeEnvMapManageUrl()
    this.initializeEnvMapServiceUrl()
    this.initializeEnvMapSaleUrl()
  }
  /**
   * 初始化环境映射 OSS URL
   *
   * 将开发、测试、预发布和生产环境的 OSS URL 分别设置到 envMapOssUrl 映射中。
   */
  private initializeEnvMapOssUrl(): void {
    this.envMapOssUrl.set('dev', import.meta.env.VITE_OSS_URL_DEV)
    this.envMapOssUrl.set('sit', import.meta.env.VITE_OSS_URL_SIT)
    this.envMapOssUrl.set('uat', import.meta.env.VITE_OSS_URL_UAT)
    this.envMapOssUrl.set('pro', import.meta.env.VITE_OSS_URL_PRO)
  }
  /**
   * 获取环境变量中OSS（对象存储服务）的URL映射
   *
   * @returns 返回包含OSS URL映射的Map对象，其中键为环境变量名，值为对应的OSS URL
   */
  public getEnvMapOssUrl(): Map<string, string> {
    return this.envMapOssUrl
  }

  /**
   * 获取 OSS（对象存储服务）的 URL。
   *
   * @returns 返回当前环境下 OSS 的 URL，若环境不存在则返回 undefined。
   */
  public getOssUrl(): string | undefined {
    return this.envMapOssUrl.get(this.getEnvironment())
  }
  /**
   * 初始化环境映射客户端URL
   *
   * 将不同的环境（如开发、SIT、UAT、生产）对应的客户端URL设置到envMapClientUrl对象中
   */
  private initializeEnvMapClientUrl(): void {
    this.envMapClientUrl.set('dev', import.meta.env.VITE_CLIENT_DEV)
    this.envMapClientUrl.set('sit', import.meta.env.VITE_CLIENT_SIT)
    this.envMapClientUrl.set('uat', import.meta.env.VITE_CLIENT_UAT)
    this.envMapClientUrl.set('pro', import.meta.env.VITE_CLIENT_PRO)
  }
  /**
   * 获取环境配置中的客户端 URL 映射关系
   *
   * @returns 返回一个 Map 对象，其中键为字符串类型，值为字符串类型，表示客户端 URL 的映射关系
   */
  public getEnvMapClientUrl(): Map<string, string> {
    return this.envMapClientUrl
  }
  /**
   * 获取客户端URL
   *
   * @returns 返回客户端URL的字符串或undefined（如果环境未定义或环境映射中未找到URL）
   */
  public getClientUrl(): string | undefined {
    return this.envMapClientUrl.get(this.getEnvironment())
  }

  /**
   * 初始化环境映射服务URL
   *
   * @description 根据环境变量设置环境映射服务URL
   * @private
   * @returns 无返回值
   */
  private initializeEnvMapServiceUrl(): void {
    this.envMapServiceUrl.set('dev', import.meta.env.VITE_SERVICE_DEV)
    this.envMapServiceUrl.set('sit', import.meta.env.VITE_SERVICE_SIT)
    this.envMapServiceUrl.set('uat', import.meta.env.VITE_SERVICE_UAT)
    this.envMapServiceUrl.set('pro', import.meta.env.VITE_SERVICE_PRO)
  }
  /**
   * 获取环境映射服务URL的Map对象
   *
   * @returns 返回包含环境映射服务URL的Map对象，键为环境标识符，值为对应的服务URL
   */
  public getEnvMapServiceUrl(): Map<string, string> {
    return this.envMapServiceUrl
  }
  /**
   * 获取服务URL
   *
   * @returns 返回当前环境对应的服务URL，若未找到则返回undefined
   */
  public getServiceUrl(): string | undefined {
    return this.envMapServiceUrl.get(this.getEnvironment())
  }

  /**
   * 初始化环境映射管理URL
   *
   * @returns 无返回值，该方法会修改this.envMapManageUrl对象
   */
  private initializeEnvMapManageUrl(): void {
    this.envMapManageUrl.set('dev', import.meta.env.VITE_MANAGE_DEV)
    this.envMapManageUrl.set('sit', import.meta.env.VITE_MANAGE_SIT)
    this.envMapManageUrl.set('uat', import.meta.env.VITE_MANAGE_UAT)
    this.envMapManageUrl.set('pro', import.meta.env.VITE_MANAGE_PRO)
  }
  /**
   * 获取环境变量映射管理URL的Map对象
   *
   * @returns 包含环境变量映射管理URL的Map对象，键为环境变量名称，值为对应的URL
   */
  public getEnvMapManageUrl(): Map<string, string> {
    return this.envMapManageUrl
  }
  /**
   * 获取管理页面的URL
   *
   * @returns 返回管理页面的URL字符串，若环境变量不存在则返回undefined
   */
  public getManageUrl(): string | undefined {
    return this.envMapManageUrl.get(this.getEnvironment())
  }
  /**
   * 初始化环境映射的销售URL
   *
   * 将环境变量中的销售URL配置存储到Map对象中
   *
   * @returns 无返回值
   */
  private initializeEnvMapSaleUrl(): void {
    this.envMapSaleUrl.set('dev', import.meta.env.VITE_SALE_DEV)
    this.envMapSaleUrl.set('sit', import.meta.env.VITE_SALE_SIT)
    this.envMapSaleUrl.set('uat', import.meta.env.VITE_SALE_UAT)
    this.envMapSaleUrl.set('pro', import.meta.env.VITE_SALE_PRO)
  }
  /**
   * 获取环境映射的销售URL映射表
   *
   * @returns 返回一个包含环境名称和对应销售URL的键值对映射表
   */
  public getEnvMapSaleUrl(): Map<string, string> {
    return this.envMapSaleUrl
  }

  /**
   * 获取销售页面的URL地址
   *
   * @returns 返回销售页面的URL地址，如果未设置则返回undefined
   */
  public getSaleUrl(): string | undefined {
    return this.envMapSaleUrl.get(this.getEnvironment())
  }

  /**
   * 获取当前环境名称
   *
   * @returns 返回当前环境名称（字符串）或''（未找到匹配的环境）
   */
  public getEnvironment(): string {
    // 开发环境，则使用开发环境的模式
    if (import.meta.env.DEV) {
      return import.meta.env.MODE
    }

    for (const [env, envUrl] of this.envMapSaleUrl.entries()) {
      if (envUrl === window.location.origin) {
        return env
      }
    }
    return ''
  }
}

export default new EnvironmentManager()
