import type { App } from 'vue'
import { setupPermissionDirective } from './permission/hasPermi'
import horizontalScroll from 'el-table-horizontal-scroll' // 让el-table支持横向滚动条一直在底部显示
import setTrackDirectives from './track/index'

/**
 * 导出指令：v-xxx
 * @methods hasPermi 按钮权限，用法: v-hasPermi
 */
export const setupPermission = (app: App<Element>) => {
  setTrackDirectives(app)
  setupPermissionDirective(app)
  app.use(horizontalScroll)
}
