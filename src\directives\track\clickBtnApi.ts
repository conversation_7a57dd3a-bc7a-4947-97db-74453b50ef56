/**
 * 请求点击事件,发送请求埋点
 */

import request from '@/config/axios'
const Prefix = '/data-collection'
import router from '@/router'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { now } from 'lodash-es'
import { formatTime } from '@/utils'
const permission = usePermissionStoreWithOut()
export const clickBtnApi = (el, binding, e): Promise<IResponse> => {
  const router_name = router.currentRoute.value.name
  const data = {
    terminal: 'sale', //终端终端 (customer:客户端 management:管理端 sale:销售端)
    click_date: formatTime(new Date(), 'yyyy-MM-dd'),
    menuName1: '', //一级菜单名称
    menuName2: '', //二级菜单名称
    menuName3: '', //三级菜单名称
    menuCode1: '', //一级菜单path
    menuCode2: '', //二级菜单path
    menuCode3: '' //三级菜单path
  }
  permission.routers.forEach((item1) => {
    //一级菜单为页面的情况
    if (router_name === 'Index') {
      data.menuName1 = '首页'
      data.menuName2 = '首页'
      data.menuName3 = '首页'
      data.menuCode1 = router_name
      data.menuCode2 = router_name
      data.menuCode3 = router_name
    } else {
      if (item1.name === router_name) {
        data.menuName1 = item1.meta.title
        data.menuName2 = item1.meta.title
        data.menuName3 = item1.meta.title
        data.menuCode1 = item1.path
        data.menuCode2 = item1.path
        data.menuCode3 = item1.path
      } else {
        if (item1.children !== undefined) {
          item1.children.forEach((item2) => {
            //二级菜单为页面的情况
            if (item2.name === router_name) {
              data.menuName1 = item1.meta.title
              data.menuName2 = item2.meta.title
              data.menuName3 = item2.meta.title
              data.menuCode1 = item1.path
              data.menuCode2 = item2.path
              data.menuCode3 = item2.path
            } else {
              if (item2.children !== undefined) {
                item2.children.forEach((item3) => {
                  //三级菜单为页面的情况
                  if (item3.name === router_name) {
                    data.menuName1 = item1.meta.title
                    data.menuName2 = item2.meta.title
                    data.menuName3 = item3.meta.title
                    data.menuCode1 = item1.path
                    data.menuCode2 = item2.path
                    data.menuCode3 = item3.path
                  } else {
                    if (item3.children !== undefined) {
                      item3.children.forEach((item4) => {
                        //4级菜单为页面的情况,但是埋点数统计还是算在三级菜单里
                        if (item4.name === router_name) {
                          data.menuName1 = item1.meta.title
                          data.menuName2 = item2.meta.title
                          data.menuName3 = item3.meta.title
                          data.menuCode1 = item1.path
                          data.menuCode2 = item2.path
                          data.menuCode3 = item3.path
                        }
                      })
                    }
                  }
                })
              }
            }
          })
        }
      }
    }
  })
  return request.post({
    url: Prefix + '/menu/click/qty/add',
    data
  })
}
