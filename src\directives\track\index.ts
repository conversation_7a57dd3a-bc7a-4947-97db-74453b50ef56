/*
 * @Author: 陈浩杰 <EMAIL>
 * @Date: 2024-11-25 15:04:16
 * @LastEditors: 陈浩杰 <EMAIL>
 * @LastEditTime: 2024-11-25 15:07:10
 * @FilePath: \umv-front-sale\src\directives\track\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/** 埋点指令封装
 * 使用  v-track:click.menu
 * 使用  v-track:click.btn
 */

// import { clickMenuApi } from './clickMenuApi'
// import { clickBtnApi } from './clickBtnApi'
const setTrackDirectives = (app) => {
  app.directive('track', {
    mounted(el, binding, vnode, prevVnode) {
      // 点击事件处理逻辑
      el.handler = function (e) {
        if (binding.modifiers.menu) {
          // clickMenuApi(el, binding, e) //埋点功能屏蔽掉接口请求
        } else if (binding.modifiers.btn) {
          // clickBtnApi(el, binding, e) //埋点功能屏蔽掉接口请求
        }
      }

      if (binding.arg == 'browse') {
        // 浏览类型发送请求记录数据
        console.log('发送请求-记录')
      } else if (binding.arg == 'click') {
        // 点击类型，监听点击事件请求记录数据
        el.addEventListener('click', el.handler, false)
      }
    },
    // 元素卸载前移除监听事件
    beforeUnmount(el) {
      el.removeEventListener('click', el.handler)
    }
  })
}

export default setTrackDirectives
