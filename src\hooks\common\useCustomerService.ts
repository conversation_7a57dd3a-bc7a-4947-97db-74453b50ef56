import { getCoinTypeConfig } from '@/api/order/index'

export function useCustomerService() {
  /** 价格币种类型
   *  @type {*} */
  const coinType = ref<string>('CNY')

  /**
   * @description 根据租户ID获取配置的币种类型
   * @param {string} tenantId
   */
  async function loadCoinType(tenantId: string): Promise<string> {
    if (!tenantId) {
      coinType.value = 'CNY'
      return 'CNY'
    }
    try {
      const { data } = await getCoinTypeConfig(tenantId)
      coinType.value = data
      return data
    } catch (ex) {
      console.error('获取租户结算币种错误', ex)
      coinType.value = 'CNY'
      return 'CNY'
    }
  }

  return {
    loadCoinType,
    coinType
  }
}
