import { onMounted, onUnmounted } from 'vue'

/**
 * 使用 interval 定时请求函数
 *
 * @param callback 定时执行的回调函数
 * @param interval 定时时间间隔，单位为毫秒，默认为 10*60 秒
 * @param options 配置选项
 * @param options.immediate 是否立即执行，默认为 true
 * @returns 包含 clear 方法的对象，用于手动清除定时器
 */
export function useIntervalRequest(
  callback: () => void,
  interval: number = 10 * 60 * 1000,
  options: { immediate?: boolean } = { immediate: true }
) {
  let timer: NodeJS.Timeout

  onMounted(() => {
    if (options.immediate) {
      callback() // 首次执行
    }
    timer = setInterval(callback, interval)
  })

  onUnmounted(() => {
    clearInterval(timer)
  })

  return {
    clear: () => clearInterval(timer)
  }
}
