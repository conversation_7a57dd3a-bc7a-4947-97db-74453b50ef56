/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-12-04 14:56:25
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-04 15:14:16
 * @Description:用于treeSelect下拉框  通过部门筛选用户
 */
import { ref } from 'vue'
import { handleTree } from '@/utils/tree'
import * as TaskApi from '@/api/bpm/task'

export const useUserSelect = () => {
  const userList = ref<any[]>([]) // 用户列表
  //树形选择控件
  const customLocationProps = {
    label: 'name',
    value: 'id',
    children: 'children',
    isLeaf: 'isLeaf'
  }
  const loadNode = async (node, resolve) => {
    console.log(node)
    console.log(resolve)

    if (node.level === 0) {
      // 查询部门列表
      const res = await TaskApi.getListAllSimpleVir()
      console.log(handleTree(res))
      res.forEach((item) => {
        item['disabled'] = true
      })
      return resolve(handleTree(res))
    }
    if (node.level > 0) {
      //加载子展开列表
      let nodeList = []
      if (node.data.children?.length) {
        nodeList = node.data.children
      }
      //加载子元素
      let childrenList = []
      if (node.data.id) {
        // 查询部门列表下人员
        const res: any = await TaskApi.getuserListApi(node.data.id)

        if (res && res?.length) {
          res.forEach((item) => {
            item['name'] = item.nickname
            item['isLeaf'] = true
            item['children'] = []
            item['parentId'] = node.data.id
          })

          userList.value.push(...res)
        } else {
          console.log(node)
          console.log(node.disabled)
          // node.disabled = true
        }
        childrenList = res ?? []
        nodeList.push(...childrenList)
      }
      console.log(nodeList)
      return resolve(nodeList)
    }
  }
  return {
    userList,
    customLocationProps,
    loadNode
  }
}
