/*
 * @Author: HoJ<PERSON>
 * @Date: 2023-06-13 16:01:28
 * @LastEditors: HoJack
 * @LastEditTime: 2023-10-25 19:26:55
 * @Description:
 */
/**
 * 配置浏览器本地存储的方式，可直接存储对象数组。
 */

import WebStorageCache from 'web-storage-cache'

type CacheType = 'sessionStorage' | 'localStorage'

export const CACHE_KEY = {
  IS_DARK: 'isDark',
  USER: 'user',
  LANG: 'lang',
  THEME: 'theme',
  LAYOUT: 'layout',
  ROLE_ROUTERS: 'roleRouters',
  ROLE_PERMISSIONS: 'rolePermissons',
  CURRENT_CLIENT_ID: 'currentClientId',
  DICT_CACHE: 'dictCache',
  CUSTOMER_INFO: 'customerInfo',
  CUSTOMER_DICT: 'customerDict',
  ADDRESS_TREE: 'addressTree',
  ACCOUNT_INFO: 'accountInfo' //账号信息
}

export const useCache = (type: CacheType = 'localStorage') => {
  const wsCache: WebStorageCache = new WebStorageCache({
    storage: type
  })

  return {
    wsCache
  }
}
