import { useTagsViewStoreWithOut } from '@/store/modules/tagsView'
import { RouteLocationNormalizedLoaded, useRouter } from 'vue-router'
import { computed, nextTick, unref } from 'vue'

export const useTagsView = () => {
  const tagsViewStore = useTagsViewStoreWithOut()

  const { replace, currentRoute } = useRouter()

  const selectedTag = computed(() => tagsViewStore.getSelectedTag)
  /**
   * 关闭所有标签页，并执行回调函数（如果有的话）
   *
   * @param callback 可选的回调函数，当所有标签页关闭后执行
   */
  const closeAll = (callback?: Fn) => {
    tagsViewStore.delAllViews()
    callback?.()
  }
  /**
   * 关闭左侧标签页
   *
   * @param callback 关闭标签页后的回调函数，可选
   */
  const closeLeft = (callback?: Fn) => {
    tagsViewStore.delLeftViews(unref(selectedTag) as RouteLocationNormalizedLoaded)
    callback?.()
  }
  /**
   * 关闭右侧视图，并可选地执行回调函数
   *
   * @param callback 可选的回调函数，在关闭右侧视图后执行
   */
  const closeRight = (callback?: Fn) => {
    tagsViewStore.delRightViews(unref(selectedTag) as RouteLocationNormalizedLoaded)
    callback?.()
  }
  /**
   * 关闭其他标签页
   *
   * @param callback 回调函数，关闭其他标签页后的操作，可选
   */
  const closeOther = (callback?: Fn) => {
    tagsViewStore.delOthersViews(unref(selectedTag) as RouteLocationNormalizedLoaded)
    callback?.()
  }
  /**
   * 关闭当前tag页
   *
   * @param view 当前视图对象，可选参数(不传时为当前路由位置对象,可以指定关闭的tab页)
   * @param callback 回调函数，可选参数
   */
  const closeCurrent = (view?: RouteLocationNormalizedLoaded, callback?: Fn) => {
    if (view?.meta?.affix) return
    tagsViewStore.delView(view || unref(currentRoute))

    callback?.()
  }
  /**
   * 刷新页面
   *
   * @param view 路由位置对象，可选参数
   * @param callback 回调函数，可选参数
   */
  const refreshPage = async (view?: RouteLocationNormalizedLoaded, callback?: Fn) => {
    tagsViewStore.delCachedView()
    const { path, query } = view || unref(currentRoute)
    await nextTick()
    replace({
      path: '/redirect' + path,
      query: query
    })
    callback?.()
  }
  /**
   * 设置页面标题
   *
   * @param title 页面标题
   * @param routerName 可选参数，路由名称(默认当前tab页)
   */
  const setTitle = (title: string, routerName?: string) => {
    tagsViewStore.setTitle(title, routerName)
  }

  return {
    closeAll,
    closeLeft,
    closeRight,
    closeOther,
    closeCurrent,
    refreshPage,
    setTitle
  }
}
