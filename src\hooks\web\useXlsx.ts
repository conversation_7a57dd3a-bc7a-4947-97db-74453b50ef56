import * as XLSX from 'xlsx'

/**
 *
 * @param file 需要解析的Excel文件
 * @returns json对象
 */
export const useXlsx = () => {
  // 文件转二进制
  const readFile = (file: File) => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file)
      reader.onload = (ev) => {
        resolve(ev.target?.result)
      }
    })
  }

  const analyzeExcel = (file: File) => {
    return new Promise(async (resolve) => {
      const data = await readFile(file)
      const workbook = XLSX.read(data, { type: 'binary', cellDates: true })
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const result = XLSX.utils.sheet_to_json(worksheet)

      resolve(result)
    })
  }
  const getExcelHeader = (file: File) => {
    return new Promise<string[]>(async (resolve, reject) => {
      try {
        const data = await readFile(file)
        const workbook = XLSX.read(data, { type: 'binary', cellDates: true })
        const worksheet = workbook.Sheets[workbook.SheetNames[0]]
        // @ts-ignore
        const range = XLSX.utils.decode_range(worksheet['!ref'])
        const header: string[] = []

        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: C })
          const cell = worksheet[cellAddress]
          // @ts-ignore
          header.push(cell.v)
        }
        resolve(header)
      } catch (e) {
        reject(e)
      }
    })
  }
  const exportExcel = (title: any[], data: any[][], fileName: string) => {
    return new Promise(async (resolve) => {
      const dataList = [title, ...data]
      const worksheet = XLSX.utils.aoa_to_sheet(dataList)
      // 将 worksheet 对象添加到 workbook 中
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      // 导出 Excel 文件
      XLSX.writeFile(workbook, `${fileName}.xlsx`)
      resolve(true)
    })
  }
  return {
    readFile,
    analyzeExcel,
    exportExcel,
    getExcelHeader
  }
}
