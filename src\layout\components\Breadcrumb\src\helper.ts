import { pathResolve, breadcrumbPathResolve } from '@/utils/routerHelper'

export const filterBreadcrumb = (
  routes: AppRouteRecordRaw[],
  parentPath = ''
): AppRouteRecordRaw[] => {
  const res: AppRouteRecordRaw[] = []

  /** 设置了hidden为false的情况下没有设置canTo为true的都不会显示在面包屑中 */
  for (const route of routes) {
    const meta = route?.meta
    meta.canTo = true //暂将所有canTo设为true
    if (meta.hidden && !meta.canTo) {
      continue
    }

    /** 只有首层Layout才需要直接忽略面包屑，以parentId为0作为判断依据。
     *  前端固定路由以是否有title作为判断依据
     */
    const data: AppRouteRecordRaw =
      !meta.alwaysShow &&
      route.children?.length === 1 &&
      (route.parentId === 0 || !route.meta.title) //如果去除这个条件，那么非首层只有一个子节点的也会被排除在外
        ? { ...route.children[0], path: pathResolve(route.path, route.children[0].path) }
        : { ...route }

    data.path = breadcrumbPathResolve(parentPath, data.path)

    if (data.children) {
      data.children = filterBreadcrumb(data.children, data.path)
    }
    if (data) {
      res.push(data)
    }
  }
  return res
}
