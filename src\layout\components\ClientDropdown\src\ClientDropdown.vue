<template>
  <ElDropdown :class="prefixCls" trigger="click" @command="setCurrentClient">
    <div class="h-full flex items-center text-[var(--top-header-text-color)]">
      <el-icon><Monitor /></el-icon>
      <span class="<md:hidden text-14px pl-[5px]">
        {{ clientName ? clientName : '当前无交互端' }}
      </span>
      <el-icon class="ml-2"><ArrowDown /></el-icon>
    </div>
    <template #dropdown>
      <ElDropdownMenu>
        <ElDropdownItem v-for="item in clientList" :key="item.id" :command="item">
          <el-icon class="mr-2"><Monitor /></el-icon>
          <span :class="[userStore.getCurrentClientId === item.id && 'active']">{{
            item.name
          }}</span>
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ClientDropdown'
})

import { useDesign } from '@/hooks/web/useDesign'
import { useUserStore } from '@/store/modules/user'
import { ArrowDown, Platform, Monitor } from '@element-plus/icons-vue'
import { useLocaleStore } from '@/store/modules/locale'
const localeStore = useLocaleStore()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('size-dropdown')

const userStore = useUserStore()
const clientList = computed(() => userStore.getClients || []) //过期角色不可见

const clientName = computed(
  () => userStore.clients.find((item) => item.id === userStore.getCurrentClientId)?.name
)

import { useSsoStore } from '@/store/modules/sso'
const ssoStore = useSsoStore()

/**
 * 设置当前客户端,跳转至端重新授权,
 *
 * @param val 当前客户端对象
 * @returns 无返回值
 */
const setCurrentClient = async (val) => {
  const clientId = val.id
  const redirectUri = val.url as string
  const oauthClient = val.oauthClient
  if (clientId === userStore.getCurrentClientId) return

  ssoStore.ssoAuth(oauthClient, redirectUri)
}
</script>
<style scoped lang="less">
.active {
  color: var(--el-color-primary);
}
.stale {
  color: var(--el-border-color-hover);
}
</style>
