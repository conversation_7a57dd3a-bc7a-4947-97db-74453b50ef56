<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 08:59:37
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-11-15 15:24:18
 * @Description: 
-->
<script setup lang="ts">
import { computed, unref, defineComponent } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'

const { t } = useI18n()
const message = useMessage()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('collapse')

defineProps({
  color: propTypes.string.def('')
})

const appStore = useAppStore()

const collapse = computed(() => appStore.getCollapse)

const toggleCollapse = () => {
  const collapsed = unref(collapse)
  appStore.setCollapse(!collapsed)
}

import { useWindowSize } from '@vueuse/core'
const { width } = useWindowSize()
/**
 * 监听屏幕宽度以决定是否折叠菜单
 *
 * 当屏幕宽度小于1366px时，如果当前未折叠菜单，则自动折叠菜单，并提示用户
 */
const watchScreenWidthCollapse = () => {
  if (width.value < 1366 && !collapse.value) {
    appStore.setCollapse(true)
    message.notifySuccess(t('sys.layout.collapse.tips'))
  }
}
defineComponent
watch(
  () => width.value,
  (val) => {
    watchScreenWidthCollapse()
  },
  {
    immediate: true
  }
)
</script>

<template>
  <div :class="prefixCls" @click="toggleCollapse">
    <el-tooltip
      :content="collapse ? t('sys.layout.collapse.expand') : t('sys.layout.collapse.collapse')"
    >
      <Icon
        :size="20"
        :icon="collapse ? 'ant-design:menu-unfold-outlined' : 'ant-design:menu-fold-outlined'"
        :color="color"
        class="cursor-pointer !w-[95%] !h-[95%] flex items-center"
      />
    </el-tooltip>
  </div>
</template>
