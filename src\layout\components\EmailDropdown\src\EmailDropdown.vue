<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-05-31 15:37:26
 * @LastEditors: <PERSON>J<PERSON>
 * @LastEditTime: 2023-12-22 11:28:58
 * @Description: 
-->
<script setup lang="ts">
defineOptions({
  name: 'EmailDropdown'
})

import { ArrowDown, Avatar } from '@element-plus/icons-vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { useUserStoreWithOut } from '@/store/modules/user'
import router from '@/router'

const appStore = useAppStore()
const userStore = useUserStoreWithOut()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('email-dropdown')

const { t } = useI18n()
const showSettingTool = () => {
  appStore.setShowSettingTool(true)
}
const changePwd = () => {
  router.push('/changePassword/index')
}
</script>

<template>
  <ElDropdown :class="prefixCls" trigger="click">
    <div class="h-full flex items-center text-[var(--top-header-text-color)] h-full">
      <el-icon><Avatar /></el-icon>
      <span class="<lg:hidden text-14px pl-1">{{
        userStore.getAccountInfo?.email
          ? userStore.getAccountInfo?.email
          : t('sys.layout.emailDropdown.unbindEmail')
      }}</span>
      <el-icon class="el-icon--right"><arrow-down /></el-icon>
    </div>
    <template #dropdown>
      <ElDropdownMenu>
        <!-- <ElDropdownItem>
          <Icon icon="ep:tools" />
          <div @click="showSettingTool">{{ t('common.setting') }}</div>
        </ElDropdownItem> -->
        <ElDropdownItem @click="router.push('/accountControl')">
          <Icon icon="ep:user" />
          <div>{{ t('sys.layout.emailDropdown.personalCenter') }}</div>
        </ElDropdownItem>
        <ElDropdownItem @click="changePwd">
          <Icon icon="ep:lock" />
          <div>{{ t('sys.layout.emailDropdown.changePassword') }}</div>
        </ElDropdownItem>
        <ElDropdownItem @click="userStore.loginOut()" divided>
          <Icon icon="ep:switch-button" />
          <div>{{ t('common.loginOut') }}</div>
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>
