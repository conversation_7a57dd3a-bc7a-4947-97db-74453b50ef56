<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 08:59:40
 * @LastEditors: HoJack
 * @LastEditTime: 2023-11-15 15:02:19
 * @Description: 
-->
<script setup lang="ts">
import { useAppStore } from '@/store/modules/app'
import { computed } from 'vue'
import { useDesign } from '@/hooks/web/useDesign'
const { t } = useI18n()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('footer')

const appStore = useAppStore()

const title = computed(() => appStore.getTitle)
</script>

<template>
  <div
    :class="prefixCls"
    class="relative text-[12px] flex justify-center items-center text-center text-[var(--el-text-color-placeholder)] bg-[var(--app-content-bg-color)] h-[var(--app-footer-height)] leading-[var(--app-footer-height)] dark:bg-[var(--el-bg-color)]"
  >
    <p class="mr-2">
      {{ t('sys.footer.Copyright') }}
    </p>
    <a
      class="mr-2 hover:text-gold hover:border-b-[1px] hover:border-b-gold"
      href="https://beian.miit.gov.cn/"
      target="_blank"
      rel="noopener noreferrer"
    >
      {{ t('sys.footer.icp') }}
    </a>
    <!-- <a
        class="hover:text-gold hover:border-b-[1px] hover:border-b-gold"
        href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=**************"
        target="_blank"
        rel="noopener noreferrer"
      >
        粤公网安备 ************** 号
      </a> -->

    <!-- {{ title }} -->
    <img src="/umvCard.svg" alt="umvCard" class="inline w-20 absolute right-2" />
  </div>
</template>
