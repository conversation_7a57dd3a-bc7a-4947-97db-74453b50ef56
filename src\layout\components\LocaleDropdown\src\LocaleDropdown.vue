<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 08:59:43
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-12-25 10:51:13
 * @Description: 
-->
<script setup lang="ts">
import { computed, unref } from 'vue'
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { useLocaleStore } from '@/store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { usePermissionStore } from '@/store/modules/permission'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('locale-dropdown')

defineProps({
  color: propTypes.string.def('')
})

const localeStore = useLocaleStore()

const langMap = computed(() => localeStore.getLocaleMap)

const currentLang = computed(() => localeStore.getCurrentLocale)

const permissionStore = usePermissionStore()

import { useDictStoreWithOut } from '@/store/modules/dict'
const dictStore = useDictStoreWithOut()

const setLang = async (lang: LocaleType) => {
  if (lang === unref(currentLang).lang) return

  localeStore.setCurrentLocale({
    lang
  })
  const { changeLocale } = useLocale()
  changeLocale(lang)
  const loadingInstance = ElLoading.service({
    lock: true,
    background: 'rgba(0, 0, 0, 0.7)'
  })
  await permissionStore.generateRoutes(true)
  await userStore.regetUserInfo()
  await dictStore.resetDict()
  // 需要重新加载页面让整个语言多初始化
  window.location.reload()
  loadingInstance.close()
}
</script>

<template>
  <ElDropdown :class="prefixCls" trigger="click" @command="setLang">
    <Icon
      :size="18"
      :icon="currentLang.lang === 'zh-CN' ? 'ion:language-sharp' : 'uil:english-to-chinese'"
      class="cursor-pointer"
      :class="$attrs.class"
      :color="color"
    />
    <template #dropdown>
      <ElDropdownMenu>
        <ElDropdownItem v-for="item in langMap" :key="item.lang" :command="item.lang">
          {{ item.name }}
        </ElDropdownItem>
      </ElDropdownMenu>
    </template>
  </ElDropdown>
</template>
