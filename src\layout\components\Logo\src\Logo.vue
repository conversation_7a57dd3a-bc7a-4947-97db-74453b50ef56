<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 08:59:44
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-06-13 11:16:12
 * @Description: 
-->
<script setup lang="ts">
import { ref, watch, computed, onMounted, unref } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { useLocaleStore } from '@/store/modules/locale'
const localeStore = useLocaleStore()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()

const show = ref(true)

const client = computed(() => appStore.getClient)

const layout = computed(() => appStore.getLayout)

const collapse = computed(() => appStore.getCollapse)

import { getCompleteToken, getTenantId } from '@/utils/auth'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
const clientMap = computed(() =>
  userStore.getClients.reduce((a, b) => {
    return { ...a, [b.id]: b }
  }, {})
)
// 点击切换端跳转至其他端
const checkoutClient = (val) => {
  if (val.id === userStore.getCurrentClientId) return
  let url = val.url
  if (url) {
    const refreshTokenResData = getCompleteToken()
    refreshTokenResData.tenantId = getTenantId()
    refreshTokenResData.clientId = val.id
    let str = ''
    for (let i in refreshTokenResData) {
      str += `&${i}=${refreshTokenResData[i]}`
    }

    url.endsWith('/') && (url = str.slice(0, -1))

    console.log('localeStore.getCurrentLocale.lang', localeStore.getCurrentLocale.lang)

    const jumpUrl =
      url +
      (url.indexOf('/#') === -1 ? '/#/' : '') +
      '?' +
      `${localeStore.getCurrentLocale ? `lang=${localeStore.getCurrentLocale.lang}&` : ''}` +
      str.substring(1)

    window.location.href = jumpUrl
  }
}

onMounted(() => {
  if (unref(collapse)) show.value = false
})

watch(
  () => collapse.value,
  (collapse: boolean) => {
    if (unref(layout) === 'topLeft' || unref(layout) === 'cutMenu') {
      show.value = true
      return
    }
    if (!collapse) {
      setTimeout(() => {
        show.value = !collapse
      }, 400)
    } else {
      show.value = !collapse
    }
  }
)

watch(
  () => layout.value,
  (layout) => {
    if (layout === 'top' || layout === 'cutMenu') {
      show.value = true
    } else {
      if (unref(collapse)) {
        show.value = false
      } else {
        show.value = true
      }
    }
  }
)
</script>

<template>
  <div>
    <!-- <router-link
      :class="[
        prefixCls,
        layout !== 'classic' ? `${prefixCls}__Top` : '',
        'flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative',
        'dark:bg-[var(--el-bg-color)]'
      ]"
      to="/"
    > -->
    <ElDropdown trigger="hover">
      <img
        src="@/assets/imgs/umvCard.svg"
        class="w-[calc(var(--logo-height)+40px)] h-[calc(var(--logo-height)-25px)]"
      />
      <div
        v-if="show"
        :class="[
          'ml-10px text-16px font-700',
          {
            'text-[var(--logo-title-text-color)]': layout === 'classic',
            'text-[var(--top-header-text-color)]':
              layout === 'topLeft' || layout === 'top' || layout === 'cutMenu'
          }
        ]"
      >
        {{ client }}
      </div>
      <template #dropdown>
        <ElDropdownMenu>
          <ElDropdownItem
            v-for="(value, key) in clientMap"
            :key="key"
            @click="checkoutClient(value)"
          >
            <span :class="[userStore.getCurrentClientId === value.id && 'blue']">{{
              value.name
            }}</span>
          </ElDropdownItem>
        </ElDropdownMenu>
      </template>
    </ElDropdown>

    <!-- </router-link> -->
  </div>
</template>
<style lang="scss" scoped>
.blue {
  color: var(--el-color-primary);
}
</style>
