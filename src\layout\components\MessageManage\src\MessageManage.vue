<template>
  <div @click="toSeeMessage">
    <el-badge
      :hidden="messageCount == 0"
      :value="messageCount > 99 ? '...' : messageCount"
      class="badge-item"
    >
      <Icon :size="18" :icon="'tabler:message'" />
    </el-badge>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@/components/Icon'
import { useMessageStore } from '@/store/modules/message'
const messageStore = useMessageStore()
const messageCount = computed(() => messageStore.messageCount)
messageStore.getUnreadMessage()

const router = useRouter()
const toSeeMessage = () => {
  router.push({
    name: 'MessageCenter'
  })
}
</script>

<style lang="less" scoped>
.badge-item {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
