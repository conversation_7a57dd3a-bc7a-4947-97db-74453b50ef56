<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 08:59:52
 * @LastEditors: <PERSON>J<PERSON>
 * @LastEditTime: 2023-08-18 17:19:05
 * @Description: 
-->
<script lang="tsx">
import { defineComponent, computed } from 'vue'
import { Collapse } from '@/layout/components/Collapse'
import { LocaleDropdown } from '@/layout/components/LocaleDropdown'
import { SizeDropdown } from '@/layout/components/SizeDropdown'
import { Screenfull } from '@/layout/components/Screenfull'
import { Breadcrumb } from '@/layout/components/Breadcrumb'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { RoleDropdown } from '@/layout/components/RoleDropdown'
import { ClientDropdown } from '@/layout/components/ClientDropdown'
import { EmailDropdown } from '@/layout/components/EmailDropdown'
import { MessageManage } from '@/layout/components/MessageManage'
//import { ShoppingCart } from '@/layout/components/ShoppingCart'

const { getPrefixCls, variables } = useDesign()

const prefixCls = getPrefixCls('tool-header')

const appStore = useAppStore()

// 面包屑
const breadcrumb = computed(() => appStore.getBreadcrumb)

// 折叠图标
const hamburger = computed(() => appStore.getHamburger)

// 全屏图标
const screenfull = computed(() => appStore.getScreenfull)

// 尺寸图标
const size = computed(() => appStore.getSize)

// 布局
const layout = computed(() => appStore.getLayout)

// 多语言图标
const locale = computed(() => appStore.getLocale)

export default defineComponent({
  name: 'ToolHeader',
  setup() {
    return () => (
      <div
        id={`${variables.namespace}-tool-header`}
        class={[
          prefixCls,
          'h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between',
          'dark:bg-[var(--el-bg-color)]'
        ]}
      >
        {layout.value !== 'top' ? (
          <div class="h-full flex items-center">
            {hamburger.value && layout.value !== 'cutMenu' ? (
              <Collapse
                class="hover-trigger rounded-tl-lg"
                color="var(--top-header-text-color)"
              ></Collapse>
            ) : undefined}
            {breadcrumb.value ? <Breadcrumb class="<md:hidden"></Breadcrumb> : undefined}
          </div>
        ) : undefined}
        <div class="h-full flex items-center">
          {screenfull.value ? (
            <Screenfull class="hover-trigger" color="var(--top-header-text-color)"></Screenfull>
          ) : undefined}
          {size.value ? (
            <SizeDropdown class="hover-trigger" color="var(--top-header-text-color)"></SizeDropdown>
          ) : undefined}
          {locale.value ? (
            <LocaleDropdown
              class="hover-trigger"
              color="var(--top-header-text-color)"
            ></LocaleDropdown>
          ) : undefined}
          {/* <MessageManage class="hover-trigger"></MessageManage> */}
          {/* <ShoppingCart class="hover-trigger" />/ */}
          {/* <RoleDropdown class="hover-trigger" color="var(--top-header-text-color)"></RoleDropdown> */}
          <ClientDropdown
            class="hover-trigger"
            color="var(--top-header-text-color)"
          ></ClientDropdown>
          <EmailDropdown
            class="hover-trigger rounded-tr-lg"
            color="var(--top-header-text-color)"
          ></EmailDropdown>
        </div>
      </div>
    )
  }
})
</script>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-tool-header';

.@{prefix-cls} {
  transition: left var(--transition-time-02);
}
</style>
