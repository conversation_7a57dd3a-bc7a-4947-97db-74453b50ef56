<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 08:59:48
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-12-26 15:10:15
 * @Description: 
-->
<script setup lang="ts">
defineOptions({
  name: 'UserInfo'
})

import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { useUserStoreWithOut } from '@/store/modules/user'
import { useRouter } from 'vue-router'
const { t } = useI18n()
const appStore = useAppStore()
const userStore = useUserStoreWithOut()

const userInfo = computed(() => userStore.getAccountInfo)

const router = useRouter()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('user-info')

//折叠菜单
const collapse = computed(() => appStore.getCollapse)

//处理图片加载失败
const handleError = () => {
  userInfo.value.avatar = new URL('@/assets/imgs/avatar.png', import.meta.url).href
}
</script>

<template>
  <div
    :class="prefixCls"
    class="flex items-center cursor-pointer"
    @click="router.push('/accountControl')"
  >
    <img
      v-if="userInfo.avatar"
      :src="userInfo.avatar"
      :onerror="handleError"
      alt=""
      class="w-[var(--logo-height)] rounded-[50%]"
    />
    <img
      v-else
      src="@/assets/imgs/avatar.png"
      alt=""
      class="w-[var(--logo-height)] rounded-[50%]"
    />
    <div class="flex flex-col pl-[10px]" v-if="!collapse">
      <span class="<lg:hidden text-14px text-[var(--top-header-text-color)]">
        {{ userInfo.nickname ? userInfo.nickname : t('sys.layout.userInfo.noUserName') }}
      </span>
      <span class="text-12px text-zinc-500 mt-2">
        {{ userInfo.deptName ?? t('sys.layout.userInfo.noDeptData') }}
      </span>
    </div>
  </div>
</template>
