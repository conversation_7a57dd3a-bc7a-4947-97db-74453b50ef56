export default {
  sort: 'Sort',
  beginCreate: 'Start generating',
  createResult: 'Generation result',
  pleaseLate: 'Please try again later',
  sortPlaceholder: 'Please select a category',

  zoon: 'Animals',
  scenery: 'Landscape',
  figure: 'Figures',
  cat: 'Cat',
  dog: 'Dog',
  outdoors: 'Outdoors',
  indoor: 'Indoors',
  happyZoon: 'Happy running animals',
  exploreZoon: 'Adventurous animals',
  traditionZoon: 'Traditional national style animals',
  winterZoon: 'Animals in winter',
  nightZoon: 'Animals under the starry night sky',
  bookZoon: 'Animals reading books',
  backHomeZoon: 'Welcome home animals',
  lazyZoon: 'Stretching animals',
  runZoon: 'Animals doing exercise',
  ink: 'Ink painting style',
  anime: 'Cartoon style',
  illustration: 'Flat illustration style',
  morningLight: 'Morning light filters through the treetops',
  reflectionsSunset: 'Afterglow of sunset',
  mountainsDistant: 'Mountains in the distance',
  nightStarry: 'The starry sky',
  forestAutumn: 'Maple leaves in autumn',
  wheatGolden: 'Golden wheat field',
  glisteningDewdrops: 'The morning dew',
  bambooEmerald: 'Verdant bamboo forest',
  wavesCalm: 'Tranquil waves',
  spanningRainbow: 'Rainbow after the rain',
  purpleVibrant: 'Purple lavender',
  riceAutumn: 'Autumn rice fields',
  valleyMisty: 'Morning valley',
  landscapeDesert: 'The desert at sunset',
  meadowVibrant: 'The grassland in spring',
  woman: 'Woman',
  man: 'Man',
  pixarStyle: 'Pixar Animation style',
  animeStyle: 'Cartoon style',
  leather: 'Motorcycle',
  princess: 'Princess',
  warrior: 'Swordsman',
  sports: 'Sports',
  chineseClothes: 'Chinese costumes',
  glasses: 'High-neck sweater',
  traditional: 'Tang suit',
  noLimit: 'Unlimited',

  ipName: 'IP Name',
  ipNamePlaceholder: 'Please enter IP Name',
  chooseDesign: 'Select Proposal',
  chooseDesignPlaceholder: 'Please select proposal',
  view3D: 'View 3D Card Face',
  ipInfo: 'IP introduction',
  noDesign: 'Do NOT Select Proposal',

  cardProductIP: 'Card Product IP',
  designIP: 'Designer Perferred'
}
