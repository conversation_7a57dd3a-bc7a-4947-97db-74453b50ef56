export default {
  orderApproval: {
    orderNodeStatus: 'Order Approval Node Status',
    customerName: 'Customer Name',
    productName: 'Product Name',
    nodeName: 'Node Name',
    handler: 'Operator',
    handlerTime: 'Actions Time',
    placeholderTextTip: 'Please enter {placeholder}',
    placeholderSelectTip: 'Please select {placeholder}',
    taskCode: 'Task Code',
    salesman: 'Sales Representative',
    orderType: 'Order Type',
    auditStatus: 'Approval Status',
    unfold: 'Expand',
    packUp: 'Collapse',
    unaudited: 'Approval in progress',
    audited: 'Approved',
    unSaleReview: 'To be Signed',
    saleReview: 'Signed',
    batchOrder: 'Batch Order',
    freeSampleCard: 'Free Sample Card',
    chargeSampleCard: 'Paid Sample Card',
    finishedProductWarehousing: 'Prepare Finished Products for Inventory',
    sampleCardWarehousing: 'Store Sample Card',
    semiWarehousing: 'Prepare Semi-finished Products for Inventory',
    orderNum: 'Order Quantity',
    audit: 'Approve',
    viewDetail: 'View Details',
    deliveryTime: 'Delivery Time',
    deliveryMode: 'Delivery Method',
    orderCreator: 'Order Creator',
    orderCreationTime: 'Order Creation Time',
    processNode: 'Workflow Node',
    orderAudit: 'Order Approval',
    orderInfo: 'Order Information',
    taskStatus: 'Task Status',
    orderCode: 'Order No.',
    orderProductRemark: 'Product Remarks',
    orderRemark: 'Order is Placed',
    submitSuccess: 'Submitted Successfully',
    cardStyleInfo: 'Card Type Information',
    cardStyleName: 'Card Type Name',
    mainCardNum: 'Main Card No.',
    cardStyleCode: 'Card GSC No.',
    cardStyleCodeAll: 'Full Name of Card GSC No.',
    latestK3Code: 'K3 Ordering Code',

    batchTime: 'Card Number, Month Year',
    cardPlanNum: 'Number of Card Proposal',
    enCapType: 'Embedding Type',
    finishedCardModelModule: 'Finished Card Type Module',
    semiCardModelModule: 'Semi-finished Card Type Module',
    chipType: 'Chip Type',
    trueChip: 'Normal Chip',
    dummyChip: 'Dummy Chip',
    noChip: 'No Chip',
    chipCode: 'Chip Model',
    chipSupplier: 'Chip Supplier',
    moduleTransferInterfaceType: 'Chip Interface',
    maxCode: 'Chip Module Code',

    color: 'Color',
    capacity: 'Memory Size',
    maskCode: 'Mask Code',
    shape: 'Shap',
    oiOrModuleCode: 'OI/Module Code',
    antenna: 'Antenna',
    aerialType: 'Antenna Type',

    property: 'Property Attribute',
    stripe: 'Lead-frame',
    chipCapacitance: 'Chip Capacitance',

    finishedStock: 'Finished Product Stock',
    semifinishedStock: 'Semi-finished Product Stock',
    cardOrgUsableFlag: 'Card Scheme Recycle',
    reusable: 'Reusable',
    nonReusable: 'Non-reusable',
    primaryFlag: 'Primary Card Indicator',
    yes: 'Yes',
    no: 'No',
    finalDrafts: 'Final Artwork Image',
    cardNoteList: 'Log of Version Changes',
    cardReviewResultList: 'Approval Records',

    auditRes: 'Approval Result',
    auditRemark: 'Approval Comments',
    handleTime: 'Actions Time'
  },
  orderSearch: {
    fileList: 'Order Voucher',
    orderInfo: 'Order Information',
    customerInfo: 'Customer Information',
    productInfo: 'Product Information',
    download: 'Download',
    unitPrice: 'Unit Price',
    productName: 'Product Name',
    cardCount: 'Quantity',
    isIndividual: 'Require Personalisation Material',
    yes: 'Yes',
    no: 'No',
    empty: 'No data',
    mailAddress: 'Mailing Address',
    logisticsInfo: 'Logistics Information',
    cardDeliveryDate: 'Supplier Expected Delivery Date',
    mailNo: 'Shipment Tracking No.',
    OrderStatus: 'Order Status',
    logisticsTracking: 'Logistics Tracking',
    isUrgent: 'Urgent Order',
    deliveryMethod: 'Delivery Method',
    orderSource: 'Order Source',
    customerCreateOrder: 'Ordering Customer',
    orderTotalPrice: 'Total Order Price',
    deliveryTime: 'Expected Delivery Date',
    orderType: 'Order Type',
    customerOrderReceiveTime: 'Order Received Date',
    ordered: 'Ordered',
    selectCustomerPlaceholder: 'Please select customer',
    management: 'Management',
    customer: 'Customer',
    sale: 'Sales Representative',
    tip: 'Tip',
    customerName: 'Customer Name',
    all: 'All',
    revocationOrderConfirm: 'Confirm order revocation?',
    orderRevocationSuccess: 'Order revoked successfully',
    UMVOrderCodePlaceholder: 'Please enter UMV order number',
    productNamePlaceholder: 'Please enter product name',
    cardProductList: 'Card Product Order List',
    orderWrite: 'Order Entry',
    UMVOrderCode: 'UMV Order Number',
    more: 'More',
    productNum: 'Product Quantity',
    price: 'Price',
    source: 'Source',
    updateTime: 'Updated Time',
    createTime: 'Created Date',
    orderCode: 'Order Code',
    view: 'View',
    revocation: 'Revoke',
    remarkNote: 'Remarks',
    addressOfPeople: 'Contact Person',
    tel: 'Phone',
    creator: 'Creator',
    creatorAcc: 'Creator Account',
    storage: 'Warehousing for Storage',
    customerPick: 'Self Pickup',
    mail: 'Mail',
    customerOrderCode: 'Customer Order Code',
    salesman: 'Sales Representative',
    salesmanPlaceholder: 'Please enter sales representative',
    completionDate: 'Completion Date',
    cardOrganization: 'Organization',

    //Excel模板表头，需要与模板中一致, 如用中文模板上传则不需要翻译
    excelHeaderOfProductName: 'Product Name',
    excelHeaderOfNum: 'Order Quantity',
    excelHeaderOfType: 'Type',
    excelHeaderOfDeliveryTime: 'Delivery Time',
    excelHeaderOfDeliveryMethod: 'Delivery Method',
    excelHeaderOfPackageMode: 'Packaging Method',
    excelHeaderOfInnerBox: 'Inner Box',
    excelHeaderOfOuterBox: 'Outer Box',
    excelHeaderOfProductPrice: 'Product Unit Price',
    excelHeaderOfWrite31: 'Write 31 Materials',
    excelHeaderOfCompanyCode: 'Our Company Code',
    excelHeaderOfCustomerCode: 'Customer Code',
    excelHeaderOfRemark: 'Remarks',
    //Excel模板卡产品类型，需要与模板中一致, 如用中文模板上传则不需要翻译
    excelCardProduct: 'Card Product',
    excelNonCardProduct: 'Non-card Product',
    //Excel模板是否写入个人化，需要与模板中一致, 如用中文模板上传则不需要翻译
    excelWrite: 'Write',
    taskCode: 'Task Code',

    viewMyself: 'View my orders only',
    saleConfirm: 'Sales Confirmation',
    orderCodeNullTip: 'Order code cannot be empty, please re-select',

    noStatus: 'No status',
    createTimeNotNull: 'Creation time cannot be empty, please re-select'
  },
  proxyCustomerToOrder: {
    productName: 'Product Name',
    unitPrice: 'Unit Price',
    noNullForCustomer: 'Customer cannot be blank',
    noNullForAddress: 'Address name cannot be blank',
    noNullForPeople: 'Recipient cannot be blank',
    noNullForPhone: 'Mobile number cannot be blank',
    noNullForMailAddress: 'Delivery address cannot be blank',
    addSuccess: 'Address edited successfully', // 前面跟新增或编辑
    addErr: 'Address edited failed', // 前面跟新增或编辑
    receivingInfo: 'Delivery Information', // 前面跟新增或编辑
    addressName: 'Address Name',
    addressNamePlaceholder: 'Please enter address name',
    area: 'Delivery Address',
    areaPlaceholder: 'Please enter delivery address',
    orderNum: 'Order Quantity',
    cardCode: 'GSC Code',
    type: 'Type',
    companyCode: 'Goldpac Code',
    chooseDayTime: 'Please select date and time',
    productUnitPrice: 'Product Unit Price',
    customerProuductCode: 'Customer Code',
    customerCode: 'Customer Code',
    write31Materiel: 'Input 31 Materials',
    outerBox: 'Outer Box',
    beingSubmitted: 'Submitting',
    inputOrderNumPlaceholder: 'Please enter order quantity',
    orderNumSizeTip: 'Order quantity shall be larger than 0',
    cardProduct: 'Card Product',
    nonCardProduct: 'Non-card Product',
    theCustomerNoProduct: 'No product is found under this account',
    selectProductPlaceholder: 'Please select product',
    selectGoodPlaceholder: 'Select Merchandise',
    greaterThan: 'Larger Than',
    lessThan: 'Smaller Than',
    inventory: 'Inventory',
    namePlaceholder: 'Please enter or select name',
    userPlaceholder: 'Please enter or select recipient',
    uploadErr: 'Upload failed',
    createSuccess: 'Created successfully',
    selectOrderType: 'Please select order type',
    selectTime: 'Please select date',
    uploadLimit: 'Upload limit exceeded, upload terminated',
    urgentExplain: 'Urgent Description',
    urgentExplainPlaceholder: 'Please enter urgent description',
    customerCodePlaceholder: 'Please enter customer order number',
    addOrderProduct: 'Add Order Product',
    inputCardNamePlaceholder: 'Please enter card type name',
    chooseProduct: 'Select Product',
    writeProduct: 'Input Product',
    importProduct: 'Import Product',
    write: 'Input',
    nonWrite: 'No Input',
    remark: 'Remarks',
    remarkPlaceholder: 'Please enter remarks',
    orderTypeNotNull: 'Order type cannot be blank',
    customerOrderReceiveTimeNotNull: 'Customer order receive time cannot be blank',
    urgentNotNull: 'Urgent setting cannot be blank',
    urgentExplainNotNull: 'Urgent description cannot be blank',
    customerNameNotNull: 'Customer name cannot be blank',
    deliveryTimeNotNull: 'EExpected deliver date cannot be blank',
    userNotNull: 'Contact person cannot be blank',
    deliveryMethodNotNull: 'Delivery method cannot be blank',
    fileListNotNull: 'Order voucher cannot be blank',
    pleaseInputRequired: 'Please enter the mandatory items',
    productNotNull: 'Order product cannot be blank',
    productNumNotNull: 'Please enter product quantity',
    uploadLimitReUpload: 'File size limit exceeded, please upload again!',
    checkFileFormat: 'Please check the file format and re-upload!',
    importFileErr: 'File import failed, please check data format!',
    checkOrderNum: 'Please check the order quantity, range from 1 - {maxNum}',
    inputNumInfo: 'Please enter the quantity information',
    inputPriceInfo: 'Please enter the unit price information',
    numNoTrue: 'Quantity must be positive integer',
    priceNoTrue: 'Unit price format is not correct',
    deliveryTimeFormatNoTrue:
      'Expected delivery date format is not correct, please follow YYYY-MM-DD',

    createOrder: 'Place Order',
    orderSaveSuccess: 'Order is saved, confirm to close this page?',
    hasSaveOrder: 'Saved order found, do you want to open it?',

    salesman: 'Sales Representative',
    salesmanTip: 'Please select sales representative',
    cardAddPersion: 'Card Type Creator',
    cardAddPersionTip: 'Please select card type creator',
    moreTip: 'Upload limit exceeded, upload terminated',
    exportOrderFileName: 'Order to Product import template',

    endCustomer: 'End Customer',
    endCustomerPlaceholder: 'Please select an end customer',

    terminalSelectIsNull: 'The end customer cannot be empty',
    totalPrice: 'Total Price'
  }
}
