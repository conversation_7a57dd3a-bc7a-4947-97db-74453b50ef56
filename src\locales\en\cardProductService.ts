export default {
  batchCardProduct: {
    merchantCardDetail: {
      cardAssociations: 'Card Scheme',
      cardCode: 'GSC Code', //卡款代码改GSC卡号
      cardDetail: 'Failed to obtain card product details',
      cardType: 'Card Type',
      cardFaceSpec: 'Card Specification',
      equalTo: 'Equal to',
      equalToLessThan: 'Less than or equal to',
      greaterThanOrEqual: 'Large than or equal to',
      productNum: 'Customer Code', //产品编号改客户代码
      quickOrder: 'Quick Order',
      viewSampleFile: 'View Sample File',
      withoutSampleFile: 'No Sample File',
      notSampleFile: 'No Sample File',
      historyOrder: 'History Order',
      orderCode: 'Order Code',
      orderPrice: 'Unit Price',
      orderProductNum: 'Number',
      orderTime: 'Order Time',
      shoppingCart: 'Add to Cart',
      successfully: 'Added to cart successfully',
      support: 'Support',
      surfaceBack: 'Special Effect - Card Face',
      surfacePositive: 'Special Effect - Card Back',
      technology: 'Card Effect',
      unitPrice: 'Unit Price',
      updateTime: 'Updated Time',
      createTime: 'Create Time',
      otherVersion: 'Other Version',
      priceRange: 'Range',
      quantity: 'Quantity',
      sheet: 'Pcs',
      up: 'above',

      latestProduceVersion: 'Final production sample version',
      latestGSCVersion: 'Latest sample version'
    },
    merchantCardList: {
      addProduct: 'New Product',
      addShoppingCar: 'Add to Cart',
      batchOrder: 'Batch Order',
      closed: 'Closed',
      components: {
        detailDialog: {
          cannotEmpty: 'Cannot be blank!',
          pleaseEnter: 'Please enter',
          saleName: 'Product Name'
        },
        search: {
          productName: 'Product Name',
          productStatus: 'Product Status'
        }
      },
      craftFiles: 'Specification File',
      createTime: 'Created Time',
      currentNone: 'None',
      detail: 'Details',
      downloadCraftFiles: 'Download Specification File',
      downloadCraftFilesAbnormal: 'Download Specification File Error:',
      downloadCraftFilesFail: 'Failed to download specification file!',
      noClientIdTips: 'Current user does NOT have customer ID, please contact system administrator',
      orderNumber: 'Order No.',
      productBackView: 'Card Back Preview',
      productFrontView: 'Card Face Preview',
      productName: 'Product Name',
      clientProductUniqueCode: 'Customer Code', //产品编号改客户代码
      productCode: 'GSC Code', //卡款代码改GSC卡号
      quickOrder: 'Quick Order',
      shoppingCart: 'Added to cart successfully',
      startUse: 'Enable',
      startUsed: 'Enabled',
      status: 'Status',
      toBeDesigned: 'Card design to be updated',
      updateTime: 'Updated Time',
      searchProductName: 'Search for product name',
      searchProductCode: 'Customer Code', //产品编号改客户代码
      searchCardCode: 'Search for GSC Code', //卡款代码改GSC卡号
      selectProductStatus: 'Select product status',
      downloadTemplateFile: 'Download Template File',
      importCreateOrder: 'Import and Order',
      orderTemplateFile: 'Create Order Template File',
      excelFormatError:
        'Failed to import the file, please check whether the data format is correct！',
      notFindProduct: 'Not find',
      createOrder: 'Create Order'
    },
    orderConfirm: {
      addAddress: 'New Address',
      address: 'Delivery address cannot be blank',
      address1: 'Address',
      addressName: 'Address Name',
      components: {
        AddAddressDialog: {
          add: 'Add',
          address: 'Address Name',
          address1: 'Please enter address name',
          addressName: 'Address name cannot be blank',
          belonging: 'Customer cannot be blank',
          default: 'Default address setting cannot be blank',
          defaultOrNot: 'Set as Default',
          detailAddress: 'Please enter detailed address',
          edit: 'Edit',
          enterRecipient: 'Please enter recipient name',
          enterTelephone: 'Please enter contact number',
          mailing: 'Delivery address cannot be blank',
          name: 'Recipient',
          no: 'No',
          phone: 'Contact No.',
          receiving: 'Shipping Information',
          recipient: 'Recipient cannot be blank',
          sendAddress: 'Delivery Address',
          telephone: 'Contact number cannot be blank',
          yes: 'Yes'
        },
        SelectAddressDialog: {
          address: 'Delivery Address',
          name: 'Address name',
          phone: 'Contact No.',
          recipient: 'Please enter recipient name',
          recipient1: 'Recipient',
          selectAddress: 'Please select the address',
          selectName: 'Please enter the address name'
        },
        AddProductDialog: {
          products: 'Product Information',
          productionName: 'Product Name',
          inputProductionName: 'Please enter the product name',

          order: 'Order Quantity',
          inputOrderNum: 'Please enter the Order Quantity',
          productCode: 'Customer Code', //产品编号改客户代码
          inputProductCode: 'Please the Customer Code',

          cardNumber: 'Card Reference No.', //卡款编号改GSC卡号
          inputCardNumber: 'Please the Card Reference No.', //卡款编号改GSC卡号

          unitPrice: 'Unit Price',
          inputUnitPrice: 'Please enter Unit Price',
          close: 'Close',
          select: 'Select'
        },
        productList: {
          no: 'No',
          order: 'Order Quantity',
          personalized: 'Personalized Material',
          products: 'Product Information',
          productCode: 'Customer Code', //产品编号改客户代码
          cardNumber: 'Card Reference No.',
          draftFile: 'Sample File',
          approvalFile: 'Approval File',
          unitPrice: 'Unit Price',
          yes: 'Yes',
          uploadApprovalFile: 'Upload Approval File',
          uploadCardApprovalAbnormal: 'Upload Approval Document Error:',
          download: 'Click To Download',
          minProductCount: 'Quantity cannot be less than MOQ'
        },
        upload: {
          clickAdd: 'click to upload',
          dragging: 'Drag  image files to here, or ',
          exceeding: 'File quantity limit exceeded!',
          fileType:
            'Please upload no more than 8 files, maximum file size is 10MB. File formats accepted: JPG, PNG, GIF, and PDF.',
          limit: 'File size limit exceeded, please upload again!'
        }
      },
      contactWay: 'Contact Method',
      contacts: 'Contact method cannot be blank',
      contacts1: 'Contact Person',
      delivery: 'Delivery Information',
      deliveryMethod: 'Delivery method cannot be blank',
      deliveryMethods: 'Delivery Method',
      enterContact: 'Please enter contact person',
      enterTelephone: 'Please enter contact number',
      expectedDelivery: 'Please enter expected delivery date',
      expectedDeliveryTime: 'Expected Delivery Date',
      fix: 'Pcs',
      individual: 'Pcs',
      inventory: 'Store in Goldpac Inventory',
      isItUrgent: 'Urgent',
      mail: 'Mail',
      mailing: 'Mailing Method',
      mailingAddress:
        'Delivery quantity and order quantity do NOT match, please check and submit again.',
      memo: 'Remarks',
      no: 'No',
      number: 'Quantity',
      orderCreation: 'Order Created',
      orderNow: 'Order Now',
      orderVoucher: 'Order Voucher',
      orderingProducts: 'Order product cannot be blank',
      pickup: 'Self-collection',
      pleaseEnter: 'Please enter the mandatory items',
      product: 'Product',
      recipient: 'Recipient',
      selectAddress: 'Please select the address',
      shippingMethod: 'Please select mailing method',
      telephone: 'Contact number cannot be blank',
      telephone1: 'Contact No.',
      total: 'Total order quantity',
      urgent: 'Urgent setting cannot be blank',
      yes: 'Yes',
      communityWithSale: 'Mail (Account Manager to Follow up)',
      sendToPersonalizationCenter: 'Send to Personalisation Center',
      other: 'Other',
      enterOtherContact: 'Please enter contact',
      orderVoucherDesc: 'The order voucher cannot be empty',
      notListed: 'is not available',
      addProduct: 'Add Product'
    },
    orderSuccess: {
      congratulations: 'Order placed successfully!',
      goTo: 'You can choose to go to',
      online: '[Online Ordering]',
      order: '[Order Management]'
    },
    shoppingCart: {
      orderQuantity: 'Order Quantity',
      placeAnOrder: 'Place Order',
      pleaseSelectOrder: 'Please Order',
      price: 'Price',
      product: 'Product(s)',
      productName: 'Product Name',
      quantity: 'Quantity',
      sheet: 'Pcs',
      unitPrice: 'Unit Price',
      notListed: 'is not available',
      cardCode: 'GSC Code', //卡款代码改GSC卡号
      productNum: 'Customer Code', //产品编号改客户代码
      unit: 'Pcs'
    }
  },
  mailingAddress: {
    address: 'Delivery Address',
    addressName: 'Address Name',
    addressee: 'Recipient',
    components: {
      search: {
        addAddress: 'Add Address',
        refresh: 'Refresh',
        searchAddress: 'Search Address',
        searchRecipient: 'Search Recipient'
      }
    },
    defaultAddress: 'Set as Default',
    delAddress: 'Confirm to delete this address?',
    delSuccess: 'Deleted successfully',
    phone: 'Contact No.',
    serialNumber: 'Serial No.',
    setSuccess: 'Set successfully'
  },
  productDemand: {
    common: {
      batchCardProducts: 'Batch Product',
      cardRequirements: 'Product Request',
      client: 'Client Platform',
      confirmThePlan: 'Confirm Proposal',
      designScheme: 'Design Proposal',
      diyProducts: 'DIY Product',
      doYouWantToPerformThisOperation: 'Confirm to execute?',
      editProductRequirements: 'Edit Product Request',
      initiateProductRequirements: 'New Product Request',
      managementPlatform: 'Management Platform',
      other: 'Others',
      prompt: 'Confirmation',
      requirementClosure: 'Close the Request',
      sampleCardApplication: 'Sample Card Application',
      sampleScheme: 'Artwork Proposal',
      unionPay: 'UnionPay',
      viewDesignProposal: 'Check design proposal',
      viewDraftProposal: 'Check artwork proposal',
      viewReceiptInformation: 'Check receipt information',
      view_3DFiles: 'Check 3D view file'
    },
    components: {
      cardBin: 'Card BIN#',
      cardOrganizationAndCardLevel: 'Card Scheme and Card Level',
      cardType: 'Card Type',
      cardTypes: 'Card Types',
      customerName: 'Customer Name',
      customerAccount: 'Customer Account',
      customerUploadsFiles: 'Customer File',
      demand: {
        cardBin: 'Card BIN#',
        customerDocuments: 'Customer File',
        designRequirements: 'Design Request',
        editSuccessful: 'Edited successfully',
        pleaseEnterARequirementDescription: 'Please enter request description',
        pleaseEnterATitle: 'Please enter title',
        pleaseEnterTheCardBin: 'Please enter card BIN#',
        pleaseEnterTheProductSelection: 'Please select the product',
        productName: 'Product Name',
        requirementDescription: 'Request Description',
        requirementTitle: 'Request Title',
        requirementType: 'Request Type',
        rulesTips: 'Content cannot be blank, please enter any information!',
        sampleRequirements: 'Artwork Request',
        selectFile: 'Select File',
        successfullyAdded: 'Added successfully',
        uploadSuccessful: 'Uploaded successfully',
        uploadTips:
          'Maximum file size: 10MB, Accepted file formats: TXT, DOC, XLSX, JPG, PNG, GIF, PDF, RAR, ZIP, PPT'
      },
      designRequirements: 'Design Request',
      dialogModule: {
        attachment: 'Attachment',
        cardName: 'Card Name',
        cardPaymentCode: 'GSC Code', //卡款代码改GSC卡号
        check: 'View',
        confirmedSuccessful: 'Confirmed successfully',
        designDescription: 'Design Description',
        designScheme: 'Design Proposal',
        display: 'Display 3D',
        download: 'Download',
        downloadAll: 'Download All',
        msgTips:
          'Confirm to use this as the final proposal, modification will be not allowed after confirmation.',
        pleaseEnterReceiptInformation: 'Please enter receipt information',
        receiptInformation: 'Receipt Information',
        remarks: 'Remarks',
        sampleFile: 'Artwork File',
        otherFile: 'Other File',
        submissionTime: 'Submission Time',
        uploadAttachments: 'Upload Attachment',
        proposalComments: 'Proposal Comments',
        agree: 'Agree',
        reject: 'Reject',
        stillNeedModify: 'Still Need Modification',
        modifyOpinion: 'Modification Opinion',
        addFeedback: 'Add Feedback',
        round: 'Round',
        CreateTime: 'Create Time',
        NeedToModify: 'Need To Modify',
        confirmPlan: 'Confirm Plan',
        feedback: 'Feedback',
        viewReceipts: 'View Receipts',
        customerFeedback: 'Customer Feedback',
        number: 'Number',
        feedbackInformation: 'Feedback Information',
        attachmentInformation: 'Attachment Information',
        feedbackTime: 'Feedback Time',
        confirmed: 'Confirmed',
        toBeConfirmed: 'To be confirmed',
        suggestion: 'suggestion',
        pleaseEnter: 'Please Enter',
        confirmPlanTips:
          'Are you sure you have selected this plan as the final one? Once confirmed, it cannot be modified. Please proceed with caution!',
        // 新增中英文
        videoFile: 'videoFile',
        viewVideoFiles: 'viewVideoFiles	',
        tDAddress: ' threeD address',
        view3DDisplay: 'view 3D display'
      },
      download: 'Download',
      expectedSubmissionDate: 'Expected Design Submission Date',
      fileUploadFailed: 'File upload failed',
      iMessage: {
        characters: 'Text',
        clearImage: 'Clear the image',
        complete: 'Complete',
        empty: 'Clear',
        errrotTips1: 'Failed to login, please contact system administrator!',
        errrotTips2: 'Failed to connect to server, please contact system administrator!',
        errrotTips3: 'No response from server, please contact system administrator!',
        errrotTips4: 'Server disconnected, trying to re-connect!',
        fileUploadFailed: 'File upload failed',
        graffitiPen: 'Paintbrush',
        imageEditingArea: 'Editing Area',
        interactiveRecording: 'Communication Record',
        kjNovaClipper: 'Crop Image',
        loading: 'Loading',
        onLine: 'Online',
        onlineCommunication: 'Online Communication',
        pleaseEnterText: 'Please enter text',
        pleaseEnterTheContent: 'Please enter content',
        pleaseInput: 'Please enter any contents',
        preserve: 'Save',
        rectangle: 'Rectangle',
        rotateImage: 'Rotate Image',
        rotundity: 'Oval',
        rubberEraser: 'Eraser',
        saveImage: 'Save Image',
        send: 'Send',
        sendDirectly: 'Send Directly',
        sendPictures: 'Send Image',
        thereSNothingMoreLeft: '(No more contents...)',
        uploadImages: 'Upload Image'
      },
      otherAttachments: 'Other Attachment',
      otherInstructions: 'Other Description',
      pleaseEnterTheProductName: 'Please enter product name',
      pleaseEnterTheRequirementType: 'Please enter requirment type',
      productName: 'Product Name',
      productType: 'Product Type',
      relatedProjects: 'Related Project',
      requirementDescription: 'Request Description',
      requirementNumber: 'Request No.',
      requirementTitle: 'Request Title',
      requirementType: 'Request Type',
      sampleRequirements: 'Artwork Request',
      searchRequirementTitle: 'Search request title',
      submissionTime: 'Submission Time',
      uploadFiles: 'Upload File',
      uploadSuccessful: 'Uploaded successfully',
      uploadTips1: 'You have deleted the file',
      uploadTips2: 'File size limit exceeded, please upload again!',
      uploadTips3: 'Please check the file format and upload again!',
      uploadTips4: 'Please wati until the file uploading is completed',
      uploadTips5: 'The number of uploaded files has been exceeded!',
      fileRepeat: 'fileRepeat',
      whole: 'All'
    },
    demandDetail: {
      basicNeeds: 'Basic Request',
      cardName: 'Card Name',
      cardNumber: 'GSC code', //卡款代码改GSC卡号
      cardPaymentCode: 'GSC code', //卡款代码改GSC卡号
      closeDemand: 'Close Request',
      closingInstructions: 'Close Description',
      confirmThePlan: 'Confirm Proposal',
      confirmed: 'Confirmed',
      demonstration: '3D Display',
      designScheme: 'Design Proposal',
      schemeName: 'Scheme Name',
      designer: 'Designer',
      designManager: 'Design Manager',
      detailedRequirements: 'Detailed Request',
      errorTips1: 'Artwork Request online communication is not supported yet.',
      errorTips2: 'Please select at least 1 record!',
      number: 'Serial No.',
      onlineCommunication: 'Online Communication',
      operationTime: 'Operation Time',
      operator: 'Operator',
      receiptInformation: 'Receipt Information',
      sampleCardOrder: 'Sample Card Order',
      samplePersonnel: 'Artwork Designer',
      sampleScheme: 'Artwork Proposal',
      state: 'Status',
      toBeConfirmed: 'To be confirmed',
      updateTime: 'Updated Time',
      viewReceipts: 'Check the receipt',
      cardPaymentCodeSearch: 'GSC code', //卡款代码改GSC卡号
      cardNameSearch: 'Card Name',
      reject: 'Reject',
      updateDate: 'Update Date',
      applicationFormNumber: 'Application Form Number',
      applicationStatus: 'Application Status',

      creationTime: 'Created Time',
      salesman: 'Sales Representative',
      query: 'search'
    },
    demandList: {
      currentStage: 'Current Status',
      newProductRequirements: 'New Product Request',
      sourceOfDemand: 'Request Source'
    },
    orderDetail: {
      address: 'Address',
      addressName: 'Address Name',
      contactPhoneNumber: 'Contact No.',
      contacts: 'Contact Person',
      creationTime: 'Created Time',
      customerInformation: 'Customer Information',
      customerSelfPickup: 'Customer Self-collection',
      download: 'Download',
      errorTips1: 'Retrieve order failed, please try again later!',
      estimatedDeliveryTime: 'Estimated Delivery Time',
      inventoryAndProxyStorage: 'Store in Goldpac Inventory',
      logisticsCompany: 'Logistics Company',
      logisticsInformation: 'Logistics Information',
      logisticsTracking: 'Logistics Tracking',
      mail: 'Mail',
      mailingInformation: 'Mailing Information',
      managementPlatform: 'Client Platform',
      no: 'No',
      operator: 'Operator',
      operatorAccount: 'Operator Account',
      creator: 'Creator',
      creatorAccount: 'Creator Account',
      orderInformation: 'Order Information',
      orderNumber: 'Order No.',
      orderSource: 'Order Source',
      orderStatus: 'Order Status',
      orderType: 'Order Type',
      orderVoucher: 'Order Voucher',
      orderingCustomers: 'Ordering Customer',
      personalizedMaterials: 'Personalized Material',
      productInformation: 'Product Information',
      quantity: 'Quantity',
      recipients: 'Recipient',
      referencePrice: 'Reference Price',
      remarksDescription: 'Remarks',
      shippingMethod: 'Shipping Method',
      shou: 'Recipient',
      totalOrderPrice: 'Total Order Price',
      updateTime: 'Updated Time',
      waybillNumber: 'Waybill No.',
      yes: 'yes',
      customerOrderCode: 'Customer Order No.'
    },
    designRecommend: {
      DesignerPerferred: 'Designer Perferred',
      IPCooperation: 'IP Cooperation',
      ToExplore: 'To Explore',
      CardCustomizationRequirement: 'Card Customization Request',
      Template: 'Template',
      CardBodyColor: 'Card Body Color',
      Theme: 'Theme',
      View3DCardFace: 'View 3D Card Face',
      RaiseRequirement: 'Raise Request',
      SelectProposal: 'Select Proposal',
      DoNOTSelectProposal: 'Do NOT Select Proposal',
      DPintroduction: 'DP introduction',
      NewCardType: 'New Card Type',
      DesingerRecommanda: 'Desinger Recommanda',
      HotIP: 'Hot IP',
      Classification: 'Classification',
      DesignIntroduction: 'Design Introduction',
      // 新增中英文
      all: 'All',
      myApplication: 'My Application',
      myCollection: 'My Collection',
      keyword: 'Keywords For example: “Cute animals”',
      collect: 'Collect',
      collected: 'Collected',
      loadMore: 'Load more',
      nothingMore: 'End of Results',
      qualitymaterials: 'Massive high-quality materials',
      viewMore: 'See more',
      optimalDesignScheme: 'Optimal design scheme',
      horizontalVersion: 'Horizontal version',
      verticalVersion: 'Vertical Version',
      irregularShape: 'IrregularShape',
      chinaChic: 'Trendy',
      anime: 'Anime',
      technology: 'Technology',
      art: 'Art',
      copyrighted: 'Copyrighted',
      applyToDIY: 'Apply To DIY',
      keywords: 'Keywords',
      pleaseSelect: 'Please Select',
      pleaseEnter: 'Please Enter',
      image: 'Image ',
      description: 'Description',
      materialUsage: 'MaterialUsage',
      bindProduct: 'Bind Product ',
      used: 'Used',
      addTime: 'AddTime',
      operation: 'Operation',
      viewProduct: 'View Product',
      delete: 'Delete',
      imageClassification: 'ImageCategory',
      keyDescription: 'Key Description ',
      materialType: 'Material Type',
      cancelCollection: 'Cancel Collection',
      selectProduct: 'Select Product',
      product: 'Product'
    }
  },
  productOrder: {
    batchOrderDetail: {
      customer: 'Client Platform',
      deliveryDate: 'Estimated Delivery Date',
      mailingAddress: 'Delivery Address',
      management: 'Management Platform',
      sale: 'Sales Platform',
      draftFile: 'Sample File',
      approvalFile: 'Approval File',
      orderStatus: 'Order Status',
      uploadApprovalFile: 'Upload Approval File',
      uploadCardApprovalAbnormal: 'Upload Approval Document Error:',
      download: 'Click To Download',
      downloadPlmApproval: 'Download From PLM',
      downloadCardApprovalFail: 'Failed to download approval document!',
      downloadCardApprovalAbnormal: 'Download Approval Document Error:',
      statusNone: 'Under Review',
      statusStocking: 'In Stock',
      statusMaking: 'In Production',
      statusMaked: 'Stocked',
      statusShipped: 'Shipped Out'
    },
    noCardOrder: {
      addAddress: 'New Address',
      batchCardOrder: 'Batch Order',
      orderingProducts: 'Order Product',
      pleaseEnterNoteInformation: 'Please enter remark information',
      pleaseSelectAnAddress: 'Please select the address',
      pleaseSelectOrderType: 'Please select the order type',
      tips1: 'To be supplemented after order is confirmed',
      tips2: 'No customer information in this user, cannot submit the order.',
      uploadTips1: 'Drag  image file to here',
      uploadTips2: 'click to upload',
      uploadTips3: 'No more than 8 files, Maximum file size:  10MB',
      uploadTips5: 'File quantity limit exceeded!',
      uploadTips4: 'Or'
    },
    noUploadedImages: 'No uploaded image',
    orderDetail: {},
    orderList: {
      createTime: 'Created Time',
      customerSExpectedDeliveryDate: 'Customer Expected Delivery Date',
      more: 'More',
      revoke: 'Recall',
      revokeSuccess: 'Revoke successfully',
      searchForProductName: 'Search Product Name',
      searchOrderCode: 'Search order no.',
      source: 'Source',
      tips1: 'Confirm to recall this order?'
    }
  }
}
