export default {
  search: 'Search',
  confirmTitle: 'System Hint',
  inputText: 'Please input',
  selectText: 'Please select',
  startTimeText: 'Start date',
  endTimeText: 'End date',
  login: 'Login',
  required: 'This is required',
  loginOut: 'Login out',
  document: 'Document',
  reminder: 'Reminder',
  loginOutMessage: 'Exit the system?',
  back: 'Back',
  ok: 'Submit',
  cancel: 'Cancel',
  reload: 'Reload current',
  closeTab: 'Close current',
  closeTheLeftTab: 'Close left',
  closeTheRightTab: 'Close right',
  closeOther: 'Close other',
  closeAll: 'Close all',
  prevLabel: 'Prev',
  nextLabel: 'Next',
  skipLabel: 'Jump',
  doneLabel: 'End',
  menu: 'Menu',
  menuDes: 'Menu bar rendered in routed structure',
  collapse: 'Collapse',
  collapseDes: 'Expand and zoom the menu bar',
  tagsView: 'Tags view',
  tagsViewDes: 'Used to record routing history',
  tool: 'Tool',
  toolDes: 'Used to set up custom systems',
  query: 'Search',
  reset: 'Reset',
  shrink: 'Hide',
  expand: 'Expand',
  delMessage: 'Delete the selected data?',
  delWarning: 'Warning',
  delOk: 'Submit',
  delCancel: 'Cancel',
  delNoData: 'Please select the data to delete',
  delSuccess: 'Deleted successfully',
  operate: 'Actions',
  modify: 'Modify',
  delete: 'Delete',
  newAdd: 'Add',
  edit: 'Edit',
  remove: 'Remove',
  close: 'Close',
  updateSuccess: 'updateSuccess',
  cancelDelete: 'cancelDelete',
  confirm: 'confirm',
  warning: 'warning',
  addSuccess: 'addSuccess',
  update: 'update',
  submitForm: 'submitForm',
  oper: 'Actions',
  add: 'add',
  save: 'Save',
  open: 'Open',
  enable: 'Enable',
  handleSuccess: 'Operation Successful!',
  exportMessage: 'Are you sure you want to export the data items?',
  tip: 'Tip',
  success: 'Success',
  fail: 'Fail',
  setting: 'Theme Settings',
  see: 'View',
  withdraw: 'Withdraw',
  previewTip: 'Only supports image and PDF preview',
  export: 'Export'
}
