export default {
  BaseTable: {
    operation: 'Actions'
  },
  BpmnProcessDesigner: {
    package: {
      designer: {
        plugins: {
          contentPad: {
            contentPadProvider: {
              changeType: 'Change Type'
            }
          },
          palette: {
            CustomPalette: {
              activeGripperTool: 'Active Gripper'
            }
          }
        },
        ProcessDesigner: {
          openFile: 'Open File',
          downloadXML: 'Download as XML file',
          downloadSVG: 'Download as SVG file',
          downloadBPMN: 'Download as BPMN file',
          downloadFile: 'Download File',
          preview: 'Preview',
          previewXML: 'Preview XML',
          previewJSON: 'Preview JSON',
          outSimulation: 'Exit Simulation',
          openSimulation: 'Run Simulation',
          simulation: 'Simulation',
          alignLeft: 'Align Left',
          alignRight: 'Align Right',
          alignTop: 'Align Top',
          alignBottom: 'Align Bottom',
          alignCenter: 'Align Horizontal Center',
          verticalCenter: 'Align Vertical Center',
          processZoomOut: 'Zoom Out',
          processZoomIn: 'Zoom In',
          processReZoom: 'Reset Zoom',
          processUndo: 'Undo',
          processRedo: 'Redo',
          processRestart: 'Reload',
          saveModel: 'Save Model',
          pushShift: 'Please use "Shift" to select more elements to set alignment',
          autoAlignTip: 'Auto alignment may cause deformation, confirm to continue?',
          warning: 'Warning',
          saveModelFail: 'Save model failed, please try again!'
        },
        ProcessViewer: {
          operationFlow: 'Service Flow',
          initiator: 'Initiator:',
          department: 'Department:',
          createTime: 'Creation Time:',
          approver: 'Approver:',
          result: 'Result:',
          endTime: 'End Date:',
          approvalSuggestion: 'Approval Comment'
        }
      },
      palette: {
        ProcessPalette: {
          testTask: 'Testing Task'
        }
      },
      penal: {
        base: {
          ElementBaseInfo: {
            signatureTip: 'How to process countersign, or sign?',
            processIdent: 'Process Identifier',
            pleaseEnter: 'Please enter',
            processName: 'Process Name',
            name: 'Name',
            idErrorTip: 'Process Identifier cannot be blank',
            nameErrorTip: 'Process Name cannot be blank'
          }
        },
        flowCondition: {
          FlowCondition: {
            type: 'Flow Type',
            normal: 'Normal Flow Path',
            default: 'Default Flow Path',
            condition: 'Condition Flow Path',
            conditionType: 'Condition Format',
            expression: 'Expression',
            script: 'Script',
            language: 'Script Language',
            scriptType: 'Script Type',
            inlineScript: 'Inline Script',
            externalScript: 'External Script',
            resource: 'Resource Address'
          }
        },
        form: {
          ElementForm: {
            formKey: 'Form Identifier',
            businessKey: 'Service Identifier',
            none: 'None',
            formFieId: 'Form Field',
            num: 'Serial No.',
            FieIdName: 'Field Name',
            FieIdType: 'Field Type',
            default: 'Default Value',
            operation: 'Actions',
            cutout: 'Remove',
            addFieId: 'Add Field',
            fieIdSetting: 'Field Setting',
            fieIdId: 'Field ID',
            typeType: 'Field Type',
            pleaseSelect: 'Please select',
            typeName: 'Type Name',
            name: 'Name',
            datePattern: 'Time Format',
            enumList: 'Enumeration List',
            addEnum: 'Add Enumeration',
            enumCode: 'Enumeration Code',
            enumName: 'Enumeration Name',
            openFieldOptionForm: 'Constraint Condition List',
            addConstraint: 'Add Constraint',
            constraintName: 'Constraint Name',
            constraintSetting: 'Constraint Setting',
            fieIdList: 'Field Attribute List',
            addAttribute: 'Add Attribute',
            attributeCode: 'Attribute Code',
            save: 'Save',
            fieldOptionFormId: 'ID',
            setting: 'Setting',
            value: 'Value'
          }
        },
        listeners: {
          ElementListeners: {
            num: 'Serial No.',
            eventType: 'Event Type',
            listenerType: 'Listener Type',
            operation: 'Actions',
            cutout: 'Remove',
            addListener: 'Add Listener',
            executeListener: 'Run Listener',
            javaClass: 'JAVA Class',
            expression: 'Expression',
            delegateExpression: 'Agent Expression',
            scriptFormat: 'Script Format',
            pleaseEnterScriptFormat: 'Please enter script format',
            scriptType: 'Script Type',
            pleaseSelectScriptType: 'Please select script type',
            inlineScript: 'Inline Script',
            externalScript: 'External Script',
            scriptValue: 'Script Content',
            pleaseInputScriptValue: 'Please enter script content',
            resourceUrl: 'Resource Address URL',
            pleaseInputResourceUrl: 'Please enter resource address URL',
            inputField: 'Inject Field:',
            addField: 'Add Field',
            fieldName: 'Field Name',
            fieldType: 'Field Type',
            fieldValue: 'Field Value',
            save: 'Save',
            fieIdSetting: 'Field Setting',
            confirmCutOutFieldTip: 'Confirm to remove this field?',
            tip: 'Tips',
            confirmCutOutListenerTip: 'Confirm to remove this listener?'
          },
          UserTaskListeners: {
            num: 'Serial No.',
            eventType: 'Event Type',
            eventId: 'Event ID',
            listenerType: 'Listener Type',
            operation: 'Actions',
            cutout: 'Remove',
            addListener: 'Add Listener',
            taskListener: 'Task Listener',
            listener: 'Listener ID',
            javaClass: 'JAVA Class',
            expression: 'Expression',
            delegateExpression: 'Agent Expression',
            scriptFormat: 'Script Format',
            pleaseEnterScriptFormat: 'Please enter script format',
            scriptType: 'Script Type',
            pleaseSelectScriptType: 'Please select script type',
            inlineScript: 'Inline Script',
            externalScript: 'External Script',
            scriptValue: 'Script Content',
            pleaseInputScriptValue: 'Please enter script content',
            resourceUrl: 'Resource Address URL',
            pleaseInputResourceUrl: 'Please enter resource address URL',
            eventDefinitionType: 'Timer Type',
            date: 'Date',
            duration: 'Duration',
            loop: 'Loop',
            none: 'None',
            timer: 'Timer',
            pleaseInputTimer: 'Please enter timer setting',
            injectKey: 'Inject Field:',
            addField: 'Add Field',
            fieldName: 'Field Name',
            fieldType: 'Field Type',
            fieldValue: 'Field Value'
          }
        }
      }
    },
    save: 'Save',
    fieldSetting: 'Field Setting',
    fieldName: 'Field Name:',
    fieldType: 'Field Type:',
    fieldValue: 'Field Value:',
    expression: 'Expression:',
    confirmCutOutFieldTip: 'Confirm to remove this field?',
    tip: 'Tips',
    confirmCutOutListenerTip: 'Confirm to remove this listener?',
    loopCharacteristics: 'Loop Characteristics',
    ParallelMultiInstance: 'Parallel Multi-event',
    SequentialMultiInstance: 'Sequential Multi-event',
    StandardLoop: 'Loop Event',
    none: 'None',
    loopCardinality: 'Number of Loop Cycle',
    collection: 'Collection',
    elementVariable: 'Element Variable',
    completionCondition: 'Completion Condition',
    asyncStatus: 'Asynchronous Status',
    asyncBefore: 'Before Asynchronization',
    asyncAfter: 'After Asynchronization',
    exclusive: 'Exclude',
    timeCycle: 'Retry Cycle',
    elementDoc: 'Element Document',
    num: 'Serial No.',
    attrName: 'Attribute Name',
    attrValue: 'Attribute Value',
    operation: 'Actions',
    addAttr: 'Add Attribute',
    attrSetting: 'Attribute Setting',
    cutout: 'Remove',
    confirmCutOutAttr: 'Confirm to delete this attribute?',
    messageList: 'Message List',
    createNew: 'New Message',
    messageId: 'Message ID',
    messageName: 'Message Name',
    signalList: 'Signal List',
    createSignal: 'New Signal',
    signalId: 'Signal ID',
    signalName: 'Signal Name',
    messageExisting: 'This message is existed, please change the ID and save again',
    signalExisting: 'This signal is existed, please change the ID and save again',
    messageExample: 'Message Instance',
    confirm: 'Confirm',
    scriptFormat: 'Script Format',
    pleaseEnterScriptFormat: 'Please enter script format',
    scriptType: 'Script Type',
    pleaseSelectScriptType: 'Please select script type',
    inlineScript: 'Inline Script',
    externalScript: 'External Script',
    scriptValue: 'Script Content',
    resultValue: 'Result Variable',
    outsideResource: 'External Resource',
    resourceAddr: 'Resource Address URL',
    dueDate: 'Expiry Time',
    followUpDate: 'Followed Up Time',
    priority: 'Priority',
    friendlyTips1: 'Task assignment rule is following',
    friendlyTips2: 'Workflow Model',
    friendlyTips3:
      '[Assignment Rule], including 7 types e.g.: Designated Role, Department Head, Department Member, Post, Workgroup, Customized Script, and etc.',
    asyncContinue: 'Asynchronous Extension',
    eliminate: 'Exclude',
    routine: 'Regular ',
    messageAndSignal: 'Message and Signal',
    circulationConditions: 'Flow Condition',
    form: 'Form',
    friendlyTips4: 'Use',
    friendlyTips5: 'Workflow Form',
    friendlyTips6: 'as replacement, with better form design function.',
    task: 'Task',
    multipleInstances: 'Multi-instances',
    executeListener: 'Run Listener',
    taskListener: 'Taks Listener',
    extendAttr: 'Attribute Extension',
    else: 'Others'
  },
  ButtonMessage: {
    isConfirm: 'Confirm?',
    tip: 'Tips'
  },
  CustomUpload: {
    sumExtends: 'Total file size limit exceeded, please upload again!',
    extends: 'File size limit exceeded, please upload again!',
    reUpload: 'Please check the file format and upload again!',
    extendsNum: 'File quantity limit exceeded!',
    uploadFail: 'File upload failed',
    waitForUpload: 'Please wait until the file uploading is completed'
  },
  Editor: {
    pleaseEnter: 'Please enter content...',
    reUpload: 'Please check the file format and upload again!'
  },
  SearchFrom: {
    startDate: 'Start Date',
    endDate: 'End Date',
    recentlyOneWeek: 'Last week',
    recentlyOneMonth: 'Last months',
    recentlyThreeMonth: 'Last three months'
  }
}
