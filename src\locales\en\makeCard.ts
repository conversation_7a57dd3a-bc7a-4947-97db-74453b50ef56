export default {
  // 公共
  common: {
    customerName: 'Customer Name',
    projectName: 'Project Name',
    serverName: 'Service Name',
    demandSource: 'Request Source',
    demandTitle: 'Request Title',
    demandCode: 'Request Code',
    demandType: 'Request Type',
    currentDutyPer: 'Current Task Taker',
    demandDesc: 'Request Description',
    cardBin: 'Card BIN#',
    currentDutyPerPlaceholder: 'Please select current task taker',
    demandTypePlaceholder: 'Please select request type',
    customerPlaceholder: 'Please select customer name',
    projectPlaceholder: 'Please select project name',
    serverPlaceholder: 'Please select service name',
    demandSourcePlaceholder: 'Please select request source',
    demandTitlePlaceholder: 'Please enter request title',
    demandCodePlaceholder: 'Please enter request code',
    unfold: 'Expand',
    packUp: 'Collapse',
    search: 'Search',
    reset: 'Reset',
    add: 'Add',
    delete: 'Delete',
    edit: 'Edit',
    detail: 'Details',
    confirm: 'Confirm',
    ok: 'Submit',
    cancel: 'Cancel',
    disposition: 'Configuration',
    check: 'View',
    export: 'Import',
    open: 'Open',
    close: 'Close',
    batchDelete: 'Delete in Batch',
    batchMove: 'Move in Batch',
    addPic: 'Add Image',
    addComponent: 'Add Module',
    addSort: 'Add Category',
    upload: 'Upload',
    uploadFile: 'Upload File',
    noData: 'No Data',
    groupManage: 'Group Management',
    copyLink: 'Copy Link',
    notHave: 'None',
    null: 'Null',
    saveSuccess: 'Saved Successfully',
    addSuccess: 'Added Successfully',
    deleteSuccess: 'Deleted Successfully',
    editSuccess: 'Edited Successfully',
    deleteContent: 'Confirm to delete',
    editSort: 'Edit Category',
    client: 'Client Platform',
    management: 'Management Platform',
    makeDemand: 'Card Personalization Request',
    designCase: 'Design Proposal',
    draftCase: 'Artwork Proposal',
    sampleCardApply: 'Sample Card Application',
    designDemand: 'Design Request',
    draftDemand: 'Artwork Request',
    closeDemand: 'Close Request',
    pleaseChooseData: 'Please select at least 1 record!',
    all: 'All',
    other: 'Others',
    UnionPay: 'UnionPay',
    ConfirmOperation: 'Confirm to execute?',
    uploadSuccess: 'Uploaded successfully',
    pleaseEnter: 'Please enter content',
    uploadFileError: 'File Upload Error',
    download: 'Download',
    sizeOutOfLimit: 'File size limit exceeded, please upload again!',
    checkFileFormat: 'Please check the file format and upload again!',
    numberOutOfLimit: 'File quantity limit exceeded, please upload again!',
    awaitUpload: 'Please wati until the file uploading is completed',
    removeTip: '{fileName} has been removed',
    noFile: 'No file is selected!',
    disableNull: 'Content cannot be blank',
    yes: 'Yes',
    no: 'No',
    submit: 'Submit',

    productName: 'Product Name',
    productType: 'Product Type',
    placeholderTextTip: 'Please enter {placeholder}',
    placeholderSelectTip: 'Please select {placeholder}',
    basicDemand: 'Basic Request',
    customerFile: 'Customer File',
    detailDemand: 'Detailed Description',
    customerAccount: 'Customer Account',
    selectFile: 'Select File',
    uploadFileNoSizeTip:
      'No more than {maxNum} files, Maximum file size: {maxSize}MB., no limitation on file format',
    uploadFileTip:
      'No more than {maxNum} files, Maximum file size:  {maxSize}MB.,Accepted file formats:{fileFormat}',
    uploadFileNoNumTip: 'Maximum file size:  {maxSize}MB., Accepted file formats:{fileFormat}',
    uploadFileNoFormatTip: 'No more than {maxNum} files, Maximum file size:  {maxSize}MB.',
    customer: 'Customer',
    estimatedTimeOfSubmission: 'Estimated Time of Submission',
    originAndLevel: 'Card Scheme and Card Level',
    backTip:
      'Confirm to use this as the final proposal, modification will be not allowed after confirmation.'
  },
  dialog: {
    editReceivingInfo: 'Edit delivery information',
    addReceivingInfo: 'New delivery information',
    chooseReceiving: 'Select Address',
    view3D: 'View 3D view file',
    viewDesign: 'View design proposal',
    viewDraft: 'View artwork proposal',
    viewFile: 'View internal document',
    viewBackMsg: 'View receipt information',
    verifyScheme: 'Confirm Proposal',
    upLoadSaveFile: 'Upload document',
    uploadDesign: 'Upload design proposal',
    uploadDraft: 'Upload artwork proposal',
    upLoadAgain: 'Upload proposal again'
  },
  index: {
    list: 'Product Request List',
    createList: 'New Product Request',
    conversionDraft: 'Convert Artwork Request',
    conversionDesign: 'Convert Design Request',
    assignTasks: 'Assign Task',
    assignSuccess: 'Assigned successfully',
    conversionSuccess: 'Converted successfully',
    dataError: 'Data error, please contact system administrator!'
  },
  im: {
    sendDirectly: 'Send Directly',
    saveImage: 'Save Image',
    imgCut: 'Crop Image',
    imgEditArea: 'Image Editing Area',
    clearImg: 'Clear Image',
    uploadImg: 'Upload Image',
    onlineChat: 'Online Communication',
    interactiveLog: 'Communication Record',
    online: 'Online',
    loading: 'Loading...',
    onMore: '(No more contents...)',
    pleaseEnter: 'Please enter content......',
    pleaseEnterTip: 'Please enter any contents',
    send: 'Send',
    loginError: 'Failed to login, please contact system administrator!',
    joinError: 'Failed to connect to server, please contact system administrator!',
    serviceError: 'No response from server, please contact system administrator!',
    reJoinError: 'Server disconnected, trying to re-connect!',
    sending: 'Sending...',
    clear: 'Clear',
    rubber: 'Eraser',
    pen: 'Paintbrush',
    text: 'Text',
    square: 'Rectangle',
    round: 'Oval',
    rotate: 'Rotate Image',
    finish: 'Complete',
    sendImg: 'Send Image'
  },
  detail: {
    assignmentDesigner: 'Assign Designer',
    assignmentDraftDesigner: 'Assign Artwork Designer',
    chooseDesigner: 'Select Designer',
    chooseDraftDesigner: 'Select Artwork Designer',
    chooseAuthDesigner: 'Please select authorized designer',
    chooseAuthDraftDesigner: 'Please select authorized artwork designer',
    pleaseChooseDesign: 'Please select designer!',
    customerFile: 'Customer Upload File',
    relatedProject: 'Related Project',
    estimatedDraftDate: 'Expected Design Submission Date',
    cardType: 'Card Type',
    cardVariety: 'Card Variety',
    cardOrgLevel: 'Card Scheme and Card Level',
    otherExplain: 'Other Description',
    otherFile: 'Other Attachment',
    backMsg: 'Receipt Information',
    accessory: 'Attachment',
    closeExplain: 'Description of closure',
    closeExplainPlaceholder: 'Please enter description of closure',
    noCloseExplainTip: 'Please enter description of closure',
    closeSuccess: 'Closed successfully',
    cardStyleCode: 'Card GSC No.',
    cardStyleName: 'Card Name',
    stylist: 'Designer',
    stylistOfDraft: 'Artwork Designer',
    interiorFile: 'Internal Document',
    status: 'Status',
    valetConfirmation: 'Confirmation on behalf',
    toBeConfirmed: 'To be Confirmed',
    confirmed: 'Confimred',
    noIMOfDraft: 'Artwork request online communication is not supported yet.',
    noDesignOfDraft: 'Artwork design proposal uploading is not supported yet.',
    sampleCardOrder: 'Sample Card Oder',
    BatchProductUpdate: 'Update Batch Product',
    cardDraftCode: 'Card GSC Code',
    view3D: '3D View',
    show3D: '3D Display',
    view: 'View',
    operator: 'Operator',
    operatorTime: 'Actions Time',
    updateProductInfo: 'Update Product Information',
    noIMOfDesign: 'Design request online communication is not supported yet.',
    productCode: 'Product Code',
    toBeListed: 'To be put on shelves',
    listed: 'On Shelves',
    productImg: 'Product Image',
    frontImg: 'Card Face Image',
    backImg: 'Card Back Image',
    updateProductErr: 'DIY Card Product Updating',
    remark: 'Remarks',
    uploadDraftFile: 'Upload artwork file',
    archiveFile: 'Documents',
    remarkNote: 'Remark Notes',
    confirmSuccess: 'Confirmed Successfully',
    uploadOtherFile: 'Upload Attachment',
    draftFile: 'Artwork File',
    noFrontImg: 'Please upload card face image!',
    noBackImg: 'Please upload card back iamge!',
    saveProductSuccess: 'Product saved successfully!',
    saveProducterror: 'Update product failed',
    batchProduct: 'Batch Card Product',
    DIYProduct: 'DIY Card Product'
  },
  sampleOrder: {
    addressName: 'Address Name',
    contact: 'Recipient',
    tel: 'Contact No.',
    area: 'Delivery Address',
    selectCustomerPlaceholder: 'Please enter customer name',
    selectAddressPlaceholder: 'Please select city and province',
    addressDetailPlaceholder: 'Please enter detailed address',
    contactPlaceholder: 'Please enter recipient',
    telPlaceholder: 'Please enter mobile number',
    namePlaceholder: 'Please enter name',
    operatorSuccess: 'Operated successfully',
    name: 'Name',
    phone: 'Mobile No.',
    address: 'Address',

    applySample: 'Apply Sample Card',
    unitPrice: 'Unit Price',
    cardCount: 'Quantity',
    maxTip: 'Maximum 48pcs',
    deliveryTime: 'Expected Delivery Date',
    deliveryTimePlaceholder: 'Please enter expected delivery date',
    isUrgent: 'Urgent?',
    addressOfPeople: 'Contact Person',
    addressOfPeoplePlaceholder: 'Please enter contact person',
    addressOfTelPlaceholder: 'Please enter mobile number',
    deliveryMethod: 'Delivery Mothod',
    storage: 'Store in Goldpac Inventory',
    customerPick: 'Self-collection by Customer',
    mail: 'Mailing',
    mailModePlaceholder: 'Please select delivery method',
    mailOfAddress: 'Address',
    packageMode: 'Package Mode',
    packageModePlaceholder: 'Please select package mode',
    innerBox: 'Inner Box',
    outerBox: 'Outer Box',
    fileList: 'Order Voucher',

    addAddress: 'New Address',
    innerBoxPlaceholder: 'Please select inner box',
    outerBoxPlaceholder: 'Please select outer box',
    product: 'Product',
    price: 'Price',
    unitP: ' Pcs',
    unitO: ' Units',
    createOrder: 'Order Now',
    deliveryMethodPlaceholder: 'Please enter delivery method!',
    mailModePlaceholderJs: 'Please select delivery method!',
    mailOfAddressPlaceholder: 'Please select mailing address!',
    unitPricePlaceholder: 'Please enter unit price!',
    cardCountPlaceholder: 'Please enter order volume!',
    deliveryTimePlaceholderJs: 'Please enter expected delivery date!',
    isUrgentPlaceholder: 'Please enter if it is urgent order or not!',
    peoplePlaceholder: 'Please enter contact person!',
    phonePlaceholder: 'Please enter mobile number!',
    getErrorTip: 'Failed to retrieve parameters, please try again later!',
    orderType: 'Order Type',
    orderCode: 'Order No.',
    orderInfo: 'Order Information',
    cardCountAndUnit: 'Quantity (Pcs)',

    logisticsInfo: 'Logistics Information',
    mailNo: 'Waybill No.',
    mailMode: 'Shipping Method',
    expressTypeText: 'Logistics Company',
    fullAddress: 'Detailed Address',
    routeAddress: 'Logistics Tracking',
    OrderStatus: 'Order Status',
    orderSource: 'Order Source',
    deliveryTimeOfDetail: 'Estimated Delivery Time',
    orderTotalPrice: 'Total Order Price',
    operateName: 'Operator',
    operateUsername: 'Operator Account',
    customerInfo: 'Customer Information',
    customerName: 'Ordering Customer',
    productInfo: 'Product Information',
    unitPriceOfDetail: 'Reference Price',
    isIndividual: 'Personalized Material',
    mailInfo: 'Mailing Information',
    getOrderErr: 'Retrieve order failed, please try again later!'
  },
  table: {
    indexNumber: 'Serial Number',
    operate: 'Actions',
    updateTime: 'Updated Time',
    submitTime: 'Submission Time',
    createTime: 'Creation Time',
    demandPhase: 'Current Phase'
  }
}
