export default {
  components: {
    publishTime: 'Publish Time',
    publishUser: 'Publisher',
    messageTypeText: 'Message Type'
  },
  list: {
    status: 'Status',
    startDate: 'Start Date',
    endDate: 'End Date',
    markRead: 'Mark as Read',
    title: 'Title',
    sortNum: 'Sort Number',
    recevieTime: 'Received Time', // 注意：原单词可能存在拼写错误，这里我假设是'receive'
    lastWeek: 'Last Week',
    lastMonth: 'Last Month',
    lastThreeMonth: 'Last Three Months', // 通常会用 'Last 3 Months' 来表达
    hasRead: 'Read',
    noRead: 'Unread',
    markReadTips: 'Are you sure you want to mark the selected messages as read?',
    operateSuccess: 'Actions Succeeded'
  }
}
