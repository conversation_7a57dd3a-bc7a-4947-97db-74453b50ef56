export default {
  batchCardProduct: {
    numberOutOfLimit: 'File quantity limit exceeded!',
    sizeOutOfLimit: 'File size limit exceeded, please upload again!',
    onlyOneTip: 'Please upload 1 file only! Uploaded file will be replaced!',
    formatTip: 'Upload file in {format} format only, please try again!',
    sizeTip: 'Maximum file size:  {size}MB, please upload again!',
    removeTip: 'File [{fileName}] is removed',
    file: 'File',
    cancelReUpload: 'Cancel Upload',
    cancelReUploadFile: 'Cancel file uploading',
    pleaseUpload: 'Please upload',

    basicInfo: 'Basic Information',
    customerName: 'Customer Name',
    customerNamePlaceholder: 'Please select customer name',
    productType: 'Product Type',
    productName: 'Product Name',
    cardCode: 'GSC Code',
    frontImg: 'Card Face Image',
    backImg: 'Card Back Image',
    productFrontImg: 'Card Face Preview',
    productBackImg: 'Card Back Preview',
    productImg: 'Product Image',
    updateTime: 'Updated Time',
    operateInfo: 'Actions Information',
    viewProduct: 'View Product',
    toBeSubmitted: 'To be submitted',
    delist: 'Removed',
    underReview: 'Checking',

    TheCustomer: 'Customer',
    TheCustomerPlaceholder: 'Please select customer',
    productTypePlaceholder: 'Please select product Type',
    productNamePlaceholder: 'Please enter Product Name',
    cardCodePlaceholder: 'Search for GSC code',
    productStatus: 'Product Status',
    productStatusPlaceholder: 'Please select product status',
    packUp: 'Collapse',
    unfold: 'Expand',
    indexNumber: 'Index No.',
    noData: 'No Data',
    status: 'Status',
    productInfo: 'Product Information',
    submitTime: 'Submission Time',
    createTime: 'Creation Time',
    tip: 'Tips',
    closeProductSuccess: 'Product closed successfully!',
    openProductSuccess: 'Product enabled successfully!',
    saveProductSuccess: 'Product saved successfully!',
    delProductSuccess: 'Product deleted successfully!',
    submit: 'Submit',
    sizeOneTip: 'File size: No more than 2MB',
    formatOneTip: 'File format: .jpg .png',
    addProduct: 'Add Product',
    closeConfirmTip: 'Confirm to close product [{name}]?',
    openConfirmTip: 'Confirm to enable product [{name}]?',
    delConfirmTip: 'Confirm to detele product [{name}]?',
    enabled: 'Enabled',
    closed: 'Closed',
    process: 'In Progress',
    rejected: 'Rejected',
    productFrontImgTip: 'Please upload card face image',
    productBackImgTip: 'Please upload card back image',
    clientProductUniqueCode: 'Customer Code',
    editPrice: 'Edit Price',
    priceType: 'Quotation Mode',
    ladderQuotation: 'Tiered Pricing',
    fixedOffer: 'Fixed Pricing',
    intervalStart: 'Range Start',
    intervalEnd: 'Range End',
    ladderPrice: 'Tiered Price',
    intervalPrice: 'Range Price',
    currency: 'Currency',
    currencyPlaceholder: 'Please select currency',
    intervalTip: 'Range cannot be blank',
    intervalPricePlaceholder: 'Please enter range price',
    intervalSymbol: 'Range Symbol',
    intervalSymbolPlaceholder: 'Please select range symbol',
    addRow: 'Add Row',
    equal: 'Equal',
    intervalBackTip: 'Range Start shall be larger than last Range End',
    intervalNextTip: 'Range End shall be larger than Range Start',
    priceTypePlaceholder: 'Please select quotation mode',
    editSuccess: 'Edited successfully',
    viewPrice: 'View Price'
  },
  diyCardProduct: {
    customerSelect: 'Select Customer',
    customerSelectPlaceholder: 'Please select customer',
    pattern: 'Template',
    patternPlaceholder: 'Please select template',
    selectPlaceholder: 'Please select',
    selectImgEditPlaceholder: 'Please select {name} image edit',
    cardSurfaceCustomization: 'Card Design Customization',
    cardOrganCustomization: 'Card Scheme Customization',
    cardColorCustomization: 'Card Body Color Customization',
    cardBackColorCustomization: 'Card Back Color Customization',
    cardLogoCustomization: 'Logo Customization',
    maskImg: 'Masking Image',
    maskImgPlaceholder: 'Please upload masking image',
    maskImgUploadPlaceholder: 'Upload masking image',
    frontImg: 'Card Face Image',
    settingRegion: 'Set Selected Area',
    top: 'Top',
    bottom: 'Bottom',
    left: 'Left',
    right: 'Right',
    frontImage: 'Card Face Image',
    backImage: 'Card Back Image',
    maskAndFrontImgCraft: 'Composite Image (Card Face Image with Masking Image)',
    maskImgCraft: 'Composite Image (Masking Image)',
    textureMaterial: 'Sticker Material',
    elementMaterial: 'Element Material',
    colorPhotoCard: 'Photo Card',
    cardImg: 'Card Face Sticker',
    cardBackImg: 'Card Back Image',
    uploadFrontImg: 'Upload Card Face Image',
    uploadBackImg: 'Please upload Card Back Image',
    uploadBackImgPlaceholder: 'Upload Card Back Image',
    halfImgCustomerSetRegion: 'Set customized upload area',
    uploadGroupImg: 'Upload Card Face Sticker Group',
    elementSplicing: 'Element Combination',
    uploadGroupElement: 'Upload Element Group',
    upload: 'Upload',
    reupload: 'Re-upload',
    halfImgSetRegion: 'Please set selected area',
    setWidth: 'Set Width',
    setRegion: 'Set Selected Area',
    setLocation: 'Set Position',
    setHight: 'Set Height',
    distanceTop: 'Set Top',
    distanceBottom: 'Set Bottom',
    distanceLeft: 'Set Left',
    distanceRight: 'Set Right',
    needTrueArrayTip: 'Please enter an object array as parameter',
    noWidth: 'Width of the first image is not set',
    noHight: 'Height of the first image is not set',
    priceAndUnit: 'Price',
    priceAndUnitPlaceholder: 'Please enter the price',
    originalPriceAndUnit: 'Original Price',
    originalPriceAndUnitPlaceholder: 'Please enter the original price',
    otherPriceAllocation: 'Other price setting',
    argumentName: 'Parameter Name',
    argumentNamePlaceholder: 'Please enter parameter name',
    argumentValue: 'Parameter Value',
    effectBeginTime: 'Effective Start Date',
    beginTime: 'Start Date',
    effectFinishDay: 'Effective End Date',
    finishTime: 'End Date',
    effectFinishTime: 'Effective End Date',
    operate: 'Actions',
    argumentValuePlaceholder: 'Please enter parameter value',
    argumentValueNoRepeat: 'Parameter value cannot be duplicated',
    effectBeginTimePlaceholder: 'Please select effective start date',
    effectBeginTimeNoRepeat: 'For the same parameter value, effective date cannot be overlapped',
    effectBeginTimeSmall: 'Effective start date shall be earlier than end date',
    effectBeginTimeOnlyOneNull: 'For the same parameter value, only 1 can be blank',
    effectFinishDayForeverSmall: 'End date shall be earlier than start date of permanent date',
    pricePlaceholder: 'Please enter the price',
    preview: 'Click to Preview',
    noImgOfGroup: 'No Image for this group',
    all: 'All',
    needImgAudit: 'Image Check Required',
    noImgAudit: 'Image Check NOT Required',
    imgAuditIdent: 'Image Check Identifier',
    imgAuditIdentPlaceholder: 'Please select image check identifier',
    bindImgAuditService: 'Bind Image Check Service',
    bindImgAuditServicePlaceholder: 'Please select and bind image check service',
    empty: 'None',
    remark: 'Remarks',
    remarkPlaceholder: 'Please enter remarks',
    serviceName: 'Service Name',
    serviceNamePlaceholder: 'Please select service',
    causeOfRejection: 'Rejection Reason',
    basicInfo: 'Basic Information',
    cardDraftCode: 'Card GSC Code',
    cardStyleName: 'Card Type Name',
    cardOtherOfCName: 'Card Type Alias (to Customer)',
    cardType: 'Card Type',
    cardIdent: 'Card Type Alias (to External)',
    subcategory: 'Category',
    customerProductName: 'Customer Product Name',
    customerProductCode: 'Customer Product Code',
    financeProductCode: 'Financial Product Code',
    ProductCode: 'Product Code',
    visibilityRange: 'Scope',
    allCity: 'All Cities',
    supplier: 'Supplier',
    productIntro: 'Product Description',
    designFile: 'Design File',
    download: 'Download',
    draftFile: 'Artwork File',
    file3D: '3D View File',
    customAttribute: 'Set Attribute',
    imgInfo: 'Image Information',
    noMaskImg: 'No Masking Image',
    viewSetRegion: 'View Selected Area',
    noBackImg: 'No Card Back Image',
    noFrontImg: 'No Card Face Image',
    noGroupImg: 'No Sticker',
    noElementSplicing: 'No Element Combination',
    setPrice: 'Price Setting',
    defaultTip: 'Tips: Default price for uneffective time',
    setImgAudit: 'Image Check Setting',
    viewHalfImgSetRegion: 'View customized upload area',
    fileDownloadErr: 'File downloading failed',
    viewProduct: 'View Product',
    fuzzySearchSelection: 'Search',
    quickAdd: 'Quick Add',
    chooseProductTip: 'Tips: You can try Information Quick Fill from similar product.',
    cardOtherOfCNamePlaceholder: 'Please enter card type alias (to customer)',
    cardTypePlaceholder: 'Please select card type',
    allImg: 'Full size photo customization',
    halfImg: 'Half size photo customization',
    cardIdentPlaceholder: 'Please enter card type alias (to external)',
    customerProductNamePlaceholder: 'Please enter customer product name',
    customerProductCodePlaceholder: 'Please enter customer product code',
    financeProductCodePlaceholder: 'Please enter financial product code',
    defaultSortPlaceholder: 'Please select the category',
    visibilityRangePlaceholder: 'Please select the scope of visibility',
    supplierPlaceholder: 'Please select the supplier',
    productIntroPlaceholder: 'Please enter the product description',
    cardDraftCodePlaceholder: 'Please select the card GSC code',
    cardStyleNamePlaceholder: 'Please select the card type name',
    colorImgTypePlaceholder: 'Please select the color photo card type',
    priceErr: 'Price must be greater than 0',
    defaultSort: 'Default Sorting',
    createImgSendErr: 'Error sending the composite image',
    addProduct: 'Add Product',
    editProduct: 'Edit Product',
    productStatus: 'Product Status',
    productStatusPlaceholder: 'Please select the product status',
    view: 'View',
    image: 'Image',
    submitConfirmTip: 'Confirm to submit product [{name}]?',
    delistConfirmTip: 'Confirm to remove product [{name}]?',
    delistSuccess: 'Product removed successfully!',
    submitSuccess: 'Product submitted successfully!',
    selectProjectPlaceholder: 'Please select the related project',
    cardAliasC: 'Card Type Alias',
    cardAliasCPlaceholder: 'Please enter the card type alias',
    cardDraftCodeTextPlaceholder: 'Please enter the card GSC code',
    submit: 'Submit',
    resubmit: 'Re-submit',
    horizontal: 'Landscape',
    vertical: 'Portrait',
    anomaly: 'Irregular'
  },
  // 样卡列表
  sampleCardList: {
    LeaderApproval: 'Leader Approval',
    ManagementApproval: 'Management Approval',
    SalesConfirmation: 'Sales Confirmation',
    Tobesubmitted: 'To be submitted',
    Completed: 'Completed',
    Cancelled: 'Cancelled',

    applyCode: 'Application No.',
    customerName: 'Customer Name',
    customerNameEnter: 'Please enter customer name',
    sampleCardApplicationType: 'Application Type',
    sampleCardApplicationTypeSelect: 'Please select type',
    orderApplicationStatus: 'Application Status',
    orderApplicationStatusSelect: 'Please select status',
    search: 'Search',
    reset: 'Reset',
    new: 'New Application',
    applyCode2: 'Application Form No.',
    orderDetial: 'Order detail',
    // 客户名称: '客户名称',
    createName: 'Applicant',
    saleUserName: 'Regional Sales',
    managerName: 'Regional  Director',
    // 申请状态: '申请状态',
    createDate: 'Created Date',
    actions: 'Actions',
    views: 'View',
    edit: 'Edit'
  },
  sampleCardEdit: {
    FreeSampleCard: 'Free Sample Cards',
    InboundSampleCard: 'Inventory Sample Cards',
    ChargeSampleCard: 'Paid Sample Cards',

    ApplicationForm: 'Application Form',
    ApplicationFormNo: 'Application Form No.',
    CustomerName: 'Customer Name',
    PleaseEnterCustomerName: 'Please enter customer name',
    ApplicationType: 'Application type',
    PleaseSelect: 'Please select',
    deliveryDate: 'Delivery Date',
    PleaseSelectDeliveryDate: 'Please select delivery date',
    urgentSign: 'Urgent Delivery',
    urgentReason: 'Urgent reason',
    PleaseEnterUrgentReason: 'Please enter urgent reason',
    saleUserName: 'salesName',
    saleUserTime: 'Sales Sumbit Date',
    managerTime: 'SalesManager Sumbit Date',
    managerName: 'SalesManager',
    ProductList: 'Product List',
    BatchImport: 'Batch Import',
    AddProducts: 'Add Products',
    DownloadTemplate: 'Download Template',
    ProductName: 'Product Name',
    CustomerProductCode: 'Customer Product Code',
    CardBaseNumber: 'Card Base Number',
    ApplicationQuantity: 'Application Quantity',
    ProjectNumber: 'Project Number',
    Remarks: 'Remarks',
    Actions: 'Actions',
    edit: 'Edit',
    delete: 'Delete',
    SampleCardElements: 'Sample Card Elements',
    //样卡要素radio
    // SampleCardFunction: 'Sample Card Function',
    None: 'None',
    NewCardConfirmation: 'New Card Confirmation',
    TenderSubmission: 'Tender Submission',
    InspectionCertification: 'Inspection/ Certification',
    SubmissionForApproval: 'Submission for Approval',
    Other: 'Other',

    SampleCardFunction: 'Sample Card Function',
    CardFrontPrintingElements: 'Card Front Printing Elements',

    //正面印和刷要素radio
    CardBaseImage: 'Card Base Image',
    ClientLogo: 'Client Logo',
    CardSchemeLogo: 'Card Scheme Logo',
    SpecialEffects: 'Special Effects',

    CardBackPrintingElements: 'Card Back Printing Elements',

    //背面印和刷要素 radio
    BackImage: 'Card Base Image',
    Text: 'Text',
    BackClientLogo: 'Client Logo',
    BackCardSchemeLogo: 'Card Scheme Logo',
    BackSpecialEffects: 'Special Effects',
    BlankWhiteBack: 'Blank White Back',

    OtherElements: 'Other Elements',
    //其他要素 radio
    SignaturePanel: 'Signature Panel',
    Hologram: 'Hologram',
    Magstripe: 'Magstripe',
    MetalSticker: 'Metal Sticker',
    HotStamping: 'Hot Stamping',
    FunctionalEMVChip: 'Functional EMV Chip',
    DummyEMVChip: 'Dummy EMV Chip',
    RealInlay: 'Real Inlay',

    PersonalisationRequirements: 'Personalisation Requirements',
    //个人化需求 radio
    CardSurfacePersonalisation: 'Card Surface Personalisation',
    MagstripePersonalisation: 'Magstripe Personalisation',
    ChipPrePersonalisation: 'Chip Pre-Personalisation',
    ChipPersonalisation: 'Chip Personalisation',

    PMTestingRequirement: 'PM Testing Requirement',
    //PM检测要求 radio
    MagstripePhysicalProperties: 'Magstripe Physical Properties',
    MagstripePersonalisationData: 'Magstripe Personalisation Data',
    ChipPhysicalProperties: 'Chip Physical Properties',
    COSVersionChip: 'COS Version Chip',
    ChipPersonalisationData: 'Chip Personalisation Data',

    SecurityMeasureRequirements: 'Security Measure Requirements',
    //安全处理要求radio
    Holepunch: 'Hole punch',
    VoidStamp: 'Void Stamp',
    ScratchedMagstripe: 'Scratched Magstripe',
    NoTreatment: 'No Treatment',

    Approva: 'Approva',
    Save: 'Save',
    Submit: 'Submit',
    Cancel: 'Cancel',
    Back: 'Back',
    PleaseEnterDeliveryDate: 'Please Enter Delivery Date',
    PleaseSelectSampleCardFunction: 'Please Select Sample Card Function',
    PleaseSelectCardFrontPrintingElements: 'Please Select Card Front Printing Elements',
    PleaseSelectCardBackPrintingElements: 'Please Select Card Back Printing Elements',
    PleaseSelectOtherElements: 'Please Select Other Elements',
    PleaseSelectPersonalisationRequirements: 'Please Select Personalisation Requirements',
    PleaseSelectPMTestingRequirement: 'Please Select PM Testing Requirement',
    SubmitSucess: 'Submit Sucess',
    SaveSucess: 'Save Sucess',
    PleaseSelectApprovaResut: 'Please Select Approva Resut',
    cancelApplication: 'Cancel Application?',
    Tips: 'Tips',
    Submit2: 'Submit',
    Cancel2: 'Cancel',
    CancelSuccess: 'Cancel Success',
    importTemplate: 'Import Template',
    ImportFail: 'Failed to import file. Please check if data format is correct!',
    ProducetNameMust: 'Product name is required',

    NoneSales: 'Unable to retrieve salesperson infomation.',
    NoneCustomer: 'Unable to retrieve customer infomation.',

    XlsHeaderInfos: {
      ProductName: 'Product Name',
      CustomerProductCode: 'Customer Product Code',
      GSCCode: 'Card Base Number',
      Quantity: 'Backup Quantity',
      ProjectNumber: 'Project Number',
      Remarks: 'Remarks'
    }
  },

  sampleProductDailog: {
    SampleCardApplicationProductFill: 'Sample card application product fill',
    ProductName: 'Product name',
    PleaseSelectProduct: 'Please enter the selected product',
    cardNumber: 'card number',
    PleaseEnterCardNumber: 'Please enter the card number',
    CustomerProductCode: 'Customer Product Code',
    ApplicationQuantity: 'Application Quantity',
    ProjectNumber: 'Project Number',
    Remarks: 'Remarks',
    PleaseEnterComments: 'Please enter comments',
    Close: 'Close',
    save: 'Save',
    correctQuantityValue: 'Please fill in the correct quantity value',
    PleaseSelectCustomer: 'Please select customer',
    UnableQueryproduct: 'Please select a customer. Unable to query the product'
  },

  preparationList: {
    customerName: 'Customer Name',
    customerNamePlaceholder: 'Please select customer name',
    ApplicationStatus: 'Application Status',
    ApplicationStatusSelect: 'Please select status',
    search: 'Search',
    reset: 'Reset',
    new: 'New Application',
    applyCode: 'Application Form No.',
    // 客户名称: '客户名称',
    createName: 'Applicant',
    saleUserName: 'Regional Sales',
    managerName: 'Regional  Director',
    LeaderApproval: 'Leader Approval',
    // 申请状态: '申请状态',
    createDate: 'Created Date',
    actions: 'Actions',
    views: 'View',
    edit: 'Edit'
  },
  preparationEdit: {
    ApplicationForm: 'Application Form',
    ApplicationFormNo: 'Application Form No.',
    CustomerName: 'Customer Name',
    PleaseEnterCustomerName: 'Please enter customer name',

    deliveryDate: 'Delivery Date',
    PleaseSelectDeliveryDate: 'Please select delivery date',
    UrgentDelivery: 'Urgent Delivery',
    urgentReason: 'Urgent reason',
    PleaseEnterUrgentReason: 'Please enter urgent reason',
    ReviewComments: 'Review Comments',
    PleaseEnterReviewComments: 'Please enter review comments',

    ProductList: 'Product List',
    DownloadTemplate: 'Download template',
    AddProducts: 'Add Products',
    BatchImport: 'Batch Import',
    ProductName: 'Product Name',
    CustomerProductCode: 'Customer Product Code',
    CardBaseNumber: 'Card Base Number',

    BackupType: 'Backup Type',
    BackupQuantity: 'Backup Quantity',
    BranchMessage: 'Branch Message',
    ProductType: 'Product Type',

    Remarks: 'Remarks',
    Actions: 'Actions',
    edit: 'Edit',
    delete: 'Delete',

    Approva: 'Approva',
    Save: 'Save',
    Submit: 'Submit',
    Cancel: 'Cancel',
    Back: 'Back',

    PleaseEnterDeliveryDate: 'Please Enter Delivery Date',
    SubmitSuccess: 'Submit success',
    SaveSuccess: 'Save success',
    PleaseSelectCommentsResult: 'Please select comments result',
    CancelApply: 'Cancel application?',
    Tips: 'Tips',
    Submit2: 'Confirm',
    Cancel2: 'Cancel',
    CancelSuccess: 'Cancel success',
    ImportBackupListTemplate: 'Backup list import template',
    ImportFileError: 'Importing file failed. Please check whether the data format is correct!',

    // finishedProductBackup: '成品备库',
    // semiProductBackup: '半成品备库',
    // CardProduct: '卡产品',
    // nonCardProduct: '非卡产品',
    // PleaseCheckBackupQuantity: '请检查备请检查备库数量，范围库数量',

    saleLeader: 'Sale Leader',
    LeaderConfirmationTime: 'Leader Confirmation Time',

    BackupTypeError: 'Backup Type Error',
    ProductTypeError: 'Product Type Error',
    QuantityError: 'Backup Quantity Error',
    QuantityRangeError: 'Backup Quantity Range Error',

    XlsHeaderInfos: {
      ProductName: 'Product Name',
      CustomerProductCode: 'Customer Product Code',
      GSCCode: 'Card Base Number',
      BackupType: 'Backup Type',
      Quantity: 'Backup Quantity',
      BranchInfo: 'Branch Message',
      ProductType: 'Product Type',
      Remarks: 'Remarks'
    }
  },
  preparationProductDialog: {
    FillBackupProduct: 'Fill Backup Product',
    productName: 'Product Name',
    PleaseSelectProduct: 'Please enter the selected product',
    cardNumber: 'card number',
    customerProductCode: 'Customer Product Code',
    BackupType: 'Backup Type',
    BackupQuantity: 'Backup Quantity',
    PleaseSelectType: 'Please select type',
    BranchMessage: 'Branch Message',
    ProductType: 'Product Type',
    Remarks: 'Remarks',
    PleaseEnterRemarks: 'Please enter remarks',
    Closed: 'Closed',
    Save: 'Save',
    // zero: '零',
    // '零', '一', '二', '三', '四', '五', '六', '七', '八', '九'九['', '十', '百', '千'] '万', '亿'
    // one: '一',
    // two: '二',
    // 三: '三',
    // 四: '四',
    // 五: '五',
    // 六: '六',
    // 七: '七',
    // 八: '八',
    // 九: '九',
    // 十: '十',
    // 百: '百',
    // 千: '千',
    // 万: '万',
    // 亿: '亿',
    PleaseEnterApplyQuantity: 'Please enter apply quantity',
    PleaseEnterCorrectQuantity: 'Please enter correct quantity',
    ApplyQuantityMorethanZero: 'Apply quantity must morethan zero',
    // PleaseSelectProduct: '请选择产品',
    PleaseSelectProductType: 'Please select product type',
    PleaseSelectCustomer: 'Please select customer'
  },
  typeData: {
    FreeSampleCard: 'Free Sample Cards',
    InboundSampleCard: 'Inventory Sample Cards',
    ChargeSampleCard: 'Paid Sample Cards',
    // 半成品备库: '半成品备库',
    // 卡产品: '卡产品',
    // 非卡产品: '非卡产品'
    finishedProductBackup: 'Finished product backup',
    semiProductBackup: 'Semi-finished product backup',
    CardProduct: 'Card product',
    nonCardProduct: 'Non card product',

    LeaderApproval: 'Leader Approval',
    ManagementApproval: 'Management Approval',
    SalesConfirmation: 'Sales Confirmation',
    Tobesubmitted: 'To be submitted',
    Completed: 'Completed',
    Cancelled: 'Cancelled',

    // SampleCardFunction: 'Sample Card Function',
    None: 'None',
    NewCardConfirmation: 'New Card Confirmation',
    TenderSubmission: 'Tender Submission',
    InspectionCertification: 'Inspection/ Certification',
    SubmissionForApproval: 'Submission for Approval',
    Other: 'Other',
    //正面印和刷要素radio
    CardBaseImage: 'Card Base Image',
    ClientLogo: 'Client Logo',
    CardSchemeLogo: 'Card Scheme Logo',
    SpecialEffects: 'Special Effects',

    //背面印和刷要素 radio
    BackImage: 'Card Base Image',
    Text: 'Text',
    BackClientLogo: 'Client Logo',
    BackCardSchemeLogo: 'Card Scheme Logo',
    BackSpecialEffects: 'Special Effects',
    BlankWhiteBack: 'Blank White Back',

    //其他要素 radio
    SignaturePanel: 'Signature Panel',
    Hologram: 'Hologram',
    Magstripe: 'Magstripe',
    MetalSticker: 'Metal Sticker',
    HotStamping: 'Hot Stamping',
    FunctionalEMVChip: 'Functional EMV Chip',
    DummyEMVChip: 'Dummy EMV Chip',
    RealInlay: 'Real Inlay',

    //个人化需求 radio
    CardSurfacePersonalisation: 'Card Surface Personalisation',
    MagstripePersonalisation: 'Magstripe Personalisation',
    ChipPrePersonalisation: 'Chip Pre-Personalisation',
    ChipPersonalisation: 'Chip Personalisation',

    //PM检测要求 radio
    MagstripePhysicalProperties: 'Magstripe Physical Properties',
    MagstripePersonalisationData: 'Magstripe Personalisation Data',
    ChipPhysicalProperties: 'Chip Physical Properties',
    COSVersionChip: 'COS Version Chip',
    ChipPersonalisationData: 'Chip Personalisation Data',

    //安全处理要求radio
    Holepunch: 'Hole punch',
    VoidStamp: 'Void Stamp',
    ScratchedMagstripe: 'Scratched Magstripe',
    NoTreatment: 'No Treatment'
  },

  appointSaleDialog: {
    selectedSaleMan: 'Select Approvaler',
    saleMen: 'Approvaler',
    PleaseSelectedSaleMan: 'Please select approver',
    closed: 'Closed',
    submit: 'Sublit',
    submitting: 'Submitting...'
  }
}
