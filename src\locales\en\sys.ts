/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-12-13 09:23:26
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-12-13 09:32:51
 * @Description:
 */
export default {
  api: {
    operationFailed: 'Actions failed',
    errorTip: 'Error Tip',
    errorMessage: 'The Actions failed, the system is abnormal!',
    timeoutMessage: 'Login timed out, please log in again!',
    apiTimeoutMessage: 'The interface request timed out, please refresh the page and try again!',
    apiRequestFailed: 'The interface request failed, please try again later!',
    networkException: 'network anomaly',
    networkExceptionMsg:
      'Please check if your network connection is normal! The network is abnormal',

    errMsg401: 'The user does not have permission (token, user name, password error)!',
    errMsg403: 'The user is authorized, but access is forbidden!',
    errMsg404: 'Network request error, the resource was not found!',
    errMsg405: 'Network request error, request method not allowed!',
    errMsg408: 'Network request timed out!',
    errMsg500: 'Server error, please contact the administrator!',
    errMsg501: 'The network is not implemented!',
    errMsg502: 'Network Error!',
    errMsg503: 'The service is unavailable, the server is temporarily overloaded or maintained!',
    errMsg504: 'Network timeout!',
    errMsg505: 'The http version does not support the request!',
    errMsg901: 'Demo mode, no write actions are possible!'
  },
  app: {
    logoutTip: 'Reminder',
    logoutMessage: 'Confirm to exit the system?',
    menuLoading: 'Menu loading...',
    loading: 'Loading…'
  },
  exception: {
    backLogin: 'Back Login',
    backHome: 'Back Home',
    subTitle403: "Sorry, you don't have access to this page.",
    subTitle404: 'Sorry, the page you visited does not exist.',
    subTitle500: 'Sorry, the server is reporting an error.',
    noDataTitle: 'No data on the current page.',
    networkErrorTitle: 'Network Error',
    networkErrorSubTitle:
      'Sorry, Your network connection has been disconnected, please check your network!'
  },
  lock: {
    unlock: 'Click to unlock',
    alert: 'Lock screen password error',
    backToLogin: 'Back to login',
    entry: 'Enter the system',
    placeholder: 'Please enter the lock screen password or user password'
  },
  login: {
    backSignIn: 'Back sign in',
    mobileSignInFormTitle: 'Mobile sign in',
    qrSignInFormTitle: 'Qr code sign in',
    ssoFormTitle: '3-party Authorization',
    signInFormTitle: 'Sign in',
    signUpFormTitle: 'Sign up',
    forgetFormTitle: 'Reset password',

    signInTitle: 'Backstage management system',
    signInDesc: 'Enter your personal details and get started!',
    policy: 'I agree to the xxx Privacy Policy',
    scanSign: `scanning the code to complete the login`,

    loginButton: 'Sign in',
    registerButton: 'Sign up',
    rememberMe: 'Remember me',
    forgetPassword: 'Forget Password?',
    otherSignIn: 'Sign in with',

    // notify
    loginSuccessTitle: 'Login successful',
    loginSuccessDesc: 'Welcome back',

    // placeholder
    accountPlaceholder: 'Please input username',
    passwordPlaceholder: 'Please input password',
    smsPlaceholder: 'Please input sms code',
    mobilePlaceholder: 'Please input mobile',
    policyPlaceholder: 'Register after checking',
    diffPwd: 'The two passwords are inconsistent',

    userName: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    email: 'Email',
    smsCode: 'SMS code',
    mobile: 'Mobile',
    ssoAuthTip: 'Single Login Authorized Callback Page',
    authCode: 'Authorization Code',
    usingCode: 'Now using authorization code, to get the access token',
    getToken: 'Get Token',
    loginFail: 'Login failed',
    ssoLoading: 'Getting access, please wait...'
  },
  permission: {
    hasPermission: 'Please set actions permission value',
    loginInvalid: 'Login is invalid'
  },
  errorCode: {
    code401: 'Authorization failed, cannot access system resource',
    code403: 'No permission for current actions',
    code404: 'Current resource does not exist',
    codeDefault: 'Unknown system error, please contact system administrator'
  },
  service: {
    invalidToken: 'Access token is invalid',
    expiredToken: 'Access token is expired',
    code901: 'Code 901, please contact system administrator',
    unFindRole: 'Cannot get user role, login is invalid, please login again!',
    loginInvalid: 'Login is invalid',
    pleaseRelogin: 'Please login again!'
  },
  hooks: {
    web: {
      validfail: 'Verification failed',
      pleaseEnterCrrentPhoneNum: 'please enter crrentPhoneNum'
    }
  },
  layout: {
    emailDropdown: {
      personalCenter: 'User Center',
      changePassword: 'Change Password',
      unbindEmail: 'No email bound'
    },
    roleDropdown: {
      roleOverdue: 'Current role is expired, please contact system administrator to allocate again',
      checkingRole: 'Switching role',
      checkingRoleTip: 'Confirm to switch role? UI and application will be changed accordingly.',
      checkingRoleFail: 'Failed to switch role',
      unallocatedRole: 'Role is not allocated'
    },
    userInfo: {
      noUserName: 'No User Name',
      noDeptData: 'No Department Data'
    },
    collapse: {
      collapse: 'Collapse menu bar',
      expand: 'Expand menu bar',
      tips: 'The screen width is too small. For a better experience, the menu has been collapsed. Click the icon in the upper left corner to expand the menu'
    }
  },
  utils: {
    formatTime: {
      just: 'Just',
      beforeSec: 'Second(s) before',
      beforeMin: 'Minute(s) before',
      beforeHour: 'Hour(s) before',
      beforeDay: 'Day(s) before',

      goodearlyMorning: 'Good Evening',
      goodMorning: 'Good Morning',
      goodforenoon: 'Good Morning',
      goodnoon: 'Good Afternoon',
      goodafternoon: 'Good Afternoon',

      gooddusk: 'Good Evening',
      goodevening: 'Good Evening',
      goodLateNight: 'Good Evening',

      day: 'Day(s)',
      hour: 'Hour(s)',
      min: 'Minute(s)',
      sec: 'Second(s)'
    }
  },
  AccountControl: {
    accountInformation: 'Account Information',
    enterpriseInformation: 'Enterprise Information',
    username: 'Account',
    mobile: 'Mobile Number',
    email: 'Email',
    nickName: 'Employee Name',
    deptName: 'Department',
    createTime: 'Registration Time',
    nodata: 'No data',
    uscc: 'Social Credit Code',
    customerNameNodata: 'Enterprise Name has no data',
    cusRelated: 'Related Enterprise/Institution',
    deptRelated: 'Department Relationship',
    deptNodata: 'No department data',
    openServices: 'Available Features',
    clearingForm: 'Clearing Method',
    noTopDept: 'No Top Department'
  },
  footer: {
    Copyright:
      'Copyright © 2024- Zhirong Financial Services Technology (Zhuhai) Co., Ltd. All Rights Reserved',
    icp: 'Guangdong ICP no. ********'
  }
}
