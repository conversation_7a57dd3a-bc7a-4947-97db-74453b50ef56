export default {
  common: {
    title: 'Title',
    time: 'Time',
    sortNum: 'Number',
    jumpTo: 'Jump to',
    select: 'Select',
    selected: 'Selected',
    startDate: 'Start Date',
    endDate: 'End Date',
    status: 'Status',
    createTime: 'Created Date',
    refresh: 'Refresh',
    detail: 'Details',
    reason: 'Reason',
    deal: 'Deal',
    pass: 'Pass',
    notPass: 'Not Pass',
    transfer: 'Transfer',
    delegate: 'Delegate',
    copy: 'Copy',
    task: 'Task',
    processing: 'Processing',
    more: 'More',
    new: 'New'
  },
  businessTodo: {
    productApproval: 'Product Approval',
    contractApproval: 'Contract Approval',
    imageApproval: 'Image Approval',
    prodValidate: 'Production Validation',
    markCardDemand: 'Card Making Demand',
    designScheme: 'Design Scheme',
    designArchive: 'Design Archive',
    draftScheme: 'Draft Scheme',
    draftArchive: 'Draft Archive',
    todoType: 'Todo Type',
    initiator: 'Initiator',
    receiveTime: 'Receive Time',
    transfer: 'Transfer',
    myTransfer: 'My Transfers',
    transferMy: 'Transferred to Me',
    transferPerson: 'Transfer Person',
    transferedPerson: 'Transferred To',
    transferType: 'Transfer Type',
    initiationTime: 'Initiation Time',
    total: 'Total',
    totalNum: 'Total Records',
    totalNo: 'Number',
    pageNo: 'Page',
    todoList: 'Business Todo List',
    transferRecord: 'Transfer Record'
  },
  components: {
    transferLog: 'Log',
    staffId: 'Staff ID',
    canNotTransferTips: 'Cannot transfer to the current logged-in user',
    staffName: 'Staff Name',
    selectStaff: 'Select Staff',
    transferReason: 'Transfer Reason'
  },
  flowTodo: {
    flowStatus: 'Flow Status',
    copyTime: 'Copy Time',
    taskName: 'Task Name',
    taskId: 'Task ID',
    flowName: 'Flow Name',
    taskAssigneeUserNickname: 'Delegatee',
    taskOwnerUserNickname: 'Delegator',
    myTaskAssignee: 'My Delegations',
    toTaskAssignee: 'Delegated To',
    approvalId: 'Approval ID',
    flowTitle: 'Flow Title',
    operateStep: 'Actions Step',
    flowInitiator: 'Flow Initiator',
    taskReason: 'Delegation Reason',
    dealTime: 'Deal Time',
    taskStatus: 'Task Status',
    flowBelong: 'Belonging Flow',
    isNeed: 'Is Needed',
    application: 'Application',
    activate: 'Activate',
    unActivate: 'Suspend',
    baseInfo: 'Basic Info',
    flowLog: 'Flow Log',
    flowChart: 'Flow Chart',
    approvalTask: 'Approval Task',
    flowName2: 'Flow',
    taskTo: 'Delegate Task',
    taskToWho: 'Delegate To',
    approvalSuggest: 'Approval Suggestion',
    copyPerson: 'Copied To',
    applicateInfo: 'Application Info',
    flowBindTips:
      'The process is not bound to a business form. Please bind it on the integrated service platform.',
    notBindForm: 'Form is not bound yet',
    approvalSuggestTips: 'Approval suggestion cannot be empty',
    approvalPassSuccess: 'Approval passed successfully',
    approvalPassFailed: 'Approval failed successfully',
    searchFlowInfoNull: 'No process information found',
    flowBindCompTips:
      'There is an error in binding the process to the business component form. Please check if the component path exists.',
    approvalRecord: 'Approval Record',
    approvalUser: 'Approver',
    operateAction: 'Actions',
    nodeName: 'Node Name',
    approvalTime: 'Approval Time',
    useTime: 'Duration',
    operateUser: 'Operator',
    noDepartment: 'No Department Yet',
    taskToDeal: 'Task Pending',
    approvalSuggestNull: 'Approval Suggestion is empty',
    nextNodeApprovalUser: 'Next Node Approver Settings',
    ruleType: 'Rule Type',
    specifyRole: 'Specify Role',
    specifyDepartment: 'Specify Department',
    loadingWait: 'Loading, please wait',
    specifyPost: 'Specify Position',
    specifyUser: 'Specify User',
    specifyUserGroup: 'Specify User Group',
    specifyScript: 'Specify Script',

    ruleTypeTips: 'Rule type cannot be empty',
    specifyRoleTips: 'Specified role cannot be empty',
    specifyDepartmentTips: 'Specified department cannot be empty',
    specifyPostTips: 'Specified position cannot be empty',
    specifyUserTips: 'Specified user cannot be empty',
    specifyUserGroupTips: 'Specified user group cannot be empty',
    specifyScriptTips: 'Specified script cannot be empty',

    newApprovalUser: 'New Approver',
    newApprovalUserTips: 'New approver cannot be empty',

    myTodo: 'My To-Dos',
    myTodoDone: 'My Completed',
    myInitiated: 'Initiated by Me',
    myCC: 'Copied to Me',

    flowCannotJump: 'This process task cannot be jumped',
    taskCannotJump: 'This business task cannot be jumped',
    noTodo: 'No To-Dos Yet',
    flowTodo: 'Process To-Dos',
    myMessage: 'My Messages',
    noMessage: 'No Messages Yet'
  }
}
