export default {
  orderApproval: {
    orderNodeStatus: '订单签审流程节点状态',
    customerName: '客户名称',
    productName: '产品名称',
    nodeName: '节点名称',
    handler: '处理人员',
    handlerTime: '处理时间',
    placeholderTextTip: '请输入{placeholder}',
    placeholderSelectTip: '请选择{placeholder}',
    taskCode: '任务编号',
    salesman: '业务员',
    orderType: '订单类型',
    auditStatus: '签审状态',
    unfold: '展开',
    packUp: '收起',
    unaudited: '未签审',
    audited: '已签审',
    unSaleReview: '未签',
    saleReview: '已签',
    batchOrder: '批量订单',
    freeSampleCard: '免费样卡',
    chargeSampleCard: '收费样卡',
    finishedProductWarehousing: '成品备库',
    sampleCardWarehousing: '入库样卡',
    semiWarehousing: '半成品备库',
    orderNum: '下单数量',
    audit: '签审',
    viewDetail: '查看详情',
    deliveryTime: '交付时间',
    deliveryMode: '交付方式',
    orderCreator: '订单创建人',
    orderCreationTime: '订单创建时间',
    processNode: '流程节点',
    orderAudit: '订单签审',
    orderInfo: '订单信息',
    taskStatus: '任务状态',
    orderCode: '订单编号',
    orderProductRemark: '产品备注',
    orderRemark: '下单备注',
    submitSuccess: '提交成功',
    cardStyleInfo: '卡款信息',
    cardStyleName: '卡款名称',
    mainCardNum: '主卡号',
    cardStyleCode: 'GSC卡号',
    cardStyleCodeAll: 'GSC卡号全称',
    latestK3Code: 'K3下单编号',

    batchTime: '卡号月年',
    cardPlanNum: '卡方案数',
    enCapType: '封装类型',
    finishedCardModelModule: '成品卡款模块',
    semiCardModelModule: '半成品卡款模块',
    chipType: '芯片类型',
    trueChip: '真芯片',
    dummyChip: '假芯片',
    noChip: '无芯片',
    chipCode: '芯片型号',
    chipSupplier: '供应商',
    moduleTransferInterfaceType: '界面类型',
    maxCode: '模块大号',

    color: '颜色',
    capacity: '容量',
    maskCode: '掩模号',
    shape: '模块外形',
    oiOrModuleCode: 'OI或模块代码',
    antenna: '天线',
    aerialType: '天线类型',

    property: '产权属性',
    stripe: '模块条带',
    chipCapacitance: '芯片电容',

    finishedStock: '成品库存量',
    semifinishedStock: '半成品库存量',
    cardOrgUsableFlag: '卡组织复用',
    reusable: '可复用',
    nonReusable: '不可复用',
    primaryFlag: '主卡号标识',
    yes: '是',
    no: '否',
    finalDrafts: '稿样终稿图片',
    cardNoteList: '版本变更履历',
    cardReviewResultList: '评审记录',

    auditRes: '评审结果',
    auditRemark: '评审意见',
    handleTime: '处理时间'
  },
  orderSearch: {
    fileList: '下单凭证',
    orderInfo: '订单信息',
    customerInfo: '客户信息',
    productInfo: '产品信息',
    download: '下载',
    unitPrice: '单价',
    productName: '产品名称',
    cardCount: '数量',
    isIndividual: '是否写入个人化物料',
    yes: '是',
    no: '否',
    empty: '暂无数据',
    mailAddress: '邮寄地址',
    logisticsInfo: '物流信息',
    cardDeliveryDate: '卡商预计交付日期',
    mailNo: '运单信息',
    OrderStatus: '订单状态',
    logisticsTracking: '物流跟踪',
    isUrgent: '是否加急',
    deliveryMethod: '交付方式',
    orderSource: '订单来源',
    customerCreateOrder: '下单客户',
    orderTotalPrice: '订单总价',
    deliveryTime: '期望交付时间',
    orderType: '订单类型',
    customerOrderReceiveTime: '客户订单接收时间',
    ordered: '已下单',
    selectCustomerPlaceholder: '请选择客户',
    management: '管理端',
    customer: '客户端',
    sale: '销售端',
    tip: '提示',
    customerName: '客户名称',
    all: '全部',
    revocationOrderConfirm: '是否撤销订单?',
    orderRevocationSuccess: '订单撤销成功',
    UMVOrderCodePlaceholder: '请输入UMV订单编号',
    productNamePlaceholder: '请输入产品名称',
    cardProductList: '卡产品订单列表',
    orderWrite: '订单录入',
    UMVOrderCode: 'UMV订单编号',
    more: '更多',
    productNum: '产品数量',
    price: '价格',
    source: '来源',
    updateTime: '更新时间',
    createTime: '创建时间',
    orderCode: '订单编号',
    view: '查看',
    revocation: '撤销',
    remarkNote: '备注说明',
    addressOfPeople: '联系人',
    tel: '联系电话',
    creator: '创建人',
    creatorAcc: '创建人账号',
    storage: '入库代存',
    customerPick: '自提',
    mail: '邮寄',
    customerOrderCode: '客户订单编号',
    salesman: '销售人员',
    salesmanPlaceholder: '请输入销售人员',
    completionDate: '完成时间',
    cardOrganization: '卡组织',

    //Excel模板表头，需要与模板中一致, 如用中文模板上传则不需要翻译
    excelHeaderOfProductName: '产品名称',
    excelHeaderOfNum: '下单数量',
    excelHeaderOfType: '类型',
    excelHeaderOfDeliveryTime: '交付时间',
    excelHeaderOfDeliveryMethod: '交付方式',
    excelHeaderOfPackageMode: '包装方式',
    excelHeaderOfInnerBox: '内盒',
    excelHeaderOfOuterBox: '外箱',
    excelHeaderOfProductPrice: '产品单价',
    excelHeaderOfWrite31: '写入31物料',
    excelHeaderOfCompanyCode: '我司代码',
    excelHeaderOfCustomerCode: '客户代码',
    excelHeaderOfRemark: '备注',
    //Excel模板卡产品类型，需要与模板中一致, 如用中文模板上传则不需要翻译
    excelCardProduct: '卡产品',
    excelNonCardProduct: '非卡产品',
    //Excel模板是否写入个人化，需要与模板中一致, 如用中文模板上传则不需要翻译
    excelWrite: '写入',
    taskCode: '任务编号',

    viewMyself: '只看自己',
    saleConfirm: '销售确认',
    orderCodeNullTip: '订单编号不能为空,请重新选择',

    noStatus: '暂无状态',
    createTimeNotNull: '创建时间不能为空'
  },
  proxyCustomerToOrder: {
    productName: '产品名称',
    unitPrice: '单价',
    noNullForCustomer: '所属客户不能为空',
    noNullForAddress: '地址名称不能为空',
    noNullForPeople: '收件人不能为空',
    noNullForPhone: '联系电话不能为空',
    noNullForMailAddress: '寄送地址不能为空',
    addSuccess: '地址成功', // 前面跟新增或编辑
    addErr: '地址失败', // 前面跟新增或编辑
    receivingInfo: '收件信息', // 前面跟新增或编辑
    addressName: '地址名称',
    addressNamePlaceholder: '请输入地址名称',
    area: '寄送地址',
    areaPlaceholder: '请输入寄送地址',
    orderNum: '下单数量',
    cardCode: 'GSC卡号',
    type: '类型',
    companyCode: '我司代码',
    chooseDayTime: '选择日期时间',
    productUnitPrice: '产品单价',
    customerProuductCode: '客户代码',
    customerCode: '客户代码',
    write31Materiel: '是否写入31物料',
    outerBox: '外箱',
    beingSubmitted: '提交中',
    inputOrderNumPlaceholder: '请输入下单数量',
    orderNumSizeTip: '下单数量必须大于零',
    cardProduct: '卡产品',
    nonCardProduct: '非卡产品',
    theCustomerNoProduct: '该客户下查询不到产品',
    selectProductPlaceholder: '请选择产品',
    selectGoodPlaceholder: '选择商品',
    greaterThan: '大于',
    lessThan: '小于',
    inventory: '库存',
    namePlaceholder: '请输入选择名称',
    userPlaceholder: '请输入选择收件人',
    uploadErr: '上传失败',
    createSuccess: '创建成功',
    selectOrderType: '请选择订单类型',
    selectTime: '请选择时间',
    uploadLimit: '上传超出限制,终止上传',
    urgentExplain: '加急说明',
    urgentExplainPlaceholder: '请输入加急说明',
    customerCodePlaceholder: '请输入客户订单编号',
    addOrderProduct: '添加下单产品',
    inputCardNamePlaceholder: '请输入卡款名称',
    chooseProduct: '选择产品',
    writeProduct: '录入产品',
    importProduct: '导入产品',
    write: '写入',
    nonWrite: '不写入',
    remark: '备注信息',
    remarkPlaceholder: '请输入备注信息',
    orderTypeNotNull: '订单类型不能为空',
    customerOrderReceiveTimeNotNull: '客户订单接收时间不能为空',
    urgentNotNull: '是否加急不能为空',
    urgentExplainNotNull: '加急说明不能为空',
    customerNameNotNull: '客户名称不能为空',
    deliveryTimeNotNull: '期望交付时间不能为空',
    userNotNull: '联系人不能为空',
    deliveryMethodNotNull: '交付方式不能为空',
    fileListNotNull: '下单凭证不能为空',
    pleaseInputRequired: '请输入必填项!',
    productNotNull: '下单产品不能为空',
    productNumNotNull: '请填写产品数量',
    uploadLimitReUpload: '文件大小超出限制,请重新上传！',
    checkFileFormat: '请检查附件格式重新上传！',
    importFileErr: '导入文件失败，请检查数据格式是否正确！',
    checkOrderNum: '请检查下单数量，范围1-{maxNum}',
    inputNumInfo: '请填写完整数量信息',
    inputPriceInfo: '请填写完整单价信息',
    numNoTrue: '数量只能为正整数',
    priceNoTrue: '单价格式不正确',
    deliveryTimeFormatNoTrue: '期望交付时间格式不正确，请输入YYYY-MM-DD格式',

    createOrder: '立刻下单',
    orderSaveSuccess: '订单暂存成功\n是否离开当前页面？',
    hasSaveOrder: '存在暂存订单，是否恢复？',

    salesman: '业务员（销售）',
    salesmanTip: '请选择业务员（销售）',
    cardAddPersion: '卡款添加人',
    cardAddPersionTip: '请选择卡款添加人',
    moreTip: '上传超出限制,终止上传',
    exportOrderFileName: '订单录入产品导入模版',

    endCustomer: '终端客户',
    endCustomerPlaceholder: '请选择终端客户',

    terminalSelectIsNull: '终端客户不能为空',

    totalPrice: '总价'
  }
}
