export default {
  batchCardProduct: {
    merchantCardDetail: {
      cardAssociations: '卡组织',
      cardCode: 'GSC卡号', //卡款代码改GSC卡号
      cardDetail: '获取卡款产品详情失败',
      cardType: '卡款类型',
      cardFaceSpec: '卡面规格',
      equalTo: '等于',
      equalToLessThan: '小于等于',
      greaterThanOrEqual: '大于等于',
      productNum: '客户代码', //产品编号改客户代码
      quickOrder: '快速下单',
      viewSampleFile: '查看稿样',
      withoutSampleFile: '未确定最终稿',
      notSampleFile: '暂无稿样',
      historyOrder: '历史订单',
      orderCode: '订单编号',
      orderPrice: '订单价格',
      orderProductNum: '数量',
      orderTime: '时间',
      shoppingCart: '加入购物车',
      successfully: '加入购物车成功',
      support: '支持',
      surfaceBack: '表面特性背面',
      surfacePositive: '表面特性正面',
      technology: '工艺',
      unitPrice: '单价',
      updateTime: '更新时间',
      createTime: '创建时间',
      otherVersion: '其他版本',
      priceRange: '区间',
      quantity: '数量',
      sheet: '张',
      up: '以上',

      latestProduceVersion: '最后生产稿样版本',
      latestGSCVersion: '最新稿样版本'
    },
    merchantCardList: {
      addProduct: '新增产品',
      addShoppingCar: '加入购物车',
      batchOrder: '批量下单',
      closed: '已关闭',
      components: {
        detailDialog: {
          cannotEmpty: '不能为空！',
          pleaseEnter: '请输入',
          saleName: '产品名称'
        },
        search: {
          productName: '产品名称',
          productStatus: '产品状态'
        }
      },
      craftFiles: '规格书',
      createTime: '创建时间',
      currentNone: '暂无',
      detail: '详情',
      downloadCraftFiles: '下载规格书',
      downloadCraftFilesAbnormal: '下载规格书异常：',
      downloadCraftFilesFail: '下载规格书失败!',
      noClientIdTips: '该用户无客户Id,请联系管理员',
      orderNumber: '序号',
      productBackView: '产品背面图',
      productFrontView: '产品正面图',
      productName: '产品名称',
      clientProductUniqueCode: '客户代码', //产品编号改客户代码
      productCode: 'GSC卡号', //卡款代码改GSC卡号
      quickOrder: '快速下单',
      shoppingCart: '加入购物车成功',
      startUse: '启用',
      startUsed: '已启用',
      status: '状态',
      toBeDesigned: '待设计',
      updateTime: '更新时间',
      searchProductName: '搜索产品名称',
      searchProductCode: '搜索客户代码', //产品编号改客户代码
      searchCardCode: '搜索GSC卡号', //卡款代码改GSC卡号
      selectProductStatus: '选择产品状态',
      downloadTemplateFile: '模板下载',
      importCreateOrder: '导入下单',
      orderTemplateFile: '下单模板文件',
      excelFormatError: '导入文件失败，请检查数据格式是否正确！',
      notFindProduct: '未找到产品',
      createOrder: '创建订单'
    },
    orderConfirm: {
      addAddress: '新增地址',
      address: '收获地址不能为空',
      address1: '地址',
      addressName: '地址名称',
      components: {
        AddAddressDialog: {
          add: '新增',
          address: '地址名称',
          address1: '请输入地址名称',
          addressName: '地址名称不能为空',
          belonging: '所属客户不能为空',
          default: '是否为默认不能为空',
          defaultOrNot: '是否为默认',
          detailAddress: '请输入详细地址',
          edit: '编辑',
          enterRecipient: '请输入收件人',
          enterTelephone: '请输入手机号',
          mailing: '寄送地址不能为空',
          name: '收件人',
          no: '否',
          phone: '手机号',
          receiving: '收件信息',
          recipient: '收件人不能为空',
          sendAddress: '寄送地址',
          telephone: '手机号不能为空',
          yes: '是'
        },
        SelectAddressDialog: {
          address: '收件地址',
          name: '名称',
          phone: '手机号',
          recipient: '请输入选择收件人',
          recipient1: '收件人',
          selectAddress: '选择地址',
          selectName: '请输入选择名称'
        },
        AddProductDialog: {
          products: '产品信息',
          productionName: '产品名称',
          inputProductionName: '请输入产品名称',

          order: '订购数量',
          inputOrderNum: '请输入订购数量',
          productCode: '客户代码', //产品编号改客户代码
          inputProductCode: '请输入产品代码',

          cardNumber: 'GSC卡号', //卡款编号改GSC卡号
          inputCardNumber: '请输入GSC卡号', //卡款编号改GSC卡号

          unitPrice: '单价',
          inputUnitPrice: '请输入单价',
          close: '关 闭',
          select: '选 择'
        },
        productList: {
          no: '否',
          order: '订购数量',
          personalized: '是否写入个人化物料',
          products: '产品信息',
          productCode: '客户代码', //产品编号改客户代码
          cardNumber: 'GSC卡号', //卡款编号改GSC卡号
          draftFile: '稿样文件',
          approvalFile: '报批文件',
          unitPrice: '单价',
          yes: '是',
          uploadApprovalFile: '报批文件上传',
          uploadCardApprovalAbnormal: '报批文件上传异常：',
          download: '点击下载',
          minProductCount: '不能小于最小下单数量'
        },
        upload: {
          clickAdd: '点此添加',
          dragging: '拖拽图片到这里，或',
          exceeding: '超出上传文件数量限制！',
          fileType: '文件数量最多8个，大小10M以内，文件格式：.jpg、.png、.gif、.pdf',
          limit: '文件大小超出限制, 请重新上传！'
        }
      },
      contactWay: '联系方式',
      contacts: '联系人不能为空',
      contacts1: '联系人',
      delivery: '交付信息',
      deliveryMethod: '交付方式不能为空',
      deliveryMethods: '交付方式',
      enterContact: '请输入联系人',
      enterTelephone: '请输入联系电话',
      expectedDelivery: '期望交付时间不能为空',
      expectedDeliveryTime: '期望交付时间',
      fix: '张',
      individual: '个',
      inventory: '入库代存',
      isItUrgent: '是否加急',
      mail: '邮寄',
      mailing: '邮寄方式不能为空',
      mailingAddress: '寄送地址产品数量与购买数量不一致，请修改后再次提交',
      memo: '备注信息',
      no: '否',
      number: '数量',
      orderCreation: '下单创建成功',
      orderNow: '立即下单',
      orderVoucher: '下单凭证',
      orderingProducts: '下单产品不能为空',
      pickup: '自提',
      pleaseEnter: '请输入必填项',
      product: '产品',
      recipient: '收件人',
      selectAddress: '选择地址',
      shippingMethod: '请选择邮寄方式',
      telephone: '联系电话不能为空',
      telephone1: '联系电话',
      total: '订单总价',
      urgent: '是否加急不能为空',
      yes: '是',
      communityWithSale: '邮寄(具体安排与销售沟通)',
      sendToPersonalizationCenter: '送个人化中心',
      other: '其他',
      enterOtherContact: '请输入备注信息',
      orderVoucherDesc: '下单凭证不能为空',
      notListed: '未启用',
      addProduct: '产品录入'
    },
    orderSuccess: {
      congratulations: '恭喜你下单成功！',
      goTo: '您可以前往',
      online: '[在线下单]',
      order: '[订单管理]'
    },
    shoppingCart: {
      orderQuantity: '订购数量',
      placeAnOrder: '下单',
      pleaseSelectOrder: '请勾选商品下单',
      price: '价格',
      product: '产品',
      productName: '产品名称',
      quantity: '数量',
      sheet: '张',
      unitPrice: '单价',
      notListed: '未启用',
      cardCode: 'GSC卡号', //卡款代码改GSC卡号
      productNum: '客户代码', //产品编号改客户代码
      unit: '个'
    }
  },
  mailingAddress: {
    address: '寄送地址',
    addressName: '地址名称',
    addressee: '收件人',
    components: {
      search: {
        addAddress: '新增地址',
        refresh: '刷新',
        searchAddress: '搜索地址名称',
        searchRecipient: '搜索收件人名称'
      }
    },
    defaultAddress: '是否默认地址',
    delAddress: '确定要删除当前地址吗?',
    delSuccess: '删除成功',
    phone: '联系电话',
    serialNumber: '序号',
    setSuccess: '设置成功'
  },
  productDemand: {
    common: {
      batchCardProducts: '批卡产品',
      cardRequirements: '产品需求',
      client: '客户端',
      confirmThePlan: '确认方案',
      designScheme: '设计方案',
      diyProducts: 'DIY产品',
      doYouWantToPerformThisOperation: '是否执行此操作？',
      editProductRequirements: '编辑产品需求',
      initiateProductRequirements: '发起产品需求',
      managementPlatform: '管理平台',
      other: '其他',
      prompt: '提示',
      requirementClosure: '需求关闭',
      sampleCardApplication: '样卡申请',
      sampleScheme: '稿样方案',
      unionPay: '银联',
      viewDesignProposal: '查看设计方案',
      viewDraftProposal: '查看稿样方案',
      viewReceiptInformation: '查看回执信息',
      view_3DFiles: '查看3D文件'
    },
    components: {
      cardBin: '卡BIN',
      cardOrganizationAndCardLevel: '卡组织及卡片级别',
      cardType: '卡款种类',
      cardTypes: '卡片类型',
      customerName: '客户名称',
      customerAccount: '客户账号',
      customerUploadsFiles: '客户文件',
      demand: {
        cardBin: '卡BIN',
        customerDocuments: '客户文件',
        designRequirements: '设计需求',
        editSuccessful: '编辑成功',
        pleaseEnterARequirementDescription: '请输入需求描述',
        pleaseEnterATitle: '请输入标题',
        pleaseEnterTheCardBin: '请输入卡BIN',
        pleaseEnterTheProductSelection: '请输入选择产品',
        productName: '产品名称',
        requirementDescription: '需求描述',
        requirementTitle: '需求标题',
        requirementType: '需求类型',
        rulesTips: '内容不能为空，请输入信息！',
        sampleRequirements: '稿样需求',
        selectFile: '选择文件',
        successfullyAdded: '添加成功',
        uploadSuccessful: '上传成功',
        // uploadTips: '文件大小100M以内，文件格式 .txt .doc .xlsx .jpg .png .gif .pdf .rar .zip .ppt'
        uploadTips:
          '文件数量最多8个，单个文件不超过100M，文件格式.text.doc.xlsx.jpg.png.gif.pdf.rar.zip.ppt等'
      },
      designRequirements: '设计需求',
      dialogModule: {
        attachment: '附件',
        cardName: '卡款名称',
        cardPaymentCode: 'GSC卡号', //卡款代码改GSC卡号
        check: '查看',
        confirmedSuccessful: '确认成功',
        designDescription: '设计说明',
        designScheme: '设计方案',
        display: '3D展示',
        download: '下载',
        downloadAll: '全部下载',
        msgTips: '是否确认选定此方案为最终方案，确认后将不可修改，请谨慎操作！',
        pleaseEnterReceiptInformation: '请输入回执信息',
        receiptInformation: '回执信息',
        remarks: '稿样说明',
        sampleFile: '稿样文件',
        otherFile: '其他文件',
        submissionTime: '提交时间',
        uploadAttachments: '上传附件',
        proposalComments: '方案意见',
        agree: '同意',
        reject: '驳回',
        stillNeedModify: '仍需修改',
        modifyOpinion: '修改意见',
        addFeedback: '追加反馈',
        round: '轮次',
        CreateTime: '创建时间',
        NeedToModify: '仍需修改',
        confirmPlan: '确认方案',
        feedback: '追加反馈',
        viewReceipts: '查看回执',
        customerFeedback: '客户反馈',
        number: '序号',
        feedbackInformation: '反馈信息',
        attachmentInformation: '附件信息',
        feedbackTime: '反馈时间',
        confirmed: '已确认',
        toBeConfirmed: '待确认',
        suggestion: '修改意见',
        pleaseEnter: '请输入',
        confirmPlanTips: '是否确认选定此方案作为最终方案，确认后将不可修改，请慎重操作！',
        // 新增中英文
        videoFile: '视频文件',
        viewVideoFiles: '查看视频文件	',
        tDAddress: ' 3D地址',
        view3DDisplay: '查看3D展示'
      },
      download: '下载',
      expectedSubmissionDate: '预计提稿日期',
      fileUploadFailed: '文件上传失败',
      iMessage: {
        characters: '文字',
        clearImage: '清空图片',
        complete: '完成',
        empty: '清空',
        errrotTips1: '登录服务器失败，请联系管理员!',
        errrotTips2: '连接服务器失败，请联系管理员！',
        errrotTips3: '服务器失去响应，请联系管理员！',
        errrotTips4: '链接失败，正在尝试重新连接！',
        fileUploadFailed: '文件上传失败',
        graffitiPen: '涂鸦笔',
        imageEditingArea: '图片编辑区',
        interactiveRecording: '互动记录',
        kjNovaClipper: '图片裁剪',
        loading: '加载中',
        onLine: '在线',
        onlineCommunication: '在线沟通',
        pleaseEnterText: '请输入文字',
        pleaseEnterTheContent: '请输入内容',
        pleaseInput: '请输入互动内容',
        preserve: '保存',
        rectangle: '矩形',
        rotateImage: '旋转图片',
        rotundity: '圆形',
        rubberEraser: '橡皮檫',
        saveImage: '保存图片',
        send: '发送',
        sendDirectly: '直接发送',
        sendPictures: '发送图片',
        thereSNothingMoreLeft: '没有更多了',
        uploadImages: '上传图片'
      },
      otherAttachments: '其他附件',
      otherInstructions: '其他说明',
      pleaseEnterTheProductName: '请输入产品名称',
      pleaseEnterTheRequirementType: '请输入需求类型',
      productName: '产品名称',
      productType: '产品类型',
      relatedProjects: '关联项目',
      requirementDescription: '需求描述',
      requirementNumber: '需求编号',
      requirementTitle: '需求标题',
      requirementType: '需求类型',
      sampleRequirements: '稿样需求',
      searchRequirementTitle: '搜索需求标题',
      submissionTime: '提交时间',
      uploadFiles: '上传文件',
      uploadSuccessful: '上传成功',
      uploadTips1: '您移除了文件',
      uploadTips2: '文件大小超出限制, 请重新上传！',
      uploadTips3: '请检查附件格式重新上传！',
      uploadTips4: '请等待文件上传完成',
      uploadTips5: '超出上传文件数量限制！',
      fileRepeat: '文件重复',
      whole: '全部'
    },
    demandDetail: {
      basicNeeds: '基本需求',
      cardName: '卡款名称',
      cardNumber: 'GSC卡号', //卡款编号改GSC卡号
      cardPaymentCode: 'GSC卡号', //卡款代码改GSC卡号
      closeDemand: '关闭需求',
      closingInstructions: '关闭说明',
      confirmThePlan: '确认方案',
      confirmed: '已确认',
      demonstration: '3D演示',
      designScheme: '设计方案',
      schemeName: '方案名称',
      designer: '设计师',
      designManager: '设计负责人',
      detailedRequirements: '详细需求',
      errorTips1: '稿样需求不支持在线沟通，请联系管理员！',
      errorTips2: '请至少选中一条数据！',
      number: '序号',
      onlineCommunication: '在线沟通',
      operationTime: '操作时间',
      operator: '操作员',
      receiptInformation: '回执信息',
      sampleCardOrder: '样卡订单',
      samplePersonnel: '稿样人员',
      sampleScheme: '稿样方案',
      state: '状态',
      toBeConfirmed: '待确认',
      updateTime: '更新时间',
      viewReceipts: '查看回执',
      cardPaymentCodeSearch: '通过GSC卡号搜索', //卡款代码改GSC卡号
      cardNameSearch: '通过卡款名称搜索',
      reject: '驳回',
      updateDate: '更新日期',
      applicationFormNumber: '申请单编号',
      applicationStatus: '申请状态',
      creationTime: '创建时间',
      salesman: '销售人员',
      // 新增中英文
      query: '查询'
    },
    demandList: {
      currentStage: '当前阶段',
      newProductRequirements: '新建产品需求',
      sourceOfDemand: '需求来源'
    },
    orderDetail: {
      address: '地址',
      addressName: '地址名称',
      contactPhoneNumber: '联系电话',
      contacts: '联系人',
      creationTime: '创建时间',
      customerInformation: '客户信息',
      customerSelfPickup: '客户自提',
      download: '下载',
      errorTips1: '获取订单失败，请稍后重试！',
      estimatedDeliveryTime: '预计交付时间',
      inventoryAndProxyStorage: '入库代存',
      logisticsCompany: '物流公司',
      logisticsInformation: '物流信息',
      logisticsTracking: '物流追踪',
      mail: '邮寄',
      mailingInformation: '邮寄信息',
      managementPlatform: '客户端',
      no: '否',
      operator: '操作员',
      operatorAccount: '操作员账号',
      creator: '创建人',
      creatorAccount: '创建人账号',
      orderInformation: '订单信息',
      orderNumber: '订单编号',
      orderSource: '订单来源',
      orderStatus: '订单状态',
      orderType: '订单类型',
      orderVoucher: '下单凭证',
      orderingCustomers: '下单客户',
      personalizedMaterials: '是否写入个人化物料',
      productInformation: '产品信息',
      quantity: '数量（张）',
      recipients: '收件人',
      referencePrice: '参考价格',
      remarksDescription: '备注说明',
      shippingMethod: '发货方式',
      shou: '收',
      totalOrderPrice: '订单总价',
      updateTime: '更新时间',
      waybillNumber: '运单号',
      yes: '是',
      customerOrderCode: '客户订单编号'
    },
    designRecommend: {
      DesignerPerferred: '设计优选',
      IPCooperation: 'IP合作',
      ToExplore: '搜索',
      CardCustomizationRequirement: '自定义卡面需求',
      Template: '版型',
      CardBodyColor: '卡基颜色',
      Theme: '主题',
      View3DCardFace: '查看3D卡面',
      RaiseRequirement: '提需求',
      SelectProposal: '选择方案',
      DoNOTSelectProposal: '不使用方案',
      DPintroduction: 'IP介绍',
      NewCardType: '新上卡款',
      DesingerRecommanda: '设计师推荐',
      HotIP: '爆款IP',
      Classification: '分类',
      DesignIntroduction: '设计介绍',
      // 新增中英文
      all: '全部',
      myApplication: '我的应用',
      myCollection: '我的收藏',
      keyword: '关键词，例如：可爱动物',
      collect: '收藏',
      collected: '已收藏',
      loadMore: '加载更多',
      nothingMore: '没有更多了',
      qualitymaterials: '海量精品素材',
      viewMore: '查看更多',
      optimalDesignScheme: '优选设计方案',
      horizontalVersion: '横版',
      verticalVersion: '竖版',
      irregularShape: '异型',
      chinaChic: '国潮',
      anime: '动漫',
      technology: '科技',
      art: '艺术',
      copyrighted: '有版权',
      applyToDIY: '应用到DIY',
      keywords: '关键词',
      pleaseSelect: '请选择',
      pleaseEnter: '请输入',
      image: '图片 ',
      description: '描述',
      materialUsage: '素材用途',
      bindProduct: '绑定产品 ',
      used: '已使用',
      addTime: '添加时间',
      operation: '操作',
      viewProduct: '查看产品',
      delete: '删除',
      imageClassification: '图片分类',
      keyDescription: '关键描述 ',
      materialType: '素材类型',
      cancelCollection: '取消收藏',
      selectProduct: '选择产品',
      product: '产品'
    }
  },
  productOrder: {
    batchOrderDetail: {
      customer: '客户端',
      deliveryDate: '卡商预计交付日期',
      mailingAddress: '邮寄地址',
      management: '管理端',
      sale: '销售端',
      draftFile: '稿样文件',
      approvalFile: '报批文件',
      orderStatus: '订单状态',
      uploadApprovalFile: '报批文件上传',
      uploadCardApprovalAbnormal: '报批文件上传异常：',
      download: '点击下载',
      downloadPlmApproval: '下载PLM批文',
      downloadCardApprovalFail: '批文不存在!',
      downloadCardApprovalAbnormal: '下载批文异常：',
      statusNone: '评审中',
      statusStocking: '入库中',
      statusMaking: '生产中',
      statusMaked: '已入库',
      statusShipped: '已出货'
    },
    noCardOrder: {
      addAddress: '添加地址',
      batchCardOrder: '批卡订单',
      orderingProducts: '下单产品',
      pleaseEnterNoteInformation: '请输入备注信息',
      pleaseSelectAnAddress: '请选择地址',
      pleaseSelectOrderType: '请选择订单类型',
      tips1: '待订单确认后由业务补充',
      tips2: '当前用户没有客户，无法提交订单',
      uploadTips1: '拖拽图片到这里',
      uploadTips2: '点此添加',
      uploadTips3: '文件数量最多8个，大小10MB以内',
      uploadTips4: '或'
    },
    noUploadedImages: '无上传图',
    orderDetail: {},
    orderList: {
      createTime: '创建时间',
      customerSExpectedDeliveryDate: '客户期望交付日期',
      more: '更多',
      revoke: '撤销',
      revokeSuccess: '撤销成功',
      searchForProductName: '搜索产品名称',
      searchOrderCode: '搜索订单编号',
      source: '来源',
      tips1: '确定要撤销当前订单吗?'
    }
  }
}
