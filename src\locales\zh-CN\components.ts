export default {
  BaseTable: {
    operation: '操作'
  },
  BpmnProcessDesigner: {
    package: {
      designer: {
        plugins: {
          contentPad: {
            contentPadProvider: {
              changeType: '修改类型'
            }
          },
          palette: {
            CustomPalette: {
              activeGripperTool: '激活抓手工具'
            }
          }
        },
        ProcessDesigner: {
          openFile: '打开文件',
          downloadXML: '下载为XML文件',
          downloadSVG: '下载为SVG文件',
          downloadBPMN: '下载为BPMN文件',
          downloadFile: '下载文件',
          preview: '预览',
          previewXML: '预览XML',
          previewJSON: '预览JSON',
          outSimulation: '退出模拟',
          openSimulation: '开启模拟',
          simulation: '模拟',
          alignLeft: '向左对齐',
          alignRight: '向右对齐',
          alignTop: '向上对齐',
          alignBottom: '向下对齐',
          alignCenter: '水平居中',
          verticalCenter: '垂直居中',
          processZoomOut: '缩小视图',
          processZoomIn: '放大视图',
          processReZoom: '重置视图并居中',
          processUndo: '撤销',
          processRedo: '恢复',
          processRestart: '重新绘制',
          saveModel: '保存模型',
          pushShift: '请按住 Shift 键选择多个元素对齐',
          autoAlignTip: '自动对齐可能造成图形变形，是否继续？',
          warning: '警告',
          saveModelFail: '保存模型失败，请重试！'
        },
        ProcessViewer: {
          operationFlow: '业务流程',
          initiator: '发起人：',
          department: '部门：',
          createTime: '创建时间：',
          approver: '审批人：',
          result: '结果：',
          endTime: '结束时间：',
          approvalSuggestion: '审批建议：'
        }
      },
      palette: {
        ProcessPalette: {
          testTask: '测试任务'
        }
      },
      penal: {
        base: {
          ElementBaseInfo: {
            signatureTip: '如何实现实现会签、或签？',
            processIdent: '流程标识',
            pleaseEnter: '请输入',
            processName: '流程名称',
            name: '名称',
            idErrorTip: '流程标识不能为空',
            nameErrorTip: '流程名称不能为空'
          }
        },
        flowCondition: {
          FlowCondition: {
            type: '流转类型',
            normal: '普通流转路径',
            default: '默认流转路径',
            condition: '条件流转路径',
            conditionType: '条件格式',
            expression: '表达式',
            script: '脚本',
            language: '脚本语言',
            scriptType: '脚本类型',
            inlineScript: '内联脚本',
            externalScript: '外部脚本',
            resource: '资源地址'
          }
        },
        form: {
          ElementForm: {
            formKey: '表单标识',
            businessKey: '业务标识',
            none: '无',
            formFieId: '表单字段',
            num: '序号',
            FieIdName: '字段名称',
            FieIdType: '字段类型',
            default: '默认值',
            operation: '操作',
            cutout: '移除',
            addFieId: '添加字段',
            fieIdSetting: '字段配置',
            fieIdId: '字段ID',
            typeType: '字段类型',
            pleaseSelect: '请选择',
            typeName: '类型名称',
            name: '名称',
            datePattern: '时间格式',
            enumList: '枚举值列表：',
            addEnum: '添加枚举值',
            enumCode: '枚举值编号',
            enumName: '枚举值名称',
            openFieldOptionForm: '约束条件列表：',
            addConstraint: '添加约束',
            constraintName: '约束名称',
            constraintSetting: '约束配置',
            fieIdList: '字段属性列表：',
            addAttribute: '添加属性',
            attributeCode: '属性编号',
            save: '保存',
            fieldOptionFormId: '编号/ID',
            setting: '配置',
            value: '值'
          }
        },
        listeners: {
          ElementListeners: {
            num: '序号',
            eventType: '事件类型',
            listenerType: '监听器类型',
            operation: '操作',
            cutout: '移除',
            addListener: '添加监听器',
            executeListener: '执行监听器',
            javaClass: 'Java类',
            expression: '表达式',
            delegateExpression: '代理表达式',
            scriptFormat: '脚本格式',
            pleaseEnterScriptFormat: '请填写脚本格式',
            scriptType: '脚本类型',
            pleaseSelectScriptType: '请选择脚本类型',
            inlineScript: '内联脚本',
            externalScript: '外部脚本',
            scriptValue: '脚本内容',
            pleaseInputScriptValue: '请填写脚本内容',
            resourceUrl: '资源地址',
            pleaseInputResourceUrl: '请填写资源地址',
            inputField: '注入字段：',
            addField: '添加字段',
            fieldName: '字段名称',
            fieldType: '字段类型',
            fieldValue: '字段值',
            save: '保存',
            fieIdSetting: '字段配置',
            confirmCutOutFieldTip: '确认移除该字段吗？',
            tip: '提示',
            confirmCutOutListenerTip: '确认移除该监听器吗？'
          },
          UserTaskListeners: {
            num: '序号',
            eventType: '事件类型',
            eventId: '事件id',
            listenerType: '监听器类型',
            operation: '操作',
            cutout: '移除',
            addListener: '添加监听器',
            taskListener: '任务监听器',
            listener: '监听器ID',
            javaClass: 'Java类',
            expression: '表达式',
            delegateExpression: '代理表达式',
            scriptFormat: '脚本格式',
            pleaseEnterScriptFormat: '请填写脚本格式',
            scriptType: '脚本类型',
            pleaseSelectScriptType: '请选择脚本类型',
            inlineScript: '内联脚本',
            externalScript: '外部脚本',
            scriptValue: '脚本内容',
            pleaseInputScriptValue: '请填写脚本内容',
            resourceUrl: '资源地址',
            pleaseInputResourceUrl: '请填写资源地址',
            eventDefinitionType: '定时器类型',
            date: '日期',
            duration: '持续时长',
            loop: '循环',
            none: '无',
            timer: '定时器',
            pleaseInputTimer: '请填写定时器配置',
            injectKey: '注入字段：',
            addField: '添加字段',
            fieldName: '字段名称',
            fieldType: '字段类型',
            fieldValue: '字段值'
          }
        }
      }
    },
    save: '保存',
    fieldSetting: '字段配置',
    fieldName: '字段名称：',
    fieldType: '字段类型：',
    fieldValue: '字段值：',
    expression: '表达式：',
    confirmCutOutFieldTip: '确认移除该字段吗？',
    tip: '提示',
    confirmCutOutListenerTip: '确认移除该监听器吗？',
    loopCharacteristics: '回路特性',
    ParallelMultiInstance: '并行多重事件',
    SequentialMultiInstance: '时序多重事件',
    StandardLoop: '循环事件',
    none: '无',
    loopCardinality: '循环基数',
    collection: '集合',
    elementVariable: '元素变量',
    completionCondition: '完成条件',
    asyncStatus: '异步状态',
    asyncBefore: '异步前',
    asyncAfter: '异步后',
    exclusive: '排除',
    timeCycle: '重试周期',
    elementDoc: '元素文档：',
    num: '序号',
    attrName: '属性名',
    attrValue: '属性值',
    operation: '操作',
    addAttr: '添加属性',
    attrSetting: '属性配置',
    cutout: '移除',
    confirmCutOutAttr: '确认移除该属性吗？',
    messageList: '消息列表',
    createNew: '创建新消息',
    messageId: '消息ID',
    messageName: '消息名称',
    signalList: '信号列表',
    createSignal: '创建新信号',
    signalId: '信号ID',
    signalName: '信号名称',
    messageExisting: '该消息已存在，请修改id后重新保存',
    signalExisting: '该信号已存在，请修改id后重新保存',
    messageExample: '消息实例',
    confirm: '确认',
    scriptFormat: '脚本格式',
    pleaseEnterScriptFormat: '请填写脚本格式',
    scriptType: '脚本类型',
    pleaseSelectScriptType: '请选择脚本类型',
    inlineScript: '内联脚本',
    externalScript: '外部脚本',
    scriptValue: '脚本内容',
    resultValue: '结果变量',
    outsideResource: '外部资源',
    resourceAddr: '资源地址',
    dueDate: '到期时间',
    followUpDate: '跟踪时间',
    priority: '优先级',
    friendlyTips1: '友情提示：任务的分配规则，使用',
    friendlyTips2: '流程模型',
    friendlyTips3:
      '下的【分配规则】替代，提供指定角色、部门负责人、部门成员、岗位、工作组、自定义脚本等 7种维护的任务分配维度，更加灵活！',
    asyncContinue: '异步延续',
    eliminate: '排除',
    routine: '常规',
    messageAndSignal: '消息与信号',
    circulationConditions: '流转条件',
    form: '表单',
    friendlyTips4: '友情提示：使用',
    friendlyTips5: '流程表单',
    friendlyTips6: '替代，提供更好的表单设计功能',
    task: '任务',
    multipleInstances: '多实例',
    executeListener: '执行监听器',
    taskListener: '任务监听器',
    extendAttr: '扩展属性',
    else: '其他'
  },
  ButtonMessage: {
    isConfirm: '是否确定？',
    tip: '提示'
  },
  CustomUpload: {
    sumExtends: '文件总大小超出限制, 请重新上传！',
    extends: '文件大小超出限制, 请重新上传！',
    reUpload: '请检查附件格式重新上传！',
    extendsNum: '超出上传文件数量限制！',
    uploadFail: '文件上传失败',
    waitForUpload: '请等待文件上传完成'
  },
  Editor: {
    pleaseEnter: '请输入内容...',
    reUpload: '请检查附件格式重新上传！'
  },
  SearchFrom: {
    startDate: '开始日期',
    endDate: '结束日期',
    recentlyOneWeek: '最近一周',
    recentlyOneMonth: '最近一个月',
    recentlyThreeMonth: '最近三个月'
  }
}
