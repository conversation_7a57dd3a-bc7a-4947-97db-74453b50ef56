export default {
  // 公共
  common: {
    customerName: '客户名称',
    projectName: '项目名称',
    serverName: '服务名称',
    demandSource: '需求来源',
    demandTitle: '需求标题',
    demandCode: '需求编号',
    demandType: '需求类型',
    currentDutyPer: '当前任务人员',
    demandDesc: '需求描述',
    cardBin: '卡BIN',
    currentDutyPerPlaceholder: '请选择当前任务人员',
    demandTypePlaceholder: '请选择需求类型',
    customerPlaceholder: '请选择客户名称',
    projectPlaceholder: '请选择项目名称',
    serverPlaceholder: '请选择服务名称',
    demandSourcePlaceholder: '请选择需求来源',
    demandTitlePlaceholder: '请输入制卡需求标题',
    demandCodePlaceholder: '请输入需求编号',
    unfold: '展开',
    packUp: '收起',
    search: '查询',
    reset: '重置',
    add: '添加',
    delete: '删除',
    edit: '编辑',
    detail: '详情',
    confirm: '确认',
    ok: '确定',
    cancel: '取消',
    disposition: '配置',
    check: '查看',
    export: '导入',
    open: '启用',
    close: '停用',
    batchDelete: '批量删除',
    batchMove: '批量移动',
    addPic: '添加图片',
    addComponent: '添加组件',
    addSort: '添加分类',
    upload: '上传',
    uploadFile: '上传文件',
    noData: '暂无数据',
    groupManage: '分组管理',
    copyLink: '复制链接',
    notHave: '无',
    null: '空',
    saveSuccess: '保存成功',
    addSuccess: '添加成功',
    deleteSuccess: '删除成功',
    editSuccess: '编辑成功',
    deleteContent: '删除后将不可恢复,是否确认删除',
    editSort: '编辑分类',
    client: '客户端',
    management: '管理平台',
    makeDemand: '制卡需求',
    designCase: '设计方案',
    draftCase: '稿样方案',
    sampleCardApply: '样卡申请',
    designDemand: '设计需求',
    draftDemand: '稿样需求',
    closeDemand: '需求关闭',
    pleaseChooseData: '请至少选中一条数据！',
    all: '全部',
    other: '其他',
    UnionPay: '银联',
    ConfirmOperation: '是否执行此操作？',
    uploadSuccess: '上传成功',
    pleaseEnter: '请输入内容',
    uploadFileError: '文件上传失败',
    download: '下载',
    sizeOutOfLimit: '文件大小超出限制, 请重新上传！',
    checkFileFormat: '请检查附件格式重新上传！',
    numberOutOfLimit: '超出上传文件数量限制！',
    awaitUpload: '请等待文件上传完成',
    removeTip: '您移除了文件【 {fileName} 】',
    noFile: '未选择任何文件！',
    disableNull: '内容不能为空，请输入信息！',
    yes: '是',
    no: '否',
    submit: '提交',

    productName: '产品名称',
    productType: '产品类型',
    placeholderTextTip: '请输入{placeholder}',
    placeholderSelectTip: '请选择{placeholder}',
    basicDemand: '基本需求',
    customerFile: '客户文件',
    detailDemand: '详细描述',
    customerAccount: '客户账号',
    selectFile: '选择文件',
    uploadFileNoSizeTip: '文件数量最多{maxNum}个，整体大小{maxSize}以内，格式不限',
    uploadFileTip: '文件数量最多{maxNum}个，大小{maxSize}以内，文件格式 {fileFormat}',
    uploadFileNoNumTip: '文件大小{maxSize}以内，格式 {fileFormat}',
    uploadFileNoFormatTip: '文件数量最多{maxNum}个，大小{maxSize}以内',
    customer: '客户',
    estimatedTimeOfSubmission: '预计提稿时间',
    originAndLevel: '卡组织及卡款级别',
    backTip: '是否确认选定此方案为最终方案，确认后将不可修改，请谨慎操作！'
  },
  dialog: {
    editReceivingInfo: '编辑收件信息',
    addReceivingInfo: '新增收件信息',
    chooseReceiving: '选择地址',
    view3D: '查看3D文件',
    viewDesign: '查看设计方案',
    viewDraft: '查看稿样方案',
    viewFile: '查看内部存档',
    viewBackMsg: '查看回执信息',
    verifyScheme: '确认方案',
    upLoadSaveFile: '上传存档文件',
    uploadDesign: '上传设计方案',
    uploadDraft: '上传稿样方案',
    upLoadAgain: '再次上传方案'
  },
  index: {
    list: '制卡需求列表',
    createList: '新建制卡需求',
    conversionDraft: '转稿样需求',
    conversionDesign: '转设计需求',
    assignTasks: '分配任务',
    assignSuccess: '分配成功',
    conversionSuccess: '转换成功',
    dataError: '数据错误，请联系管理员！'
  },
  im: {
    sendDirectly: '直接发送',
    saveImage: '保存图片',
    imgCut: '图片裁剪',
    imgEditArea: '图片编辑区',
    clearImg: '清空图片',
    uploadImg: '上传图片',
    onlineChat: '在线沟通',
    interactiveLog: '互动记录',
    online: '在线',
    loading: '加载中~',
    onMore: '没有更多了~',
    pleaseEnter: '请输入互动内容......',
    pleaseEnterTip: '请输入互动内容',
    send: '发送',
    loginError: '登录服务器失败，请联系管理员!',
    joinError: '连接服务器失败，请联系管理员！',
    serviceError: '服务器失去响应，请联系管理员！',
    reJoinError: '链接失败，正在尝试重新连接！',
    sending: '发送中...',
    clear: '清空',
    rubber: '橡皮檫',
    pen: '涂鸦笔',
    text: '文字',
    square: '矩形',
    round: '圆形',
    rotate: '旋转图片',
    finish: '完成',
    sendImg: '发送图片'
  },
  detail: {
    assignmentDesigner: '分配设计师',
    assignmentDraftDesigner: '分配稿样设计师',
    chooseDesigner: '选择设计师',
    chooseDraftDesigner: '选择稿样设计师',
    chooseAuthDesigner: '请选择已授权的设计师',
    chooseAuthDraftDesigner: '请选择已授权的稿样设计师',
    pleaseChooseDesign: '请选择设计师！',
    customerFile: '客户上传文件',
    relatedProject: '关联项目',
    estimatedDraftDate: '预计提稿日期',
    cardType: '卡片类型',
    cardVariety: '卡款种类',
    cardOrgLevel: '卡组织及卡片级别',
    otherExplain: '其他说明',
    otherFile: '其他附件',
    backMsg: '回执信息',
    accessory: '附件',
    closeExplain: '关闭说明',
    closeExplainPlaceholder: '请输入关闭说明',
    noCloseExplainTip: '请填写关闭说明！',
    closeSuccess: '关闭成功',
    cardStyleCode: 'GSC卡号',
    cardStyleName: '卡款名称',
    stylist: '设计师',
    stylistOfDraft: '稿样设计人员',
    interiorFile: '内部存档',
    status: '状态',
    valetConfirmation: '代客确认',
    toBeConfirmed: '待确认',
    confirmed: '已确认',
    noIMOfDraft: '稿样需求不支持在线沟通，请联系管理员！',
    noDesignOfDraft: '稿样需求不支持上传设计方案，请联系管理员！',
    sampleCardOrder: '样卡订单',
    BatchProductUpdate: '更新批产品',
    cardDraftCode: 'GSC卡号',
    view3D: '3D演示',
    show3D: '3D展示',
    view: '查看',
    operator: '操作人',
    operatorTime: '操作时间',
    updateProductInfo: '产品信息更新',
    noIMOfDesign: '设计需求不支持在线沟通，请联系管理员！',
    productCode: '产品编号',
    toBeListed: '待上架',
    listed: '已上架',
    productImg: '产品图片',
    frontImg: '正面图片',
    backImg: '背面图片',
    updateProductErr: 'DIY卡产品更新操作对接中',
    remark: '备注',
    uploadDraftFile: '上传稿样文件',
    archiveFile: '存档文件',
    remarkNote: '备注说明',
    confirmSuccess: '确认成功',
    uploadOtherFile: '上传附件',
    draftFile: '稿样文件',
    noFrontImg: '请上传产品正面图片！',
    noBackImg: '请上传产品背面图片！',
    saveProductSuccess: '保存产品成功！',
    saveProducterror: '更新产品失败：',
    batchProduct: '批卡产品',
    DIYProduct: 'DIY产品'
  },
  sampleOrder: {
    addressName: '地址名称',
    contact: '收件人',
    tel: '联系电话',
    area: '寄送地址',
    selectCustomerPlaceholder: '请输入客户名称',
    selectAddressPlaceholder: '请选择省市区',
    addressDetailPlaceholder: '请输入详细地址',
    contactPlaceholder: '请输入收件人',
    telPlaceholder: '请输入手机号',
    namePlaceholder: '请输入名称',
    operatorSuccess: '操作成功',
    name: '名称',
    phone: '手机号',
    address: '收件地址',

    applySample: '申请样卡',
    unitPrice: '单价',
    cardCount: '数量',
    maxTip: '最大输入48张',
    deliveryTime: '期望交付时间',
    deliveryTimePlaceholder: '请选择期望交付时间',
    isUrgent: '是否加急',
    addressOfPeople: '联系人',
    addressOfPeoplePlaceholder: '请输入联系人',
    addressOfTelPlaceholder: '请输入联系电话',
    deliveryMethod: '交付方式',
    storage: '入库代存',
    customerPick: '客户自提',
    mail: '邮寄',
    mailModePlaceholder: '请选择邮寄方式',
    mailOfAddress: '地址',
    packageMode: '包装方式',
    packageModePlaceholder: '请选择包装方式',
    innerBox: '内盒',
    outerBox: '外盒',
    fileList: '下单凭证',

    addAddress: '新增地址',
    innerBoxPlaceholder: '请选择内盒',
    outerBoxPlaceholder: '请选择外盒',
    product: '产品',
    price: '价格',
    unitP: '张',
    unitO: '个',
    createOrder: '立即下单',
    deliveryMethodPlaceholder: '请输入交付方式！',
    mailModePlaceholderJs: '请选择邮寄方式！',
    mailOfAddressPlaceholder: '请选择邮寄地址！',
    unitPricePlaceholder: '请输入单价！',
    cardCountPlaceholder: '请输入卡款数量！',
    deliveryTimePlaceholderJs: '请输入期望交付日期！',
    isUrgentPlaceholder: '请输入是否加急！',
    peoplePlaceholder: '请输入联系人！',
    phonePlaceholder: '请输入联系电话！',
    getErrorTip: '参数获取异常，请稍后重试！',
    orderType: '订单类型',
    orderCode: '订单编号',
    orderInfo: '订单信息',
    cardCountAndUnit: '数量（张）',

    logisticsInfo: '物流信息',
    mailNo: '运单号',
    mailMode: '发货方式',
    expressTypeText: '物流公司',
    fullAddress: '详细地址',
    routeAddress: '物流追踪',
    OrderStatus: '订单状态',
    orderSource: '订单来源',
    deliveryTimeOfDetail: '预计交付时间',
    orderTotalPrice: '订单总价',
    operateName: '操作员',
    operateUsername: '操作员账号',
    customerInfo: '客户信息',
    customerName: '下单客户',
    productInfo: '产品信息',
    unitPriceOfDetail: '参考价格',
    isIndividual: '是否写入个人化物料',
    mailInfo: '邮寄信息',
    getOrderErr: '获取订单失败，请稍后重试！'
  },
  table: {
    indexNumber: '序号',
    operate: '操作',
    updateTime: '更新时间',
    submitTime: '提交时间',
    createTime: '创建时间',
    demandPhase: '当前阶段'
  }
}
