export default {
  batchCardProduct: {
    numberOutOfLimit: '超出上传文件数量限制！',
    sizeOutOfLimit: '文件大小超出限制, 请重新上传！',
    onlyOneTip: '只能上传一个文件，已将文件进行替换！',
    formatTip: '文件上传暂只支持{format}格式, 请重新上传！',
    sizeTip: '单个文件不能超过{size}M, 请重新上传！',
    removeTip: '您移除了文件【{fileName}】',
    file: '文件',
    cancelReUpload: '取消重传',
    cancelReUploadFile: '取消重传文件',
    pleaseUpload: '请上传',

    basicInfo: '基础信息',
    customerName: '客户名称',
    customerNamePlaceholder: '请选择客户名称',
    productType: '产品类型',
    productName: '产品名称',
    cardCode: 'GSC卡号',
    frontImg: '正面图片',
    backImg: '背面图片',
    productFrontImg: '产品正面图',
    productBackImg: '产品背面图',
    productImg: '产品图片',
    updateTime: '更新时间',
    operateInfo: '运营信息',
    viewProduct: '查看产品',
    toBeSubmitted: '待提交',
    delist: '已下架',
    underReview: '审核中',

    TheCustomer: '所属客户',
    TheCustomerPlaceholder: '请选择所属客户',
    productTypePlaceholder: '请选择产品类型',
    productNamePlaceholder: '请输入产品名称',
    cardCodePlaceholder: '请输入GSC卡号',
    productStatus: '产品状态',
    productStatusPlaceholder: '请选择产品状态',
    packUp: '收起条件',
    unfold: '展开条件',
    indexNumber: '序号',
    noData: '暂无',
    status: '状态',
    productInfo: '产品信息',
    submitTime: '提交时间',
    createTime: '创建时间',
    tip: '提示',
    closeProductSuccess: '产品关闭成功！',
    openProductSuccess: '产品启用成功！',
    saveProductSuccess: '保存产品成功！',
    delProductSuccess: '产品删除成功！',
    submit: '提 交',
    sizeOneTip: '文件大小：2M以内',
    formatOneTip: '文件格式：.jpg .png',
    addProduct: '添加产品',
    closeConfirmTip: '是否关闭产品【{name}】？',
    openConfirmTip: '是否启用产品【{name}】？',
    delConfirmTip: '是否删除产品【{name}】？',
    enabled: '已启用',
    closed: '已关闭',
    process: '处理中',
    rejected: '已驳回',
    productFrontImgTip: '请上传产品正面图片',
    productBackImgTip: '请上传产品背面图片',
    clientProductUniqueCode: '客户代码',
    editPrice: '修改价格',
    priceType: '报价方式',
    ladderQuotation: '阶梯报价',
    fixedOffer: '固定报价',
    intervalStart: '区间开始',
    intervalEnd: '区间结束',
    ladderPrice: '阶梯价格',
    intervalPrice: '区间价格',
    currency: '币种',
    currencyPlaceholder: '请选择币种',
    intervalTip: '区间不能为空',
    intervalPricePlaceholder: '请填写区间价格',
    intervalSymbol: '区间符号',
    intervalSymbolPlaceholder: '请选择区间符号',
    addRow: '添加行',
    equal: '等于',
    intervalBackTip: '区间开始不能小于等于上一行区间结束',
    intervalNextTip: '区间结束不能小于等于区间开始',
    priceTypePlaceholder: '请选择报价方式！',
    editSuccess: '修改成功',
    viewPrice: '查看价格'
  },
  diyCardProduct: {
    customerSelect: '选择客户',
    customerSelectPlaceholder: '请选择客户',
    pattern: '版型',
    patternPlaceholder: '请选择版型',
    selectPlaceholder: '请选择',
    selectImgEditPlaceholder: '请选择{name}-图片编辑',
    cardSurfaceCustomization: '卡面定制',
    cardOrganCustomization: '卡组织定制',
    cardColorCustomization: '卡基颜色定制',
    cardBackColorCustomization: '卡背面颜色定制',
    cardLogoCustomization: 'Logo标识定制',
    maskImg: '蒙层图片',
    maskImgPlaceholder: '请上传蒙层图片',
    maskImgUploadPlaceholder: '上传蒙层图片',
    frontImg: '卡面正面图片',
    settingRegion: '设置框定区域',
    top: '上',
    bottom: '下',
    left: '左',
    right: '右',
    frontImage: '卡正面图片',
    backImage: '卡背面图片',
    maskAndFrontImgCraft: '合成图片（蒙层图片+卡正面图片）',
    maskImgCraft: '合成图片（蒙层图片）',
    textureMaterial: '贴图素材',
    elementMaterial: '元素拼接素材',
    colorPhotoCard: '彩照卡',
    cardImg: '卡面贴图',
    cardBackImg: '卡面背面图片',
    uploadFrontImg: '上传卡正面图片',
    uploadBackImg: '请上传背面图片',
    uploadBackImgPlaceholder: '上传背面图片',
    halfImgCustomerSetRegion: '设置半幅彩照中用户自定义上传区域',
    uploadGroupImg: '上传卡面贴图组',
    elementSplicing: '元素拼接',
    uploadGroupElement: '上传元素组',
    upload: '上传',
    reupload: '重新上传',
    halfImgSetRegion: '请设置半幅彩照的框定区域',
    setWidth: '框定宽度',
    setRegion: '框定区域范围',
    setLocation: '框定区域位置',
    setHight: '框定高度',
    distanceTop: '距离上方',
    distanceBottom: '距离下方',
    distanceLeft: '距离左边',
    distanceRight: '距离右边',
    needTrueArrayTip: '请传入一个正确的对象数组作为参数',
    noWidth: '第一张图片宽度未设置',
    noHight: '第一张图片高度未设置',
    priceAndUnit: '价格（元）',
    priceAndUnitPlaceholder: '请输入价格（元）',
    originalPriceAndUnit: '原价（元）',
    originalPriceAndUnitPlaceholder: '请输入原价（元）',
    otherPriceAllocation: '其他价格配置',
    argumentName: '参数名称',
    argumentNamePlaceholder: '请输入参数名称',
    argumentValue: '参数值',
    effectBeginTime: '生效开始时间',
    beginTime: '开始日期',
    effectFinishDay: '生效结束日期',
    finishTime: '结束日期',
    effectFinishTime: '生效结束时间',
    operate: '操作',
    argumentValuePlaceholder: '请输入参数值',
    argumentValueNoRepeat: '参数值不能重复',
    effectBeginTimePlaceholder: '请选择生效开始时间',
    effectBeginTimeNoRepeat: '同参数值生效时间配置不能重合',
    effectBeginTimeSmall: '生效开始时间需小于生效结束时间',
    effectBeginTimeOnlyOneNull: '同参数值生效结束时间只能有一个为空',
    effectFinishDayForeverSmall: '结束时间需小于永久时间的开始时间',
    pricePlaceholder: '请输入价格',
    preview: '点击预览',
    noImgOfGroup: '此分组暂无图片',
    all: '全部',
    needImgAudit: '需要图审',
    noImgAudit: '无需图审',
    imgAuditIdent: '图审标识',
    imgAuditIdentPlaceholder: '请选择图审标识',
    bindImgAuditService: '绑定图审服务',
    bindImgAuditServicePlaceholder: '请选择绑定的图审服务',
    empty: '无',
    remark: '备注',
    remarkPlaceholder: '请输入备注',
    serviceName: '服务名称',
    serviceNamePlaceholder: '请选择服务',
    causeOfRejection: '驳回原因',
    basicInfo: '基础信息',
    cardDraftCode: 'GSC卡号',
    cardStyleName: '卡款名称',
    cardOtherOfCName: '卡款别名（C端）',
    cardType: '卡款类型',
    cardIdent: '卡款标识（对外）',
    subcategory: '所属分类',
    customerProductName: '客户产品名称',
    customerProductCode: '客户产品编号',
    financeProductCode: '财务产品编码',
    ProductCode: '产品编号',
    visibilityRange: '可见范围',
    allCity: '全部城市',
    supplier: '供应商',
    productIntro: '产品简介',
    designFile: '设计文件',
    download: '下载',
    draftFile: '稿样文件',
    file3D: '3D文件',
    customAttribute: '定制属性',
    imgInfo: '图片信息',
    noMaskImg: '无蒙层图片',
    viewSetRegion: '查看框定区域',
    noBackImg: '无卡背面图片',
    noFrontImg: '无卡正面图片',
    noGroupImg: '无卡面贴图组',
    noElementSplicing: '无元素拼接组',
    setPrice: '价格配置',
    defaultTip: '提示：未在生效时间范围的价格默认价格',
    setImgAudit: '图审配置',
    viewHalfImgSetRegion: '查看半幅彩照中用户自定义上传区域',
    fileDownloadErr: '文件下载失败',
    viewProduct: '查看产品',
    fuzzySearchSelection: '可模糊搜索选择',
    quickAdd: '快捷添加',
    chooseProductTip: '提示：您可以选择相似产品快速填充产品信息，仅更改差异化的信息即可',
    cardOtherOfCNamePlaceholder: '请输入卡款别名（C端）',
    cardTypePlaceholder: '请选择卡款类型',
    allImg: '全幅彩照卡',
    halfImg: '半幅彩照卡',
    cardIdentPlaceholder: '请输入卡款标识（对外）',
    customerProductNamePlaceholder: '请输入客户产品名称',
    customerProductCodePlaceholder: '请输入客户产品编号',
    financeProductCodePlaceholder: '请输入财务产品编码',

    defaultSortPlaceholder: '请选择所属分类',
    visibilityRangePlaceholder: '请选择可见范围',
    supplierPlaceholder: '请选择供应商',
    productIntroPlaceholder: '请输入产品简介',
    cardDraftCodePlaceholder: '请选择GSC卡号',
    cardStyleNamePlaceholder: '请选择卡款名称',
    colorImgTypePlaceholder: '请选择彩照卡类型',
    priceErr: '价格不能小于或等于0',
    defaultSort: '默认分类',
    createImgSendErr: '生成合成图发送异常',
    addProduct: '添加产品',
    editProduct: '编辑产品',
    productStatus: '产品状态',
    productStatusPlaceholder: '请选择产品状态',
    view: '查看',
    image: '图片',
    submitConfirmTip: '是否提交产品【{name}】？',
    delistConfirmTip: '是否下架产品【{name}】？',
    delistSuccess: '产品下架成功！',
    submitSuccess: '产品提交成功！',
    selectProjectPlaceholder: '请选择关联项目',
    cardAliasC: '卡款别名',
    cardAliasCPlaceholder: '请输入卡款别名',
    cardDraftCodeTextPlaceholder: '请输入GSC卡号',
    submit: '提交',
    resubmit: '重新提交',
    horizontal: '横版',
    vertical: '竖版',
    anomaly: '异型'
  },

  // 样卡列表
  sampleCardList: {
    LeaderApproval: '领导审批',
    ManagementApproval: '经理审批',
    SalesConfirmation: '销售审批',
    Tobesubmitted: '待提交',
    Completed: '完成',
    Cancelled: '取消',

    applyCode: '申请单号',
    customerName: '客户名称',
    customerNameEnter: '请输入客户名称',
    sampleCardApplicationType: '申请类型',
    sampleCardApplicationTypeSelect: '请选择类型',
    orderApplicationStatus: '申请状态',
    orderApplicationStatusSelect: '请选择状态',
    search: '查询',
    reset: '重置',
    new: '新增',
    applyCode2: '申请单编号',
    orderDetial: '订单详情',
    // 客户名称: '客户名称',
    createName: '申请人',
    saleUserName: '区域销售',
    managerName: '区域负责人',
    // 申请状态: '申请状态',
    createDate: '创建时间',
    actions: '操作',
    views: '查看',
    edit: '编辑'
  },
  sampleCardEdit: {
    FreeSampleCard: '免费样卡',
    InboundSampleCard: '入库样卡',
    ChargeSampleCard: '收费样卡',

    ApplicationForm: '申请订单',
    ApplicationFormNo: '申请单号',
    CustomerName: '客户名称',
    PleaseEnterCustomerName: '请选择客户',
    ApplicationType: '申请类型',
    PleaseSelect: '请选择',
    deliveryDate: '交付时间',
    PleaseSelectDeliveryDate: '选择日期时间',
    urgentSign: '是否加急',
    urgentReason: '加急原因',
    PleaseEnterUrgentReason: '请输入加急原因',
    saleUserName: '销售人员',
    saleUserTime: '确认时间',
    managerTime: '确认时间',
    managerName: '销售经理',
    ProductList: '产品列表',
    BatchImport: '批量导入',
    AddProducts: '添加产品',
    DownloadTemplate: '下载模板',
    ProductName: '产品名称',
    CustomerProductCode: '客户产品代码',
    CardBaseNumber: '卡基编号',
    ApplicationQuantity: '申请数量',
    ProjectNumber: '方案数',
    Remarks: '备注',
    Actions: '操作',
    edit: '编辑',
    delete: '删除',
    SampleCardElements: '样卡要素',
    //样卡要素radio
    // SampleCardFunction: 'Sample Card Function',
    None: '无',
    NewCardConfirmation: '新卡确认',
    TenderSubmission: '投标',
    InspectionCertification: '送检/ 认证',
    SubmissionForApproval: '报批',
    Other: '其他',

    SampleCardFunction: '样卡用途',
    CardFrontPrintingElements: '正面印和刷要素',
    //正面印和刷要素radio
    CardBaseImage: '正面底图图案 ',
    ClientLogo: '正面客户LOGO',
    CardSchemeLogo: '正面卡组织LOGO',
    SpecialEffects: '正面特殊工艺',

    CardBackPrintingElements: '背面印和刷要素',

    //背面印和刷要素 radio
    BackImage: '背面地图图案',
    Text: '背面文字',
    BackClientLogo: '背面客户LOGO',
    BackCardSchemeLogo: '背面卡组织LOGO',
    BackSpecialEffects: '背面特殊工艺',
    BlankWhiteBack: '背面纯白无内容',

    OtherElements: '其他要素',

    //其他要素 radio
    SignaturePanel: '签名条',
    Hologram: '全息图',
    Magstripe: '磁条',
    MetalSticker: '立金',
    HotStamping: '烫印箔',
    FunctionalEMVChip: '真芯片',
    DummyEMVChip: '假芯片',
    RealInlay: '真INLAY',

    PersonalisationRequirements: '个人化需求',
    //个人化需求  radios

    CardSurfacePersonalisation: '卡面个人化',
    MagstripePersonalisation: '磁条个人化',
    ChipPrePersonalisation: '芯片预个人化',
    ChipPersonalisation: '芯片个人化',

    PMTestingRequirement: 'PM检测要求',
    //PM检测要求 radio
    MagstripePhysicalProperties: '磁条物理特性',
    MagstripePersonalisationData: '磁条个人化数据',
    ChipPhysicalProperties: '芯片物理特性',
    COSVersionChip: '芯片COS版本',
    ChipPersonalisationData: '芯片个人化数据',

    SecurityMeasureRequirements: '安全处理要求',
    //安全处理要求radio
    Holepunch: '打孔',
    VoidStamp: '盖作废章',
    ScratchedMagstripe: '划磁',
    NoTreatment: '不做处理',

    Approva: '审 核',
    Save: '保 存',
    Submit: '提 交',
    Cancel: '取 消',
    Back: '返 回',
    PleaseEnterDeliveryDate: '请填写交付时间',
    PleaseSelectSampleCardFunction: '请选择样卡用途',
    PleaseSelectCardFrontPrintingElements: '请选择正面印和刷要素',
    PleaseSelectCardBackPrintingElements: '请选择背面印和刷要素',
    PleaseSelectOtherElements: '请选择其他要素',
    PleaseSelectPersonalisationRequirements: '请选择个人化需求',
    PleaseSelectPMTestingRequirement: '请选择PM检测要求',
    PleaseSelectSecurityMeasureRequirements: '请选择安全处理要求',
    PleaseSelectPleaseSelectDeliveryDate: '请选择交付时间',
    SubmitSucess: '提交成功',
    SaveSucess: '保存成功',
    PleaseSelectApprovaResut: '请选择评审结果',
    cancelApplication: '是否取消申请?',
    Tips: '提示',
    Submit2: '确定',
    Cancel2: '取消',
    CancelSuccess: '取消成功',
    importTemplate: '产品列表导入模版',
    ImportFail: '导入文件失败，请检查数据格式是否正确！',
    ProducetNameMust: '产品名称为必填项',

    NoneSales: '无法获取销售人员信息',
    NoneCustomer: '无法获取客户信息',

    XlsHeaderInfos: {
      ProductName: '产品名称',
      CustomerProductCode: '客户产品代码',
      GSCCode: '卡基编号',
      Quantity: '申请数量',
      ProjectNumber: '方案数',
      Remarks: '备注'
    }
  },
  sampleProductDailog: {
    SampleCardApplicationProductFill: '样卡申请产品填写',
    ProductName: '产品名称',
    PleaseSelectProduct: '请输入选择产品',
    cardNumber: '卡款编号',
    PleaseEnterCardNumber: '请输入卡款编号',
    CustomerProductCode: '客户产品编码',
    // 客户产品编码: '客户产品编码',
    ApplicationQuantity: '申请数量',
    ProjectNumber: '方案数',
    Remarks: '备注',
    PleaseEnterComments: '请输入备注',
    Close: '关闭',
    save: '保存',
    correctQuantityValue: '请填写正确数量值',
    PleaseSelectCustomer: '请选择客户',
    UnableQueryproduct: '请选择客户，无法查询产品'
  },
  preparationList: {
    customerName: '客户名称',
    customerNamePlaceholder: '请输入客户名称',
    ApplicationStatus: '申请状态',
    ApplicationStatusSelect: '请选择状态',
    search: '查询',
    reset: '重置',
    new: '新增',
    applyCode: '申请单编号',
    // 客户名称: '客户名称',
    createName: '申请人',
    saleUserName: '区域销售',
    managerName: '区域负责人',
    LeaderApproval: '领导审批',
    // 申请状态: '申请状态',
    createDate: '创建时间',
    actions: '操作',
    views: '查看',
    edit: '编辑'
  },
  preparationEdit: {
    ApplicationForm: '申请订单',
    ApplicationFormNo: '申请单号',
    CustomerName: '客户名称',
    PleaseEnterCustomerName: '请选择客户',

    deliveryDate: '交付日期',
    PleaseSelectDeliveryDate: '选择日期',
    UrgentDelivery: '是否加急',
    urgentReason: '加急原因',
    PleaseEnterUrgentReason: '请输入加急原因',
    ReviewComments: '评审意见',
    PleaseEnterReviewComments: '请输入评审意见',

    ProductList: '产品列表',
    DownloadTemplate: '下载模板',
    AddProducts: '添加产品',
    BatchImport: '批量导入',
    ProductName: '产品名称',
    CustomerProductCode: '客户产品代码',
    CardBaseNumber: '卡基编号',

    BackupType: '备库类型',
    BackupQuantity: '备库数量',
    BranchMessage: '分行信息',
    ProductType: '产品类型',

    Remarks: '备注',
    Actions: '操作',
    edit: '编辑',
    delete: '删除',

    Approva: '审 核',
    Save: '保 存',
    Submit: '提 交',
    Cancel: '取 消',
    Back: '返 回',

    PleaseEnterDeliveryDate: '请填写交付日期',
    SubmitSuccess: '提交成功',
    SaveSuccess: '保存成功',
    PleaseSelectCommentsResult: '请选择评审结果',
    CancelApply: '是否取消申请?',
    Tips: '提示',
    Submit2: '确定',
    Cancel2: '取消',
    CancelSuccess: '取消成功',
    ImportBackupListTemplate: '备库列表导入模版',
    ImportFileError: '导入文件失败，请检查数据格式是否正确！',

    // finishedProductBackup: '成品备库',
    // semiProductBackup: '半成品备库',
    // CardProduct: '卡产品',
    // nonCardProduct: '非卡产品',
    // PleaseCheckBackupQuantity: '请检查备请检查备库数量，范围库数量'

    saleLeader: '销售领导',
    LeaderConfirmationTime: '领导确认时间',

    BackupTypeError: '备库类型错误',
    ProductTypeError: '产品类型错误',
    QuantityError: '备库数量错误',
    QuantityRangeError: '备库数量范围错误',

    XlsHeaderInfos: {
      ProductName: '产品名称',
      CustomerProductCode: '客户产品代码',
      GSCCode: '卡基编号',
      BackupType: '备库类型',
      Quantity: '备库数量',
      BranchInfo: '分行信息',
      ProductType: '产品类型',
      Remarks: '备注'
    }
  },
  preparationProductDialog: {
    FillBackupProduct: '备库产品填写',
    productName: '产品名称',
    PleaseSelectProduct: '请输入选择产品',
    cardNumber: '卡款编号',
    customerProductCode: '客户产品编号',
    BackupType: '备库类型',
    BackupQuantity: '备库数量',
    PleaseSelectType: '请选择类型',
    BranchMessage: '分行信息',
    ProductType: '产品类型',
    Remarks: '备注',
    PleaseEnterRemarks: '请输入备注',
    Closed: '关闭',
    Save: '保存',
    zero: '零',
    // '零', '一', '二', '三', '四', '五', '六', '七', '八', '九'九['', '十', '百', '千'] '万', '亿'
    // one: '一',
    // two: '二',
    // 三: '三',
    // 四: '四',
    // 五: '五',
    // 六: '六',
    // 七: '七',
    // 八: '八',
    // 九: '九',
    // 十: '十',
    // 百: '百',
    // 千: '千',
    // 万: '万',
    // 亿: '亿',
    PleaseEnterApplyQuantity: '请填写申请数量',
    PleaseEnterCorrectQuantity: '请填写正确数量值',
    ApplyQuantityMorethanZero: '申请数量不能小于零',
    // PleaseSelectProduct: '请选择产品',
    PleaseSelectProductType: '请选择卡产品类型',
    PleaseSelectCustomer: '请选择客户'
  },

  typeData: {
    FreeSampleCard: '免费样卡',
    InboundSampleCard: '入库样卡',
    ChargeSampleCard: '收费样卡',
    // 半成品备库: '半成品备库',
    // 卡产品: '卡产品',
    // 非卡产品: '非卡产品'
    finishedProductBackup: '成品备库',
    semiProductBackup: '半成品备库',
    CardProduct: '卡产品',
    nonCardProduct: '非卡产品',

    LeaderApproval: '领导审批',
    ManagementApproval: '经理审批',
    SalesConfirmation: '销售审批',
    Tobesubmitted: '待提交',
    Completed: '完成',
    Cancelled: '取消',

    //样卡要素radio
    // SampleCardFunction: 'Sample Card Function',
    None: '无',
    NewCardConfirmation: '新卡确认',
    TenderSubmission: '投标',
    InspectionCertification: '送检/ 认证',
    SubmissionForApproval: '报批',
    Other: '其他',

    //正面印和刷要素radio
    CardBaseImage: '正面底图图案 ',
    ClientLogo: '正面客户LOGO',
    CardSchemeLogo: '正面卡组织LOGO',
    SpecialEffects: '正面特殊工艺',

    //背面印和刷要素 radio
    BackImage: '背面地图图案',
    Text: '背面文字',
    BackClientLogo: '背面客户LOGO',
    BackCardSchemeLogo: '背面卡组织LOGO',
    BackSpecialEffects: '背面特殊工艺',
    BlankWhiteBack: '背面纯白无内容',

    //其他要素 radio
    SignaturePanel: '签名条',
    Hologram: '全息图',
    Magstripe: '磁条',
    MetalSticker: '立金',
    HotStamping: '烫印箔',
    FunctionalEMVChip: '真芯片',
    DummyEMVChip: '假芯片',
    RealInlay: '真INLAY',

    //个人化需求  radios

    CardSurfacePersonalisation: '卡面个人化',
    MagstripePersonalisation: '磁条个人化',
    ChipPrePersonalisation: '芯片预个人化',
    ChipPersonalisation: '芯片个人化',

    //PM检测要求 radio
    MagstripePhysicalProperties: '磁条物理特性',
    MagstripePersonalisationData: '磁条个人化数据',
    ChipPhysicalProperties: '芯片物理特性',
    COSVersionChip: '芯片COS版本',
    ChipPersonalisationData: '芯片个人化数据',

    //安全处理要求radio
    Holepunch: '打孔',
    VoidStamp: '盖作废章',
    ScratchedMagstripe: '划磁',
    NoTreatment: '不做处理'
  },
  appointSaleDialog: {
    selectedSaleMan: '审批人员选择',
    saleMen: '审批人员',
    PleaseSelectedSaleMan: '请选择审批人员',
    closed: '关 闭',
    submit: '确 认',
    submitting: '提交中...'
  }
}
