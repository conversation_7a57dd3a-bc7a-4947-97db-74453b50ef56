/*
 * @Author: HoJack
 * @Date: 2023-12-13 09:23:26
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-13 09:26:50
 * @Description:
 */
export default {
  home: '首页',
  login: '登录',
  ssoCallback: '单点登录回调页面',
  level: '多级菜单',
  menu: '菜单',
  menu1: '菜单1',
  menu11: '菜单1-1',
  menu111: '菜单1-1-1',
  menu12: '菜单1-2',
  menu2: '菜单2',
  dashboard: '首页',
  analysis: '分析页',
  workplace: '工作台',
  guide: '引导',
  component: '组件',
  icon: '图标',
  echart: '图表',
  countTo: '数字动画',
  watermark: '水印',
  qrcode: '二维码',
  highlight: '高亮',
  infotip: '信息提示',
  form: '表单',
  defaultForm: '全部示例',
  search: '查询',
  table: '表格',
  defaultTable: '基础示例',
  editor: '编辑器',
  richText: '富文本',
  dialog: '弹窗',
  imageViewer: '图片预览',
  descriptions: '描述',
  example: '综合示例',
  exampleDialog: '综合示例 - 弹窗',
  examplePage: '综合示例 - 页面',
  exampleAdd: '综合示例 - 新增',
  exampleEdit: '综合示例 - 编辑',
  exampleDetail: '综合示例 - 详情',
  errorPage: '错误页面',
  authorization: '权限管理',
  user: '用户管理',
  role: '角色管理',
  document: '文档',
  inputPassword: '密码输入框',
  sticky: '黏性',
  Algorithm: '算法管理',
  accountControl: '账户中心'
}
