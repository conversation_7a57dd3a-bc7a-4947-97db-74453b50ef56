/*
 * @Author: HoJack
 * @Date: 2023-12-13 09:23:26
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-13 09:27:49
 * @Description:
 */
export default {
  api: {
    operationFailed: '操作失败',
    errorTip: '错误提示',
    errorMessage: '操作失败,系统异常!',
    timeoutMessage: '登录超时,请重新登录!',
    apiTimeoutMessage: '接口请求超时,请刷新页面重试!',
    apiRequestFailed: '请求出错，请稍候重试',
    networkException: '网络异常',
    networkExceptionMsg: '网络异常，请检查您的网络连接是否正常!',
    errMsg401: '用户没有权限（令牌、用户名、密码错误）!',
    errMsg403: '用户得到授权，但是访问是被禁止的。!',
    errMsg404: '网络请求错误,未找到该资源!',
    errMsg405: '网络请求错误,请求方法未允许!',
    errMsg408: '网络请求超时!',
    errMsg500: '服务器错误,请联系管理员!',
    errMsg501: '网络未实现!',
    errMsg502: '网络错误!',
    errMsg503: '服务不可用，服务器暂时过载或维护!',
    errMsg504: '网络超时!',
    errMsg505: 'http版本不支持该请求!',
    errMsg901: '演示模式，无法进行写操作!'
  },
  app: {
    logoutTip: '温馨提醒',
    logoutMessage: '是否确认退出系统?',
    menuLoading: '菜单加载中...',
    loading: '加载中...'
  },
  exception: {
    backLogin: '返回登录',
    backHome: '返回首页',
    subTitle403: '抱歉，您无权访问此页面。',
    subTitle404: '抱歉，您访问的页面不存在。',
    subTitle500: '抱歉，服务器报告错误。',
    noDataTitle: '当前页无数据',
    networkErrorTitle: '网络错误',
    networkErrorSubTitle: '抱歉，您的网络连接已断开，请检查您的网络！'
  },
  lock: {
    unlock: '点击解锁',
    alert: '锁屏密码错误',
    backToLogin: '返回登录',
    entry: '进入系统',
    placeholder: '请输入锁屏密码或者用户密码'
  },
  login: {
    backSignIn: '返回',
    signInFormTitle: '登录',
    ssoFormTitle: '三方授权',
    mobileSignInFormTitle: '手机登录',
    qrSignInFormTitle: '二维码登录',
    signUpFormTitle: '注册',
    forgetFormTitle: '重置密码',
    signInTitle: '开箱即用的中后台管理系统',
    signInDesc: '输入您的个人详细信息开始使用！',
    policy: '我同意xxx隐私政策',
    scanSign: `扫码后点击"确认"，即可完成登录`,
    loginButton: '登录',
    registerButton: '注册',
    rememberMe: '记住我',
    forgetPassword: '忘记密码?',
    otherSignIn: '其他登录方式',
    // notify
    loginSuccessTitle: '登录成功',
    loginSuccessDesc: '欢迎回来',
    // placeholder
    accountPlaceholder: '请输入账号',
    passwordPlaceholder: '请输入密码',
    smsPlaceholder: '请输入验证码',
    mobilePlaceholder: '请输入手机号码',
    policyPlaceholder: '勾选后才能注册',
    diffPwd: '两次输入密码不一致',
    userName: '账号',
    password: '密码',
    confirmPassword: '确认密码',
    email: '邮箱',
    smsCode: '短信验证码',
    mobile: '手机号码',
    ssoAuthTip: 'SSO 授权后的回调页',
    authCode: '授权码: ',
    usingCode: '正在使用 code 授权码，进行 accessToken 访问令牌的获取',
    getToken: '获取token',
    loginFail: '登录失败',
    ssoLoading: '正在获取访问权限,请稍等...'
  },
  permission: {
    hasPermission: '请设置操作权限值',
    loginInvalid: '登录已失效'
  },
  errorCode: {
    code401: '认证失败，无法访问系统资源',
    code403: '当前操作没有权限',
    code404: '访问资源不存在',
    codeDefault: '系统未知错误，请反馈给管理员'
  },
  service: {
    invalidToken: '无效的刷新令牌',
    expiredToken: '刷新令牌已过期',
    code901: 'code为901,请联系管理员',
    unFindRole: '未能找到用户角色,登录已失效,请重新登录!',
    loginInvalid: '登录已失效',
    pleaseRelogin: ',请重新登录!'
  },
  hooks: {
    web: {
      validfail: '校验失败',
      pleaseEnterCrrentPhoneNum: '请输入正确的手机号'
    }
  },
  layout: {
    emailDropdown: {
      personalCenter: '个人中心',
      changePassword: '修改密码',
      unbindEmail: '未绑定邮箱'
    },
    roleDropdown: {
      roleOverdue: '该角色已过期，请联系管理员重新创建',
      checkingRole: '切换角色中...',
      checkingRoleTip: '切换到该角色将跳转至其他应用，是否继续？',
      checkingRoleFail: '切换角色失败',
      unallocatedRole: '未分配角色'
    },
    userInfo: {
      noUserName: '无用户名',
      noDeptData: '暂无部门数据'
    },
    collapse: {
      collapse: '收起菜单栏',
      expand: '展开菜单栏',
      tips: '屏幕宽度过小，为了更好的体验,已折叠菜单，可点击左上角图标展开菜单'
    }
  },
  utils: {
    formatTime: {
      just: '刚刚',
      beforeSec: '秒前',
      beforeMin: '分钟前',
      beforeHour: '小时前',
      beforeDay: '天前',
      goodearlyMorning: '凌晨好',
      goodMorning: '早上好',
      goodforenoon: '上午好',
      goodnoon: '中午好',
      goodafternoon: '下午好',
      gooddusk: '傍晚好',
      goodevening: '晚上好',
      goodLateNight: '夜里好',
      day: '天',
      hour: '小时',
      min: '分钟',
      sec: '秒'
    }
  },
  AccountControl: {
    accountInformation: '账号信息',
    enterpriseInformation: '企业信息',
    username: '账号',
    mobile: '手机号码',
    email: '邮箱',
    nickName: '员工姓名',
    deptName: '部门',
    createTime: '注册时间',
    nodata: '暂无数据',
    uscc: '社会统一信用代码',
    customerNameNodata: '企业名称暂无数据',
    cusRelated: '关联企业/机构',
    deptRelated: '部门关系',
    deptNodata: '暂无部门数据',
    openServices: '开通服务',
    clearingForm: '结算方式',
    noTopDept: '无顶级部门'
  },
  footer: {
    Copyright: 'Copyright ©2024-智融金服科技（珠海）有限公司 All Rights Reserved',
    icp: '粤ICP备********号'
  }
}
