export default {
  common: {
    title: '标题',
    time: '时间',
    sortNum: '序号',
    jumpTo: '跳至',
    select: '选择',
    selected: '已选',
    startDate: '开始日期',
    endDate: '结束日期',
    status: '状态',
    createTime: '创建时间',
    refresh: '刷新',
    detail: '详情',
    reason: '原因',
    deal: '处理',
    pass: '通过',
    notPass: '不通过',
    transfer: '转办',
    delegate: '委派',
    copy: '抄送',
    task: '任务',
    processing: '处理中',
    more: '更多',
    new: '新'
  },
  businessTodo: {
    productApproval: '产品审批',
    contractApproval: '合同审批',
    imageApproval: '图像审批',
    prodValidate: '生产验证',
    markCardDemand: '制卡需求',
    designScheme: '设计方案',
    designArchive: '设计归档',
    draftScheme: '稿样方案',
    draftArchive: '稿样归档',
    todoType: '待办类型',
    initiator: '发起人',
    receiveTime: '接收时间',
    transfer: '转办',
    myTransfer: '我转办的',
    transferMy: '转办我的',
    transferPerson: '转办人',
    transferedPerson: '受转人',
    transferType: '转办类型',
    initiationTime: '发起时间',
    total: '共',
    totalNum: '条记录',
    totalNo: '第',
    pageNo: '页',
    todoList: '业务待办',
    transferRecord: '转办记录'
  },
  components: {
    transferLog: '日志',
    staffId: '员工编号',
    canNotTransferTips: '不能转办给当前登录的用户',
    staffName: '员工名称',
    selectStaff: '选择成员',
    transferReason: '转办原因'
  },
  flowTodo: {
    flowStatus: '流程状态',
    copyTime: '抄送时间',
    taskName: '任务名称',
    taskId: '任务编号',
    flowName: '流程名称',
    taskAssigneeUserNickname: '被委派人',
    taskOwnerUserNickname: '委派人',
    myTaskAssignee: '我委派的',
    toTaskAssignee: '被委派的',
    approvalId: '审批编号',
    flowTitle: '流程标题',
    operateStep: '操作步骤',
    flowInitiator: '流程发起人',
    taskReason: '委派原因',
    dealTime: '处理时间',
    taskStatus: '任务状态',
    flowBelong: '所属流程',
    isNeed: '是否要',
    application: '申请',
    activate: '激活',
    unActivate: '挂起',
    baseInfo: '基本信息',
    flowLog: '流程日志',
    flowChart: '流程图',
    approvalTask: '审批任务',
    flowName2: '流程名',
    taskTo: '委派任务',
    taskToWho: '委派给',
    approvalSuggest: '审批建议',
    copyPerson: '抄送人',
    applicateInfo: '申请信息',
    flowBindTips: '流程没有绑定业务表单，请上综合服务平台上绑定',
    notBindForm: '暂未绑定表单',
    approvalSuggestTips: '审批建议不能为空',
    approvalPassSuccess: '审批通过成功',
    approvalPassFailed: '审批不通过成功',
    searchFlowInfoNull: '查询不到流程信息',
    flowBindCompTips: '流程绑定业务组件表单有误,请检查组件路径是否存在',
    approvalRecord: '审批记录',
    approvalUser: '审批人',
    operateAction: '操作动作',
    nodeName: '节点名称',
    approvalTime: '审批时间',
    useTime: '耗时',
    operateUser: '操作人',
    noDepartment: '暂无部门',
    taskToDeal: '任务待处理',
    approvalSuggestNull: '审批意见为空',
    nextNodeApprovalUser: '下节点审批人设置',
    ruleType: '规则类型',
    specifyRole: '指定角色',
    specifyDepartment: '指定部门',
    loadingWait: '加载中，请稍后',
    specifyPost: '指定岗位',
    specifyUser: '指定用户',
    specifyUserGroup: '指定用户组',
    specifyScript: '指定脚本',

    ruleTypeTips: '规则类型不能为空',
    specifyRoleTips: '指定角色不能为空',
    specifyDepartmentTips: '指定部门不能为空',
    specifyPostTips: '指定岗位不能为空',
    specifyUserTips: '指定用户不能为空',
    specifyUserGroupTips: '指定用户组不能为空',
    specifyScriptTips: '指定脚本不能为空',

    newApprovalUser: '新审批人',
    newApprovalUserTips: '新审批人不能为空',

    myTodo: '我的待办',
    myTodoDone: '我的已办',
    myInitiated: '我发起的',
    myCC: '抄送我的',

    flowCannotJump: '该流程待办,无法跳转',
    taskCannotJump: '该业务待办,无法跳转',
    noTodo: '暂无待办',
    flowTodo: '流程待办',
    myMessage: '我的消息',
    noMessage: '暂无消息'
  }
}
