export default {
  orderApproval: {
    orderNodeStatus: '訂單籤審流程節點狀態',
    customerName: '客戶名稱',
    productName: '產品名稱',
    nodeName: '節點名稱',
    handler: '處理人員',
    handlerTime: '處理時間',
    placeholderTextTip: '請輸入{placeholder}',
    placeholderSelectTip: '請選擇{placeholder}',
    taskCode: '任務編號',
    salesman: '業務員',
    orderType: '訂單類型',
    auditStatus: '籤審狀態',
    unfold: '展開',
    packUp: '收起',
    unaudited: '未籤審',
    audited: '已籤審',
    unSaleReview: '未籤',
    saleReview: '已籤',
    batchOrder: '批量訂單',
    freeSampleCard: '免費樣卡',
    chargeSampleCard: '收費樣卡',
    finishedProductWarehousing: '成品備庫',
    sampleCardWarehousing: '入庫樣卡',
    semiWarehousing: '半成品備庫',
    orderNum: '下單數量',
    audit: '籤審',
    viewDetail: '查看詳情',
    deliveryTime: '交付時間',
    deliveryMode: '交付方式',
    orderCreator: '訂單創建人',
    orderCreationTime: '訂單創建時間',
    processNode: '流程節點',
    orderAudit: '訂單籤審',
    orderInfo: '訂單信息',
    taskStatus: '任務狀態',
    orderCode: '訂單編號',
    orderProductRemark: '產品備註',
    orderRemark: '下單備註',
    submitSuccess: '提交成功',
    cardStyleInfo: '卡款信息',
    cardStyleName: '卡款名稱',
    mainCardNum: '主卡號',
    cardStyleCode: 'GSC卡號',
    cardStyleCodeAll: 'GSC卡號全稱',
    latestK3Code: 'K3下單編號',

    batchTime: '卡號月年',
    cardPlanNum: '卡方案數',
    enCapType: '封裝類型',
    finishedCardModelModule: '成品卡款模塊',
    semiCardModelModule: '半成品卡款模塊',
    chipType: '芯片類型',
    trueChip: '真芯片',
    dummyChip: '假芯片',
    noChip: '無芯片',
    chipCode: '芯片型號',
    chipSupplier: '供應商',
    moduleTransferInterfaceType: '界面類型',
    maxCode: '模塊大號',

    color: '顏色',
    capacity: '容量',
    maskCode: '掩模號',
    shape: '模塊外形',
    oiOrModuleCode: 'OI或模塊代碼',
    antenna: '天線',
    aerialType: '天線類型',

    property: '產權屬性',
    stripe: '模塊條帶',
    chipCapacitance: '芯片電容',

    finishedStock: '成品庫存量',
    semifinishedStock: '半成品庫存量',
    cardOrgUsableFlag: '卡組織複用',
    reusable: '可複用',
    nonReusable: '不可複用',
    primaryFlag: '主卡號標識',
    yes: '是',
    no: '否',
    finalDrafts: '稿樣終稿圖片',
    cardNoteList: '版本變更履歷',
    cardReviewResultList: '評審記錄',

    auditRes: '評審結果',
    auditRemark: '評審意見',
    handleTime: '處理時間'
  },
  orderSearch: {
    fileList: '下單憑證',
    orderInfo: '訂單信息',
    customerInfo: '客戶信息',
    productInfo: '產品信息',
    download: '下載',
    unitPrice: '單價',
    productName: '產品名稱',
    cardCount: '數量',
    isIndividual: '是否寫入個人化物料',
    yes: '是',
    no: '否',
    empty: '暫無數據',
    mailAddress: '郵寄地址',
    logisticsInfo: '物流信息',
    cardDeliveryDate: '卡商預計交付日期',
    mailNo: '運單信息',
    OrderStatus: '訂單狀態',
    logisticsTracking: '物流跟蹤',
    isUrgent: '是否加急',
    deliveryMethod: '交付方式',
    orderSource: '訂單來源',
    customerCreateOrder: '下單客戶',
    orderTotalPrice: '訂單總價',
    deliveryTime: '期望交付時間',
    orderType: '訂單類型',
    customerOrderReceiveTime: '客戶訂單接收時間',
    ordered: '已下單',
    selectCustomerPlaceholder: '請選擇客戶',
    management: '管理端',
    customer: '客戶端',
    sale: '銷售端',
    tip: '提示',
    customerName: '客戶名稱',
    all: '全部',
    revocationOrderConfirm: '是否撤銷訂單?',
    orderRevocationSuccess: '訂單撤銷成功',
    UMVOrderCodePlaceholder: '請輸入UMV訂單編號',
    productNamePlaceholder: '請輸入產品名稱',
    cardProductList: '卡產品訂單列表',
    orderWrite: '訂單錄入',
    UMVOrderCode: 'UMV訂單編號',
    more: '更多',
    productNum: '產品數量',
    price: '價格',
    source: '來源',
    updateTime: '更新時間',
    createTime: '創建時間',
    orderCode: '訂單編號',
    view: '查看',
    revocation: '撤銷',
    remarkNote: '備註說明',
    addressOfPeople: '聯繫人',
    tel: '聯繫電話',
    creator: '創建人',
    creatorAcc: '創建人賬號',
    storage: '入庫代存',
    customerPick: '自提',
    mail: '郵寄',
    customerOrderCode: '客戶訂單編號',
    salesman: '銷售人員',
    salesmanPlaceholder: '請輸入銷售人員',
    completionDate: '完成時間',
    cardOrganization: '卡組織',

    //Excel模板表頭，需要與模板中一致, 如用中文模板上傳則不需要翻譯
    excelHeaderOfProductName: '產品名稱',
    excelHeaderOfNum: '下單數量',
    excelHeaderOfType: '類型',
    excelHeaderOfDeliveryTime: '交付時間',
    excelHeaderOfDeliveryMethod: '交付方式',
    excelHeaderOfPackageMode: '包裝方式',
    excelHeaderOfInnerBox: '內盒',
    excelHeaderOfOuterBox: '外箱',
    excelHeaderOfProductPrice: '產品單價',
    excelHeaderOfWrite31: '寫入31物料',
    excelHeaderOfCompanyCode: '我司代碼',
    excelHeaderOfCustomerCode: '客戶代碼',
    excelHeaderOfRemark: '備註',
    //Excel模板卡產品類型，需要與模板中一致, 如用中文模板上傳則不需要翻譯
    excelCardProduct: '卡產品',
    excelNonCardProduct: '非卡產品',
    //Excel模板是否寫入個人化，需要與模板中一致, 如用中文模板上傳則不需要翻譯
    excelWrite: '寫入',
    taskCode: '任務編號',

    viewMyself: '只看自己',
    saleConfirm: '銷售確認',
    orderCodeNullTip: '訂單編號不能為空,請重新選擇',

    noStatus: '暫無狀態',
    createTimeNotNull: '創建時間不能為空'
  },
  proxyCustomerToOrder: {
    productName: '產品名稱',
    unitPrice: '單價',
    cardCode: 'GSC卡號',
    customerProuductCode: '客戶代碼',
    noNullForCustomer: '所屬客戶不能為空',
    noNullForAddress: '地址名稱不能為空',
    noNullForPeople: '收件人不能為空',
    noNullForPhone: '聯繫電話不能為空',
    noNullForMailAddress: '寄送地址不能為空',
    addSuccess: '地址成功', // 前面跟新增或編輯
    addErr: '地址失敗', // 前面跟新增或編輯
    receivingInfo: '收件信息', // 前面跟新增或編輯
    addressName: '地址名稱',
    addressNamePlaceholder: '請輸入地址名稱',
    area: '寄送地址',
    areaPlaceholder: '請輸入寄送地址',
    orderNum: '下單數量',
    type: '類型',
    companyCode: '我司代碼',
    chooseDayTime: '選擇日期時間',
    productUnitPrice: '產品單價',
    customerCode: '客戶代碼',
    write31Materiel: '是否寫入31物料',
    outerBox: '外箱',
    beingSubmitted: '提交中',
    inputOrderNumPlaceholder: '請輸入下單數量',
    orderNumSizeTip: '下單數量必須大於零',
    cardProduct: '卡產品',
    nonCardProduct: '非卡產品',
    theCustomerNoProduct: '該客戶下查詢不到產品',
    selectProductPlaceholder: '請選擇產品',
    selectGoodPlaceholder: '選擇商品',
    greaterThan: '大於',
    lessThan: '小於',
    inventory: '庫存',
    namePlaceholder: '請輸入選擇名稱',
    userPlaceholder: '請輸入選擇收件人',
    uploadErr: '上傳失敗',
    createSuccess: '創建成功',
    selectOrderType: '請選擇訂單類型',
    selectTime: '請選擇時間',
    uploadLimit: '上傳超出限制,終止上傳',
    urgentExplain: '加急說明',
    urgentExplainPlaceholder: '請輸入加急說明',
    customerCodePlaceholder: '請輸入客戶訂單編號',
    addOrderProduct: '添加下單產品',
    inputCardNamePlaceholder: '請輸入卡款名稱',
    chooseProduct: '選擇產品',
    writeProduct: '錄入產品',
    importProduct: '導入產品',
    write: '寫入',
    nonWrite: '不寫入',
    remark: '備註信息',
    remarkPlaceholder: '請輸入備註信息',
    orderTypeNotNull: '訂單類型不能為空',
    customerOrderReceiveTimeNotNull: '客戶訂單接收時間不能為空',
    urgentNotNull: '是否加急不能為空',
    urgentExplainNotNull: '加急說明不能為空',
    customerNameNotNull: '客戶名稱不能為空',
    deliveryTimeNotNull: '期望交付時間不能為空',
    userNotNull: '聯繫人不能為空',
    deliveryMethodNotNull: '交付方式不能為空',
    fileListNotNull: '下單憑證不能為空',
    pleaseInputRequired: '請輸入必填項!',
    productNotNull: '下單產品不能為空',
    productNumNotNull: '請填寫產品數量',
    uploadLimitReUpload: '文件大小超出限制,請重新上傳！',
    checkFileFormat: '請檢查附件格式重新上傳！',
    importFileErr: '導入文件失敗，請檢查數據格式是否正確！',
    checkOrderNum: '請檢查下單數量，範圍1-{maxNum}',
    inputNumInfo: '請填寫完整數量信息',
    inputPriceInfo: '請填寫完整單價信息',
    numNoTrue: '數量只能為正整數',
    priceNoTrue: '單價格式不正確',
    deliveryTimeFormatNoTrue: '期望交付時間格式不正確，請輸入YYYY-MM-DD格式',

    createOrder: '立刻下單',
    orderSaveSuccess: '訂單暫存成功\n是否離開當前頁面？',
    hasSaveOrder: '存在暫存訂單，是否恢復？',

    salesman: '業務員（銷售）',
    salesmanTip: '請選擇業務員（銷售）',
    cardAddPersion: '卡款添加人',
    cardAddPersionTip: '請選擇卡款添加人',
    moreTip: '上傳超出限制,終止上傳',
    exportOrderFileName: '訂單錄入產品導入模版',

    endCustomer: '終端客戶',
    endCustomerPlaceholder: '請選擇終端客戶',

    terminalSelectIsNull: '終端客戶不能為空',

    totalPrice: '總價'
  }
}
