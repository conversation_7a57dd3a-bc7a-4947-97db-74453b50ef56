export default {
  batchCardProduct: {
    merchantCardDetail: {
      cardAssociations: '卡組織',
      cardCode: 'GSC卡號', //卡款代碼改GSC卡號
      cardDetail: '獲取卡款產品詳情失敗',
      cardType: '卡款類型',
      cardFaceSpec: '卡面規格',
      equalTo: '等於',
      equalToLessThan: '小於等於',
      greaterThanOrEqual: '大於等於',
      productNum: '客戶代碼', //產品編號改客戶代碼
      quickOrder: '快速下單',
      viewSampleFile: '查看稿樣',
      withoutSampleFile: '未確定最終稿',
      notSampleFile: '暫無稿樣',
      historyOrder: '歷史訂單',
      orderCode: '訂單編號',
      orderPrice: '訂單價格',
      orderProductNum: '數量',
      orderTime: '時間',
      shoppingCart: '加入購物車',
      successfully: '加入購物車成功',
      support: '支持',
      surfaceBack: '表面特性背面',
      surfacePositive: '表面特性正面',
      technology: '工藝',
      unitPrice: '單價',
      updateTime: '更新時間',
      createTime: '創建時間',
      otherVersion: '其他版本',
      priceRange: '區間',
      quantity: '數量',
      sheet: '張',
      up: '以上',

      latestProduceVersion: '最後生產稿樣版本',
      latestGSCVersion: '最新稿樣版本'
    },
    merchantCardList: {
      addProduct: '新增產品',
      addShoppingCar: '加入購物車',
      batchOrder: '批量下單',
      closed: '已關閉',
      components: {
        detailDialog: {
          cannotEmpty: '不能為空！',
          pleaseEnter: '請輸入',
          saleName: '產品名稱'
        },
        search: {
          productName: '產品名稱',
          productStatus: '產品狀態'
        }
      },
      craftFiles: '規格書',
      createTime: '創建時間',
      currentNone: '暫無',
      detail: '詳情',
      downloadCraftFiles: '下載規格書',
      downloadCraftFilesAbnormal: '下載規格書異常：',
      downloadCraftFilesFail: '下載規格書失敗!',
      noClientIdTips: '該用戶無客戶Id,請聯系管理員',
      orderNumber: '序號',
      productBackView: '產品背面圖',
      productFrontView: '產品正面圖',
      productName: '產品名稱',
      clientProductUniqueCode: '客戶代碼', //產品編號改客戶代碼
      productCode: 'GSC卡號', //卡款代碼改GSC卡號
      quickOrder: '快速下單',
      shoppingCart: '加入購物車成功',
      startUse: '啟用',
      startUsed: '已啟用',
      status: '狀態',
      toBeDesigned: '待設計',
      updateTime: '更新時間',
      searchProductName: '搜索產品名稱',
      searchProductCode: '搜索客戶代碼', //產品編號改客戶代碼
      searchCardCode: '搜索GSC卡號', //卡款代碼改GSC卡號
      selectProductStatus: '選擇產品狀態',
      downloadTemplateFile: '模板下載',
      importCreateOrder: '導入下單',
      orderTemplateFile: '下單模板文件',
      excelFormatError: '導入文件失敗，請檢查數據格式是否正確！',
      notFindProduct: '未找到产品',
      createOrder: '創建訂單'
    },
    orderConfirm: {
      addAddress: '新增地址',
      address: '收獲地址不能為空',
      address1: '地址',
      addressName: '地址名稱',
      components: {
        AddAddressDialog: {
          add: '新增',
          address: '地址名稱',
          address1: '請輸入地址名稱',
          addressName: '地址名稱不能為空',
          belonging: '所屬客戶不能為空',
          default: '是否為默認不能為空',
          defaultOrNot: '是否為默認',
          detailAddress: '請輸入詳細地址',
          edit: '編輯',
          enterRecipient: '請輸入收件人',
          enterTelephone: '請輸入手機號',
          mailing: '寄送地址不能為空',
          name: '收件人',
          no: '否',
          phone: '手機號',
          receiving: '收件信息',
          recipient: '收件人不能為空',
          sendAddress: '寄送地址',
          telephone: '手機號不能為空',
          yes: '是'
        },
        SelectAddressDialog: {
          address: '收件地址',
          name: '名稱',
          phone: '手機號',
          recipient: '請輸入選擇收件人',
          recipient1: '收件人',
          selectAddress: '選擇地址',
          selectName: '請輸入選擇名稱'
        },
        AddProductDialog: {
          products: '產品信息',
          productionName: '產品名稱',
          inputProductionName: '請輸入產品名稱',

          order: '訂購數量',
          inputOrderNum: '請輸入訂購數量',
          productCode: '客戶代碼', //產品編號改客戶代碼
          inputProductCode: '請輸入產品代碼',

          cardNumber: 'GSC卡號', //卡款編號改GSC卡號
          inputCardNumber: '請輸入GSC卡號', //卡款編號改GSC卡號

          unitPrice: '單價',
          inputUnitPrice: '請輸入單價',
          close: '關 閉',
          select: '選 擇'
        },
        productList: {
          no: '否',
          order: '訂購數量',
          personalized: '是否寫入個人化物料',
          products: '產品信息',
          productCode: '客戶代碼', //產品編號改客戶代碼
          cardNumber: 'GSC卡號', //卡款編號改GSC卡號
          draftFile: '稿樣文件',
          approvalFile: '報批文件',
          unitPrice: '單價',
          yes: '是',
          uploadApprovalFile: '報批文件上傳',
          uploadCardApprovalAbnormal: '報批文件上傳異常：',
          download: '點擊下載',
          minProductCount: '不能小於最小下單數量'
        },
        upload: {
          clickAdd: '點此添加',
          dragging: '拖拽圖片到這裏，或',
          exceeding: '超出上傳文件數量限製！',
          fileType: '文件數量最多8個，大小10M以內，文件格式：.jpg、.png、.gif、.pdf',
          limit: '文件大小超出限製, 請重新上傳！'
        }
      },
      contactWay: '聯系方式',
      contacts: '聯系人不能為空',
      contacts1: '聯系人',
      delivery: '交付信息',
      deliveryMethod: '交付方式不能為空',
      deliveryMethods: '交付方式',
      enterContact: '請輸入聯系人',
      enterTelephone: '請輸入聯系電話',
      expectedDelivery: '期望交付時間不能為空',
      expectedDeliveryTime: '期望交付時間',
      fix: '張',
      individual: '個',
      inventory: '入庫代存',
      isItUrgent: '是否加急',
      mail: '郵寄',
      mailing: '郵寄方式不能為空',
      mailingAddress: '寄送地址產品數量與購買數量不一致，請修改後再次提交',
      memo: '備註信息',
      no: '否',
      number: '數量',
      orderCreation: '下單創建成功',
      orderNow: '立即下單',
      orderVoucher: '下單憑證',
      orderingProducts: '下單產品不能為空',
      pickup: '自提',
      pleaseEnter: '請輸入必填項',
      product: '產品',
      recipient: '收件人',
      selectAddress: '選擇地址',
      shippingMethod: '請選擇郵寄方式',
      telephone: '聯系電話不能為空',
      telephone1: '聯系電話',
      total: '訂單總價',
      urgent: '是否加急不能為空',
      yes: '是',
      communityWithSale: '郵寄(具體安排與銷售溝通)',
      sendToPersonalizationCenter: '送個人化中心',
      other: '其他',
      enterOtherContact: '請輸入備註信息',
      orderVoucherDesc: '下單憑證不能為空',
      notListed: '未啟用',
      addProduct: '產品錄入'
    },
    orderSuccess: {
      congratulations: '恭喜你下單成功！',
      goTo: '您可以前往',
      online: '[在線下單]',
      order: '[訂單管理]'
    },
    shoppingCart: {
      orderQuantity: '訂購數量',
      placeAnOrder: '下單',
      pleaseSelectOrder: '請勾選商品下單',
      price: '價格',
      product: '產品',
      productName: '產品名稱',
      quantity: '數量',
      sheet: '張',
      unitPrice: '單價',
      notListed: '未啟用',
      cardCode: 'GSC卡號', //卡款代码改GSC卡号
      productNum: '客戶代碼', //产品编号改客户代码
      unit: '個'
    }
  },
  mailingAddress: {
    address: '寄送地址',
    addressName: '地址名稱',
    addressee: '收件人',
    components: {
      search: {
        addAddress: '新增地址',
        refresh: '刷新',
        searchAddress: '搜索地址名稱',
        searchRecipient: '搜索收件人名稱'
      }
    },
    defaultAddress: '是否默認地址',
    delAddress: '確定要刪除當前地址嗎?',
    delSuccess: '刪除成功',
    phone: '聯系電話',
    serialNumber: '序號',
    setSuccess: '設置成功'
  },
  productDemand: {
    common: {
      batchCardProducts: '批卡產品',
      cardRequirements: '產品需求',
      client: '客戶端',
      confirmThePlan: '確認方案',
      designScheme: '設計方案',
      diyProducts: 'DIY產品',
      doYouWantToPerformThisOperation: '是否執行此操作？',
      editProductRequirements: '編輯產品需求',
      initiateProductRequirements: '發起產品需求',
      managementPlatform: '管理平臺',
      other: '其他',
      prompt: '提示',
      requirementClosure: '需求關閉',
      sampleCardApplication: '樣卡申請',
      sampleScheme: '稿樣方案',
      unionPay: '銀聯',
      viewDesignProposal: '查看設計方案',
      viewDraftProposal: '查看稿樣方案',
      viewReceiptInformation: '查看回執信息',
      view_3DFiles: '查看3D文件'
    },
    components: {
      cardBin: '卡BIN',
      cardOrganizationAndCardLevel: '卡組織及卡片級別',
      cardType: '卡款種類',
      cardTypes: '卡片類型',
      customerName: '客戶名稱',
      customerAccount: '客戶賬號',
      customerUploadsFiles: '客戶文件',
      demand: {
        cardBin: '卡BIN',
        customerDocuments: '客戶文件',
        designRequirements: '設計需求',
        editSuccessful: '編輯成功',
        pleaseEnterARequirementDescription: '請輸入需求描述',
        pleaseEnterATitle: '請輸入標題',
        pleaseEnterTheCardBin: '請輸入卡BIN',
        pleaseEnterTheProductSelection: '請輸入選擇產品',
        productName: '產品名稱',
        requirementDescription: '需求描述',
        requirementTitle: '需求標題',
        requirementType: '需求類型',
        rulesTips: '內容不能為空，請輸入信息！',
        sampleRequirements: '稿樣需求',
        selectFile: '選擇文件',
        successfullyAdded: '添加成功',
        uploadSuccessful: '上傳成功',
        // uploadTips: '文件大小100M以內，文件格式 .txt .doc .xlsx .jpg .png .gif .pdf .rar .zip .ppt'
        uploadTips:
          '文件數量最多8個，單個文件不超過100M，文件格式.text.doc.xlsx.jpg.png.gif.pdf.rar.zip.ppt等'
      },
      designRequirements: '設計需求',
      dialogModule: {
        attachment: '附件',
        cardName: '卡款名稱',
        cardPaymentCode: 'GSC卡號', //卡款代碼改GSC卡號
        check: '查看',
        confirmedSuccessful: '確認成功',
        designDescription: '設計說明',
        designScheme: '設計方案',
        display: '3D展示',
        download: '下載',
        downloadAll: '全部下載',
        msgTips: '是否確認選定此方案為最終方案，確認後將不可修改，請謹慎操作！',
        pleaseEnterReceiptInformation: '請輸入回執信息',
        receiptInformation: '回執信息',
        remarks: '稿樣說明',
        sampleFile: '稿樣文件',
        otherFile: '其他文件',
        submissionTime: '提交時間',
        uploadAttachments: '上傳附件',
        proposalComments: '方案意見',
        agree: '同意',
        reject: '駁回',
        stillNeedModify: '仍需修改',
        modifyOpinion: '修改意見',
        addFeedback: '追加反饋',
        round: '輪次',
        CreateTime: '創建時間',
        NeedToModify: '仍需修改',
        confirmPlan: '確認方案',
        feedback: '追加迴響',
        viewReceipts: '查看回執',
        customerFeedback: '客戶回饋',
        number: '序號',
        feedbackInformation: '反饋信息',
        attachmentInformation: '附件資訊',
        feedbackTime: '迴響時間',
        confirmed: '已確認',
        toBeConfirmed: '待確認',
        suggestion: '修改意見',
        pleaseEnter: '請輸入',
        confirmPlanTips: '是否確認選定此方案作為最終方案，確認後將不可修改，請慎重操作！'
      },
      download: '下載',
      expectedSubmissionDate: '預計提稿日期',
      fileUploadFailed: '文件上傳失敗',
      iMessage: {
        characters: '文字',
        clearImage: '清空圖片',
        complete: '完成',
        empty: '清空',
        errrotTips1: '登錄服務器失敗，請聯系管理員!',
        errrotTips2: '連接服務器失敗，請聯系管理員！',
        errrotTips3: '服務器失去響應，請聯系管理員！',
        errrotTips4: '鏈接失敗，正在嘗試重新連接！',
        fileUploadFailed: '文件上傳失敗',
        graffitiPen: '塗鴉筆',
        imageEditingArea: '圖片編輯區',
        interactiveRecording: '互動記錄',
        kjNovaClipper: '圖片裁剪',
        loading: '加載中',
        onLine: '在線',
        onlineCommunication: '在線溝通',
        pleaseEnterText: '請輸入文字',
        pleaseEnterTheContent: '請輸入內容',
        pleaseInput: '請輸入互動內容',
        preserve: '保存',
        rectangle: '矩形',
        rotateImage: '旋轉圖片',
        rotundity: '圓形',
        rubberEraser: '橡皮檫',
        saveImage: '保存圖片',
        send: '發送',
        sendDirectly: '直接發送',
        sendPictures: '發送圖片',
        thereSNothingMoreLeft: '沒有更多了',
        uploadImages: '上傳圖片'
      },
      otherAttachments: '其他附件',
      otherInstructions: '其他說明',
      pleaseEnterTheProductName: '請輸入產品名稱',
      pleaseEnterTheRequirementType: '請輸入需求類型',
      productName: '產品名稱',
      productType: '產品類型',
      relatedProjects: '關聯項目',
      requirementDescription: '需求描述',
      requirementNumber: '需求編號',
      requirementTitle: '需求標題',
      requirementType: '需求類型',
      sampleRequirements: '稿樣需求',
      searchRequirementTitle: '搜索需求標題',
      submissionTime: '提交時間',
      uploadFiles: '上傳文件',
      uploadSuccessful: '上傳成功',
      uploadTips1: '您移除了文件',
      uploadTips2: '文件大小超出限製, 請重新上傳！',
      uploadTips3: '請檢查附件格式重新上傳！',
      uploadTips4: '請等待文件上傳完成',
      uploadTips5: '超出上傳文件數量限製！',
      fileRepeat: '文件重復',
      whole: '全部'
    },
    demandDetail: {
      basicNeeds: '基本需求',
      cardName: '卡款名稱',
      cardNumber: 'GSC卡號', //卡款編號改GSC卡號
      cardPaymentCode: 'GSC卡號', //卡款代碼改GSC卡號
      closeDemand: '關閉需求',
      closingInstructions: '關閉說明',
      confirmThePlan: '確認方案',
      confirmed: '已確認',
      demonstration: '3D演示',
      designScheme: '設計方案',
      schemeName: '方案名称',
      designer: '設計師',
      designManager: '設計負責人',
      detailedRequirements: '詳細需求',
      errorTips1: '稿樣需求不支持在線溝通，請聯系管理員！',
      errorTips2: '請至少選中一條數據！',
      number: '序號',
      onlineCommunication: '在線溝通',
      operationTime: '操作時間',
      operator: '操作員',
      receiptInformation: '回執信息',
      sampleCardOrder: '樣卡訂單',
      samplePersonnel: '稿樣人員',
      sampleScheme: '稿樣方案',
      state: '狀態',
      toBeConfirmed: '待確認',
      updateTime: '更新時間',
      viewReceipts: '查看回執',
      cardPaymentCodeSearch: '通過GSC卡號搜索', //卡款代碼改GSC卡號
      cardNameSearch: '通過卡款名稱搜索',
      reject: '駁回',
      updateDate: '更新日期',
      applicationFormNumber: '申請單編號',
      applicationStatus: '申請狀態',
      creationTime: '創建時間',
      salesman: '銷售人員'
    },
    demandList: {
      currentStage: '當前階段',
      newProductRequirements: '新建產品需求',
      sourceOfDemand: '需求來源'
    },
    orderDetail: {
      address: '地址',
      addressName: '地址名稱',
      contactPhoneNumber: '聯系電話',
      contacts: '聯系人',
      creationTime: '創建時間',
      customerInformation: '客戶信息',
      customerSelfPickup: '客戶自提',
      download: '下載',
      errorTips1: '獲取訂單失敗，請稍後重試！',
      estimatedDeliveryTime: '預計交付時間',
      inventoryAndProxyStorage: '入庫代存',
      logisticsCompany: '物流公司',
      logisticsInformation: '物流信息',
      logisticsTracking: '物流追蹤',
      mail: '郵寄',
      mailingInformation: '郵寄信息',
      managementPlatform: '客戶端',
      no: '否',
      operator: '操作員',
      operatorAccount: '操作員賬號',
      creator: '創建人',
      creatorAccount: '創建人賬號',
      orderInformation: '訂單信息',
      orderNumber: '訂單編號',
      orderSource: '訂單來源',
      orderStatus: '訂單狀態',
      orderType: '訂單類型',
      orderVoucher: '下單憑證',
      orderingCustomers: '下單客戶',
      personalizedMaterials: '是否寫入個人化物料',
      productInformation: '產品信息',
      quantity: '數量（張）',
      recipients: '收件人',
      referencePrice: '參考價格',
      remarksDescription: '備註說明',
      shippingMethod: '發貨方式',
      shou: '收',
      totalOrderPrice: '訂單總價',
      updateTime: '更新時間',
      waybillNumber: '運單號',
      yes: '是',
      customerOrderCode: '客戶訂單編號'
    },
    designRecommend: {
      DesignerPerferred: '設計優選',
      IPCooperation: 'IP合作',
      ToExplore: '蒐索',
      CardCustomizationRequirement: '自定義卡面需求',
      Template: '版型',
      CardBodyColor: '卡基顏色',
      Theme: '主題',
      View3DCardFace: '查看3D卡面',
      RaiseRequirement: '提需求',
      SelectProposal: '選擇方案',
      DoNOTSelectProposal: '不使用方案',
      DPintroduction: 'IP介紹',
      NewCardType: '新上卡款',
      DesingerRecommanda: '設計師推薦',
      HotIP: '爆款IP',
      Classification: '分類',
      DesignIntroduction: '設計介紹'
    }
  },
  productOrder: {
    batchOrderDetail: {
      customer: '客戶端',
      deliveryDate: '卡商預計交付日期',
      mailingAddress: '郵寄地址',
      management: '管理端',
      sale: '銷售端',
      draftFile: '稿樣文件',
      approvalFile: '報批文件',
      orderStatus: '訂單狀態',
      uploadApprovalFile: '報批文件上傳',
      uploadCardApprovalAbnormal: '報批文件上傳異常：',
      download: '點擊下載',
      downloadPlmApproval: '下載PLM批文',
      downloadCardApprovalFail: '批文不存在!',
      downloadCardApprovalAbnormal: '下載批文異常：',
      statusNone: '評審中',
      statusStocking: '入庫中',
      statusMaking: '生產中',
      statusMaked: '已入庫',
      statusShipped: '已出貨'
    },
    noCardOrder: {
      addAddress: '添加地址',
      batchCardOrder: '批卡訂單',
      orderingProducts: '下單產品',
      pleaseEnterNoteInformation: '請輸入備註信息',
      pleaseSelectAnAddress: '請選擇地址',
      pleaseSelectOrderType: '請選擇訂單類型',
      tips1: '待訂單確認後由業務補充',
      tips2: '當前用戶沒有客戶，無法提交訂單',
      uploadTips1: '拖拽圖片到這裏',
      uploadTips2: '點此添加',
      uploadTips3: '文件數量最多8個，大小10MB以內',
      uploadTips4: '或'
    },
    noUploadedImages: '無上傳圖',
    orderDetail: {},
    orderList: {
      createTime: '創建時間',
      customerSExpectedDeliveryDate: '客戶期望交付日期',
      more: '更多',
      revoke: '撤銷',
      revokeSuccess: '撤銷成功',
      searchForProductName: '搜索產品名稱',
      searchOrderCode: '搜索訂單編號',
      source: '來源',
      tips1: '確定要撤銷當前訂單嗎?'
    }
  }
}
