export default {
  BaseTable: {
    operation: '操作'
  },
  BpmnProcessDesigner: {
    package: {
      designer: {
        plugins: {
          contentPad: {
            contentPadProvider: {
              changeType: '修改類型'
            }
          },
          palette: {
            CustomPalette: {
              activeGripperTool: '激活抓手工具'
            }
          }
        },
        ProcessDesigner: {
          openFile: '打開文件',
          downloadXML: '下載為XML文件',
          downloadSVG: '下載為SVG文件',
          downloadBPMN: '下載為BPMN文件',
          downloadFile: '下載文件',
          preview: '預覽',
          previewXML: '預覽XML',
          previewJSON: '預覽JSON',
          outSimulation: '退出模擬',
          openSimulation: '開啟模擬',
          simulation: '模擬',
          alignLeft: '向左對齊',
          alignRight: '向右對齊',
          alignTop: '向上對齊',
          alignBottom: '向下對齊',
          alignCenter: '水平居中',
          verticalCenter: '垂直居中',
          processZoomOut: '縮小視圖',
          processZoomIn: '放大視圖',
          processReZoom: '重置視圖並居中',
          processUndo: '撤銷',
          processRedo: '恢復',
          processRestart: '重新繪製',
          saveModel: '保存模型',
          pushShift: '請按住 Shift 鍵選擇多個元素對齊',
          autoAlignTip: '自動對齊可能造成圖形變形，是否繼續？',
          warning: '警告',
          saveModelFail: '保存模型失敗，請重試！'
        },
        ProcessViewer: {
          operationFlow: '業務流程',
          initiator: '發起人：',
          department: '部門：',
          createTime: '創建時間：',
          approver: '審批人：',
          result: '結果：',
          endTime: '結束時間：',
          approvalSuggestion: '審批建議：'
        }
      },
      palette: {
        ProcessPalette: {
          testTask: '測試任務'
        }
      },
      penal: {
        base: {
          ElementBaseInfo: {
            signatureTip: '如何實現實現會簽、或簽？',
            processIdent: '流程標識',
            pleaseEnter: '請輸入',
            processName: '流程名稱',
            name: '名稱',
            idErrorTip: '流程標識不能為空',
            nameErrorTip: '流程名稱不能為空'
          }
        },
        flowCondition: {
          FlowCondition: {
            type: '流轉類型',
            normal: '普通流轉路徑',
            default: '默認流轉路徑',
            condition: '條件流轉路徑',
            conditionType: '條件格式',
            expression: '表達式',
            script: '腳本',
            language: '腳本語言',
            scriptType: '腳本類型',
            inlineScript: '內聯腳本',
            externalScript: '外部腳本',
            resource: '資源地址'
          }
        },
        form: {
          ElementForm: {
            formKey: '表單標識',
            businessKey: '業務標識',
            none: '無',
            formFieId: '表單字段',
            num: '序號',
            FieIdName: '字段名稱',
            FieIdType: '字段類型',
            default: '默認值',
            operation: '操作',
            cutout: '移除',
            addFieId: '添加字段',
            fieIdSetting: '字段配置',
            fieIdId: '字段ID',
            typeType: '字段類型',
            pleaseSelect: '請選擇',
            typeName: '類型名稱',
            name: '名稱',
            datePattern: '時間格式',
            enumList: '枚舉值列表：',
            addEnum: '添加枚舉值',
            enumCode: '枚舉值編號',
            enumName: '枚舉值名稱',
            openFieldOptionForm: '約束條件列表：',
            addConstraint: '添加約束',
            constraintName: '約束名稱',
            constraintSetting: '約束配置',
            fieIdList: '字段屬性列表：',
            addAttribute: '添加屬性',
            attributeCode: '屬性編號',
            save: '保存',
            fieldOptionFormId: '編號/ID',
            setting: '配置',
            value: '值'
          }
        },
        listeners: {
          ElementListeners: {
            num: '序號',
            eventType: '事件類型',
            listenerType: '監聽器類型',
            operation: '操作',
            cutout: '移除',
            addListener: '添加監聽器',
            executeListener: '執行監聽器',
            javaClass: 'Java類',
            expression: '表達式',
            delegateExpression: '代理表達式',
            scriptFormat: '腳本格式',
            pleaseEnterScriptFormat: '請填寫腳本格式',
            scriptType: '腳本類型',
            pleaseSelectScriptType: '請選擇腳本類型',
            inlineScript: '內聯腳本',
            externalScript: '外部腳本',
            scriptValue: '腳本內容',
            pleaseInputScriptValue: '請填寫腳本內容',
            resourceUrl: '資源地址',
            pleaseInputResourceUrl: '請填寫資源地址',
            inputField: '註入字段：',
            addField: '添加字段',
            fieldName: '字段名稱',
            fieldType: '字段類型',
            fieldValue: '字段值',
            save: '保存',
            fieIdSetting: '字段配置',
            confirmCutOutFieldTip: '確認移除該字段嗎？',
            tip: '提示',
            confirmCutOutListenerTip: '確認移除該監聽器嗎？'
          },
          UserTaskListeners: {
            num: '序號',
            eventType: '事件類型',
            eventId: '事件id',
            listenerType: '監聽器類型',
            operation: '操作',
            cutout: '移除',
            addListener: '添加監聽器',
            taskListener: '任務監聽器',
            listener: '監聽器ID',
            javaClass: 'Java類',
            expression: '表達式',
            delegateExpression: '代理表達式',
            scriptFormat: '腳本格式',
            pleaseEnterScriptFormat: '請填寫腳本格式',
            scriptType: '腳本類型',
            pleaseSelectScriptType: '請選擇腳本類型',
            inlineScript: '內聯腳本',
            externalScript: '外部腳本',
            scriptValue: '腳本內容',
            pleaseInputScriptValue: '請填寫腳本內容',
            resourceUrl: '資源地址',
            pleaseInputResourceUrl: '請填寫資源地址',
            eventDefinitionType: '定時器類型',
            date: '日期',
            duration: '持續時長',
            loop: '循環',
            none: '無',
            timer: '定時器',
            pleaseInputTimer: '請填寫定時器配置',
            injectKey: '註入字段：',
            addField: '添加字段',
            fieldName: '字段名稱',
            fieldType: '字段類型',
            fieldValue: '字段值'
          }
        }
      }
    },
    save: '保存',
    fieldSetting: '字段配置',
    fieldName: '字段名稱：',
    fieldType: '字段類型：',
    fieldValue: '字段值：',
    expression: '表達式：',
    confirmCutOutFieldTip: '確認移除該字段嗎？',
    tip: '提示',
    confirmCutOutListenerTip: '確認移除該監聽器嗎？',
    loopCharacteristics: '回路特性',
    ParallelMultiInstance: '並行多重事件',
    SequentialMultiInstance: '時序多重事件',
    StandardLoop: '循環事件',
    none: '無',
    loopCardinality: '循環基數',
    collection: '集合',
    elementVariable: '元素變量',
    completionCondition: '完成條件',
    asyncStatus: '異步狀態',
    asyncBefore: '異步前',
    asyncAfter: '異步後',
    exclusive: '排除',
    timeCycle: '重試周期',
    elementDoc: '元素文檔：',
    num: '序號',
    attrName: '屬性名',
    attrValue: '屬性值',
    operation: '操作',
    addAttr: '添加屬性',
    attrSetting: '屬性配置',
    cutout: '移除',
    confirmCutOutAttr: '確認移除該屬性嗎？',
    messageList: '消息列表',
    createNew: '創建新消息',
    messageId: '消息ID',
    messageName: '消息名稱',
    signalList: '信號列表',
    createSignal: '創建新信號',
    signalId: '信號ID',
    signalName: '信號名稱',
    messageExisting: '該消息已存在，請修改id後重新保存',
    signalExisting: '該信號已存在，請修改id後重新保存',
    messageExample: '消息實例',
    confirm: '確認',
    scriptFormat: '腳本格式',
    pleaseEnterScriptFormat: '請填寫腳本格式',
    scriptType: '腳本類型',
    pleaseSelectScriptType: '請選擇腳本類型',
    inlineScript: '內聯腳本',
    externalScript: '外部腳本',
    scriptValue: '腳本內容',
    resultValue: '結果變量',
    outsideResource: '外部資源',
    resourceAddr: '資源地址',
    dueDate: '到期時間',
    followUpDate: '跟蹤時間',
    priority: '優先級',
    friendlyTips1: '友情提示：任務的分配規則，使用',
    friendlyTips2: '流程模型',
    friendlyTips3:
      '下的【分配規則】替代，提供指定角色、部門負責人、部門成員、崗位、工作組、自定義腳本等 7種維護的任務分配維度，更加靈活！',
    asyncContinue: '異步延續',
    eliminate: '排除',
    routine: '常規',
    messageAndSignal: '消息與信號',
    circulationConditions: '流轉條件',
    form: '表單',
    friendlyTips4: '友情提示：使用',
    friendlyTips5: '流程表單',
    friendlyTips6: '替代，提供更好的表單設計功能',
    task: '任務',
    multipleInstances: '多實例',
    executeListener: '執行監聽器',
    taskListener: '任務監聽器',
    extendAttr: '擴展屬性',
    else: '其他'
  },
  ButtonMessage: {
    isConfirm: '是否確定？',
    tip: '提示'
  },
  CustomUpload: {
    sumExtends: '文件總大小超出限製, 請重新上傳！',
    extends: '文件大小超出限製, 請重新上傳！',
    reUpload: '請檢查附件格式重新上傳！',
    extendsNum: '超出上傳文件數量限製！',
    uploadFail: '文件上傳失敗',
    waitForUpload: '請等待文件上傳完成'
  },
  Editor: {
    pleaseEnter: '請輸入內容...',
    reUpload: '請檢查附件格式重新上傳！'
  },
  SearchFrom: {
    startDate: '開始日期',
    endDate: '結束日期',
    recentlyOneWeek: '最近一周',
    recentlyOneMonth: '最近一個月',
    recentlyThreeMonth: '最近三個月'
  }
}
