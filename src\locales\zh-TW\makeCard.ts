export default {
  // 公共
  common: {
    customerName: '客戶名稱',
    projectName: '項目名稱',
    serverName: '服務名稱',
    demandSource: '需求來源',
    demandTitle: '需求標題',
    demandCode: '需求編號',
    demandType: '需求類型',
    currentDutyPer: '當前任務人員',
    demandDesc: '需求描述',
    cardBin: '卡BIN',
    currentDutyPerPlaceholder: '請選擇當前任務人員',
    demandTypePlaceholder: '請選擇需求類型',
    customerPlaceholder: '請選擇客戶名稱',
    projectPlaceholder: '請選擇項目名稱',
    serverPlaceholder: '請選擇服務名稱',
    demandSourcePlaceholder: '請選擇需求來源',
    demandTitlePlaceholder: '請輸入製卡需求標題',
    demandCodePlaceholder: '請輸入需求編號',
    unfold: '展開',
    packUp: '收起',
    search: '查詢',
    reset: '重置',
    add: '添加',
    delete: '刪除',
    edit: '編輯',
    detail: '詳情',
    confirm: '確認',
    ok: '確定',
    cancel: '取消',
    disposition: '配置',
    check: '查看',
    export: '導入',
    open: '啟用',
    close: '停用',
    batchDelete: '批量刪除',
    batchMove: '批量移動',
    addPic: '添加圖片',
    addComponent: '添加組件',
    addSort: '添加分類',
    upload: '上傳',
    uploadFile: '上傳文件',
    noData: '暫無數據',
    groupManage: '分組管理',
    copyLink: '復製鏈接',
    notHave: '無',
    null: '空',
    saveSuccess: '保存成功',
    addSuccess: '添加成功',
    deleteSuccess: '刪除成功',
    editSuccess: '編輯成功',
    deleteContent: '刪除後將不可恢復,是否確認刪除',
    editSort: '編輯分類',
    client: '客戶端',
    management: '管理平臺',
    makeDemand: '製卡需求',
    designCase: '設計方案',
    draftCase: '稿樣方案',
    sampleCardApply: '樣卡申請',
    designDemand: '設計需求',
    draftDemand: '稿樣需求',
    closeDemand: '需求關閉',
    pleaseChooseData: '請至少選中一條數據！',
    all: '全部',
    other: '其他',
    UnionPay: '銀聯',
    ConfirmOperation: '是否執行此操作？',
    uploadSuccess: '上傳成功',
    pleaseEnter: '請輸入內容',
    uploadFileError: '文件上傳失敗',
    download: '下載',
    sizeOutOfLimit: '文件大小超出限製, 請重新上傳！',
    checkFileFormat: '請檢查附件格式重新上傳！',
    numberOutOfLimit: '超出上傳文件數量限製！',
    awaitUpload: '請等待文件上傳完成',
    removeTip: '您移除了文件【 {fileName} 】',
    noFile: '未選擇任何文件！',
    disableNull: '內容不能為空，請輸入信息！',
    yes: '是',
    no: '否',
    submit: '提交',

    productName: '產品名稱',
    productType: '產品類型',
    placeholderTextTip: '請輸入{placeholder}',
    placeholderSelectTip: '請選擇{placeholder}',
    basicDemand: '基本需求',
    customerFile: '客戶文件',
    detailDemand: '詳細描述',
    customerAccount: '客戶賬號',
    selectFile: '選擇文件',
    uploadFileNoSizeTip: '文件數量最多{maxNum}個，整體大小{maxSize}以內，格式不限',
    uploadFileTip: '文件數量最多{maxNum}個，大小{maxSize}以內，文件格式 {fileFormat}',
    uploadFileNoNumTip: '文件大小{maxSize}以內，格式 {fileFormat}',
    uploadFileNoFormatTip: '文件數量最多{maxNum}個，大小{maxSize}以內',
    customer: '客戶',
    estimatedTimeOfSubmission: '預計提稿時間',
    originAndLevel: '卡組織及卡款級別',
    backTip: '是否確認選定此方案為最終方案，確認後將不可修改，請謹慎操作！'
  },
  dialog: {
    editReceivingInfo: '編輯收件信息',
    addReceivingInfo: '新增收件信息',
    chooseReceiving: '選擇地址',
    view3D: '查看3D文件',
    viewDesign: '查看設計方案',
    viewDraft: '查看稿樣方案',
    viewFile: '查看內部存檔',
    viewBackMsg: '查看回執信息',
    verifyScheme: '確認方案',
    upLoadSaveFile: '上傳存檔文件',
    uploadDesign: '上傳設計方案',
    uploadDraft: '上傳稿樣方案',
    upLoadAgain: '再次上傳方案'
  },
  index: {
    list: '製卡需求列表',
    createList: '新建製卡需求',
    conversionDraft: '轉稿樣需求',
    conversionDesign: '轉設計需求',
    assignTasks: '分配任務',
    assignSuccess: '分配成功',
    conversionSuccess: '轉換成功',
    dataError: '數據錯誤，請聯系管理員！'
  },
  im: {
    sendDirectly: '直接發送',
    saveImage: '保存圖片',
    imgCut: '圖片裁剪',
    imgEditArea: '圖片編輯區',
    clearImg: '清空圖片',
    uploadImg: '上傳圖片',
    onlineChat: '在線溝通',
    interactiveLog: '互動記錄',
    online: '在線',
    loading: '加載中~',
    onMore: '沒有更多了~',
    pleaseEnter: '請輸入互動內容......',
    pleaseEnterTip: '請輸入互動內容',
    send: '發送',
    loginError: '登錄服務器失敗，請聯系管理員!',
    joinError: '連接服務器失敗，請聯系管理員！',
    serviceError: '服務器失去響應，請聯系管理員！',
    reJoinError: '鏈接失敗，正在嘗試重新連接！',
    sending: '發送中...',
    clear: '清空',
    rubber: '橡皮檫',
    pen: '塗鴉筆',
    text: '文字',
    square: '矩形',
    round: '圓形',
    rotate: '旋轉圖片',
    finish: '完成',
    sendImg: '發送圖片'
  },
  detail: {
    assignmentDesigner: '分配設計師',
    assignmentDraftDesigner: '分配稿樣設計師',
    chooseDesigner: '選擇設計師',
    chooseDraftDesigner: '選擇稿樣設計師',
    chooseAuthDesigner: '請選擇已授權的設計師',
    chooseAuthDraftDesigner: '請選擇已授權的稿樣設計師',
    pleaseChooseDesign: '請選擇設計師！',
    customerFile: '客戶上傳文件',
    relatedProject: '關聯項目',
    estimatedDraftDate: '預計提稿日期',
    cardType: '卡片類型',
    cardVariety: '卡款種類',
    cardOrgLevel: '卡組織及卡片級別',
    otherExplain: '其他說明',
    otherFile: '其他附件',
    backMsg: '回執信息',
    accessory: '附件',
    closeExplain: '關閉說明',
    closeExplainPlaceholder: '請輸入關閉說明',
    noCloseExplainTip: '請填寫關閉說明！',
    closeSuccess: '關閉成功',
    cardStyleCode: 'GSC卡號',
    cardStyleName: '卡款名稱',
    stylist: '設計師',
    stylistOfDraft: '稿樣設計人員',
    interiorFile: '內部存檔',
    status: '狀態',
    valetConfirmation: '代客確認',
    toBeConfirmed: '待確認',
    confirmed: '已確認',
    noIMOfDraft: '稿樣需求不支持在線溝通，請聯系管理員！',
    noDesignOfDraft: '稿樣需求不支持上傳設計方案，請聯系管理員！',
    sampleCardOrder: '樣卡訂單',
    BatchProductUpdate: '更新批產品',
    cardDraftCode: 'GSC卡號',
    view3D: '3D演示',
    show3D: '3D展示',
    view: '查看',
    operator: '操作人',
    operatorTime: '操作時間',
    updateProductInfo: '產品信息更新',
    noIMOfDesign: '設計需求不支持在線溝通，請聯系管理員！',
    productCode: '產品編號',
    toBeListed: '待上架',
    listed: '已上架',
    productImg: '產品圖片',
    frontImg: '正面圖片',
    backImg: '背面圖片',
    updateProductErr: 'DIY卡產品更新操作對接中',
    remark: '備註',
    uploadDraftFile: '上傳稿樣文件',
    archiveFile: '存檔文件',
    remarkNote: '備註說明',
    confirmSuccess: '確認成功',
    uploadOtherFile: '上傳附件',
    draftFile: '稿樣文件',
    noFrontImg: '請上傳產品正面圖片！',
    noBackImg: '請上傳產品背面圖片！',
    saveProductSuccess: '保存產品成功！',
    saveProducterror: '更新產品失敗：',
    batchProduct: '批卡產品',
    DIYProduct: 'DIY產品'
  },
  sampleOrder: {
    addressName: '地址名稱',
    contact: '收件人',
    tel: '聯系電話',
    area: '寄送地址',
    selectCustomerPlaceholder: '請輸入客戶名稱',
    selectAddressPlaceholder: '請選擇省市區',
    addressDetailPlaceholder: '請輸入詳細地址',
    contactPlaceholder: '請輸入收件人',
    telPlaceholder: '請輸入手機號',
    namePlaceholder: '請輸入名稱',
    operatorSuccess: '操作成功',
    name: '名稱',
    phone: '手機號',
    address: '收件地址',

    applySample: '申請樣卡',
    unitPrice: '單價',
    cardCount: '數量',
    maxTip: '最大輸入48張',
    deliveryTime: '期望交付時間',
    deliveryTimePlaceholder: '請選擇期望交付時間',
    isUrgent: '是否加急',
    addressOfPeople: '聯系人',
    addressOfPeoplePlaceholder: '請輸入聯系人',
    addressOfTelPlaceholder: '請輸入聯系電話',
    deliveryMethod: '交付方式',
    storage: '入庫代存',
    customerPick: '客戶自提',
    mail: '郵寄',
    mailModePlaceholder: '請選擇郵寄方式',
    mailOfAddress: '地址',
    packageMode: '包裝方式',
    packageModePlaceholder: '請選擇包裝方式',
    innerBox: '內盒',
    outerBox: '外盒',
    fileList: '下單憑證',

    addAddress: '新增地址',
    innerBoxPlaceholder: '請選擇內盒',
    outerBoxPlaceholder: '請選擇外盒',
    product: '產品',
    price: '價格',
    unitP: '張',
    unitO: '個',
    createOrder: '立即下單',
    deliveryMethodPlaceholder: '請輸入交付方式！',
    mailModePlaceholderJs: '請選擇郵寄方式！',
    mailOfAddressPlaceholder: '請選擇郵寄地址！',
    unitPricePlaceholder: '請輸入單價！',
    cardCountPlaceholder: '請輸入卡款數量！',
    deliveryTimePlaceholderJs: '請輸入期望交付日期！',
    isUrgentPlaceholder: '請輸入是否加急！',
    peoplePlaceholder: '請輸入聯系人！',
    phonePlaceholder: '請輸入聯系電話！',
    getErrorTip: '參數獲取異常，請稍後重試！',
    orderType: '訂單類型',
    orderCode: '訂單編號',
    orderInfo: '訂單信息',
    cardCountAndUnit: '數量（張）',

    logisticsInfo: '物流信息',
    mailNo: '運單號',
    mailMode: '發貨方式',
    expressTypeText: '物流公司',
    fullAddress: '詳細地址',
    routeAddress: '物流追蹤',
    OrderStatus: '訂單狀態',
    orderSource: '訂單來源',
    deliveryTimeOfDetail: '預計交付時間',
    orderTotalPrice: '訂單總價',
    operateName: '操作員',
    operateUsername: '操作員賬號',
    customerInfo: '客戶信息',
    customerName: '下單客戶',
    productInfo: '產品信息',
    unitPriceOfDetail: '參考價格',
    isIndividual: '是否寫入個人化物料',
    mailInfo: '郵寄信息',
    getOrderErr: '獲取訂單失敗，請稍後重試！'
  },
  table: {
    indexNumber: '序號',
    operate: '操作',
    updateTime: '更新時間',
    submitTime: '提交時間',
    createTime: '創建時間',
    demandPhase: '當前階段'
  }
}
