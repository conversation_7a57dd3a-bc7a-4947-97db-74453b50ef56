export default {
  batchCardProduct: {
    numberOutOfLimit: '超出上傳文件數量限製！',
    sizeOutOfLimit: '文件大小超出限製, 請重新上傳！',
    onlyOneTip: '隻能上傳一個文件，已將文件進行替換！',
    formatTip: '文件上傳暫隻支持{format}格式, 請重新上傳！',
    sizeTip: '單個文件不能超過{size}M, 請重新上傳！',
    removeTip: '您移除了文件【{fileName}】',
    file: '文件',
    cancelReUpload: '取消重傳',
    cancelReUploadFile: '取消重傳文件',
    pleaseUpload: '請上傳',

    basicInfo: '基礎信息',
    customerName: '客戶名稱',
    customerNamePlaceholder: '請選擇客戶名稱',
    productType: '產品類型',
    productName: '產品名稱',
    cardCode: 'GSC卡號',
    frontImg: '正麵圖片',
    backImg: '背麵圖片',
    productFrontImg: '產品正麵圖',
    productBackImg: '產品背麵圖',
    productImg: '產品圖片',
    updateTime: '更新時間',
    operateInfo: '運營信息',
    viewProduct: '查看產品',
    toBeSubmitted: '待提交',
    delist: '已下架',
    underReview: '審核中',

    TheCustomer: '所屬客戶',
    TheCustomerPlaceholder: '請選擇所屬客戶',
    productTypePlaceholder: '請選擇產品類型',
    productNamePlaceholder: '請輸入產品名稱',
    cardCodePlaceholder: '請輸入GSC卡號',
    productStatus: '產品狀態',
    productStatusPlaceholder: '請選擇產品狀態',
    packUp: '收起條件',
    unfold: '展開條件',
    indexNumber: '序號',
    noData: '暫無',
    status: '狀態',
    productInfo: '產品信息',
    submitTime: '提交時間',
    createTime: '創建時間',
    tip: '提示',
    closeProductSuccess: '產品關閉成功！',
    openProductSuccess: '產品啟用成功！',
    saveProductSuccess: '保存產品成功！',
    delProductSuccess: '產品刪除成功！',
    submit: '提 交',
    sizeOneTip: '文件大小：2M以內',
    formatOneTip: '文件格式：.jpg .png',
    addProduct: '添加產品',
    closeConfirmTip: '是否關閉產品【{name}】？',
    openConfirmTip: '是否啟用產品【{name}】？',
    delConfirmTip: '是否刪除產品【{name}】？',
    enabled: '已啟用',
    closed: '已關閉',
    process: '處理中',
    rejected: '已駁回',
    productFrontImgTip: '請上傳產品正麵圖片',
    productBackImgTip: '請上傳產品背麵圖片',
    clientProductUniqueCode: '客戶代碼',
    editPrice: '修改價格',
    priceType: '報價方式',
    ladderQuotation: '階梯報價',
    fixedOffer: '固定報價',
    intervalStart: '區間開始',
    intervalEnd: '區間結束',
    ladderPrice: '階梯價格',
    intervalPrice: '區間價格',
    currency: '幣種',
    currencyPlaceholder: '請選擇幣種',
    intervalTip: '區間不能為空',
    intervalPricePlaceholder: '請填寫區間價格',
    intervalSymbol: '區間符號',
    intervalSymbolPlaceholder: '請選擇區間符號',
    addRow: '添加行',
    equal: '等於',
    intervalBackTip: '區間開始不能小於等於上一行區間結束',
    intervalNextTip: '區間結束不能小於等於區間開始',
    priceTypePlaceholder: '請選擇報價方式！',
    editSuccess: '修改成功',
    viewPrice: '查看價格'
  },
  diyCardProduct: {
    customerSelect: '選擇客戶',
    customerSelectPlaceholder: '請選擇客戶',
    pattern: '版型',
    patternPlaceholder: '請選擇版型',
    selectPlaceholder: '請選擇',
    selectImgEditPlaceholder: '請選擇{name}-圖片編輯',
    cardSurfaceCustomization: '卡麵定製',
    cardOrganCustomization: '卡組織定製',
    cardColorCustomization: '卡基顏色定製',
    cardBackColorCustomization: '卡背麵顏色定製',
    cardLogoCustomization: 'Logo標識定製',
    maskImg: '蒙層圖片',
    maskImgPlaceholder: '請上傳蒙層圖片',
    maskImgUploadPlaceholder: '上傳蒙層圖片',
    frontImg: '卡麵正麵圖片',
    settingRegion: '設置框定區域',
    top: '上',
    bottom: '下',
    left: '左',
    right: '右',
    frontImage: '卡正麵圖片',
    backImage: '卡背麵圖片',
    maskAndFrontImgCraft: '合成圖片（蒙層圖片+卡正麵圖片）',
    maskImgCraft: '合成圖片（蒙層圖片）',
    textureMaterial: '貼圖素材',
    elementMaterial: '元素拚接素材',
    colorPhotoCard: '彩照卡',
    cardImg: '卡麵貼圖',
    cardBackImg: '卡麵背麵圖片',
    uploadFrontImg: '上傳卡正麵圖片',
    uploadBackImg: '請上傳背麵圖片',
    uploadBackImgPlaceholder: '上傳背麵圖片',
    halfImgCustomerSetRegion: '設置半幅彩照中用戶自定義上傳區域',
    uploadGroupImg: '上傳卡麵貼圖組',
    elementSplicing: '元素拚接',
    uploadGroupElement: '上傳元素組',
    upload: '上傳',
    reupload: '重新上傳',
    halfImgSetRegion: '請設置半幅彩照的框定區域',
    setWidth: '框定寬度',
    setRegion: '框定區域範圍',
    setLocation: '框定區域位置',
    setHight: '框定高度',
    distanceTop: '距離上方',
    distanceBottom: '距離下方',
    distanceLeft: '距離左邊',
    distanceRight: '距離右邊',
    needTrueArrayTip: '請傳入一個正確的對象數組作為參數',
    noWidth: '第一張圖片寬度未設置',
    noHight: '第一張圖片高度未設置',
    priceAndUnit: '價格（元）',
    priceAndUnitPlaceholder: '請輸入價格（元）',
    originalPriceAndUnit: '原價（元）',
    originalPriceAndUnitPlaceholder: '請輸入原價（元）',
    otherPriceAllocation: '其他價格配置',
    argumentName: '參數名稱',
    argumentNamePlaceholder: '請輸入參數名稱',
    argumentValue: '參數值',
    effectBeginTime: '生效開始時間',
    beginTime: '開始日期',
    effectFinishDay: '生效結束日期',
    finishTime: '結束日期',
    effectFinishTime: '生效結束時間',
    operate: '操作',
    argumentValuePlaceholder: '請輸入參數值',
    argumentValueNoRepeat: '參數值不能重複',
    effectBeginTimePlaceholder: '請選擇生效開始時間',
    effectBeginTimeNoRepeat: '同參數值生效時間配置不能重合',
    effectBeginTimeSmall: '生效開始時間需小於生效結束時間',
    effectBeginTimeOnlyOneNull: '同參數值生效結束時間隻能有一個為空',
    effectFinishDayForeverSmall: '結束時間需小於永久時間的開始時間',
    pricePlaceholder: '請輸入價格',
    preview: '點擊預覽',
    noImgOfGroup: '此分組暫無圖片',
    all: '全部',
    needImgAudit: '需要圖審',
    noImgAudit: '無需圖審',
    imgAuditIdent: '圖審標識',
    imgAuditIdentPlaceholder: '請選擇圖審標識',
    bindImgAuditService: '綁定圖審服務',
    bindImgAuditServicePlaceholder: '請選擇綁定的圖審服務',
    empty: '無',
    remark: '備注',
    remarkPlaceholder: '請輸入備注',
    serviceName: '服務名稱',
    serviceNamePlaceholder: '請選擇服務',
    causeOfRejection: '駁回原因',
    basicInfo: '基礎信息',
    cardDraftCode: 'GSC卡號',
    cardStyleName: '卡款名稱',
    cardOtherOfCName: '卡款別名（C端）',
    cardType: '卡款類型',
    cardIdent: '卡款標識（對外）',
    subcategory: '所屬分類',
    customerProductName: '客戶產品名稱',
    customerProductCode: '客戶產品編號',
    financeProductCode: '財務產品編碼',
    ProductCode: '產品編號',
    visibilityRange: '可見範圍',
    allCity: '全部城市',
    supplier: '供應商',
    productIntro: '產品簡介',
    designFile: '設計文件',
    download: '下載',
    draftFile: '稿樣文件',
    file3D: '3D文件',
    customAttribute: '定製屬性',
    imgInfo: '圖片信息',
    noMaskImg: '無蒙層圖片',
    viewSetRegion: '查看框定區域',
    noBackImg: '無卡背麵圖片',
    noFrontImg: '無卡正麵圖片',
    noGroupImg: '無卡麵貼圖組',
    noElementSplicing: '無元素拚接組',
    setPrice: '價格配置',
    defaultTip: '提示：未在生效時間範圍的價格默認價格',
    setImgAudit: '圖審配置',
    viewHalfImgSetRegion: '查看半幅彩照中用戶自定義上傳區域',
    fileDownloadErr: '文件下載失敗',
    viewProduct: '查看產品',
    fuzzySearchSelection: '可模糊搜索選擇',
    quickAdd: '快捷添加',
    chooseProductTip: '提示：您可以選擇相似產品快速填充產品信息，僅更改差異化的信息即可',
    cardOtherOfCNamePlaceholder: '請輸入卡款別名（C端）',
    cardTypePlaceholder: '請選擇卡款類型',
    allImg: '全幅彩照卡',
    halfImg: '半幅彩照卡',
    cardIdentPlaceholder: '請輸入卡款標識（對外）',
    customerProductNamePlaceholder: '請輸入客戶產品名稱',
    customerProductCodePlaceholder: '請輸入客戶產品編號',
    financeProductCodePlaceholder: '請輸入財務產品編碼',

    defaultSortPlaceholder: '請選擇所屬分類',
    visibilityRangePlaceholder: '請選擇可見範圍',
    supplierPlaceholder: '請選擇供應商',
    productIntroPlaceholder: '請輸入產品簡介',
    cardDraftCodePlaceholder: '請選擇GSC卡號',
    cardStyleNamePlaceholder: '請選擇卡款名稱',
    colorImgTypePlaceholder: '請選擇彩照卡類型',
    priceErr: '價格不能小於或等於0',
    defaultSort: '默認分類',
    createImgSendErr: '生成合成圖發送異常',
    addProduct: '添加產品',
    editProduct: '編輯產品',
    productStatus: '產品狀態',
    productStatusPlaceholder: '請選擇產品狀態',
    view: '查看',
    image: '圖片',
    submitConfirmTip: '是否提交產品【{name}】？',
    delistConfirmTip: '是否下架產品【{name}】？',
    delistSuccess: '產品下架成功！',
    submitSuccess: '產品提交成功！',
    selectProjectPlaceholder: '請選擇關聯項目',
    cardAliasC: '卡款別名',
    cardAliasCPlaceholder: '請輸入卡款別名',
    cardDraftCodeTextPlaceholder: '請輸入GSC卡號',
    submit: '提交',
    resubmit: '重新提交',
    horizontal: '橫版',
    vertical: '豎版',
    anomaly: '異型'
  },

  // 樣卡列表
  sampleCardList: {
    LeaderApproval: '領導審批',
    ManagementApproval: '經理審批',
    SalesConfirmation: '銷售審批',
    Tobesubmitted: '待提交',
    Completed: '完成',
    Cancelled: '取消',

    applyCode: '申請單號',
    customerName: '客戶名稱',
    customerNameEnter: '請輸入客戶名稱',
    sampleCardApplicationType: '申請類型',
    sampleCardApplicationTypeSelect: '請選擇類型',
    orderApplicationStatus: '申請狀態',
    orderApplicationStatusSelect: '請選擇狀態',
    search: '查詢',
    reset: '重置',
    new: '新增',
    applyCode2: '申請單編號',
    orderDetial: '訂單詳情',
    // 客戶名稱: '客戶名稱',
    createName: '申請人',
    saleUserName: '區域銷售',
    managerName: '區域負責人',
    // 申請狀態: '申請狀態',
    createDate: '創建時間',
    actions: '操作',
    views: '查看',
    edit: '編輯'
  },
  sampleCardEdit: {
    FreeSampleCard: '免費樣卡',
    InboundSampleCard: '入庫樣卡',
    ChargeSampleCard: '收費樣卡',

    ApplicationForm: '申請訂單',
    ApplicationFormNo: '申請單號',
    CustomerName: '客戶名稱',
    PleaseEnterCustomerName: '請選擇客戶',
    ApplicationType: '申請類型',
    PleaseSelect: '請選擇',
    deliveryDate: '交付時間',
    PleaseSelectDeliveryDate: '選擇日期時間',
    urgentSign: '是否加急',
    urgentReason: '加急原因',
    PleaseEnterUrgentReason: '請輸入加急原因',
    saleUserName: '銷售人員',
    saleUserTime: '確認時間',
    managerTime: '確認時間',
    managerName: '銷售經理',
    ProductList: '產品列表',
    BatchImport: '批量導入',
    AddProducts: '添加產品',
    DownloadTemplate: '下載模板',
    ProductName: '產品名稱',
    CustomerProductCode: '客戶產品代碼',
    CardBaseNumber: '卡基編號',
    ApplicationQuantity: '申請數量',
    ProjectNumber: '方案數',
    Remarks: '備注',
    Actions: '操作',
    edit: '編輯',
    delete: '刪除',
    SampleCardElements: '樣卡要素',
    //樣卡要素radio
    // SampleCardFunction: 'Sample Card Function',
    None: '無',
    NewCardConfirmation: '新卡確認',
    TenderSubmission: '投標',
    InspectionCertification: '送檢/ 認證',
    SubmissionForApproval: '報批',
    Other: '其他',

    SampleCardFunction: '樣卡用途',
    CardFrontPrintingElements: '正麵印和刷要素',
    //正麵印和刷要素radio
    CardBaseImage: '正麵底圖圖案 ',
    ClientLogo: '正麵客戶LOGO',
    CardSchemeLogo: '正麵卡組織LOGO',
    SpecialEffects: '正麵特殊工藝',

    CardBackPrintingElements: '背麵印和刷要素',

    //背麵印和刷要素 radio
    BackImage: '背麵地圖圖案',
    Text: '背麵文字',
    BackClientLogo: '背麵客戶LOGO',
    BackCardSchemeLogo: '背麵卡組織LOGO',
    BackSpecialEffects: '背麵特殊工藝',
    BlankWhiteBack: '背麵純白無內容',

    OtherElements: '其他要素',

    //其他要素 radio
    SignaturePanel: '簽名條',
    Hologram: '全息圖',
    Magstripe: '磁條',
    MetalSticker: '立金',
    HotStamping: '燙印箔',
    FunctionalEMVChip: '真芯片',
    DummyEMVChip: '假芯片',
    RealInlay: '真INLAY',

    PersonalisationRequirements: '個人化需求',
    //個人化需求  radios

    CardSurfacePersonalisation: '卡麵個人化',
    MagstripePersonalisation: '磁條個人化',
    ChipPrePersonalisation: '芯片預個人化',
    ChipPersonalisation: '芯片個人化',

    PMTestingRequirement: 'PM檢測要求',
    //PM檢測要求 radio
    MagstripePhysicalProperties: '磁條物理特性',
    MagstripePersonalisationData: '磁條個人化數據',
    ChipPhysicalProperties: '芯片物理特性',
    COSVersionChip: '芯片COS版本',
    ChipPersonalisationData: '芯片個人化數據',

    SecurityMeasureRequirements: '安全處理要求',
    //安全處理要求radio
    Holepunch: '打孔',
    VoidStamp: '蓋作廢章',
    ScratchedMagstripe: '劃磁',
    NoTreatment: '不做處理',

    Approva: '審 核',
    Save: '保 存',
    Submit: '提 交',
    Cancel: '取 消',
    Back: '返 回',
    PleaseEnterDeliveryDate: '請填寫交付時間',
    PleaseSelectSampleCardFunction: '請選擇樣卡用途',
    PleaseSelectCardFrontPrintingElements: '請選擇正麵印和刷要素',
    PleaseSelectCardBackPrintingElements: '請選擇背麵印和刷要素',
    PleaseSelectOtherElements: '請選擇其他要素',
    PleaseSelectPersonalisationRequirements: '請選擇個人化需求',
    PleaseSelectPMTestingRequirement: '請選擇PM檢測要求',
    PleaseSelectSecurityMeasureRequirements: '請選擇安全處理要求',
    PleaseSelectPleaseSelectDeliveryDate: '請選擇交付時間',
    SubmitSucess: '提交成功',
    SaveSucess: '保存成功',
    PleaseSelectApprovaResut: '請選擇評審結果',
    cancelApplication: '是否取消申請?',
    Tips: '提示',
    Submit2: '確定',
    Cancel2: '取消',
    CancelSuccess: '取消成功',
    importTemplate: '產品列表導入模版',
    ImportFail: '導入文件失敗，請檢查數據格式是否正確！',
    ProducetNameMust: '產品名稱為必填項',

    NoneSales: '無法獲取銷售人員信息',
    NoneCustomer: '無法獲取客戶信息',

    XlsHeaderInfos: {
      ProductName: '產品名稱',
      CustomerProductCode: '客戶產品代碼',
      GSCCode: '卡基編號',
      ProjectNumber: '方案數',
      Quantity: '備庫數量',
      Remarks: '備注'
    }
  },
  sampleProductDailog: {
    SampleCardApplicationProductFill: '樣卡申請產品填寫',
    ProductName: '產品名稱',
    PleaseSelectProduct: '請輸入選擇產品',
    cardNumber: '卡款編號',
    PleaseEnterCardNumber: '請輸入卡款編號',
    CustomerProductCode: '客戶產品編碼',
    // 客戶產品編碼: '客戶產品編碼',
    ApplicationQuantity: '申請數量',
    ProjectNumber: '方案數',
    Remarks: '備注',
    PleaseEnterComments: '請輸入備注',
    Close: '關閉',
    save: '保存',
    correctQuantityValue: '請填寫正確數量值',
    PleaseSelectCustomer: '請選擇客戶',
    UnableQueryproduct: '請選擇客戶，無法查詢產品'
  },
  preparationList: {
    customerName: '客戶名稱',
    customerNamePlaceholder: '請輸入客戶名稱',
    ApplicationStatus: '申請狀態',
    ApplicationStatusSelect: '請選擇狀態',
    search: '查詢',
    reset: '重置',
    new: '新增',
    applyCode: '申請單編號',
    // 客戶名稱: '客戶名稱',
    createName: '申請人',
    saleUserName: '區域銷售',
    managerName: '區域負責人',
    LeaderApproval: '領導審批',
    // 申請狀態: '申請狀態',
    createDate: '創建時間',
    actions: '操作',
    views: '查看',
    edit: '編輯'
  },
  preparationEdit: {
    ApplicationForm: '申請訂單',
    ApplicationFormNo: '申請單號',
    CustomerName: '客戶名稱',
    PleaseEnterCustomerName: '請選擇客戶',

    deliveryDate: '交付日期',
    PleaseSelectDeliveryDate: '選擇日期',
    UrgentDelivery: '是否加急',
    urgentReason: '加急原因',
    PleaseEnterUrgentReason: '請輸入加急原因',
    ReviewComments: '評審意見',
    PleaseEnterReviewComments: '請輸入評審意見',

    ProductList: '產品列表',
    DownloadTemplate: '下載模板',
    AddProducts: '添加產品',
    BatchImport: '批量導入',
    ProductName: '產品名稱',
    CustomerProductCode: '客戶產品代碼',
    CardBaseNumber: '卡基編號',

    BackupType: '備庫類型',
    BackupQuantity: '備庫數量',
    BranchMessage: '分行信息',
    ProductType: '產品類型',

    Remarks: '備注',
    Actions: '操作',
    edit: '編輯',
    delete: '刪除',

    Approva: '審 核',
    Save: '保 存',
    Submit: '提 交',
    Cancel: '取 消',
    Back: '返 回',

    PleaseEnterDeliveryDate: '請填寫交付日期',
    SubmitSuccess: '提交成功',
    SaveSuccess: '保存成功',
    PleaseSelectCommentsResult: '請選擇評審結果',
    CancelApply: '是否取消申請?',
    Tips: '提示',
    Submit2: '確定',
    Cancel2: '取消',
    CancelSuccess: '取消成功',
    ImportBackupListTemplate: '備庫列表導入模版',
    ImportFileError: '導入文件失敗，請檢查數據格式是否正確！',

    // finishedProductBackup: '成品備庫',
    // semiProductBackup: '半成品備庫',
    // CardProduct: '卡產品',
    // nonCardProduct: '非卡產品',
    // PleaseCheckBackupQuantity: '請檢查備請檢查備庫數量，範圍庫數量'

    saleLeader: '銷售領導',
    LeaderConfirmationTime: '領導確認時間',

    BackupTypeError: '備庫類型錯誤',
    ProductTypeError: '產品類型錯誤',
    QuantityError: '備庫數量錯誤',
    QuantityRangeError: '備庫數量填寫範圍錯誤',

    XlsHeaderInfos: {
      ProductName: '產品名稱',
      CustomerProductCode: '客戶產品代碼',
      GSCCode: '卡基編號',
      BackupType: '備庫類型',
      Quantity: '備庫數量',
      BranchInfo: '分行信息',
      ProductType: '產品類型',
      Remarks: '備注'
    }
  },
  preparationProductDialog: {
    FillBackupProduct: '備庫產品填寫',
    productName: '產品名稱',
    PleaseSelectProduct: '請輸入選擇產品',
    cardNumber: '卡款編號',
    customerProductCode: '客戶產品編號',
    BackupType: '備庫類型',
    BackupQuantity: '備庫數量',
    PleaseSelectType: '請選擇類型',
    BranchMessage: '分行信息',
    ProductType: '產品類型',
    Remarks: '備注',
    PleaseEnterRemarks: '請輸入備注',
    Closed: '關閉',
    Save: '保存',
    zero: '零',
    // '零', '一', '二', '三', '四', '五', '六', '七', '八', '九'九['', '十', '百', '千'] '萬', '億'
    // one: '一',
    // two: '二',
    // 三: '三',
    // 四: '四',
    // 五: '五',
    // 六: '六',
    // 七: '七',
    // 八: '八',
    // 九: '九',
    // 十: '十',
    // 百: '百',
    // 千: '千',
    // 萬: '萬',
    // 億: '億',
    PleaseEnterApplyQuantity: '請填寫申請數量',
    PleaseEnterCorrectQuantity: '請填寫正確數量值',
    ApplyQuantityMorethanZero: '申請數量不能小於零',
    // PleaseSelectProduct: '請選擇產品',
    PleaseSelectProductType: '請選擇卡產品類型',
    PleaseSelectCustomer: '請選擇客戶'
  },

  typeData: {
    FreeSampleCard: '免費樣卡',
    InboundSampleCard: '入庫樣卡',
    ChargeSampleCard: '收費樣卡',
    // 半成品備庫: '半成品備庫',
    // 卡產品: '卡產品',
    // 非卡產品: '非卡產品'
    finishedProductBackup: '成品備庫',
    semiProductBackup: '半成品備庫',
    CardProduct: '卡產品',
    nonCardProduct: '非卡產品',

    LeaderApproval: '領導審批',
    ManagementApproval: '經理審批',
    SalesConfirmation: '銷售審批',
    Tobesubmitted: '待提交',
    Completed: '完成',
    Cancelled: '取消',

    //樣卡要素radio
    // SampleCardFunction: 'Sample Card Function',
    None: '無',
    NewCardConfirmation: '新卡確認',
    TenderSubmission: '投標',
    InspectionCertification: '送檢/ 認證',
    SubmissionForApproval: '報批',
    Other: '其他',

    //正麵印和刷要素radio
    CardBaseImage: '正麵底圖圖案 ',
    ClientLogo: '正麵客戶LOGO',
    CardSchemeLogo: '正麵卡組織LOGO',
    SpecialEffects: '正麵特殊工藝',

    //背麵印和刷要素 radio
    BackImage: '背麵地圖圖案',
    Text: '背麵文字',
    BackClientLogo: '背麵客戶LOGO',
    BackCardSchemeLogo: '背麵卡組織LOGO',
    BackSpecialEffects: '背麵特殊工藝',
    BlankWhiteBack: '背麵純白無內容',

    //其他要素 radio
    SignaturePanel: '簽名條',
    Hologram: '全息圖',
    Magstripe: '磁條',
    MetalSticker: '立金',
    HotStamping: '燙印箔',
    FunctionalEMVChip: '真芯片',
    DummyEMVChip: '假芯片',
    RealInlay: '真INLAY',

    //個人化需求  radios

    CardSurfacePersonalisation: '卡麵個人化',
    MagstripePersonalisation: '磁條個人化',
    ChipPrePersonalisation: '芯片預個人化',
    ChipPersonalisation: '芯片個人化',

    //PM檢測要求 radio
    MagstripePhysicalProperties: '磁條物理特性',
    MagstripePersonalisationData: '磁條個人化數據',
    ChipPhysicalProperties: '芯片物理特性',
    COSVersionChip: '芯片COS版本',
    ChipPersonalisationData: '芯片個人化數據',

    //安全處理要求radio
    Holepunch: '打孔',
    VoidStamp: '蓋作廢章',
    ScratchedMagstripe: '劃磁',
    NoTreatment: '不做處理'
  },
  appointSaleDialog: {
    selectedSaleMan: '審批人員選擇',
    saleMen: '審批人員',
    PleaseSelectedSaleMan: '請選擇審批人員',
    closed: '關閉',
    submit: '確認',
    submitting: '提交中...'
  }
}
