/*
 * @Author: HoJack
 * @Date: 2023-12-13 09:23:26
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-13 09:27:49
 * @Description:
 */
export default {
  api: {
    operationFailed: '操作失敗',
    errorTip: '錯誤提示',
    errorMessage: '操作失敗,系統異常!',
    timeoutMessage: '登錄超時,請重新登錄!',
    apiTimeoutMessage: '接口請求超時,請刷新頁面重試!',
    apiRequestFailed: '請求出錯，請稍候重試',
    networkException: '網絡異常',
    networkExceptionMsg: '網絡異常，請檢查您的網絡連接是否正常!',
    errMsg401: '用戶沒有權限（令牌、用戶名、密碼錯誤）!',
    errMsg403: '用戶得到授權，但是訪問是被禁止的。!',
    errMsg404: '網絡請求錯誤,未找到該資源!',
    errMsg405: '網絡請求錯誤,請求方法未允許!',
    errMsg408: '網絡請求超時!',
    errMsg500: '服務器錯誤,請聯系管理員!',
    errMsg501: '網絡未實現!',
    errMsg502: '網絡錯誤!',
    errMsg503: '服務不可用，服務器暫時過載或維護!',
    errMsg504: '網絡超時!',
    errMsg505: 'http版本不支持該請求!',
    errMsg901: '演示模式，無法進行寫操作!'
  },
  app: {
    logoutTip: '溫馨提醒',
    logoutMessage: '是否確認退出系統?',
    menuLoading: '菜單加載中...',
    loading: '加載中...'
  },
  exception: {
    backLogin: '返回登錄',
    backHome: '返回首頁',
    subTitle403: '抱歉，您無權訪問此頁面。',
    subTitle404: '抱歉，您訪問的頁面不存在。',
    subTitle500: '抱歉，服務器報告錯誤。',
    noDataTitle: '當前頁無數據',
    networkErrorTitle: '網絡錯誤',
    networkErrorSubTitle: '抱歉，您的網絡連接已斷開，請檢查您的網絡！'
  },
  lock: {
    unlock: '點擊解鎖',
    alert: '鎖屏密碼錯誤',
    backToLogin: '返回登錄',
    entry: '進入系統',
    placeholder: '請輸入鎖屏密碼或者用戶密碼'
  },
  login: {
    backSignIn: '返回',
    signInFormTitle: '登錄',
    ssoFormTitle: '三方授權',
    mobileSignInFormTitle: '手機登錄',
    qrSignInFormTitle: '二維碼登錄',
    signUpFormTitle: '註冊',
    forgetFormTitle: '重置密碼',
    signInTitle: '開箱即用的中後臺管理系統',
    signInDesc: '輸入您的個人詳細信息開始使用！',
    policy: '我同意xxx隱私政策',
    scanSign: `掃碼後點擊"確認"，即可完成登錄`,
    loginButton: '登錄',
    registerButton: '註冊',
    rememberMe: '記住我',
    forgetPassword: '忘記密碼?',
    otherSignIn: '其他登錄方式',
    // notify
    loginSuccessTitle: '登錄成功',
    loginSuccessDesc: '歡迎回來',
    // placeholder
    accountPlaceholder: '請輸入賬號',
    passwordPlaceholder: '請輸入密碼',
    smsPlaceholder: '請輸入驗證碼',
    mobilePlaceholder: '請輸入手機號碼',
    policyPlaceholder: '勾選後才能註冊',
    diffPwd: '兩次輸入密碼不一致',
    userName: '賬號',
    password: '密碼',
    confirmPassword: '確認密碼',
    email: '郵箱',
    smsCode: '短信驗證碼',
    mobile: '手機號碼',
    ssoAuthTip: 'SSO 授權後的回調頁',
    authCode: '授權碼: ',
    usingCode: '正在使用 code 授權碼，進行 accessToken 訪問令牌的獲取',
    getToken: '獲取token',
    loginFail: '登錄失敗',
    ssoLoading: '正在獲取訪問權限,請稍等...'
  },
  permission: {
    hasPermission: '請設置操作權限值',
    loginInvalid: '登錄已失效'
  },
  errorCode: {
    code401: '認證失敗，無法訪問系統資源',
    code403: '當前操作沒有權限',
    code404: '訪問資源不存在',
    codeDefault: '系統未知錯誤，請反饋給管理員'
  },
  service: {
    invalidToken: '無效的刷新令牌',
    expiredToken: '刷新令牌已過期',
    code901: 'code為901,請聯系管理員',
    unFindRole: '未能找到用戶角色,登錄已失效,請重新登錄!',
    loginInvalid: '登錄已失效',
    pleaseRelogin: ',請重新登錄!'
  },
  hooks: {
    web: {
      validfail: '校驗失敗',
      pleaseEnterCrrentPhoneNum: '請輸入正確的手機號'
    }
  },
  layout: {
    emailDropdown: {
      personalCenter: '個人中心',
      changePassword: '修改密碼',
      unbindEmail: '未綁定郵箱'
    },
    roleDropdown: {
      roleOverdue: '該角色已過期，請聯系管理員重新創建',
      checkingRole: '切換角色中...',
      checkingRoleTip: '切換到該角色將跳轉至其他應用，是否繼續？',
      checkingRoleFail: '切換角色失敗',
      unallocatedRole: '未分配角色'
    },
    userInfo: {
      noUserName: '無用戶名',
      noDeptData: '暫無部門數據'
    },
    collapse: {
      collapse: '收起菜單欄',
      expand: '展開菜單欄',
      tips: '屏幕寬度過小，爲了更好的體驗,已摺疊菜單，可點擊左上角圖標展開菜單'
    }
  },
  utils: {
    formatTime: {
      just: '剛剛',
      beforeSec: '秒前',
      beforeMin: '分鐘前',
      beforeHour: '小時前',
      beforeDay: '天前',
      goodearlyMorning: '淩晨好',
      goodMorning: '早上好',
      goodforenoon: '上午好',
      goodnoon: '中午好',
      goodafternoon: '下午好',
      gooddusk: '傍晚好',
      goodevening: '晚上好',
      goodLateNight: '夜裏好',
      day: '天',
      hour: '小時',
      min: '分鐘',
      sec: '秒'
    }
  },
  AccountControl: {
    accountInformation: '賬號信息',
    enterpriseInformation: '企業信息',
    username: '賬號',
    mobile: '手機號碼',
    email: '郵箱',
    nickName: '員工姓名',
    deptName: '部門',
    createTime: '註冊時間',
    nodata: '暫無數據',
    uscc: '社會統一信用代碼',
    customerNameNodata: '企業名稱暫無數據',
    cusRelated: '關聯企業/機構',
    deptRelated: '部門關系',
    deptNodata: '暫無部門數據',
    openServices: '開通服務',
    clearingForm: '結算方式',
    noTopDept: '無頂級部門'
  },
  footer: {
    Copyright: 'Copyright ©2024-智融金服科技（珠海）有限公司 All Rights Reserved',
    icp: '粵ICP備********號'
  }
}
