export default {
  common: {
    title: '標題',
    time: '時間',
    sortNum: '序號',
    jumpTo: '跳至',
    select: '選擇',
    selected: '已選',
    startDate: '開始日期',
    endDate: '結束日期',
    status: '狀態',
    createTime: '創建時間',
    refresh: '刷新',
    detail: '詳情',
    reason: '原因',
    deal: '處理',
    pass: '通過',
    notPass: '不通過',
    transfer: '轉辦',
    delegate: '委派',
    copy: '抄送',
    task: '任務',
    processing: '處理中',
    more: '更多',
    new: '新'
  },
  businessTodo: {
    productApproval: '產品審批',
    contractApproval: '合同審批',
    imageApproval: '圖像審批',
    prodValidate: '生產驗證',
    markCardDemand: '制卡需求',
    designScheme: '設計方案',
    designArchive: '設計歸檔',
    draftScheme: '稿樣方案',
    draftArchive: '稿樣歸檔',
    todoType: '待辦類型',
    initiator: '發起人',
    receiveTime: '接收時間',
    transfer: '轉辦',
    myTransfer: '我轉辦的',
    transferMy: '轉辦我的',
    transferPerson: '轉辦人',
    transferedPerson: '受轉人',
    transferType: '轉辦類型',
    initiationTime: '發起時間',
    total: '共',
    totalNum: '條記錄',
    totalNo: '第',
    pageNo: '頁',
    todoList: '業務待辦',
    transferRecord: '轉辦記錄'
  },
  components: {
    transferLog: '日誌',
    staffId: '員工編號',
    canNotTransferTips: '不能轉辦給當前登錄的用戶',
    staffName: '員工名稱',
    selectStaff: '選擇成員',
    transferReason: '轉辦原因'
  },
  flowTodo: {
    flowStatus: '流程狀態',
    copyTime: '抄送時間',
    taskName: '任務名稱',
    taskId: '任務編號',
    flowName: '流程名稱',
    taskAssigneeUserNickname: '被委派人',
    taskOwnerUserNickname: '委派人',
    myTaskAssignee: '我委派的',
    toTaskAssignee: '被委派的',
    approvalId: '審批編號',
    flowTitle: '流程標題',
    operateStep: '操作步驟',
    flowInitiator: '流程發起人',
    taskReason: '委派原因',
    dealTime: '處理時間',
    taskStatus: '任務狀態',
    flowBelong: '所屬流程',
    isNeed: '是否要',
    application: '申請',
    activate: '激活',
    unActivate: '掛起',
    baseInfo: '基本信息',
    flowLog: '流程日誌',
    flowChart: '流程圖',
    approvalTask: '審批任務',
    flowName2: '流程名',
    taskTo: '委派任務',
    taskToWho: '委派給',
    approvalSuggest: '審批建議',
    copyPerson: '抄送人',
    applicateInfo: '申請信息',
    flowBindTips: '流程沒有綁定業務表單，請上綜合服務平臺上綁定',
    notBindForm: '暫未綁定表單',
    approvalSuggestTips: '審批建議不能為空',
    approvalPassSuccess: '審批通過成功',
    approvalPassFailed: '審批不通過成功',
    searchFlowInfoNull: '查詢不到流程信息',
    flowBindCompTips: '流程綁定業務組件表單有誤,請檢查組件路徑是否存在',
    approvalRecord: '審批記錄',
    approvalUser: '審批人',
    operateAction: '操作動作',
    nodeName: '節點名稱',
    approvalTime: '審批時間',
    useTime: '耗時',
    operateUser: '操作人',
    noDepartment: '暫無部門',
    taskToDeal: '任務待處理',
    approvalSuggestNull: '審批意見為空',
    nextNodeApprovalUser: '下節點審批人設置',
    ruleType: '規則類型',
    specifyRole: '指定角色',
    specifyDepartment: '指定部門',
    loadingWait: '加載中，請稍後',
    specifyPost: '指定崗位',
    specifyUser: '指定用戶',
    specifyUserGroup: '指定用戶組',
    specifyScript: '指定腳本',

    ruleTypeTips: '規則類型不能為空',
    specifyRoleTips: '指定角色不能為空',
    specifyDepartmentTips: '指定部門不能為空',
    specifyPostTips: '指定崗位不能為空',
    specifyUserTips: '指定用戶不能為空',
    specifyUserGroupTips: '指定用戶組不能為空',
    specifyScriptTips: '指定腳本不能為空',

    newApprovalUser: '新審批人',
    newApprovalUserTips: '新審批人不能為空',

    myTodo: '我的待辦',
    myTodoDone: '我的已辦',
    myInitiated: '我發起的',
    myCC: '抄送我的',

    flowCannotJump: '該流程待辦,無法跳轉',
    taskCannotJump: '該業務待辦,無法跳轉',
    noTodo: '暫無待辦',
    flowTodo: '流程待辦',
    myMessage: '我的消息',
    noMessage: '暫無消息'
  }
}
