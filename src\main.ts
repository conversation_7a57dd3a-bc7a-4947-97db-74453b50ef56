/*
 * @Author: Ho<PERSON><PERSON>
 * @Date: 2023-06-13 08:59:52
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-13 11:28:48
 * @Description:
 */
// 引入windi css
import '@/styles/var.css'

import '@/plugins/windi.css'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

// 引入element-plus
import { setupElementPlus } from '@/plugins/elementPlus'
import 'element-plus/dist/index.css' //引入element-plus样式

// 引入全局样式
import '@/styles/index.less'

//引入组织架构图
import { setupTreeOrg } from '@/plugins/treeOrg'

// 引入动画
import '@/plugins/animate.css'

// 路由
import { setupRouter } from './router'

// 权限
import { setupPermission } from './directives'

import { createApp } from 'vue'

import App from './App.vue'

// 导入缓存管理器
import { cacheManager } from '@/utils/cacheManager'

import './permission'

// 创建实例
const setupAll = async () => {
  // 在应用初始化前先进行版本检测
  try {
    await cacheManager.init()
  } catch (error) {
    console.error('缓存管理器初始化失败:', error)
  }

  const app = createApp(App)

  await setupI18n(app)

  setupStore(app)

  setupElementPlus(app)

  setupFormCreate(app)

  setupGlobCom(app)

  setupRouter(app)

  setupPermission(app)

  setupTreeOrg(app)

  app.mount('#app')
}

setupAll()
