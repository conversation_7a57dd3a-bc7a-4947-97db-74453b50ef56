/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 16:01:28
 * @LastEditors: Ho<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-04-16 08:59:51
 * @Description:
 */
import router from './router'
import type { RouteRecordRaw } from 'vue-router'
import { useTitle } from '@/hooks/web/useTitle'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { useDictStoreWithOut } from '@/store/modules/dict'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { useUserStoreWithOut } from '@/store/modules/user'
import { getAccessToken } from '@/utils/auth'
import { GetQueryString } from '@/utils/routerHelper'
import { OauthUrlQuery } from '@/api/login/types'
import { handleAuthorized } from '@/config/axios/service'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import { useI18n } from '@/hooks/web/useI18n'
import { useLocaleStore } from '@/store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'

const localeStore = useLocaleStore()

const { wsCache } = useCache()

const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()
//单点登录-store
import { useSsoStoreWithOut } from '@/store/modules/sso'
const ssoStore = useSsoStoreWithOut()

const whiteList = ['/ssoError', '/ssoCallback'] // 不重定向白名单(包含token交校验白名单和无token白名单)

let authorizedErrorCount = 0 //处理登录失败相关接口次数,2次以上才重新登录

router.beforeEach(async (to, from, next) => {
  // 开始加载进度条
  start()
  loadStart()

  // 处理国际化设置
  const urlQueryObj = GetQueryString() as OauthUrlQuery
  if (urlQueryObj && urlQueryObj.hasOwnProperty('lang')) {
    // 检测国际化
    const lang = urlQueryObj.lang as LocaleType
    if (localeStore.localeMap.some((item) => item.lang === lang)) {
      localeStore.setCurrentLocale({ lang })
      const { changeLocale } = useLocale()
      changeLocale(lang)
    } else {
      console.warn(`国际化语言设置无效，语言 '${lang}' 不被支持`)
    }
  }
  // 获取token和路由状态
  const hasToken = getAccessToken()
  const isWhiteListRoute = whiteList.includes(to.path)

  // 已登录且不在白名单中，处理权限验证
  if (hasToken && !isWhiteListRoute) {
    const dictStore = useDictStoreWithOut()
    const userStore = useUserStoreWithOut()
    const permissionStore = usePermissionStoreWithOut()
    try {
      // 已经添加了动态路由，则正常跳转
      if (permissionStore.getIsAddRouters) {
        // 处理组件路径对不上的情况
        if (to.meta.isEmptyComponent) {
          next('/404')
          return
        }
        next()
        return
      }

      //  获取所有字典
      if (!dictStore.getIsSetDict) {
        await dictStore.setDictMap()
      }
      // 获取用户信息(用户+角色信息)
      if (!userStore.getIsSetUser) {
        //获取用户信息(用户+角色信息)
        await userStore.setUserInfoAction()
      }
      // 校验用户权限
      const userInfo = wsCache.get(CACHE_KEY.USER)
      // 校验是否含有当前端的权限，不能将此判断直接放进setUserInfoAction里，否则会造成router的钩子循环调用
      const matchObj = userInfo?.clients.find(
        (el) => el.oauthClient === import.meta.env.VITE_APP_CLIENT_ID
      )
      if (!matchObj) {
        next('/ssoError?noClient=1')
        return
      }
      // 后端过滤菜单
      await permissionStore.generateRoutes()
      permissionStore.getAddRouters.forEach((route) => {
        router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
      })
      // 标记路由已添加（在重定向前设置，避免重复触发）
      permissionStore.setIsAddRouters(true)

      const redirectPath = from.query.redirect || to.path
      const redirect = decodeURIComponent(redirectPath as string)
      const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
      next(nextData)
    } catch (error) {
      console.log(error)
      //多次失败才会出发重新登录
      if (++authorizedErrorCount > 1) {
        const { t } = useI18n()
        handleAuthorized(t('sys.permission.loginInvalid'))
        authorizedErrorCount = 0
      }
      next()
    }
  } else {
    // 未登录或在白名单中
    if (isWhiteListRoute) {
      next()
      return
    }

    // 无token重定向至流程权限中心进行授权码登录
    ssoStore.ssoLogin()
  }
})

router.afterEach((to) => {
  useTitle(to?.meta?.title as string)
  done() // 结束Progress
  loadDone()
})

//解决部署后缓存问题
import { cacheManager } from '@/utils/cacheManager'

router.onError(async (error) => {
  if (import.meta.env.DEV) return

  const fetchResourcesErrors = [
    'Failed to fetch dynamically imported module',
    'Importing a module script failed'
  ]

  // 检查是否是资源加载错误
  const isResourceError = fetchResourcesErrors.some(
    (item) => error?.message && error.message?.includes(item)
  )

  if (isResourceError) {
    // 资源加载错误，直接刷新页面
    console.warn('检测到资源加载错误，直接刷新页面:', error.message)
    window.location.reload()
  } else {
    // 其他路由错误，进行版本检测
    console.warn('检测到路由错误，进行版本检测:', error.message)

    try {
      // 执行完整的版本检测和刷新逻辑
      await cacheManager.init()
    } catch (checkError) {
      console.error('版本检测失败，直接刷新页面:', checkError)
      window.location.reload()
    }
  }
})
