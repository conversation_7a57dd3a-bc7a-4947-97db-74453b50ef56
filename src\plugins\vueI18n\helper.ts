export const setHtmlPageLang = (locale: LocaleType) => {
  document.querySelector('html')?.setAttribute('lang', locale)
}

/**
 * 导入翻译文件
 * @param lang 语言
 * @returns
 */
import { set } from 'lodash-es'
//import.meta.glob暂不支持变量拼接路径.有几种翻译就引入几次
const langFiles = {
  'zh-CN': import.meta.glob(`../../locales/zh-CN/**/*.ts`, { eager: true }),
  'zh-TW': import.meta.glob(`../../locales/zh-TW/**/*.ts`, { eager: true }),
  en: import.meta.glob(`../../locales/en/**/*.ts`, { eager: true })
}
export const importLangModule = (lang: LocaleType) => {
  const langModule = {} //翻译文件
  const files: any = langFiles[lang]

  for (const key in files) {
    const moduleName = key.replace(
      new RegExp(`(\\.\\.\\/\\.\\.\\/locales\\/${lang}\\/|\\.ts)`, 'g'),
      ''
    )
    //支持翻译文件夹下创建二级目录,如:zh-CN/demo/demo.ts
    const moduleNameArray = moduleName.split('/')
    if (moduleNameArray.length > 1) {
      set(langModule, moduleNameArray[0], langModule[moduleNameArray[1]] || {})
      set(langModule[moduleNameArray[0]], moduleNameArray[1], files[key].default)
    } else {
      set(langModule, moduleName, files[key].default || {})
    }
  }
  langModule['OAuth 2.0'] = 'OAuth 2.0' //避免菜单名是 OAuth 2.0 时，一直 warn 报错
  return langModule
}
