/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-13 16:01:28
 * @LastEditors: HoJack
 * @LastEditTime: 2023-07-21 11:09:27
 * @Description:
 */
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'
import type { App } from 'vue'
import { constantRouterMap } from './modules/remaining'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHashHistory(),
  strict: true,
  routes: constantRouterMap as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

const originalPush = router.push
// 重写push方法，捕获异常报错
router.push = function push(location) {
  try {
    return originalPush.call(this, location)
  } catch (error) {
    console.error(error)
    ElMessage.error('用户权限不足，无法访问')
    return Promise.reject(error)
  }
}

// 白名单应该包含基本静态路由
const WHITE_NAME_LIST: string[] = []
const getRouteNames = (array: any[]) =>
  array.forEach((item) => {
    WHITE_NAME_LIST.push(item.name)
    getRouteNames(item.children || [])
  })
getRouteNames(constantRouterMap)

export const resetRouter = (): void => {
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (name && !WHITE_NAME_LIST.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  app.use(router)
}

export default router
