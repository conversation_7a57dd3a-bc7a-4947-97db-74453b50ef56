/*
 * @Author: HoJack
 * @Date: 2023-07-20 15:53:01
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-25 10:55:28
 * @Description:
 */
import { Layout } from '@/utils/routerHelper'
import { useI18n } from '@/hooks/web/useI18n'
const { t } = useI18n()

/**
 * redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

    alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
                              只有一个时，会将那个子路由当做根路由显示在侧边栏，
                              若你想不管路由下面的 children 声明的个数都显示你的根路由，
                              你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
                              一直显示根路由(默认 false)

    title: 'title'            设置该路由在侧边栏和面包屑中展示的名字

    icon: 'svg-name'          设置该路由的图标

    noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)

    breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)

    affix: true               如果设置为true，则会一直固定在tag项中(默认 false)

    noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)

    activeMenu: '/dashboard'  显示高亮的路由路径

    followAuth: '/dashboard'  跟随哪个路由进行权限过滤

    noMenuView: false         如果设置为true，则菜单和tags不会显示(默认 false)

    canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)
  }
 **/

/**
 * 静态默认菜单
 */
export const constantRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/index',
    name: 'Home',
    meta: {},
    children: [
      {
        path: '/index',
        component: () => import('@/views/ProductsShow/BatchCardProduct/BatchCardProduct.vue'),
        name: 'Index',
        meta: {
          title: t('router.home'),
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        }
      }
    ]
  },

  {
    path: '/ssoCallback',
    component: () => import('@/views/Login/SsoCallback.vue'),
    name: 'SsoCallback',
    meta: {
      hidden: true,
      title: t('router.ssoCallback'),
      noTagsView: true
    }
  },
  {
    path: '/ssoError',
    component: () => import('@/views/Login/SsoError.vue'),
    name: 'SsoError',
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/redirect',
    component: Layout,
    name: 'Redirect',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/changePassword',
    component: Layout,
    name: 'ChangePassword',
    children: [
      {
        path: 'index',
        name: 'ChangePassword',
        component: () => import('@/views/System/ChangePassword/index.vue'),
        meta: {
          hidden: true,
          noTagsView: true
        }
      },
      {
        path: '/accountControl',
        component: () => import('@/views/System/AccountControl/index.vue'),
        name: 'AccountControl',
        meta: {
          hidden: true,
          title: t('router.accountControl'),
          noTagsView: true
        }
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFound',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/error',
    component: Layout,
    redirect: '/error/404',
    name: 'Error',
    meta: {
      hidden: true,
      title: t('router.errorPage'),
      icon: 'ci:error',
      alwaysShow: true
    },
    children: [
      {
        path: '404-demo',
        component: () => import('@/views/Error/404.vue'),
        name: '404Demo',
        meta: {
          title: '404'
        }
      },
      {
        path: '403-demo',
        component: () => import('@/views/Error/403.vue'),
        name: '403Demo',
        meta: {
          title: '403'
        }
      },
      {
        path: '500-demo',
        component: () => import('@/views/Error/500.vue'),
        name: '500Demo',
        meta: {
          title: '500'
        }
      }
    ]
  },
  {
    path: '/TodoManagement',
    name: 'TodoManagement',
    component: Layout,
    meta: {
      title: '待办管理',
      icon: 'ep:circle-plus',
      noCache: false,
      alwaysShow: true
    },
    children: [
      {
        path: 'FlowTodo',
        component: () => import('@/views/TodoManagement/FlowTodo/Index.vue'),
        name: 'FlowTodo',
        meta: {
          title: '流程待办',
          icon: 'ep:circle-plus',
          noCache: false
        }
      },
      {
        path: 'BusinessTodo',
        component: () => import('@/views/TodoManagement/BusinessTodo/Index.vue'),
        name: 'BusinessTodo',
        meta: {
          title: '业务待办',
          icon: 'ep:circle-plus',
          noCache: false
        }
      },
      {
        path: 'TodoProcessInstanceDetail',
        component: () =>
          import('@/views/TodoManagement/FlowTodo/TodoProcessInstanceDetail/Index.vue'),
        name: 'TodoProcessInstanceDetail',
        meta: {
          title: '待办详情',
          icon: 'ep:circle-plus',
          noCache: false
        }
      },
      {
        path: 'MessageCenter',
        component: () => import('@/views/MessageCenter/List/index.vue'),
        name: 'MessageCenter',
        meta: {
          title: '消息中心',
          icon: 'ep:circle-plus',
          noCache: false
        }
      }
    ]
  }
]

/**
 * 用于开发调试的路由,如果调试完毕需要注释掉
 */
export const asyncRouterMap: AppRouteRecordRaw[] = [
  // {
  //   path: '/ProductsShow',
  //   component: Layout,
  //   name: 'ProductsShow',
  //   meta: {
  //     title: '产品展示',
  //     icon: 'ep:home-filled',
  //     noCache: false,
  //     affix: false
  //   },
  //   children: [
  //     {
  //       path: 'BatchCardProduct',
  //       component: () => import('@/views/ProductsShow/BatchCardProduct/BatchCardProduct.vue'),
  //       name: 'BatchCardProduct',
  //       meta: {
  //         title: '批卡产品',
  //         icon: 'ep:home-filled',
  //         noCache: false,
  //         affix: false
  //       }
  //     },
  //     {
  //       path: 'DiyCardProduct',
  //       component: () => import('@/views/ProductsShow/DiyCardProduct/DiyCardProduct.vue'),
  //       name: 'DiyCardProduct',
  //       meta: {
  //         title: 'DIY产品',
  //         icon: 'ep:home-filled',
  //         noCache: false,
  //         affix: false
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/CardProductBusiness',
  //   component: Layout,
  //   name: 'CardProductBusiness',
  //   meta: {
  //     title: '卡产品业务',
  //     icon: 'ep:home-filled',
  //     noCache: false,
  //     affix: false
  //   },
  //   children: [
  //     {
  //       path: 'MakeCardService',
  //       component: () =>
  //         import('@/views/CardProductBusiness/ProductDemand/MakeCardService/Index.vue'),
  //       name: 'MakeCardService',
  //       meta: {
  //         title: '产品需求',
  //         icon: 'ep:home-filled',
  //         noCache: false,
  //         affix: false
  //       },
  //       children: [
  //         {
  //           path: 'DemandDetail',
  //           component: () =>
  //             import(
  //               '@/views/CardProductBusiness/ProductDemand/MakeCardService/DemandDetail/Index.vue'
  //             ),
  //           name: 'DemandDetail',
  //           meta: {
  //             title: '制卡需求详情',
  //             hidden: true,
  //             noCache: false
  //           }
  //         },
  //         {
  //           path: 'SampleCardDetail',
  //           component: () =>
  //             import(
  //               '@/views/CardProductBusiness/ProductDemand/MakeCardService/SampleCard/Detail.vue'
  //             ),
  //           name: 'SampleCardDetail',
  //           meta: {
  //             title: '订单详情',
  //             hidden: true,
  //             noCache: false
  //           }
  //         },
  //         {
  //           path: 'SampleCardApply',
  //           component: () =>
  //             import(
  //               '@/views/CardProductBusiness/ProductDemand/MakeCardService/SampleCard/Apply.vue'
  //             ),
  //           name: 'SampleCardApply',
  //           meta: {
  //             title: '样卡申请',
  //             hidden: true,
  //             noCache: true,
  //             affix: false
  //           }
  //         }
  //       ]
  //     },
  //     {
  //       path: 'ProxyCustomerToOrder',
  //       component: () => import('@/views/CardProductBusiness/ProxyCustomerToOrder/Index.vue'),
  //       name: 'ProxyCustomerToOrder',
  //       meta: {
  //         title: '代客下单',
  //         icon: 'ep:home-filled',
  //         noCache: true,
  //         affix: false
  //       }
  //     },
  //     {
  //       path: 'OrderApproval',
  //       component: () =>
  //         import('@/views/CardProductBusiness/OrderApproval/SalesNodes/List/Index.vue'),
  //       name: 'OrderApproval',
  //       meta: {
  //         title: '订单签审',
  //         icon: 'ep:home-filled',
  //         noCache: false,
  //         affix: false
  //       }
  //     },
  //     {
  //       path: 'OrderSearch',
  //       component: () => import('@/views/CardProductBusiness/OrderSearch/Index.vue'),
  //       name: 'OrderSearch',
  //       meta: {
  //         title: '订单查询',
  //         icon: 'ep:home-filled',
  //         noCache: false,
  //         affix: false
  //       },
  //       children: [
  //         {
  //           path: 'BatchCardOrderDetails',
  //           component: () =>
  //             import('@/views/CardProductBusiness/OrderSearch/BatchCardOrderDetails.vue'),
  //           name: 'BatchCardOrderDetails',
  //           meta: {
  //             title: '批卡订单详情',
  //             hidden: true,
  //             noCache: true
  //           }
  //         },
  //         {
  //           path: 'SampleCardOrderDetails',
  //           component: () =>
  //             import('@/views/CardProductBusiness/OrderSearch/SampleCardOrderDetails.vue'),
  //           name: 'SampleCardOrderDetails',
  //           meta: {
  //             title: '样卡订单详情',
  //             hidden: true,
  //             noCache: true
  //           }
  //         }
  //       ]
  //     }
  //   ]
  // },
  // {
  //   path: '/CustomerService',
  //   component: Layout,
  //   name: 'CustomerService',
  //   meta: {
  //     title: '客户服务',
  //     icon: 'ep:home-filled',
  //     noCache: false,
  //     affix: false,
  //     alwaysShow: true
  //   },
  //   children: [
  //     {
  //       path: 'ComplaintManagement',
  //       component: () => import('@/views/CustomerService/ComplaintManagement/Index.vue'),
  //       name: 'ComplaintManagement',
  //       meta: {
  //         title: '投诉管理',
  //         icon: 'ep:home-filled',
  //         noCache: false,
  //         affix: false
  //       },
  //       children: [
  //         {
  //           path: 'ComplaintDetail',
  //           component: () =>
  //             import('@/views/CustomerService/ComplaintManagement/ComplaintDetail/Index.vue'),
  //           name: 'ComplaintDetail',
  //           meta: {
  //             title: '投诉详情',
  //             noCache: true,
  //             hidden: true,
  //             affix: false
  //           }
  //         }
  //       ]
  //     }
  //   ]
  // },
  // 待办
  // {
  //   path: '/AIService',
  //   name: 'AIService',
  //   component: Layout,
  //   meta: {
  //     title: 'AI服务',
  //     icon: 'ep:circle-plus',
  //     noCache: false,
  //     alwaysShow: true
  //   },
  //   children: [
  //     {
  //       path: 'AIGCManagement',
  //       component: () => import('@/views/AIService/AIGCManagement/Index.vue'),
  //       name: 'AIGCManagement',
  //       meta: {
  //         title: 'AIGC',
  //         icon: 'ep:circle-plus',
  //         noCache: false
  //       }
  //     },
  //     {
  //       path: 'FixedIP',
  //       component: () => import('@/views/AIService/FixedIP/Index.vue'),
  //       name: 'FixedIP',
  //       meta: {
  //         title: '动漫IP',
  //         icon: 'ep:circle-plus',
  //         noCache: false
  //       }
  //     }
  //   ]
  // }
]
