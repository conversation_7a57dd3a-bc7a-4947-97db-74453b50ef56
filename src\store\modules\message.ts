import { defineStore } from 'pinia'
import * as messageCenterApi from '@/api/messageManagement/messageCenter/index'

export const useMessageStore = defineStore('message', {
  state: () => {
    return {
      messageCount: 0 // 未读消息数量
    }
  },

  getters: {},

  actions: {
    async getUnreadMessage() {
      try {
        const res = await messageCenterApi.countUnreadMessageTo() // 读取消息中心未读的消息数量
        this.messageCount = res ? res : 0
      } catch (error) {}
    }
  }
})
