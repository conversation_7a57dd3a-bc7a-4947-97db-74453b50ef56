import { defineStore } from 'pinia'
import { store } from '../index'
import { cloneDeep } from 'lodash-es'
import { constantRouterMap, asyncRouterMap } from '@/router/modules/remaining'
import { generateRoute, flatMultiLevelRoutes, filterRouteByCondition } from '@/utils/routerHelper'
import { getRouteAndPermission, getRouteAndPermissionRenovation } from '@/api/login'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import router from '@/router/index'

const { wsCache } = useCache()
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

export interface PermissionState {
  permissions: string[]
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  menuTabRouters: AppRouteRecordRaw[]
  isAddRouters: boolean
}

interface RouteAndPermission {
  menus: AppCustomRouteRecordRaw[]
  permissions: string[]
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    permissions: [],
    routers: [],
    addRouters: [],
    menuTabRouters: [],
    isAddRouters: false
  }),
  getters: {
    getPermissions(): string[] {
      return this.permissions
    },
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },

    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    },
    // 是否已经添加了动态路由
    getIsAddRouters(): boolean {
      return this.isAddRouters
    }
  },
  actions: {
    /**
     * 异步生成路由
     *
     * @param ifResetRoutes 是否重新发起请求生成路由
     * @returns 返回一个Promise对象，该对象在解析时返回void类型
     */
    async generateRoutes(ifResetRoutes?: boolean | undefined): Promise<unknown> {
      return new Promise<void>(async (resolve) => {
        let res: RouteAndPermission
        let resRoute: AppCustomRouteRecordRaw[]
        const handledRoute: AppCustomRouteRecordRaw[] = []

        if (
          !ifResetRoutes &&
          wsCache.get(CACHE_KEY.ROLE_PERMISSIONS) &&
          wsCache.get(CACHE_KEY.ROLE_ROUTERS)
        ) {
          //获取缓存-路由菜单和按钮权限
          this.permissions = wsCache.get(CACHE_KEY.ROLE_PERMISSIONS) as string[]
          resRoute = wsCache.get(CACHE_KEY.ROLE_ROUTERS) as AppCustomRouteRecordRaw[]
        } else {
          //无端权限，需要重新登录，跳转到SSO错误页面
          if (!userStore.getCurrentClientId) {
            router.push({
              path: '/ssoError',
              query: {
                noClient: 1
              }
            })
            return
          }
          // 路由和权限接口
          res = await getRouteAndPermissionRenovation({
            clientId: userStore.getCurrentClientId
          })
          res.menus.forEach((el) => {
            if (el.catalog && el.children) {
              el.children.forEach((item) => {
                !item.path.startsWith('/') && (item.path = `/${item.path}`)
              })
              handledRoute.push(...el.children)
            }
          })

          resRoute = filterRouteByCondition(handledRoute)

          this.permissions = res.permissions
          //缓存-路由菜单和按钮权限
          wsCache.set(CACHE_KEY.ROLE_ROUTERS, resRoute)
          wsCache.set(CACHE_KEY.ROLE_PERMISSIONS, res.permissions)
        }

        let routerMap: AppRouteRecordRaw[] = generateRoute(resRoute as AppCustomRouteRecordRaw[])
        // let routerMap: AppRouteRecordRaw[] = []
        // routerMap = cloneDeep(asyncRouterMap).concat(routerMap) //使用本地动态路由 todo:后面全部接入由接口接入
        routerMap = cloneDeep(asyncRouterMap).concat(routerMap)
        // 动态路由，404一定要放到最后面
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])
        // 渲染菜单的所有路由
        this.routers = cloneDeep(constantRouterMap).concat(routerMap)

        resolve()
      })
    },

    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    },
    // 设置是否已经添加了动态路由
    setIsAddRouters(state: boolean): void {
      this.isAddRouters = state
    }
  }
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}
