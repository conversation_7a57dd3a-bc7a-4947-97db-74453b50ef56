import { defineStore } from 'pinia'
import { remove } from 'lodash-es'

/**
 * @description 定义样卡申请参数对象
 * @interface requirement
 */
export interface requirementModel {
  /**
   * @description 需求ID
   * @type {string}
   * @memberof requirement
   */
  requirementId: string

  /**
   * @description 样卡申请的稿样方案ID数组
   * @type {string[]}
   * @memberof requirement
   */
  schemeInfoIds: string[]
}

declare interface sampleCardApplicationModel {
  //需求参数
  requirementParams: requirementModel[]
}

export const useSampleCardApplicationStore = defineStore('sampleCardApplicationStore', {
  state: (): sampleCardApplicationModel => {
    return {
      requirementParams: []
    }
  },
  actions: {
    /**
     * @description 设置卡产品需求样卡申请参数
     * @param {string} requirementId
     * @param {string[]} schemeInfoIds
     */
    setRequirement(requirementId: string, schemeInfoIds: string[]): void {
      const item: requirementModel = this.$state.requirementParams.filter(
        (item) => item.requirementId == requirementId
      )[0]
      if (item) {
        item.schemeInfoIds = [...schemeInfoIds]
      } else {
        const newItem: requirementModel = {
          requirementId: requirementId,
          schemeInfoIds: schemeInfoIds
        }
        this.$state.requirementParams.push(newItem)
      }
    },

    /**
     * @description 获取卡产品需求样卡申请参数
     * @param {string} requirementId
     * @return {*}  {requirement}
     */
    getRequirementById(requirementId: string): requirementModel {
      const params = this.$state.requirementParams.filter(
        (item) => item.requirementId == requirementId
      )[0]
      console.log('params', params)
      return params
    },

    /**
     * @description 删除卡产品需求样卡申请参数
     * @param {string} requirementId
     */
    removeRequirementById(requirementId: string): void {
      remove(this.$state.requirementParams, (item) => item.requirementId == requirementId)
    }
  }
})
