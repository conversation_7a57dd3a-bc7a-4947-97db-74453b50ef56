/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-21 10:08:00
 * @LastEditors: HoJack
 * @LastEditTime: 2023-07-21 12:02:50
 * @Description:
 */
import { store } from '../index'
import { defineStore } from 'pinia'
import { getToken, autoAuthorize } from '@/api/login/oauth2'
import * as authUtil from '@/utils/auth'
import { resetRouter } from '@/router'
import { useCache } from '@/hooks/web/useCache'
import { ElMessage } from 'element-plus'
const { wsCache } = useCache()
import envController from '@/controller/envController'

import { useLocaleStoreWithOut } from '@/store/modules/locale'
const localeStore = useLocaleStoreWithOut()

//用户信息store
import { useUserStoreWithOut } from '@/store/modules/user'

interface ssoVO {
  clientId: string
  authUrl: string
  appUrl: string
  redirectUri: string
  responseType: string
}

export const useSsoStore = defineStore('sso', {
  state: (): ssoVO => ({
    clientId: import.meta.env.VITE_APP_CLIENT_ID,
    authUrl: '',
    appUrl: window.location.origin,
    redirectUri: '',
    responseType: 'code'
  }),
  getters: {},
  actions: {
    /**
     * 设置重定向 URI
     *
     * 将当前应用的 URL 编码并拼接 '/#/' 后缀，然后赋值给 redirectUri 属性
     *
     * @returns 返回编码后的重定向 URI
     */
    setRedirectUri() {
      this.redirectUri = encodeURIComponent(this.appUrl + '/#/')
      return this.redirectUri
    },
    /**
     * 设置授权URL
     *
     * @returns 无返回值，但会更新实例的 redirectUri 属性
     */
    setAuthUrl() {
      try {
        if (envController.getEnvMapServiceUrl().size === 0) {
          throw new Error('未找到授权地址')
        }
        if (envController.getEnvironment() === '') {
          throw new Error('未找到环境')
        }
        this.authUrl = envController.getServiceUrl() as string
      } catch (error) {
        console.log(error)
      }
    },
    /**
     * SSO回调处理函数,授权码模式获取token
     *
     * @returns 返回一个Promise对象，resolve为true表示登录成功，reject为false表示登录失败
     */
    async ssoCallback(query) {
      const { t } = useI18n()
      const router = useRouter()

      //loading
      const loading = ElLoading.service({
        lock: true,
        text: t('sys.login.ssoLoading'),
        background: 'rgba(255, 255, 255, 0.3)'
      })
      return new Promise<boolean>(async (resolve, reject) => {
        try {
          //保存租户id
          authUtil.setTenantId(query.tenantId as string)

          //通过授权获取token
          const ifSuccess = await this.getTokenByCode(query?.code as string)
          //成功跳转首页  todo:可以根据业务跳转指定页面(由于没有首页将无限重定向到授权登录)
          if (ifSuccess) {
            //重置用户信息,路由守卫中重新获取用户信息,避免登录前后信息不一致
            const userStore = useUserStoreWithOut()
            userStore.resetState()
            resolve(true)
            router.push('/')
            loading.close()
          }
        } catch (error) {
          console.log(error)
          loading.close()

          reject(false)
        }
      })
    },

    //授权码获取token
    async getTokenByCode(code: string) {
      const { t } = useI18n()

      return new Promise<boolean>(async (resolve, reject) => {
        try {
          const params = {
            code: code,
            redirect_uri: decodeURIComponent(this.setRedirectUri()),
            grant_type: 'authorization_code',
            'client-id': this.clientId
          }
          console.log('获取授权码参数', params)

          const ssoRes = await getToken(params)

          if (ssoRes.code !== 0) {
            reject(false)
          }

          authUtil.setToken(ssoRes.data)

          resolve(true)
        } catch (error) {
          ElMessage.error(t('store.store.getCodeFail'))
          reject(false)
        }
      })
    },
    /**
     * 单点登录授权-获取授权码并重定向到第三方应用
     *
     * @param clientId 客户端ID(oauth2.0中配置的clientId)
     * @param redirectUri 重定向URI
     * @returns 无返回值
     */
    async ssoAuth(clientId, redirectUri) {
      const loadingInstance = ElLoading.service({
        lock: true
      })
      try {
        //哈希路由设置
        const thisUrl = new URL(redirectUri)
        redirectUri = thisUrl.origin + '/#/'
        //获取授权码
        const data = (await autoAuthorize(clientId, redirectUri)) as string
        console.log('重定向第三方应用地址:', data)
        if (!data) {
          return
        }
        const currentLang = localeStore.getCurrentLocale.lang
        const url =
          data.replace(redirectUri, redirectUri + 'ssoCallback') + //修改重定向地址,跳转至单点登录回调地址
          `&lang=${currentLang}` +
          `&tenantId=${authUtil.getTenantId()}` +
          `&goPath=ssoCallback`
        console.log('url', url)
        location.href = url
      } catch (error) {
        console.log(error)
      } finally {
        loadingInstance.close()
      }
    },
    async ssoLogin() {
      const lang = localeStore.getCurrentLocale.lang

      // 先获取当前路由地址
      const currentRoute = window.location.hash.replace('#', '') || '/'

      resetRouter() // 重置静态路由表
      wsCache.clear()
      authUtil.removeTenantId()
      authUtil.removeToken()
      const userStore = useUserStoreWithOut()
      userStore.resetState() //清除用户信息

      // 在清除缓存后保存当前路由地址，用于登录成功后跳转
      // 避免保存登录相关的页面地址
      const shouldNotSave = ['/ssoCallback', '/ssoError', '/404'].some((path) =>
        currentRoute.includes(path)
      )
      if (!shouldNotSave) {
        wsCache.set('sso_redirect_url', currentRoute)
        console.log('保存当前路由地址:', currentRoute)
      } else {
        console.log('当前页面不需要保存重定向地址:', currentRoute)
      }

      this.setRedirectUri()
      this.setAuthUrl() //设置授权地址
      const url =
        this.authUrl +
        '/#/sso?client_id=' +
        this.clientId +
        '&l=1' +
        `${lang ? `&lang=${lang}` : ''}` +
        '&redirect_uri=' +
        this.redirectUri +
        '&response_type=' +
        this.responseType

      window.location.href = url
    }
  }
})

export const useSsoStoreWithOut = () => {
  return useSsoStore(store)
}
