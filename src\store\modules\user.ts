import { store } from '../index'
import { defineStore } from 'pinia'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { getInfo, loginOutApi, getUserInfo } from '@/api/login'
import { useTagsViewStore } from './tagsView'
import { resetRouter } from '@/router'
import { useI18n } from '@/hooks/web/useI18n'
// import { getCustomerByTenantId } from '@/api/Contractmanagement/index'
import { refreshToken, handleAuthorized } from '@/config/axios/service'
import * as authUtil from '@/utils/auth'
import * as UserApi from '@/api/user'
import { isEmpty, isNumber } from 'lodash-es'

import { useLocaleStore } from '@/store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'
import { useLocaleStore } from '@/store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'

const localeStore = useLocaleStore()

const { wsCache } = useCache()
const message = useMessage()

// const { replace } = useRouter()

interface UserVO {
  id: number
  avatar: string
  nickname: string
}
interface RoleVO {
  id: number
  name: string
  code: string
  sort: number
  type: number
  isStale?: boolean
}

interface ClientVO {
  applicationVOlist: any[]
  code: string
  createTime: number
  description: string
  id: number
  name: string
  oauthClient: string
  status: number
  url: string
}

interface UserInfoVO {
  roles: RoleVO[]
  isSetUser: boolean
  user: UserVO
  customerInfo: any
  accountInfo: UserApi.AccountType //账号信息
  clients: ClientVO[]
  currentClientId: number | undefined
}

export const useUserStore = defineStore('admin-user', {
  state: (): UserInfoVO => ({
    roles: [],
    currentClientId: undefined,
    clients: [],
    isSetUser: false,
    user: {
      id: 0,
      avatar: '',
      nickname: ''
    },
    customerInfo: undefined,
    accountInfo: {
      avatar: '',
      createTime: '',
      deptName: '',
      deptId: 0,
      email: '',
      mobile: '',
      nickname: '',
      username: ''
    }
  }),
  getters: {
    getRoles(): RoleVO[] {
      return this.roles
    },

    getCurrentClientId(): number | undefined {
      const cacheClientId = wsCache.get(CACHE_KEY.CURRENT_CLIENT_ID)
      if (cacheClientId) {
        this.currentClientId = cacheClientId
      }
      return this.currentClientId
    },
    getClients(): ClientVO[] {
      return this.clients
    },
    getIsSetUser(): boolean {
      return this.isSetUser
    },
    getUser(): UserVO {
      return this.user
    },
    getUserId(): number {
      return this.user.id
    },
    getCustomerInfo(): any {
      return wsCache.get(CACHE_KEY.CUSTOMER_INFO) || this.customerInfo
    },
    getAccountInfo(): UserApi.AccountType {
      return this.accountInfo || wsCache.get(CACHE_KEY.ACCOUNT_INFO)
    }
  },
  actions: {
    handleClear() {
      const tenantId = authUtil.getTenantId()
      authUtil.removeToken()

      const tagsViewStore = useTagsViewStore()
      tagsViewStore.delAllViews()
      resetRouter() // 重置静态路由表
      const lang = localeStore.getCurrentLocale.lang
      wsCache.clear()
      sessionStorage.clear()
      localeStore.setCurrentLocale({
        lang
      })
      const { changeLocale } = useLocale()
      changeLocale(lang)
      authUtil.setTenantId(tenantId)
      window.location.replace('/')
    },
    // 退出登录
    loginOut(title?: string) {
      const { t } = useI18n()
      ElMessageBox.confirm(title ? title : t('common.loginOutMessage'), t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }).then(async () => {
        try {
          const res = await loginOutApi()
          if (res) {
            this.handleClear()
          }
        } catch (error) {
          console.log('退出登录接口调取失败', error)
          this.handleClear()
        }
      })
    },
    forceLoginOut() {
      const { t } = useI18n()
      ElMessageBox.alert(t('store.user.soonLogout'), t('store.user.logout'), {
        confirmButtonText: t('common.ok'),
        callback: async () => {
          debugger
          const res = await loginOutApi()
          if (res) {
            this.handleClear()
          }
        }
      })
    },
    // 获取客户相关信息
    async getCustomerInfoAction(refreshUser?: boolean) {
      const { t } = useI18n()
      const customerInfo = wsCache.get(CACHE_KEY.CUSTOMER_INFO)

      if (!customerInfo || refreshUser) {
        try {
          // customerInfo = await getCustomerByTenantId(authUtil.getTenantId())

          wsCache.set(CACHE_KEY.CUSTOMER_INFO, customerInfo)
        } catch (error) {
          message.error(t('store.user.customerInfoRelogin'))
        }
      }
      this.customerInfo = customerInfo
    },
    //获取账号信息
    async setAccountInfoAction(refreshUser?: boolean) {
      const { t } = useI18n()
      let accountInfo = wsCache.get(CACHE_KEY.ACCOUNT_INFO)

      if (!accountInfo || refreshUser) {
        try {
          accountInfo = await UserApi.getUserAccountInfo(this.user.id)
        } catch (error) {
          message.error(t('store.user.accountRelogin'))
        }
      }
      this.accountInfo = accountInfo
      wsCache.set(CACHE_KEY.ACCOUNT_INFO, accountInfo)
    },
    /**
     * 设置用户信息
     *
     * @param refreshUser 是否刷新用户信息，默认为 false
     * @returns 返回设置结果，如果未登录则返回 null
     */
    async setUserInfoAction(refreshUser?: boolean) {
      const { t } = useI18n()
      if (!authUtil.getAccessToken()) {
        this.resetState()
        return null
      }

      let userInfo = wsCache.get(CACHE_KEY.USER)

      const userInfoRecord = wsCache.get(CACHE_KEY.USER)

      if (isEmpty(userInfo) || refreshUser) {
        try {
          // userInfo = await getInfo()
          userInfo = await getUserInfo()
        } catch (error) {
          // message.error('获取用户信息失败')
          console.log('获取用户信息失败' + error)
          throw t('store.user.getUserInfoFail')
        }
      }
      this.user = userInfo.user

      this.isSetUser = true
      wsCache.set(CACHE_KEY.USER, userInfo)

      // 无交互端ID缓存则设置当前交互端ID
      const cacheClientId = wsCache.get(CACHE_KEY.CURRENT_CLIENT_ID)

      // 获取列表第一个客户列表
      if (!cacheClientId) {
        userInfo.clients &&
          userInfo.clients.length > 0 &&
          this.setCurrentClientId(userInfo.clients[0].id)

        if (!userInfo.clients || userInfo.clients.length === 0) {
          ElMessage.error('登录失败，请联系管理员！')
          return
        }
      }

      // if (userInfo.roles.length === 0) {
      //   message.warning(t('store.user.noRole'))
      //   return
      // }

      this.roles = userInfo.roles
      this.clients = userInfo.clients

      /************************* 刷新token获取正确菜单, 930节点后端要求先这样处理，但会增加登录后的加载时间，建议后期后端更改该逻辑 ********************************/
      // if (!refreshUser && !userInfoRecord) {
      //   //切换角色时不刷新token
      //   try {
      //     const refreshTokenRes: any = await refreshToken()
      //     authUtil.setToken(refreshTokenRes.data.data)
      //   } catch (e) {
      //     // return handleAuthorized()
      //   }
      // }
      /************************* end ********************************/

      // 获取客户信息
      this.getCustomerInfoAction(refreshUser)
      //获取账号信息
      this.setAccountInfoAction(refreshUser)
    },
    async regetUserInfo() {
      // 重新获取用户信息
      let userInfo
      try {
        // userInfo = await getInfo()
        userInfo = await getUserInfo()
        console.log('userInfo', userInfo)
      } catch (error) {
        // message.error('获取用户信息失败')
        console.log('获取用户信息失败')
        throw '获取用户信息失败'
      }
      this.isSetUser = true
      this.user = userInfo.user
      wsCache.set(CACHE_KEY.USER, userInfo)
    },

    resetState() {
      this.roles = []
      this.isSetUser = false
      this.user = {
        id: 0,
        avatar: '',
        nickname: ''
      }
      this.accountInfo = {
        avatar: '',
        createTime: '',
        deptName: '',
        deptId: 0,
        email: '',
        mobile: '',
        nickname: '',
        username: ''
      }
      this.customerInfo = {}
      wsCache.delete(CACHE_KEY.USER) // 删除用户信息缓存
      wsCache.delete(CACHE_KEY.ROLE_PERMISSIONS) // 删除角色权限缓存
      wsCache.delete(CACHE_KEY.ROLE_ROUTERS) // 删除角色路由缓存
      wsCache.delete(CACHE_KEY.ACCOUNT_INFO) // 删除账号信息缓存
      wsCache.delete(CACHE_KEY.CUSTOMER_INFO) // 删除客户信息缓存
    },

    setCurrentClientId(clientId: number | undefined) {
      if (!isNumber(clientId)) return
      this.currentClientId = clientId
      wsCache.set(CACHE_KEY.CURRENT_CLIENT_ID, clientId)
    }
  }
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
