@import './var.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}


// v-show动画过渡
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}

//修改primary按钮的渐变颜色
.el-button--primary {
  &.el-button--default:not(.is-plain){
    background: linear-gradient(114deg, #eecd91, #d5a147);
    color: #ffff;
    border-color: #ffff;
    &:hover{
      background:linear-gradient(114deg, #d5a147, #e0a949);
     }
    }
}

// tui-image-editor组件弹窗的样式
.tui-edit-img_dialog {
  padding: 0 !important;

  .el-dialog__header {
    padding: 16px 16px 0 16px;
  }

  .el-dialog__body {
    padding: 0 !important;
    height: calc(100% - 134px);
  }
}

// /* ---el-table滚动条公共样式--- */
.el-scrollbar {
  // 横向滚动条
  .el-scrollbar__bar.is-horizontal .el-scrollbar__thumb {
    opacity: 0.8; // 默认滚动条自带透明度
    height: 10px; // 横向滑块的宽度
    border-radius: 2px; // 圆角度数
    background: linear-gradient(114deg, #f9e0b3, #eecd91);   
     box-shadow: 0 0 6px rgba(0, 0, 0, 0.15); // 滑块阴影
  }
  .el-scrollbar__bar.is-horizontal{
    height: 10px;
  }
  // 纵向滚动条
  .el-scrollbar__bar.is-vertical .el-scrollbar__thumb {
    opacity: 0.8;
    width: 8px; // 纵向滑块的宽度
    border-radius: 2px;
    background: linear-gradient(114deg, #f9e0b3, #eecd91); 
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.15);
  }
}