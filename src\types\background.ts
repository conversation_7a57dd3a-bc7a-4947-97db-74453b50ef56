export type BackgroundTypes = {
  name?: string
  id?: number
  componentName?: any
  componentConfigName?: any
  types?: string
  limit?: number
  data?: DataTypes
  type?: number
}

export type DataTypes = {
  imageUrl?: string[]
  borderRadius?: string
  borderColor?: string
  background?: string
}

export type InsideTypes = {
  name?: string
  id?: number
  limit?: number
  type?: number
  data?: DataTypes
}

// export type InsideDataTypes = {
//   imageUrl?: string[]
//   borderRadius?: string
//   borderColor?: string
//   background?: string
// }

export type ButtonTypes = {
  imageUrl?: string[]
  borderRadius?: number
  borderRadiusBoolean?: boolean
  gradientBorderRadius?: number
  borderColor?: string
  gradientBorderColor?: string
  background?: string
  gradientBackColor?: string
  fontFamily?: string
  fontSize?: number
  fontColor?: string
  gradientFontColor?: string
  top?: number
  left?: number
  right?: number
  bottom?: number
  text?: string
  borderTopLeftRadius?: number
  borderTopRightRadius?: number
  borderBottomLeftRadius?: number
  borderBottomRightRadius?: number
  imgs?: string[]
  btnBackground?: string
  gradientBtnBackground?: string
  showPicBackground?: boolean
  showBtnBackground?: boolean
  radiusCheck?: boolean
  backgroundCheck?: boolean
  borderColorCheck?: boolean
}
