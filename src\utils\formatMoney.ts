import { samePoint } from './pattern'
// import { first, filter, chain } from 'lodash-es'
/*
 * @Author: <PERSON>J<PERSON>
 * @Date: 2023-11-06 11:33:22
 * @LastEditors: HoJack
 * @LastEditTime: 2023-11-06 11:59:16
 * @Description:
 */
//金额转千位符
const formatMoney = (val) => {
  if (!val || val == 0) return '￥0.00'
  if (val == Infinity) return '∞'
  val = val.toString().replace('[$,]', '')
  if (isNaN(val)) {
    val = '0'
  }
  const sign = val == (val = Math.abs(val))
  val = Math.floor(val * 100 + 0.50000000001)
  let cents: number | string = val % 100
  val = Math.floor(val / 100).toString()
  if (cents < 10) {
    cents = '0' + cents
  }
  for (let i = 0; i < Math.floor((val.length - (1 + i)) / 3); i++) {
    val = val.substring(0, val.length - (4 * i + 3)) + ',' + val.substring(val.length - (4 * i + 3))
  }
  return '￥' + (sign ? '' : '-') + val + '.' + cents
}

export function formatMoneyRMB(money: string): string {
  return Number(money) > 0 ? `￥${money}` : 0 + ''
}

/***退款金额增加负号 */
export function formaRefundMoneyRMB(money: string): string {
  if (Number(money) > 0) {
    return `￥-${money}`
  } else if (Number(money) < 0) {
    return `￥${money}`
  } else {
    return 0 + ''
  }
}

/**
 * 货币统一格式并待币种符号
 * 并按照租户设置小数位格式化显示
 * @param money
 * @param unit
 * @returns
 */
export function formatMoneyDigitsEx(money: string, unit: string | undefined): string {
  const coinType: string = moneyUnitMapper(unit)

  // const decmaliLen: number = 4
  const moneyLogo = '' //￥ 暂时隐藏金额标识
  if (money == '' || money == undefined || money.length <= 0) {
    return moneyLogo + 0
  }

  const mm = Number(money)
  // if (mm <= 0) {
  //   return moneyLogo + 0
  // }

  money = money + ''
  const index = money?.lastIndexOf('.')
  //let isFloat = false
  let decmaliLen = 3 //默认小数点为
  if (index > -1) {
    //const bo = samePoint.test(money.toString())
    // isFloat = bo ? false : true
    // isFloat = true
    // if (bo) {
    decmaliLen = money.substring(index + 1).length //获取小数位数
    // }
  }

  console.log('money', money, 'coinType', coinType, 'mm', mm, 'decmaliLen', decmaliLen)
  const num = mm.toLocaleString('zh-CN', {
    style: 'currency',
    currency: coinType ?? 'CNY',
    minimumFractionDigits: decmaliLen,
    maximumFractionDigits: decmaliLen
  })
  return num
}

/**
 * 后端币种符号转前端显示的币种符号的对照表
 * 防止后端给到的是RMB时无法正确显示币种符号
 * @type {*} */
const moneyUnitTransformArray = [
  { key: 'RMB', value: 'CNY' },
  { key: 'HKD', value: 'HKD' }
]

/**
 * @description 后端币种符号转前端显示的币种符号
 *              防止后端给到的是RMB时无法正确显示币种符号
 * @param {string} unit
 * @return {*}  {string}
 */
function moneyUnitMapper(unit: string | undefined): string {
  try {
    // const result = chain(moneyUnitTransformArray)
    //   .filter((item) => item.key == unit)
    //   .first()
    //   .value()
    const result = moneyUnitTransformArray.filter((item) => item.key == unit)[0]
    if (result) {
      return result.value
    }
    return unit || moneyUnitTransformArray[0].value
  } catch (ex) {
    console.error(`币种${unit}转换失败，`, ex)
    return moneyUnitTransformArray[0].value
  }
}

export default formatMoney
