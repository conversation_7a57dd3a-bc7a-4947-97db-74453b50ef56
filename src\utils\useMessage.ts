import { ElMessage } from 'element-plus'
type MessageType = 'success' | 'warning' | 'info' | 'error'
interface MessageOptions {
  duration?: number
  offset?: number
  showClose?: boolean
  onClose?: () => void
  customClass?: string
  center?: boolean
}
interface Message<T> {
  success: T
  warning: T
  info: T
  error: T
}
interface MessageParams {
  (message: string, options?: MessageOptions): void
}
interface IMessageHandle {
  close: () => void
}
export default function useMessage() {
  let messageInstance: IMessageHandle | null = null

  const handleMessage = (type: MessageType, message: string, options?: MessageOptions) => {
    if (messageInstance) {
      messageInstance.close()
    }
    messageInstance = ElMessage({
      type: type,
      message: message,
      ...options
    })
  }
  const message: Message<MessageParams> = {
    success: (message: string, options?: MessageOptions) => {
      handleMessage('success', message, options)
    },
    warning: (message: string, options?: MessageOptions) => {
      handleMessage('warning', message, options)
    },
    info: (message: string, options?: MessageOptions) => {
      handleMessage('info', message, options)
    },
    error: (message: string, options?: MessageOptions) => {
      handleMessage('error', message, options)
    }
  }
  return message
}
