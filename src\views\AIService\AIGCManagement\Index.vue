<template>
  <ContentWrap>
    <el-form :model="AIPageDTO" ref="formRef" label-width="110px">
      <el-form-item :label="t('AIService.sort')" prop="resultTextPage">
        <el-cascader
          style="width: 100%"
          v-model="AIPageDTO.resultTextPage"
          :options="sortAIList"
          :props="sortProps"
          @change="sortChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" v-track:click.btn @click="confirm" :loading="loading">
          {{ t('AIService.beginCreate') }}
        </el-button>
      </el-form-item>
    </el-form>

    <div class="mt-10px result" v-if="resultImg">
      <div class="title">{{ t('AIService.createResult') }}</div>
      <el-image
        :src="resultImgShow"
        fit="contain"
        class="ai-result-img"
        :preview-src-list="[resultImgShow]"
      />
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import * as aigcApi from '@/api/aigc/index'
import { example } from './example'
import uuid from '@/utils/randomTool.js'
const { t } = useI18n()

interface IAIPageDTO {
  resultTextPage: any
  resultText: string
}

// 页面数据
const AIPageDTO = ref<IAIPageDTO>({
  resultTextPage: undefined,
  resultText: ''
})

// 分类数据Props
const sortProps = {
  label: 'text',
  value: 'data',
  children: 'children'
}

// 分类数据
const sortAIList = example
const sortChange = (e) => {
  AIPageDTO.value.resultText = e.join(',') + ','
}

// 生成按钮
const loading = ref(false)
const confirm = () => {
  getResultText()
}

// 生成结果
const resultImg = ref()
const resultImgShow = computed(() => {
  return resultImg.value
})

const timer = ref()

// 查询结果
const getAigcResult = async (orderNo) => {
  try {
    const { data } = await aigcApi.getAigcResult({ orderNo: orderNo })
    // handleResult  0 处理中  1 成功  2失败
    if (data.handleResult == 1) {
      resultImg.value = data.handleTargetImage
      clearInterval(timer.value)
    }
    if (data.handleResult == 0) {
      timer.value = setTimeout(() => {
        getAigcResult(orderNo)
      }, 1000)
      return data
    }
    if (data.handleResult == 2) {
      ElMessage.error(t('AIService.pleaseLate'))
    }
    loading.value = false
    return data
  } catch (err) {
    console.error(err)
    clearInterval(timer.value)
    loading.value = false
  }
}

const getResultText = async () => {
  if (!AIPageDTO.value.resultText) {
    return ElMessage.error(t('AIService.sortPlaceholder'))
  }

  loading.value = true
  try {
    let params = {
      orderNo: uuid.uuid(),
      aiText: AIPageDTO.value.resultText
    }
    const { data } = await aigcApi.createAigcTask(params)
    if (data) {
      await getAigcResult(data.handleAuditNo)
    }
  } catch (error) {
    console.error(error)
  }
}
</script>

<style lang="scss" scoped>
.result {
  display: flex;
  .title {
    width: 92px;
    text-align: right;
  }
  .ai-result-img {
    height: 600px;
    flex: 1;
    margin-left: 20px;
    background: #f5f5f5;
    border-radius: 10px;
  }
}
</style>
