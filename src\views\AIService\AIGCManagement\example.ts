const { t } = useI18n()

export const example = [
  {
    text: t('AIService.zoon'),
    data: '',
    value: '1',
    children: [
      {
        text: t('AIService.cat'),
        data: 'cat',
        value: '1-1',
        children: [
          {
            data: 'outdoors',
            text: t('AIService.outdoors'),
            value: '1-1-1',
            children: [
              {
                data: 'grass,running,blur sky,white cloud,Elated',
                text: t('AIService.happyZoon'),
                value: '1-1-1-1'
              },
              {
                data: 'forest,running,blur sky,mountain forest，streams,Excited,flowers',
                text: t('AIService.exploreZoon'),
                value: '1-1-1-2'
              },
              {
                data: 'traditional Chinese architecture,bokeh background,sunlight,vibrant color,clarity,shallow depth of field,warm,daytime,fluffy,sky,trees and flower,Smile',
                text: t('AIService.traditionZoon'),
                value: '1-1-1-3'
              },
              {
                data: 'snow,winter,ski goggles,snow-covered trees,winter gear,cool,daytime,clear skies,white,goggles,pet fashion,standing,four-legged',
                text: t('AIService.winterZoon'),
                value: '1-1-1-4'
              },
              {
                data: 'moonlight,colorful fireworks,starry sky',
                text: t('AIService.nightZoon'),
                value: '1-1-1-5'
              }
            ]
          },
          {
            data: 'indoor',
            text: t('AIService.indoor'),
            value: '1-1-2',
            children: [
              {
                data: 'sofa,seiza,holding book',
                text: t('AIService.bookZoon'),
                value: '1-1-2-1'
              },
              {
                data: 'salute,squatting,Welcome home,Beside the dining table',
                text: t('AIService.backHomeZoon'),
                value: '1-1-2-2'
              },
              {
                data: 'stretch,Just woke up from sleep',
                text: t('AIService.lazyZoon'),
                value: '1-1-2-3'
              },
              {
                data: 'Exercise,Tsundere',
                text: t('AIService.runZoon'),
                value: '1-1-2-4'
              },
              {
                data: 'Curiosity,Watching tablets',
                text: 'hie',
                value: '1-1-2-4'
              }
            ]
          }
        ]
      },
      {
        text: t('AIService.dog'),
        data: 'dog',
        value: '1-2',
        children: [
          {
            data: 'outdoors',
            text: t('AIService.outdoors'),
            value: '1-2-1',
            children: [
              {
                data: 'grass,running,blur sky,white cloud,Elated',
                text: t('AIService.happyZoon'),
                value: '1-2-1-1'
              },
              {
                data: 'forest,running,blur sky,mountain forest，streams,Excited,flowers',
                text: t('AIService.exploreZoon'),
                value: '1-2-1-2'
              },
              {
                data: 'traditional Chinese architecture,bokeh background,sunlight,vibrant color,clarity,shallow depth of field,warm,daytime,fluffy,sky,trees and flower,Smile',
                text: t('AIService.traditionZoon'),
                value: '1-2-1-3'
              },
              {
                data: 'snow,winter,ski goggles,snow-covered trees,winter gear,cool,daytime,clear skies,white,goggles,pet fashion,standing,four-legged',
                text: t('AIService.winterZoon'),
                value: '1-2-1-4'
              },
              {
                data: 'moonlight,colorful fireworks,starry sky',
                text: t('AIService.nightZoon'),
                value: '1-2-1-5'
              }
            ]
          },
          {
            data: 'indoor',
            text: t('AIService.indoor'),
            value: '1-2-2',
            children: [
              {
                data: 'sofa,seiza,holding book',
                text: t('AIService.bookZoon'),
                value: '1-2-2-1'
              },
              {
                data: 'salute,squatting,Welcome home,Beside the dining table',
                text: t('AIService.backHomeZoon'),
                value: '1-2-2-2'
              },
              {
                data: 'stretch,Just woke up from sleep',
                text: t('AIService.lazyZoon'),
                value: '1-2-2-3'
              },
              {
                data: 'Exercise,Tsundere',
                text: t('AIService.runZoon'),
                value: '1-2-2-4'
              },
              {
                data: 'Curiosity,Watching tablets',
                text: 'hie',
                value: '1-2-2-5'
              }
            ]
          }
        ]
      }
    ]
  },
  {
    text: t('AIService.scenery'),
    data: '',
    value: '2',
    children: [
      {
        text: t('AIService.noLimit'),
        data: '',
        value: '2-1',
        children: [
          {
            value: '2-1-1',
            text: t('AIService.morningLight'),
            data: 'Morning light filtering through tree branches, casting golden hues on a winding path, impressionist style, warm color palette, wide-angle perspective'
          },
          {
            value: '2-1-2',
            text: t('AIService.reflectionsSunset'),
            data: 'Sunset reflections on a tranquil lake, shimmering waves like dancing specks of gold, romanticist style, warm color palette with oranges and golds, wide-angle perspective.'
          },
          {
            value: '2-1-3',
            text: t('AIService.mountainsDistant'),
            data: 'Distant mountains bathed in soft lavender hues by the morning sunrise, creating a mystical and ethereal landscape, symbolic style, pastel color palette, long-focus perspective'
          },
          {
            value: '2-1-4',
            text: t('AIService.nightStarry'),
            data: 'Starry night sky resembling countless diamonds on black velvet, expressive style, contrasting colors of white and silver stars against deep blue and black sky, wide-angle perspective'
          },
          {
            value: '2-1-4',
            text: t('AIService.forestAutumn'),
            data: 'Autumn forest vibrant with fiery maple leaves, impressionist style, rich color palette of reds, oranges, and yellows, wide-angle perspective capturing the grandeur of the scene'
          },
          {
            value: '2-1-5',
            text: t('AIService.wheatGolden'),
            data: 'Golden wheat field under a blue sky with white clouds, rippling like a golden ocean, realistic style, vibrant color palette, wide-angle perspective capturing the expanse'
          },
          {
            value: '2-1-6',
            text: t('AIService.glisteningDewdrops'),
            data: 'Dewdrops glistening on grass tips in the early morning, crystal clear and radiant, naturalist style, soft color palette with hints of morning light, macro lens perspective for detailed focus'
          },
          {
            value: '2-1-7',
            text: t('AIService.bambooEmerald'),
            data: 'Emerald bamboo forest with sunlight filtering through the leaves, creating a dappled, tranquil, and mysterious atmosphere, symbolic style, contrasting colors of green and golden light, wide-angle perspective'
          },
          {
            value: '2-1-8',
            text: t('AIService.wavesCalm'),
            data: 'Calm waves gently lapping against the sandy beach, creating a serene whooshing sound, realistic style, color palette of blues, yellows, and browns, wide-angle perspective capturing the tranquility'
          },
          {
            value: '2-1-10',
            text: t('AIService.spanningRainbow'),
            data: 'Rainbow spanning the sky after a rainstorm, bringing hope and vitality to the overcast sky, romanticist style, vibrant color palette of the rainbow against grey and white clouds, wide-angle perspective.'
          },
          {
            value: '2-1-11',
            text: t('AIService.purpleVibrant'),
            data: 'Vibrant purple lavender fields swaying in the breeze, releasing a gentle floral fragrance, impressionist style, rich color palette of purples and greens, wide-angle perspective capturing the serene beauty'
          },
          {
            value: '2-1-12',
            text: t('AIService.riceAutumn'),
            data: 'Autumn rice fields with heavy, golden rice ears bowing down, signaling the harvest season, realistic style, warm color palette of golds and greens, wide-angle perspective capturing the vastness of the fields'
          },
          {
            value: '2-1-13',
            text: t('AIService.valleyMisty'),
            data: 'Misty valley at dawn with clouds swirling around the mountains, creating a mystical and serene landscape, surreal style, soft color palette with whites, greys, and hints of morning sky, wide-angle perspective'
          },
          {
            value: '2-1-14',
            text: t('AIService.landscapeDesert'),
            data: 'Desert landscape under the setting sun, with golden sand dunes rolling majestically, creating a spectacular and mysterious atmosphere, expressionist style, contrasting colors of gold, red, and orange, wide-angle perspective'
          },
          {
            value: '2-1-15',
            text: t('AIService.meadowVibrant'),
            data: 'Vibrant meadow in spring with a profusion of wildflowers, creating a colorful and lively landscape, impressionist style, rich color palette of greens and multicolored flowers, wide-angle perspective'
          }
        ]
      },
      {
        text: t('AIService.ink'),
        data: 'ink paiting style',
        value: '2-2',
        children: [
          {
            value: '2-2-1',
            text: t('AIService.morningLight'),
            data: 'Morning light filtering through tree branches, casting golden hues on a winding path, impressionist style, warm color palette, wide-angle perspective'
          },
          {
            value: '2-2-2',
            text: t('AIService.reflectionsSunset'),
            data: 'Sunset reflections on a tranquil lake, shimmering waves like dancing specks of gold, romanticist style, warm color palette with oranges and golds, wide-angle perspective.'
          },
          {
            value: '2-2-3',
            text: t('AIService.mountainsDistant'),
            data: 'Distant mountains bathed in soft lavender hues by the morning sunrise, creating a mystical and ethereal landscape, symbolic style, pastel color palette, long-focus perspective'
          },
          {
            value: '2-2-4',
            text: t('AIService.nightStarry'),
            data: 'Starry night sky resembling countless diamonds on black velvet, expressive style, contrasting colors of white and silver stars against deep blue and black sky, wide-angle perspective'
          },
          {
            value: '2-2-4',
            text: t('AIService.forestAutumn'),
            data: 'Autumn forest vibrant with fiery maple leaves, impressionist style, rich color palette of reds, oranges, and yellows, wide-angle perspective capturing the grandeur of the scene'
          },
          {
            value: '2-2-5',
            text: t('AIService.wheatGolden'),
            data: 'Golden wheat field under a blue sky with white clouds, rippling like a golden ocean, realistic style, vibrant color palette, wide-angle perspective capturing the expanse'
          },
          {
            value: '2-2-6',
            text: t('AIService.glisteningDewdrops'),
            data: 'Dewdrops glistening on grass tips in the early morning, crystal clear and radiant, naturalist style, soft color palette with hints of morning light, macro lens perspective for detailed focus'
          },
          {
            value: '2-2-7',
            text: t('AIService.bambooEmerald'),
            data: 'Emerald bamboo forest with sunlight filtering through the leaves, creating a dappled, tranquil, and mysterious atmosphere, symbolic style, contrasting colors of green and golden light, wide-angle perspective'
          },
          {
            value: '2-2-8',
            text: t('AIService.wavesCalm'),
            data: 'Calm waves gently lapping against the sandy beach, creating a serene whooshing sound, realistic style, color palette of blues, yellows, and browns, wide-angle perspective capturing the tranquility'
          },
          {
            value: '2-2-10',
            text: t('AIService.spanningRainbow'),
            data: 'Rainbow spanning the sky after a rainstorm, bringing hope and vitality to the overcast sky, romanticist style, vibrant color palette of the rainbow against grey and white clouds, wide-angle perspective.'
          },
          {
            value: '2-2-11',
            text: t('AIService.purpleVibrant'),
            data: 'Vibrant purple lavender fields swaying in the breeze, releasing a gentle floral fragrance, impressionist style, rich color palette of purples and greens, wide-angle perspective capturing the serene beauty'
          },
          {
            value: '2-2-12',
            text: t('AIService.riceAutumn'),
            data: 'Autumn rice fields with heavy, golden rice ears bowing down, signaling the harvest season, realistic style, warm color palette of golds and greens, wide-angle perspective capturing the vastness of the fields'
          },
          {
            value: '2-2-13',
            text: t('AIService.valleyMisty'),
            data: 'Misty valley at dawn with clouds swirling around the mountains, creating a mystical and serene landscape, surreal style, soft color palette with whites, greys, and hints of morning sky, wide-angle perspective'
          },
          {
            value: '2-2-14',
            text: t('AIService.landscapeDesert'),
            data: 'Desert landscape under the setting sun, with golden sand dunes rolling majestically, creating a spectacular and mysterious atmosphere, expressionist style, contrasting colors of gold, red, and orange, wide-angle perspective'
          },
          {
            value: '2-2-15',
            text: t('AIService.meadowVibrant'),
            data: 'Vibrant meadow in spring with a profusion of wildflowers, creating a colorful and lively landscape, impressionist style, rich color palette of greens and multicolored flowers, wide-angle perspective'
          }
        ]
      },
      {
        text: t('AIService.anime'),
        data: 'anime style',
        value: '2-3',
        children: [
          {
            value: '2-3-1',
            text: t('AIService.morningLight'),
            data: 'Morning light filtering through tree branches, casting golden hues on a winding path, impressionist style, warm color palette, wide-angle perspective'
          },
          {
            value: '2-3-2',
            text: t('AIService.reflectionsSunset'),
            data: 'Sunset reflections on a tranquil lake, shimmering waves like dancing specks of gold, romanticist style, warm color palette with oranges and golds, wide-angle perspective.'
          },
          {
            value: '2-3-3',
            text: t('AIService.mountainsDistant'),
            data: 'Distant mountains bathed in soft lavender hues by the morning sunrise, creating a mystical and ethereal landscape, symbolic style, pastel color palette, long-focus perspective'
          },
          {
            value: '2-3-4',
            text: t('AIService.nightStarry'),
            data: 'Starry night sky resembling countless diamonds on black velvet, expressive style, contrasting colors of white and silver stars against deep blue and black sky, wide-angle perspective'
          },
          {
            value: '2-3-4',
            text: t('AIService.forestAutumn'),
            data: 'Autumn forest vibrant with fiery maple leaves, impressionist style, rich color palette of reds, oranges, and yellows, wide-angle perspective capturing the grandeur of the scene'
          },
          {
            value: '2-3-5',
            text: t('AIService.wheatGolden'),
            data: 'Golden wheat field under a blue sky with white clouds, rippling like a golden ocean, realistic style, vibrant color palette, wide-angle perspective capturing the expanse'
          },
          {
            value: '2-3-6',
            text: t('AIService.glisteningDewdrops'),
            data: 'Dewdrops glistening on grass tips in the early morning, crystal clear and radiant, naturalist style, soft color palette with hints of morning light, macro lens perspective for detailed focus'
          },
          {
            value: '2-3-7',
            text: t('AIService.bambooEmerald'),
            data: 'Emerald bamboo forest with sunlight filtering through the leaves, creating a dappled, tranquil, and mysterious atmosphere, symbolic style, contrasting colors of green and golden light, wide-angle perspective'
          },
          {
            value: '2-3-8',
            text: t('AIService.wavesCalm'),
            data: 'Calm waves gently lapping against the sandy beach, creating a serene whooshing sound, realistic style, color palette of blues, yellows, and browns, wide-angle perspective capturing the tranquility'
          },
          {
            value: '2-3-10',
            text: t('AIService.spanningRainbow'),
            data: 'Rainbow spanning the sky after a rainstorm, bringing hope and vitality to the overcast sky, romanticist style, vibrant color palette of the rainbow against grey and white clouds, wide-angle perspective.'
          },
          {
            value: '2-3-11',
            text: t('AIService.purpleVibrant'),
            data: 'Vibrant purple lavender fields swaying in the breeze, releasing a gentle floral fragrance, impressionist style, rich color palette of purples and greens, wide-angle perspective capturing the serene beauty'
          },
          {
            value: '2-3-12',
            text: t('AIService.riceAutumn'),
            data: 'Autumn rice fields with heavy, golden rice ears bowing down, signaling the harvest season, realistic style, warm color palette of golds and greens, wide-angle perspective capturing the vastness of the fields'
          },
          {
            value: '2-3-13',
            text: t('AIService.valleyMisty'),
            data: 'Misty valley at dawn with clouds swirling around the mountains, creating a mystical and serene landscape, surreal style, soft color palette with whites, greys, and hints of morning sky, wide-angle perspective'
          },
          {
            value: '2-3-14',
            text: t('AIService.landscapeDesert'),
            data: 'Desert landscape under the setting sun, with golden sand dunes rolling majestically, creating a spectacular and mysterious atmosphere, expressionist style, contrasting colors of gold, red, and orange, wide-angle perspective'
          },
          {
            value: '2-3-15',
            text: t('AIService.meadowVibrant'),
            data: 'Vibrant meadow in spring with a profusion of wildflowers, creating a colorful and lively landscape, impressionist style, rich color palette of greens and multicolored flowers, wide-angle perspective'
          }
        ]
      },
      {
        text: t('AIService.illustration'),
        data: 'flat illustration style',
        value: '2-4',
        children: [
          {
            value: '2-4-1',
            text: t('AIService.morningLight'),
            data: 'Morning light filtering through tree branches, casting golden hues on a winding path, impressionist style, warm color palette, wide-angle perspective'
          },
          {
            value: '2-4-2',
            text: t('AIService.reflectionsSunset'),
            data: 'Sunset reflections on a tranquil lake, shimmering waves like dancing specks of gold, romanticist style, warm color palette with oranges and golds, wide-angle perspective.'
          },
          {
            value: '2-4-3',
            text: t('AIService.mountainsDistant'),
            data: 'Distant mountains bathed in soft lavender hues by the morning sunrise, creating a mystical and ethereal landscape, symbolic style, pastel color palette, long-focus perspective'
          },
          {
            value: '2-4-4',
            text: t('AIService.nightStarry'),
            data: 'Starry night sky resembling countless diamonds on black velvet, expressive style, contrasting colors of white and silver stars against deep blue and black sky, wide-angle perspective'
          },
          {
            value: '2-4-4',
            text: t('AIService.forestAutumn'),
            data: 'Autumn forest vibrant with fiery maple leaves, impressionist style, rich color palette of reds, oranges, and yellows, wide-angle perspective capturing the grandeur of the scene'
          },
          {
            value: '2-4-5',
            text: t('AIService.wheatGolden'),
            data: 'Golden wheat field under a blue sky with white clouds, rippling like a golden ocean, realistic style, vibrant color palette, wide-angle perspective capturing the expanse'
          },
          {
            value: '2-4-6',
            text: t('AIService.glisteningDewdrops'),
            data: 'Dewdrops glistening on grass tips in the early morning, crystal clear and radiant, naturalist style, soft color palette with hints of morning light, macro lens perspective for detailed focus'
          },
          {
            value: '2-4-7',
            text: t('AIService.bambooEmerald'),
            data: 'Emerald bamboo forest with sunlight filtering through the leaves, creating a dappled, tranquil, and mysterious atmosphere, symbolic style, contrasting colors of green and golden light, wide-angle perspective'
          },
          {
            value: '2-4-8',
            text: t('AIService.wavesCalm'),
            data: 'Calm waves gently lapping against the sandy beach, creating a serene whooshing sound, realistic style, color palette of blues, yellows, and browns, wide-angle perspective capturing the tranquility'
          },
          {
            value: '2-4-10',
            text: t('AIService.spanningRainbow'),
            data: 'Rainbow spanning the sky after a rainstorm, bringing hope and vitality to the overcast sky, romanticist style, vibrant color palette of the rainbow against grey and white clouds, wide-angle perspective.'
          },
          {
            value: '2-4-11',
            text: t('AIService.purpleVibrant'),
            data: 'Vibrant purple lavender fields swaying in the breeze, releasing a gentle floral fragrance, impressionist style, rich color palette of purples and greens, wide-angle perspective capturing the serene beauty'
          },
          {
            value: '2-4-12',
            text: t('AIService.riceAutumn'),
            data: 'Autumn rice fields with heavy, golden rice ears bowing down, signaling the harvest season, realistic style, warm color palette of golds and greens, wide-angle perspective capturing the vastness of the fields'
          },
          {
            value: '2-4-13',
            text: t('AIService.valleyMisty'),
            data: 'Misty valley at dawn with clouds swirling around the mountains, creating a mystical and serene landscape, surreal style, soft color palette with whites, greys, and hints of morning sky, wide-angle perspective'
          },
          {
            value: '2-4-14',
            text: t('AIService.landscapeDesert'),
            data: 'Desert landscape under the setting sun, with golden sand dunes rolling majestically, creating a spectacular and mysterious atmosphere, expressionist style, contrasting colors of gold, red, and orange, wide-angle perspective'
          },
          {
            value: '2-4-15',
            text: t('AIService.meadowVibrant'),
            data: 'Vibrant meadow in spring with a profusion of wildflowers, creating a colorful and lively landscape, impressionist style, rich color palette of greens and multicolored flowers, wide-angle perspective'
          }
        ]
      }
    ]
  },
  {
    text: t('AIService.figure'),
    data: '',
    value: '3',
    children: [
      {
        text: t('AIService.woman'),
        data: 'woman',
        value: '3-1',
        children: [
          {
            text: t('AIService.pixarStyle'),
            data: 'pixar style',
            value: '3-1-1',
            children: [
              {
                value: '3-1-1-1',
                text: t('AIService.leather'),
                data: ' Leather, Jeans,smile,brown eyes,simple background,brown hair,blush,white background,black and white vest,Motorcycle,On a motorbike.'
              },
              {
                value: '3-1-1-2',
                text: t('AIService.princess'),
                data: 'princess,princess dress,smile,brown eyes,simple background,brown hair,looking at viewer,long sleeves,buttons,closed mouth,blush'
              },
              {
                value: '3-1-1-3',
                text: t('AIService.warrior'),
                data: 'Warrior, armour, smile,brown eyes,simple background,brown hair,looking at viewer,long sleeves,buttons,closed mouth,blush,standing'
              },
              {
                value: '3-1-1-4',
                text: t('AIService.sports'),
                data: 'smile,brown eyes,sportswear，outdoors,sports，motion'
              },
              {
                value: '3-1-1-5',
                text: t('AIService.chineseClothes'),
                data: 'chinese clothes,beautiful  dress,flower print,hair flower'
              }
            ]
          },
          {
            text: t('AIService.animeStyle'),
            data: 'anime style',
            value: '3-1-2',
            children: [
              {
                value: '3-1-2-1',
                text: t('AIService.leather'),
                data: ' Leather, Jeans,smile,brown eyes,simple background,brown hair,blush,white background,black and white vest,Motorcycle,On a motorbike.'
              },
              {
                value: '3-1-2-2',
                text: t('AIService.princess'),
                data: 'princess,princess dress,smile,brown eyes,simple background,brown hair,looking at viewer,long sleeves,buttons,closed mouth,blush'
              },
              {
                value: '3-1-2-3',
                text: t('AIService.warrior'),
                data: 'Warrior, armour, smile,brown eyes,simple background,brown hair,looking at viewer,long sleeves,buttons,closed mouth,blush,standing'
              },
              {
                value: '3-1-2-4',
                text: t('AIService.sports'),
                data: 'smile,brown eyes,sportswear，outdoors,sports，motion'
              },
              {
                value: '3-1-2-5',
                text: t('AIService.chineseClothes'),
                data: 'chinese clothes,beautiful  dress,flower print,hair flower'
              }
            ]
          },
          {
            text: t('AIService.illustration'),
            data: 'flat illustration style',
            value: '3-1-3',
            children: [
              {
                value: '3-1-3-1',
                text: t('AIService.leather'),
                data: ' Leather, Jeans,smile,brown eyes,simple background,brown hair,blush,white background,black and white vest,Motorcycle,On a motorbike.'
              },
              {
                value: '3-1-3-2',
                text: t('AIService.princess'),
                data: 'princess,princess dress,smile,brown eyes,simple background,brown hair,looking at viewer,long sleeves,buttons,closed mouth,blush'
              },
              {
                value: '3-1-3-3',
                text: t('AIService.warrior'),
                data: 'Warrior, armour, smile,brown eyes,simple background,brown hair,looking at viewer,long sleeves,buttons,closed mouth,blush,standing'
              },
              {
                value: '3-1-3-4',
                text: t('AIService.sports'),
                data: 'smile,brown eyes,sportswear，outdoors,sports，motion'
              },
              {
                value: '3-1-3-5',
                text: t('AIService.chineseClothes'),
                data: 'chinese clothes,beautiful  dress,flower print,hair flower'
              }
            ]
          }
        ]
      },
      {
        text: t('AIService.man'),
        data: 'man',
        value: '3-2',
        children: [
          {
            text: t('AIService.pixarStyle'),
            data: 'pixar style',
            value: '3-2-1',
            children: [
              {
                value: '3-2-1-1',
                text: t('AIService.glasses'),
                data: 'male focus, glasses,  smile, brown eyes, brown background, black hair, sweater, looking at viewer,simple background, upper body, turtleneck, white sweater'
              },
              {
                value: '3-2-1-2',
                text: t('AIService.leather'),
                data: 'Motorcycle, handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              },
              {
                value: '3-2-1-3',
                text: t('AIService.traditional'),
                data: 'traditional Chinese garments Handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              },
              {
                value: '3-2-1-4',
                text: t('AIService.warrior'),
                data: 'Wuxia, handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              }
            ]
          },
          {
            text: t('AIService.animeStyle'),
            data: 'anime style',
            value: '3-2-2',
            children: [
              {
                value: '3-2-2-1',
                text: t('AIService.glasses'),
                data: 'male focus, glasses,  smile, brown eyes, brown background, black hair, sweater, looking at viewer,simple background, upper body, turtleneck, white sweater'
              },
              {
                value: '3-2-2-2',
                text: t('AIService.leather'),
                data: 'Motorcycle, handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              },
              {
                value: '3-2-2-3',
                text: t('AIService.traditional'),
                data: 'traditional Chinese garments Handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              },
              {
                value: '3-2-2-4',
                text: t('AIService.warrior'),
                data: 'Wuxia, handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              }
            ]
          },
          {
            text: t('AIService.illustration'),
            data: 'flat illustration style',
            value: '3-2-3',
            children: [
              {
                value: '3-2-3-1',
                text: t('AIService.glasses'),
                data: 'male focus, glasses,  smile, brown eyes, brown background, black hair, sweater, looking at viewer,simple background, upper body, turtleneck, white sweater'
              },
              {
                value: '3-2-3-2',
                text: t('AIService.leather'),
                data: 'Motorcycle, handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              },
              {
                value: '3-2-3-3',
                text: t('AIService.traditional'),
                data: 'traditional Chinese garments Handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              },
              {
                value: '3-2-3-4',
                text: t('AIService.warrior'),
                data: 'Wuxia, handsome guy, delicate facial features, black hair, high resolution, atmosphere, pure desire, exquisite face'
              }
            ]
          }
        ]
      }
    ]
  }
]
