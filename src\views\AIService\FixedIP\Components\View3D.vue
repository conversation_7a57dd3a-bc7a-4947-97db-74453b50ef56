<template>
  <Dialog
    v-model="isShow"
    min-width="650px"
    :maxHeight="'auto'"
    :title="t('AIService.view3D')"
    top="50px"
    center
    @close="cancel"
  >
    <model-gltf
      v-if="isShowModel"
      class="view-3d"
      :src="url3D"
      :backgroundColor="0xf6f6f6"
      :rotation="rotation"
      @load="onLoad"
      @progress="onSuccess"
      v-loading="loading"
    >
      <template #progress-bar>
        <el-progress :stroke-width="3" :percentage="progress" :show-text="false" />
      </template>
    </model-gltf>
    <!-- 操作区 -->
    <div class="flex justify-end mt-20px">
      <el-button type="primary" size="large" @click="cancel">{{ t('common.ok') }}</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts" setup>
import { ModelGltf } from 'vue-3d-model'
const { t } = useI18n()
let props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  src: {
    type: String,
    default: ''
  }
})

// 关闭弹窗
const emits = defineEmits(['update:modelValue'])
const cancel = () => {
  emits('update:modelValue', false)
}

let url3D = computed(() => {
  return props.src
})
let loading = ref(true)

// 弹窗
const isShow = computed(() => {
  return props.modelValue
})

const rotation = reactive({
  x: 0,
  y: 0,
  z: 0
})

const onLoad = () => {
  // rotate() // 自动旋转
}
const rotate = () => {
  requestAnimationFrame(rotate)
  rotation.z += 0.01
}

let progress = ref(0)
let isShowModel = ref(false)

const onSuccess = (e) => {
  progress.value = Number(((e.loaded / e.total) * 100).toFixed(0))
  loading.value = false
}

onMounted(async () => {
  await nextTick()
  isShowModel.value = true
})
</script>

<style lang="less" scoped>
.btn {
  width: 122px;
}
.view-3d {
  width: 100%;
  height: 500px !important;
  border-radius: 10px;
  position: relative !important;
  z-index: 999 !important;
}
</style>
