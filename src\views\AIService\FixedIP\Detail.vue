<template>
  <Dialog
    v-model="isShow"
    min-width="650px"
    :maxHeight="'auto'"
    :title="t('makeCard.common.detail')"
    top="50px"
    center
    @close="cancel"
  >
    <div class="ip-detail">
      <el-form ref="queryRef" :model="props.detailInfo" :label-width="ifEn ? '180px' : '90px'">
        <el-form-item :label="t('tableDemo.title')">
          {{ props.detailInfo.commonKnowledgeTitle }}
        </el-form-item>
        <el-form-item :label="t('productsShow.diyCardProduct.image')">
          <el-image
            :src="resultImgShow(props.detailInfo?.commonKnowledgeInfoSmallcoverurl)"
            :preview-src-list="[resultImgShow(props.detailInfo?.commonKnowledgeInfoSmallcoverurl)]"
            fit="contain"
            class="ip-img"
            preview-teleported
          />
        </el-form-item>
        <el-form-item :label="t('AIService.chooseDesign')">
          <el-select
            v-model="draftValue"
            :placeholder="t('AIService.chooseDesignPlaceholder')"
            filterable
            clearable
            style="width: 100%"
            @change="filterHandler"
          >
            <el-option
              v-for="item in tableData"
              :key="String(item.value)"
              :label="item.text"
              :value="String(item.value)"
            />
          </el-select>
          <div class="mt-10px" v-if="draftValue">
            <el-image
              :src="resultImgShow(draftInfo?.commonSchemeInfoTechniquephoto)"
              :preview-src-list="[resultImgShow(draftInfo?.commonSchemeInfoTechniquephoto)]"
              fit="contain"
              class="ip-img"
              preview-teleported
            />
            <el-button
              type="primary"
              class="ml-10px"
              v-if="draftInfo?.commonSchemeInfoThreedshow"
              @click="handleOpen(draftInfo?.commonSchemeInfoThreedshow)"
              >{{ t('AIService.view3D') }}</el-button
            >
            <div>{{ draftInfo?.commonSchemeInfoRemark }}</div>
          </div>
        </el-form-item>
        <el-form-item :label="t('AIService.ipInfo')">
          <Editor v-model="ipInfo" :readonly="true" :height="320" class="width-100" />
        </el-form-item>
        <div class="operate">
          <el-button type="primary" @click="cancel">{{ t('common.close') }}</el-button>
        </div>
      </el-form>
    </div>
  </Dialog>

  <View3D v-if="view3DDialog" v-model="view3DDialog" :src="src3D" />
</template>

<script setup lang="ts">
import View3D from './Components/View3D.vue'
import * as schemeInfoApi from '@/api/makeCardService/schemeInfo/index'
import envController from '@/controller/envController'

const { t, ifEn } = useI18n()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  detailInfo: {
    type: Object,
    default: () => {}
  }
})

// 弹窗
const isShow = computed(() => {
  return props.modelValue
})

// IP介绍富文本
const ipInfo = computed(() => {
  return props.detailInfo?.commonKnowledgeInfoContent
})

// 图片预览
const resultImgShow = (url) => {
  return envController.getOssUrl() + '/' + url
}

// 方案数据
const tableData = ref()
// 选择的方案
const draftValue = ref()
// 选择的方案数据
const draftInfo = ref()

// 获取数据
const getList = async () => {
  let getListQueryParams = {
    pageNo: 1,
    pageSize: 9999,
    conds: [
      {
        groupOp: ' and ',
        rules: [
          {
            field: 'commonSchemeInfoType',
            op: 'eq',
            data: props.detailInfo?.commonSchemeInfoType || 0 // 0 IP 1 AIGC
          }
        ],
        conds: []
      }
    ]
  }
  if (props.detailInfo?.commonKnowledgeInfoId) {
    getListQueryParams.conds[0].rules.push({
      field: 'commonSchemeInfoKnowledgeid',
      op: 'eq',
      data: props.detailInfo?.commonKnowledgeInfoId
    })
  }
  try {
    let { data } = await schemeInfoApi.getListApi(getListQueryParams)
    if (data && data?.records?.length > 0) {
      data?.records?.forEach((item) => {
        item.value = item.commonSchemeInfoId
        item.text = item.commonSchemeInfoTitle
      })
    } else {
      data = {
        records: []
      }
    }
    tableData.value = [...data?.records, { value: '', text: t('AIService.noDesign') }] || []
    draftValue.value = tableData.value[0]?.value
    draftInfo.value = tableData.value[0] || {}
  } catch (err) {
    console.error(err)
  }
}

// 切换方案
const filterHandler = (selectValue) => {
  draftInfo.value = tableData.value.find((item: any) => {
    return item?.commonSchemeInfoId == selectValue
  })
}

/** 查看3D按钮操作 */
const view3DDialog = ref(false)
const src3D = ref()
async function handleOpen(row) {
  // src3D.value = row
  src3D.value = envController.getOssUrl() + row
  view3DDialog.value = true
}

// 关闭弹窗
const emits = defineEmits(['update:modelValue'])
const cancel = () => {
  emits('update:modelValue', false)
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="less">
.ip-detail {
  width: 100%;
  .operate {
    display: flex;
    justify-content: flex-end;
  }
  .ip-img {
    width: 200px;
    height: 200px;
  }
}
</style>
