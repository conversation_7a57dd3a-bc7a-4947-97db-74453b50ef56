<!-- 智能服务-设计优选 -->
<template>
  <ContentWrap ifTable>
    <template #search>
      <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="90px">
        <el-form-item
          :label="t('cardProductBusiness.orderSearch.customerName')"
          :label-width="ifEn ? '150px' : '90px'"
          prop="commonKnowledgeInfoMerchantname"
        >
          <el-select
            v-model="queryParams.commonKnowledgeInfoMerchantname"
            clearable
            filterable
            style="width: 300px"
          >
            <el-option
              v-for="(item, key) in customerNameList"
              :label="item"
              :value="item"
              :key="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('AIService.sort')" prop="commonKnowledgeInfoType">
          <el-select
            v-model="queryParams.commonKnowledgeInfoType"
            clearable
            filterable
            style="width: 300px"
          >
            <el-option
              v-for="item in typeList"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('AIService.ipName')" prop="commonKnowledgeTitle">
          <el-input
            class="search-input"
            v-model="queryParams.commonKnowledgeTitle"
            :placeholder="t('AIService.ipNamePlaceholder')"
            style="width: 300px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-track:click.btn @click="handleQuery">{{
            t('common.query')
          }}</el-button>
          <el-button type="warning" @click="resetQuery">{{ t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </template>
    <el-table v-loading="loading" v-horizontal-scroll :data="list" max-height="600">
      <el-table-column
        :label="t('messageCenter.list.sortNum')"
        align="center"
        :width="ifEn ? '120' : '55'"
      >
        <template #default="scope">
          {{ setIndex(scope.$index) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="t('tableDemo.title')"
        align="center"
        prop="commonKnowledgeTitle"
        min-width="100"
      />
      <el-table-column
        :label="t('productsShow.batchCardProduct.customerName')"
        align="center"
        prop="commonKnowledgeInfoMerchantname"
        min-width="100"
      />
      <el-table-column
        :label="t('AIService.sort')"
        align="center"
        prop="commonKnowledgeInfoType"
        min-width="100"
      >
        <template #default="scope">
          <el-tag type="primary" v-if="scope.row.commonKnowledgeInfoType === 0">
            {{ t('AIService.cardProductIP') }}
          </el-tag>
          <el-tag type="success" v-if="scope.row.commonKnowledgeInfoType === 1">
            {{ t('AIService.designIP') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('cardProductService.batchCardProduct.merchantCardList.status')"
        align="center"
        prop="commonKnowledgeInfoStatus"
        min-width="100"
      >
        <template #default="scope">
          <el-tag class="mx-1" type="primary" v-if="scope.row.commonKnowledgeInfoStatus === 0">
            {{ t('makeCard.detail.toBeListed') }}
          </el-tag>
          <el-tag class="mx-1" type="success" v-if="scope.row.commonKnowledgeInfoStatus === 1">
            {{ t('makeCard.detail.listed') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('productsShow.diyCardProduct.image')"
        align="center"
        prop="commonKnowledgeInfoSmallcoverurl"
        min-width="200"
      >
        <template #default="scope">
          <el-image
            :src="resultImgShow(scope.row?.commonKnowledgeInfoSmallcoverurl)"
            :preview-src-list="[resultImgShow(scope.row?.commonKnowledgeInfoSmallcoverurl)]"
            fit="contain"
            class="ip-img"
            preview-teleported
          />
        </template>
      </el-table-column>
      <el-table-column :label="t('common.operate')" fixed="right" align="center" width="100">
        <template #default="scope">
          <el-button type="primary" link v-track:click.btn @click="handleOpen(scope.row)">{{
            t('common.see')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #pagination>
      <Pagination
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :total="total"
      />
    </template>
  </ContentWrap>
  <detail v-if="detailDialog" v-model="detailDialog" :detailInfo="detailDialogInfo" />
</template>

<script setup lang="ts">
defineOptions({
  name: 'FixedIP'
})

import detail from './Detail.vue'
import * as knowledgeInfoApi from '@/api/makeCardService/knowledgeInfo/index'
import { fastSearchApi } from '@/api/order'
const { t, ifEn } = useI18n()

const queryRef = ref()
const queryParams = ref({
  commonKnowledgeTitle: '',
  commonKnowledgeInfoType: undefined,
  pageNo: 1,
  pageSize: 10,
  commonKnowledgeInfoMerchantname: ''
})

// 详情弹窗
const detailDialog = ref(false)
const detailDialogInfo = ref()

// 列表加载状态
const loading = ref(false)
// 列表数据
const list = ref([])
// 列表总数
const total = ref(0)

// 图片预览
import envController from '@/controller/envController'

const resultImgShow = (url) => {
  return envController.getOssUrl() + '/' + url
}

// 客户列表
const customerNameList = ref()
const getCustomerName = async () => {
  const res = await fastSearchApi()
  customerNameList.value = res
}

// IP类型
const typeList = [
  {
    label: t('AIService.cardProductIP'),
    value: 0
  },
  {
    label: t('AIService.designIP'),
    value: 1
  }
]

/** 查询按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryRef.value.resetFields()
  handleQuery()
}

/** 查询列表 */
async function getList() {
  try {
    loading.value = true

    let getListQueryParams = {
      pageNo: queryParams.value.pageNo,
      pageSize: queryParams.value.pageSize,
      conds: [
        {
          groupOp: ' and ',
          rules: [
            {
              field: 'commonKnowledgeTitle',
              op: 'cn',
              data: queryParams.value.commonKnowledgeTitle
            }
          ],
          conds: []
        }
      ]
    }
    if (queryParams.value.commonKnowledgeInfoType !== undefined) {
      getListQueryParams.conds[0].rules.push({
        field: 'commonKnowledgeInfoType',
        op: 'eq',
        data: queryParams.value.commonKnowledgeInfoType // 0 卡产品 1 优选
      })
    }
    if (queryParams.value.commonKnowledgeInfoMerchantname) {
      getListQueryParams.conds[0].rules.push({
        field: 'commonKnowledgeInfoMerchantname',
        op: 'eq',
        data: queryParams.value.commonKnowledgeInfoMerchantname
      })
    }
    const { data } = await knowledgeInfoApi.getListApi(getListQueryParams)
    total.value = data?.amount || 0
    list.value = data?.records
  } finally {
    loading.value = false
  }
}

/** 生成序列号 */
function setIndex(index) {
  let num = index + 1 + (queryParams.value.pageNo - 1) * queryParams.value.pageSize
  return num > 9 ? num : `0${num}`
}

/** 查看按钮操作 */
async function handleOpen(row) {
  detailDialogInfo.value = row
  detailDialog.value = true
}

onMounted(() => {
  getList()
  getCustomerName()
})
onActivated(() => {
  if (!loading.value) {
    getList()
  }
})
</script>

<style lang="less">
.ip-img {
  width: 300px;
  height: 100px;
}
</style>
