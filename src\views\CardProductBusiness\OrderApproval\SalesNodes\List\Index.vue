<!-- 卡产品服务/订单签审 -->
<template>
  <ContentWrap ifTable>
    <template #search>
      <Search ref="searchRef" @search="search" @reset="reset" />
    </template>
    <ElTable v-loading="loadding" v-horizontal-scroll :data="tableData" style="width: 100%">
      <ElTableColumn :label="t('tableDemo.index')" type="index" width="100" :index="indexMethod" />
      <ElTableColumn
        prop="customerName"
        width="200"
        :label="t('cardProductBusiness.orderApproval.customerName')"
      />
      <ElTableColumn
        prop="orderTypeKey"
        width="200"
        :label="t('cardProductBusiness.orderApproval.orderType')"
      >
        <template #default="{ row }">
          <span>{{ getDictLabel('order_type', row.orderTypeKey) }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="taskCode"
        :label="t('cardProductBusiness.orderApproval.taskCode')"
        width="200"
      >
        <template #default="{ row }">
          <a @click="() => showFlow(row)">{{ row.taskCode }}</a>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="productName"
        :label="t('cardProductBusiness.orderApproval.productName')"
        width="200"
      />
      <ElTableColumn
        prop="processorName"
        :label="t('cardProductBusiness.orderApproval.salesman')"
        width="200"
      />
      <ElTableColumn
        prop="quantity"
        :label="t('cardProductBusiness.orderApproval.orderNum')"
        width="200"
      />
      <ElTableColumn
        prop="deliveryAt"
        :label="t('cardProductBusiness.orderApproval.deliveryTime')"
        width="200"
      >
        <template #default="{ row }">
          {{ row.deliveryAt ? dayjs(row.deliveryAt).format('YYYY-MM-DD') : '' }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="deliveryType"
        :label="t('cardProductBusiness.orderApproval.deliveryMode')"
        width="200"
      >
        <template #default="{ row }"> {{ deliveryTypeDisplay(row.deliveryType) }} </template>
      </ElTableColumn>
      <ElTableColumn
        prop="saleReviewStatus"
        :label="t('cardProductBusiness.orderApproval.auditStatus')"
        width="200"
      >
        <template #default="{ row }">
          <span>{{
            row.saleReviewStatus
              ? t('cardProductBusiness.orderApproval.saleReview')
              : t('cardProductBusiness.orderApproval.unSaleReview')
          }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="orderCreateName"
        :label="t('cardProductBusiness.orderApproval.orderCreator')"
        width="200"
      />
      <ElTableColumn
        prop="orderCreateDate"
        :label="t('cardProductBusiness.orderApproval.orderCreationTime')"
        width="200"
      />
      <ElTableColumn prop="P1" :label="t('common.operate')" fixed="right" width="220">
        <template #default="{ row }">
          <ElButton
            type="primary"
            link
            size="small"
            v-track:click.btn
            @click="() => showSign(row)"
            v-if="!row?.saleReviewStatus"
            >{{ t('cardProductBusiness.orderApproval.audit') }}</ElButton
          >
          <ElButton
            type="primary"
            link
            size="small"
            v-track:click.btn
            @click="() => showSign(row)"
            v-if="row?.saleReviewStatus"
            >{{ t('cardProductBusiness.orderApproval.viewDetail') }}</ElButton
          >
          <ElButton
            type="primary"
            link
            size="small"
            v-track:click.btn
            @click="() => showFlow(row)"
            >{{ t('cardProductBusiness.orderApproval.processNode') }}</ElButton
          >
        </template>
      </ElTableColumn>
    </ElTable>
    <template #pagination>
      <Pagination
        v-model:page="pagination.pageNo"
        v-model:limit="pagination.pageSize"
        :total="total"
        @pagination="getData"
      />
    </template>
  </ContentWrap>
  <FlowModal ref="flowModalRef" />
  <SignModal ref="signModalRef" :oprateOpts="oprateOpts" @success="search" />
</template>
<script setup lang="ts">
defineOptions({
  name: 'OrderApproval'
})

import { getDictLabel } from '@/utils/dict'
import dayjs from 'dayjs'
import { first, filter } from 'lodash-es'
import Search from './components/Search.vue'
import FlowModal from './components/FlowModal.vue'
import SignModal from './components/SignModal.vue'
import { getStrDictOptions } from '@/utils/dict'
import {
  saleReviewTaskList,
  resultListVO,
  saleReviewTaskVO,
  saleReviewOptionResult,
  reviewTaskListReviewTaskResult
} from '@/api/orderHandle/pdmOrder'
import { useRouter } from 'vue-router'
const { t } = useI18n()

const pagination = reactive({
  pageNo: 1,
  pageSize: 10
})

const { push } = useRouter()

const total = ref(0)

const flowModalRef = ref()

const signModalRef = ref()

const tableData: Ref<saleReviewTaskVO[]> = ref([])

const searchRef = ref()

const search = () => {
  pagination.pageNo = 1
  getData()
}

const reset = () => {
  pagination.pageNo = 1
  getData()
}

const showFlow = async (row?: any) => {
  flowModalRef.value.open(row)
}

const showSign = (row?: saleReviewTaskVO) => {
  signModalRef.value.open(row)
}

const loadding = ref(false)

const getData = async () => {
  try {
    loadding.value = true
    const res: resultListVO<saleReviewTaskVO> = await saleReviewTaskList(
      {
        processorId: 298,
        // saleReviewStatus: false
        ...searchRef.value.queryData
      },
      pagination
    )
    console.log('res', res)
    tableData.value = res.list
    // tableData.value = [{ taskCode: '666', reviewTaskId: '1738123732523835394' }]
    total.value = res?.total || 0
  } catch (e) {
    console.error('e', e)
  } finally {
    loadding.value = false
  }

  // const res = await batchPageApi(searchRef.value.queryData, pagination)
  // tableData.value = res?.list
  // total.value = res?.total || 0
}

const oprateOpts = ref([])

const getOprateSaleNode = async () => {
  oprateOpts.value = await saleReviewOptionResult()
}

// 设置序号
const indexMethod = (index: number): number => {
  return (pagination.pageNo - 1) * pagination.pageSize + index + 1
}

onMounted(() => {
  getData()
  getOprateSaleNode()
})

onActivated(() => {
  if (!loadding.value) {
    getData()
  }
})

// 交付方式
const mailModeOptions = computed(() => {
  return getStrDictOptions('mail_mode')
})
const deliveryTypeDisplay = (deliveryType) => {
  try {
    const item = first(filter(mailModeOptions.value, (item) => item.value === deliveryType))
    return item ? item.label : deliveryType
  } catch (ex) {
    console.error('交付方式异常：', ex)
    return deliveryType
  }
}
</script>

<style scoped lang="less">
a {
  color: blue;
  cursor: pointer;
}
</style>
