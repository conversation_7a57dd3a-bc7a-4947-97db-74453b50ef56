<template>
  <div class="flow-modal">
    <ElDialog v-model="show" :title="t('cardProductBusiness.orderApproval.orderNodeStatus')">
      <div v-loading="loading">
        <p>
          <span class="title">{{ t('cardProductBusiness.orderApproval.customerName') }}: </span>
          {{ rowData?.customerName }}
        </p>
        <p>
          <span class="title">{{ t('cardProductBusiness.orderApproval.productName') }}: </span>
          {{ rowData?.productName }}
        </p>
        <div class="time-line mt-16px">
          <ElTimeline>
            <ElTimelineItem v-for="item in list" :key="item.id">
              <div>
                <p>
                  <span class="title">{{ t('cardProductBusiness.orderApproval.nodeName') }}：</span>
                  {{ getNodeName(item?.node) }}
                </p>
                <p>
                  <span class="title">{{ t('cardProductBusiness.orderApproval.handler') }}：</span>
                  {{ item?.createName }}
                </p>
                <p>
                  <span class="title"
                    >{{ t('cardProductBusiness.orderApproval.handlerTime') }}：</span
                  >
                  {{ item?.createDate }}
                </p>
              </div>
            </ElTimelineItem>
          </ElTimeline>
        </div>
      </div>

      <template #footer>
        <el-button @click="show = false">{{ t('common.close') }}</el-button>
      </template>
    </ElDialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'FlowModal'
})

import { reviewTaskListReviewTaskResult } from '@/api/orderHandle/pdmOrder'
import { useDictStoreWithOut } from '@/store/modules/dict'
const { t } = useI18n()
const dictStore = useDictStoreWithOut()

const show = ref(false)
const loading = ref(false)
const rowData = ref()
const list = ref([])
const open = async (row) => {
  try {
    rowData.value = row
    show.value = true
    loading.value = true

    const res = await reviewTaskListReviewTaskResult({
      reviewTaskId: row?.reviewTaskId
    })
    list.value = res
  } finally {
    loading.value = false
  }
}

const getNodeName = (value) => {
  const arr = dictStore.getDictByType('review_node')?.find((item) => {
    return item.value === value
  })
  return arr?.label || '---'
}

defineExpose({
  open
})
</script>
<style scoped lang="less">
.flow-modal {
  .title {
    font-weight: bold;
  }
}
</style>
