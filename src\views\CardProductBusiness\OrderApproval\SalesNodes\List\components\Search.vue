<template>
  <ElForm label-position="right" :model="queryData" ref="formRef">
    <ElRow :gutter="20">
      <ElCol :span="6">
        <ElFormItem
          :label="t('cardProductBusiness.orderApproval.customerName')"
          prop="customerName"
        >
          <ElInput
            v-model="queryData.customerName"
            :placeholder="
              t('cardProductBusiness.orderApproval.placeholderTextTip', {
                placeholder: t('cardProductBusiness.orderApproval.customerName').toLowerCase()
              })
            "
            maxlength="40"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="6">
        <ElFormItem :label="t('cardProductBusiness.orderApproval.productName')" prop="productName">
          <ElInput
            v-model="queryData.productName"
            :placeholder="
              t('cardProductBusiness.orderApproval.placeholderTextTip', {
                placeholder: t('cardProductBusiness.orderApproval.productName').toLowerCase()
              })
            "
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="6">
        <ElFormItem :label="t('cardProductBusiness.orderApproval.taskCode')" prop="taskCode">
          <ElInput
            v-model="queryData.taskCode"
            :placeholder="
              t('cardProductBusiness.orderApproval.placeholderTextTip', {
                placeholder: t('cardProductBusiness.orderApproval.taskCode').toLowerCase()
              })
            "
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol v-show="showMore" :span="6">
        <ElFormItem :label="t('cardProductBusiness.orderApproval.salesman')" prop="processorName">
          <ElInput
            v-model="queryData.processorName"
            :placeholder="
              t('cardProductBusiness.orderApproval.placeholderTextTip', {
                placeholder: t('cardProductBusiness.orderApproval.salesman').toLowerCase()
              })
            "
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol v-show="showMore" :span="6">
        <ElFormItem :label="t('cardProductBusiness.orderApproval.orderType')" prop="orderType">
          <ElSelect
            v-model="queryData.orderType"
            :placeholder="
              t('cardProductBusiness.orderApproval.placeholderSelectTip', {
                placeholder: t('cardProductBusiness.orderApproval.orderType').toLowerCase()
              })
            "
            clearable
            style="width: 100%; min-width: 100%"
          >
            <ElOption
              v-for="item in getDictOptions('order_type')"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol v-show="showMore" :span="6">
        <ElFormItem
          :label="t('cardProductBusiness.orderApproval.auditStatus')"
          prop="saleReviewStatus"
        >
          <ElSelect
            v-model="queryData.saleReviewStatus"
            :placeholder="
              t('cardProductBusiness.orderApproval.placeholderSelectTip', {
                placeholder: t('cardProductBusiness.orderApproval.auditStatus').toLowerCase()
              })
            "
            style="width: 100%; min-width: 100%"
          >
            <ElOption
              v-for="item in reviewStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="6">
        <el-row>
          <ElButton type="primary" v-track:click.btn @click="search">{{
            t('common.query')
          }}</ElButton>
          <ElButton type="warning" @click="reset()">{{ t('common.reset') }}</ElButton>

          <div class="linkmar">
            <ElLink type="primary" :underline="false" @click="showMore = !showMore">
              {{
                !showMore
                  ? t('cardProductBusiness.orderApproval.unfold')
                  : t('cardProductBusiness.orderApproval.packUp')
              }}
              <ElIcon>
                <ArrowUpBold v-show="showMore" />
                <ArrowDownBold v-show="!showMore" />
              </ElIcon>
            </ElLink>
          </div>
        </el-row>
      </ElCol>
    </ElRow>
  </ElForm>
</template>
<script setup lang="ts">
import { ArrowUpBold, ArrowDownBold } from '@element-plus/icons-vue'
import { getDictOptions } from '@/utils/dict'

const { t } = useI18n()

interface IqueryData {
  customerName: string
  productName: string
  taskCode: string
  processorName: string
  orderType: string
  saleReviewStatus: string | number
}

const formRef = ref()

const emits = defineEmits(['search', 'reset'])

const queryData: Ref<IqueryData> = ref({
  customerName: '',
  productName: '',
  taskCode: '',
  processorName: '',
  orderType: '',
  saleReviewStatus: false
})

const originQueryData: Ref<IqueryData> = ref({
  customerName: '',
  productName: '',
  taskCode: '',
  processorName: '',
  orderType: '',
  saleReviewStatus: false
})

const reviewStatusOptions = ref([
  {
    label: t('cardProductBusiness.orderApproval.unaudited'),
    value: false
  },
  {
    label: t('cardProductBusiness.orderApproval.audited'),
    value: true
  }
])

const showMore = ref(true)

const reset = () => {
  formRef.value.resetFields() // 初始化校验
  queryData.value = originQueryData.value
  emits('search')
}
const search = () => {
  console.log(666)
  emits('search')
}

defineExpose({
  queryData
})
</script>
<style lang="less" scoped>
.linkmar {
  display: flex;
  align-items: center;
  margin-left: 20px;
}
</style>
