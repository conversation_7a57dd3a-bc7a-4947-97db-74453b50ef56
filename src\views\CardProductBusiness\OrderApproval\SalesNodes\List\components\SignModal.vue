<template>
  <div class="sign-modal">
    <ElDialog
      v-model="show"
      :title="t('cardProductBusiness.orderApproval.orderAudit')"
      :style="{ width: '1200px' }"
      :lock-scroll="false"
      @close="close"
    >
      <div class="sign-content">
        <ElDivider content-position="left">{{
          t('cardProductBusiness.orderApproval.orderInfo')
        }}</ElDivider>
        <ElCard>
          <ElDescriptions border :column="3" size="small">
            <ElDescriptionsItem :span="2">
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.customerName')
                }}</div></template
              >{{ orderInfo?.customerName }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.orderType')
                }}</div></template
              >{{ orderInfo?.orderTypeName }}
            </ElDescriptionsItem>
            <ElDescriptionsItem :span="2">
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.productName')
                }}</div></template
              >{{ orderInfo?.productName }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.taskStatus')
                }}</div></template
              >{{ orderInfo?.taskStatusName }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.orderCode')
                }}</div></template
              >{{ orderInfo?.orderCode }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.taskCode')
                }}</div></template
              >{{ orderInfo?.taskCode }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.orderNum')
                }}</div></template
              >{{ orderInfo?.quantity }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.salesman')
                }}</div></template
              >{{ orderInfo?.processorName }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.orderCreator')
                }}</div></template
              >{{ orderInfo?.orderCreateName }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.orderCreationTime')
                }}</div></template
              >{{ orderInfo?.orderCreateDate }}
            </ElDescriptionsItem>

            <!-- <ElDescriptionsItem>
              <template #label><div class="cell-item">接收时间</div></template
              >2023-06-09 00:00:00
            </ElDescriptionsItem> -->
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.deliveryMode')
                }}</div></template
              >{{ deliveryTypeDisplay(orderInfo?.deliveryType) }}
            </ElDescriptionsItem>
            <ElDescriptionsItem>
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.deliveryTime')
                }}</div></template
              >{{ orderInfo?.deliveryAt ? dayjs(orderInfo?.deliveryAt).format('YYYY-MM-DD') : '' }}
            </ElDescriptionsItem>
            <ElDescriptionsItem :span="3">
              <template #label
                ><div class="cell-item">{{
                  t('cardProductBusiness.orderApproval.orderProductRemark')
                }}</div></template
              >{{ orderInfo?.orderProductRemark }}
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>
        <ElDivider content-position="left">{{
          t('cardProductBusiness.orderApproval.cardStyleInfo')
        }}</ElDivider>
        <ElForm label-width="70px" :model="cardInfoList" ref="formRef">
          <ElCard v-for="(item, index) in cardInfoList" :key="item.id + index">
            <ElDescriptions border :column="3" size="small">
              <ElDescriptionsItem :span="3">
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.cardStyleName')
                  }}</div></template
                >
                {{ item?.cardName }}
                <ElTag class="ml-8px" v-if="item?.primaryFlag" effect="dark">{{
                  t('cardProductBusiness.orderApproval.mainCardNum')
                }}</ElTag>
              </ElDescriptionsItem>
              <ElDescriptionsItem>
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.cardStyleCode')
                  }}</div></template
                >
                {{ item?.cardCode }}
              </ElDescriptionsItem>
              <ElDescriptionsItem>
                <template #label
                  ><div class="cell-item"
                    >{{ t('cardProductBusiness.orderApproval.cardStyleCodeAll') }}
                  </div></template
                >
                {{ item?.cardFullCode }}
              </ElDescriptionsItem>
              <ElDescriptionsItem>
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.latestK3Code')
                  }}</div></template
                >
                {{ item?.latestK3Code }}
              </ElDescriptionsItem>

              <ElDescriptionsItem>
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.batchTime')
                  }}</div></template
                >
                {{ item?.batchTime }}
              </ElDescriptionsItem>
              <ElDescriptionsItem>
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.cardPlanNum')
                  }}</div></template
                >
                {{ item?.cardPlanNum }}
              </ElDescriptionsItem>
              <ElDescriptionsItem>
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.enCapType')
                  }}</div></template
                >
                {{ item?.encapType }}
              </ElDescriptionsItem>
              <ElDescriptionsItem :span="3">
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.finishedCardModelModule')
                  }}</div></template
                >
                <ElCollapse v-for="it in item?.finishedCardModuleList" :key="it?.chipCode">
                  <ElCollapseItem
                    :name="`finish${it?.chipCode}`"
                    :title="`${t('cardProductBusiness.orderApproval.maxCode')}：${it?.maxCode}`"
                  >
                    <ElDescriptions border :column="2" size="small">
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.chipType')
                          }}</div></template
                        >
                        <span v-if="it?.chipType === 0">{{
                          t('cardProductBusiness.orderApproval.trueChip')
                        }}</span>
                        <span v-if="it?.chipType === 1">{{
                          t('cardProductBusiness.orderApproval.dummyChip')
                        }}</span>
                        <span v-if="it?.chipType === 2">{{
                          t('cardProductBusiness.orderApproval.noChip')
                        }}</span>
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.chipCode')
                          }}</div></template
                        >
                        {{ it?.chipCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.chipSupplier')
                          }}</div></template
                        >
                        {{ it?.chipSupplier }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.moduleTransferInterfaceType')
                          }}</div></template
                        >
                        {{ it?.moduleTransferInterfaceType }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.color')
                          }}</div></template
                        >
                        {{ it?.color }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.capacity')
                          }}</div></template
                        >
                        {{ it?.capacity }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.maskCode')
                          }}</div></template
                        >
                        {{ it?.maskCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.maxCode')
                          }}</div></template
                        >
                        {{ it?.maxCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.shape')
                          }}</div></template
                        >
                        {{ it?.shape }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.oiOrModuleCode')
                          }}</div></template
                        >
                        {{ it?.oiOrModuleCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.antenna')
                          }}</div></template
                        >
                        {{ it?.antenna }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.aerialType')
                          }}</div></template
                        >
                        {{ it?.aerialType }}
                      </ElDescriptionsItem>
                    </ElDescriptions>
                  </ElCollapseItem>
                </ElCollapse>
              </ElDescriptionsItem>
              <ElDescriptionsItem :span="3">
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.semiCardModelModule')
                  }}</div></template
                >
                <ElCollapse v-for="it in item?.semiFinishedCardModuleList" :key="it?.chipCode">
                  <ElCollapseItem
                    :name="`finish${it?.chipCode}`"
                    :title="`${t('cardProductBusiness.orderApproval.maxCode')}：${it?.maxCode}`"
                  >
                    <ElDescriptions border :column="2" size="small">
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.chipType')
                          }}</div></template
                        >
                        <span v-if="it?.chipType === 0">{{
                          t('cardProductBusiness.orderApproval.trueChip')
                        }}</span>
                        <span v-if="it?.chipType === 1">{{
                          t('cardProductBusiness.orderApproval.dummyChip')
                        }}</span>
                        <span v-if="it?.chipType === 2">{{
                          t('cardProductBusiness.orderApproval.noChip')
                        }}</span>
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.chipCode')
                          }}</div></template
                        >
                        {{ it?.chipCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.chipSupplier')
                          }}</div></template
                        >
                        {{ it?.chipSupplier }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.moduleTransferInterfaceType')
                          }}</div></template
                        >
                        {{ it?.moduleTransferInterfaceType }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.color')
                          }}</div></template
                        >
                        {{ it?.color }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.capacity')
                          }}</div></template
                        >
                        {{ it?.capacity }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.maskCode')
                          }}</div></template
                        >
                        {{ it?.maskCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.maxCode')
                          }}</div></template
                        >
                        {{ it?.maxCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.shape')
                          }}</div></template
                        >
                        {{ it?.shape }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.oiOrModuleCode')
                          }}</div></template
                        >
                        {{ it?.oiOrModuleCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.property')
                          }}</div></template
                        >
                        {{ it?.property }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.maxCode')
                          }}</div></template
                        >
                        {{ it?.maxCode }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.stripe')
                          }}</div></template
                        >
                        {{ it?.stripe }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.antenna')
                          }}</div></template
                        >
                        {{ it?.antenna }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.aerialType')
                          }}</div></template
                        >
                        {{ it?.aerialType }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem>
                        <template #label
                          ><div class="cell-item">{{
                            t('cardProductBusiness.orderApproval.chipCapacitance')
                          }}</div></template
                        >
                        {{ it?.capacitance }}
                      </ElDescriptionsItem>
                    </ElDescriptions>
                  </ElCollapseItem>
                </ElCollapse>
              </ElDescriptionsItem>

              <ElDescriptionsItem :span="3">
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.orderRemark')
                  }}</div></template
                >
                {{ item?.orderRemark }}
              </ElDescriptionsItem>
              <ElDescriptionsItem :span="3">
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.finishedStock')
                  }}</div></template
                >
                {{ item?.finishedStock }}
              </ElDescriptionsItem>
              <ElDescriptionsItem :span="3">
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.semifinishedStock')
                  }}</div></template
                >
                {{ item?.semifinishedStock }}
              </ElDescriptionsItem>
              <ElDescriptionsItem>
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.cardOrgUsableFlag')
                  }}</div></template
                >
                <span v-if="item.cardOrgUsableFlag === true">{{
                  t('cardProductBusiness.orderApproval.reusable')
                }}</span>
                <span v-if="item.cardOrgUsableFlag === false">{{
                  t('cardProductBusiness.orderApproval.nonReusable')
                }}</span>
              </ElDescriptionsItem>
              <ElDescriptionsItem>
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.primaryFlag')
                  }}</div></template
                >
                {{
                  item?.primaryFlag
                    ? t('cardProductBusiness.orderApproval.yes')
                    : t('cardProductBusiness.orderApproval.no')
                }}
              </ElDescriptionsItem>
              <ElDescriptionsItem>
                <template #label>
                  <div class="cell-item">
                    {{ t('cardProductBusiness.orderApproval.finalDrafts') }}
                  </div>
                </template>
                <span
                  class="final-url"
                  @click="clickFinalDrafts(item)"
                  :src="item?.finalDraftsFileUrl"
                  >{{ item?.finalDraftsFileName }}</span
                >
              </ElDescriptionsItem>
              <ElDescriptionsItem :span="3">
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.cardNoteList')
                  }}</div></template
                >
                <div class="cell-content">
                  <p v-for="(it, i) in item?.cardNoteList" :key="i"
                    >{{ t('cardProductBusiness.orderApproval.cardStyleCode') }}：{{ it?.cardCode
                    }}{{ it?.cardNote ? `————${it?.cardNote}` : ''
                    }}<span v-if="it?.remark" :style="{ color: '#FF0000' }"
                      >——{{ it?.remark }}</span
                    ></p
                  >
                </div>
              </ElDescriptionsItem>
              <ElDescriptionsItem :span="3"
                ><template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.cardReviewResultList')
                  }}</div></template
                >
                <div class="record-content mt-16px">
                  <ElTimeline>
                    <ElTimelineItem v-for="it in item.cardReviewResultList" :key="it.id">
                      <div>
                        <p>
                          <span class="title"
                            >{{ t('cardProductBusiness.orderApproval.nodeName') }}：</span
                          >
                          {{ getNodeName(it?.node) }}
                        </p>
                        <p>
                          <span class="title"
                            >{{ t('cardProductBusiness.orderApproval.handler') }}：</span
                          >
                          {{ it?.createName }}
                        </p>
                        <p>
                          <span class="title"
                            >{{ t('cardProductBusiness.orderApproval.auditRes') }}：</span
                          >
                          {{ it?.resultName }}
                        </p>
                        <p>
                          <span class="title"
                            >{{ t('cardProductBusiness.orderApproval.auditRemark') }}：</span
                          >
                          {{ it?.remark }}
                        </p>
                        <p>
                          <span class="title"
                            >{{ t('cardProductBusiness.orderApproval.handleTime') }}：</span
                          >
                          {{ it?.createDate }}
                        </p>
                      </div>
                    </ElTimelineItem>
                  </ElTimeline>
                </div>
              </ElDescriptionsItem>
              <ElDescriptionsItem
                :span="3"
                v-if="item?.saleNeedReview && !orderInfo?.saleReviewStatus"
              >
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.auditRes')
                  }}</div></template
                >
                <ElFormItem
                  label=" "
                  :prop="`${index}.reviewResultCode`"
                  :rules="{
                    required: true,
                    trigger: ['blur', 'change'],
                    message: t('cardProductBusiness.orderApproval.placeholderSelectTip', {
                      placeholder: t('cardProductBusiness.orderApproval.auditRes')
                    })
                  }"
                  ><ElRadioGroup v-model="item.reviewResultCode">
                    <ElRadio v-for="ir in oprateOpts" :label="ir.code" :key="ir.code">{{
                      ir.name
                    }}</ElRadio>
                  </ElRadioGroup></ElFormItem
                >
              </ElDescriptionsItem>
              <ElDescriptionsItem
                :span="3"
                v-if="item?.saleNeedReview && !orderInfo?.saleReviewStatus"
              >
                <template #label
                  ><div class="cell-item">{{
                    t('cardProductBusiness.orderApproval.auditRemark')
                  }}</div></template
                >
                <div class="cell-content">
                  <ElFormItem
                    label=" "
                    :prop="`${index}.reviewResultRemark`"
                    :rules="{
                      required: true,
                      trigger: ['blur', 'change'],
                      message: t('cardProductBusiness.orderApproval.placeholderSelectTip', {
                        placeholder: t('cardProductBusiness.orderApproval.auditRemark')
                      })
                    }"
                  >
                    <ElInput
                      v-model="item.reviewResultRemark"
                      :placeholder="
                        t('cardProductBusiness.orderApproval.placeholderTextTip', {
                          placeholder: t('cardProductBusiness.orderApproval.auditRemark')
                        })
                      "
                      type="textarea"
                    />
                  </ElFormItem>
                </div>
              </ElDescriptionsItem>
            </ElDescriptions>
          </ElCard>
        </ElForm>
      </div>
      <template #footer>
        <el-button @click="close">{{ t('common.close') }}</el-button>
        <el-button
          :loading="loading"
          type="primary"
          @click="submit"
          v-if="!orderInfo?.saleReviewStatus"
          >{{ t('common.submitForm') }}</el-button
        >
      </template>
    </ElDialog>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'SignModal'
})

import { useDictStoreWithOut } from '@/store/modules/dict'
import dayjs from 'dayjs'
import { getStrDictOptions } from '@/utils/dict'
import { first, filter } from 'lodash-es'
import {
  resultListVO,
  saleReviewTaskVO,
  saleReviewCardList,
  saleCardInfoVO,
  saleReviewCardResult,
  downloadFinalDraftsFile
} from '@/api/orderHandle/pdmOrder'
const { t } = useI18n()
const dictStore = useDictStoreWithOut()

const list = ref([])

interface oprateOptsType {
  code: string
  name: string
}

const props = defineProps({
  oprateOpts: {
    type: [Array<oprateOptsType>],
    default: () => []
  }
})

const orderInfo: Ref<saleReviewTaskVO | undefined> = ref()

const cardInfoList: Ref<Array<saleCardInfoVO | never>> = ref([])

const show = ref(false)
const open = async (row: saleReviewTaskVO) => {
  const res: Array<saleCardInfoVO | never> = await saleReviewCardList({
    reviewTaskId: row.reviewTaskId
  })
  console.log('res', res)
  show.value = true
  orderInfo.value = row
  cardInfoList.value = res
}

const emits = defineEmits(['success'])

const close = () => {
  orderInfo.value = undefined
  cardInfoList.value = []
  show.value = false
}

const formRef = ref()

const loading = ref(false)

const submit = async () => {
  console.log('cardInfoList', cardInfoList.value)
  const valid = await formRef.value.validate()
  if (!valid) return // 验证不通过直接返回
  try {
    loading.value = true
    const arr = cardInfoList.value
      .filter((el) => {
        return el.saleNeedReview
      })
      .map((el) => {
        return {
          reviewCardId: el.id,
          reviewResultCode: el.reviewResultCode,
          reviewResultRemark: el.reviewResultRemark
        }
      })
    await saleReviewCardResult({
      reviewTaskId: orderInfo?.value?.reviewTaskId,
      saleReviewCardResultList: arr
    })
    ElMessage.success(t('cardProductBusiness.orderApproval.submitSuccess'))
    emits('success')
    close()
  } finally {
    loading.value = false
  }
}

const loadding = ref(false)

const clickFinalDrafts = async (item) => {
  const res = await downloadFinalDraftsFile({
    fileUrl: item.finalDraftsFileUrl,
    fileName: item.finalDraftsFileName
  })
  const blob = new Blob([res.data], {
    type: res.data.type
  })
  const a = document.createElement('a')
  // 只有图片和pdf需要预览，其他都是下载
  if (res.data.type.indexOf('image') === -1 && res.data.type.indexOf('pdf') === -1) {
    //下载需要文件名，而finalDraftsFileName可能不包含后缀，finalDraftsFileUrl是包含后缀的，所以取finalDraftsFileUrl的名字
    const nameArr = item.finalDraftsFileUrl.split('/')
    a.download = nameArr[nameArr.length - 1]
  }
  a.href = window.URL.createObjectURL(blob)
  a.setAttribute('target', '_blank')
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

defineExpose({
  open
})

// 交付方式
const mailModeOptions = computed(() => {
  return getStrDictOptions('mail_mode')
})
const deliveryTypeDisplay = (deliveryType) => {
  try {
    const item = first(filter(mailModeOptions.value, (item) => item.value === deliveryType))
    return item ? item.label : deliveryType
  } catch (ex) {
    console.error('交付方式异常：', ex)
    return deliveryType
  }
}

const getNodeName = (value) => {
  const arr = dictStore.getDictByType('review_node')?.find((item) => {
    return item.value === value
  })
  return arr?.label || '---'
}
</script>
<style scoped lang="less">
:deep(.el-dialog__body) {
  height: 65vh;
  min-height: 100px;
  overflow-y: auto;
}

.title {
  font-weight: bold;
}

.final-url {
  color: darkblue;
  cursor: pointer;
}
</style>
