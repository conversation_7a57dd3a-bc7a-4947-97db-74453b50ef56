<!-- 
  卡产品订单-批卡订单详情
 -->

<template>
  <h2>{{ t('cardProductBusiness.orderSearch.orderInfo') }}</h2>
  <ElForm label-position="right" label-width="auto">
    <ElRow :gutter="10">
      <ElCol :span="8" v-for="item in orderView" :key="item.label">
        <ElFormItem :label="item.label">
          {{ getOrderVlaue(item) }}
        </ElFormItem>
      </ElCol>
    </ElRow>
    <ElRow>
      <ElCol :span="8">
        <el-form-item :label="t('cardProductBusiness.orderSearch.fileList')"
          ><div class="content">
            <div v-if="fileListValue.length === 0">-</div>
            <div v-for="(item, index) in fileListValue" :key="'pz' + index">
              <span @click="previewImage(item.eosUrl)" style="color: red">{{ item.fileName }}</span>
              <el-button
                class="color-blue ml-20px cursor-pointer"
                v-if="item.eosUrl"
                type="primary"
                link
                :loading="downBtnLoading.includes(index)"
                @click="downFile(item.eosUrl, item.fileName, index)"
                >{{ t('cardProductBusiness.orderSearch.download') }}
              </el-button>
            </div>
          </div></el-form-item
        >
      </ElCol>
      <ElCol :span="8">
        <ElFormItem :label="t('cardProductBusiness.orderSearch.deliveryMethod')">
          {{ deliveryMethod }}
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>

  <h2>{{ t('cardProductBusiness.orderSearch.customerInfo') }}</h2>
  <ElForm label-position="right" label-width="auto">
    <ElRow :gutter="10">
      <ElCol :span="8" v-for="item in customView" :key="item.label">
        <ElFormItem :label="item.label" prop="data1">
          {{ getOrderVlaue(item) }}
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>
  <h2>{{ t('cardProductBusiness.orderSearch.productInfo') }}</h2>
  <ElTable
    :data="productionTableData"
    class="table"
    :headerCellStyle="{ background: '#F8F8F8', height: '45px' }"
    v-loading="tableLoading"
  >
    <ElTableColumn prop="productionName" :label="t('cardProductBusiness.orderSearch.productName')">
      <template #default="scope">
        <div class="product-warp flex-index" v-if="scope.row?.saleName">
          <ElImage
            v-if="scope.row?.imgList?.length"
            class="pro-img"
            :src="showImg(scope.row?.imgList)"
            alt=""
          />
          <div class="img-name">{{ scope.row?.saleName }}</div>
        </div>
        <span v-else>---</span>
      </template>
    </ElTableColumn>
    <ElTableColumn
      prop="unitPrice"
      :label="t('cardProductBusiness.orderSearch.unitPrice')"
      width="140"
    >
      <template #default="{ row }">
        {{ formatMoneyDigitsEx(row.unitPrice, coinType) }}
      </template>
    </ElTableColumn>

    <ElTableColumn
      prop="cardCount"
      :label="t('cardProductBusiness.orderSearch.cardCount')"
      width="150"
    />
    <ElTableColumn
      prop="oneTotalPrice"
      :label="t('cardProductBusiness.orderSearch.price')"
      width="150"
    >
      <template #default="{ row }">
        {{ formatMoneyDigitsEx(row.oneTotalPrice, coinType) }}
      </template>
    </ElTableColumn>

    <ElTableColumn prop="isIndividual" :label="t('cardProductBusiness.orderSearch.isIndividual')">
      <template #default="{ row }">
        {{
          row.isIndividual
            ? t('cardProductBusiness.orderSearch.yes')
            : t('cardProductBusiness.orderSearch.no') ?? t('cardProductBusiness.orderSearch.empty')
        }}
      </template>
    </ElTableColumn>
    <ElTableColumn prop="address" :label="t('cardProductBusiness.orderSearch.mailAddress')">
      <template #default="scope">
        <div class="addr-warp" v-for="item in scope.row.receivingList" :key="item.id">
          {{ item.fullAddress }}{{ item.cardCount ? ` * ${item.cardCount}` : '' }}
        </div>
      </template>
    </ElTableColumn>
    <ElTableColumn
      prop="cardDeliveryDate"
      :label="t('cardProductBusiness.orderSearch.cardDeliveryDate')"
    >
      <template #default="{ row }">
        {{
          row.cardDeliveryDate
            ? dayjs(row.cardDeliveryDate).format('YYYY-MM-DD')
            : t('cardProductBusiness.orderSearch.empty')
        }}
      </template>
    </ElTableColumn>
    <ElTableColumn prop="status" :label="t('cardProductBusiness.orderSearch.OrderStatus')">
      <template #default="{ row }">
        {{ displayProductStatus(row.status) }}
      </template>
    </ElTableColumn>
    <ElTableColumn prop="status" :label="t('cardProductBusiness.orderSearch.completionDate')">
      <template #default="{ row }">
        {{ displayProductCompletionDate(row.status) }}
      </template>
    </ElTableColumn>
    <ElTableColumn prop="status" :label="t('cardProductBusiness.orderSearch.cardOrganization')">
      <template #default="{ row }">
        {{ displayProductOrgName(row.productId) }}
      </template>
    </ElTableColumn>
    displayProductOrgName
  </ElTable>
  <h2>{{ t('cardProductBusiness.orderSearch.logisticsInfo') }}</h2>
  <ElTable
    :data="logisticsTableData"
    class="table"
    :headerCellStyle="{ background: '#F8F8F8', height: '45px' }"
    v-loading="tableLoading"
  >
    <ElTableColumn prop="mailNo" :label="t('cardProductBusiness.orderSearch.mailNo')" />
    <ElTableColumn
      prop="customerName"
      :label="t('cardProductBusiness.orderSearch.logisticsTracking')"
    >
      <template #default="{ row }">
        <ElTimeline class="pt-24px">
          <ElTimelineItem
            v-for="(item, index) in row?.routeList"
            :key="index"
            :timestamp="item.remark"
          >
            <span class="mr-10px title">{{ item.opName }}</span>
            <span class="time">{{ item.opTime }}</span>
          </ElTimelineItem>
        </ElTimeline>
      </template>
    </ElTableColumn>
  </ElTable>
</template>
<script setup lang="ts">
defineOptions({
  name: 'CardOrderDetails'
})

import dayjs from 'dayjs'
import { batchDetailsApi, fastSearchApi } from '@/api/order'
import { orderData, customData, orderSourceList } from './lib'
import { getDictLabel } from '@/utils/dict'
import { formatMoneyDigitsEx } from '@/utils/formatMoney'
import { searchTraceMailNo, LogisticsDataVO } from '@/api/order/index'

import { batchOrderProductStatus } from '@/api/order/types/batchOrderProductStatus.d'
import { useOrderService } from './hooks/userOrderSerivce'
const orderService = useOrderService()

const { t } = useI18n()

const coinType = ref<string>('')

const route = useRoute()
let orderId = route.query?.orderId
const logisticsTableData: Ref<LogisticsDataVO[]> = ref([])
const productionTableData = ref([])
const tableLoading = ref(false)
const orderView = ref(orderData)
const customView = ref(customData)

const productLogs = ref<batchOrderProductStatus[]>([]) //下单产品日志
const productOrgs = ref<any[]>([]) //下单产品的卡组织信息

const int = async () => {
  const data = await batchDetailsApi(orderId)
  orderView.value.forEach((item) => {
    let keyList = item.key.split('.')
    item.value = keyList.length === 2 ? data[keyList[0]][keyList[1]] : data[item.key]
  })
  customView.value.forEach((item) => {
    let keyList = item.key.split('.')
    item.value = keyList.length === 2 ? data[keyList[0]][keyList[1]] : data[item.key]
  })
  productionTableData.value = data?.orderDetailExt.productionList

  deliveryMethod.value = data?.orderExt.mailMode
    ? getDictLabel('mail_mode', data?.orderExt.mailMode)
    : '-' //交付方式
  data?.orderExt.fileList?.length > 0 && (fileListValue.value = [...data?.orderExt.fileList])

  coinType.value = data?.orderExt.coinType ?? 'CNY' //币种类型

  // 获取物流信息，需要从产品信息去查，而这个产品信息，在详情里面的产品列表里面带出
  productionTableData.value.forEach(async (_el: any) => {
    try {
      if (!_el?.mailNo) return
      // 单个单个获取物流信息，目前后端给出的一次性获取多个物流信息并不好用，同时后端建议单个单个获取
      const res = await searchTraceMailNo({
        mailNo: _el?.mailNo,
        expressType: _el?.expressType
      })
      logisticsTableData.value.push(res)
    } catch (e) {
      console.log(e)
    }
  })
}

const showImg = (arr) => {
  let res = ''
  if (!arr || arr?.length === 0) return ''
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].imageType === 'front') {
      res = arr[i].imageUrl
      break
    }
  }
  return res
}

const fileListValue: Ref<{ eosUrl: string; fileName: string; saveFileName: string }[]> = ref([])
const deliveryMethod = ref<string>()

let downBtnLoading = ref<number[]>([])

// 下载数据
const downFile = async (eosUrl, fileName, index) => {
  const download = (url) => {
    console.log(url)
    const fileUrl = url // 文件的URL地址
    const link = document.createElement('a')
    link.href = fileUrl
    link.setAttribute('download', url.split('/').pop())
    link.click()
  }
  try {
    if (eosUrl.indexOf('http') === -1) {
      const domain = `${window.location.protocol}//${window.location.host}`
      download(`${domain}${eosUrl}`)
    } else {
      download(envController.getSaleUrl() + eosUrl)
    }
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    downBtnLoading.value.splice(downBtnLoading.value.indexOf(index), 1)
  }
}

// 预览
import envController from '@/controller/envController'

const previewImage = (url) => {
  if (url == '') return
  let fileType = url.split('.').pop()
  let fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning(t('common.previewTip'))
  }
  window.open(`${envController.getSaleUrl()}${url}`, '_blank')
}

const getOrderVlaue = (item) => {
  if (item.value === '' || item.value === undefined || item.value === null) return '-'
  if (item.label === t('cardProductBusiness.orderSearch.OrderStatus'))
    return getDictLabel('batch_order_status', item.value)
  if (item.label === t('cardProductBusiness.orderSearch.orderType'))
    return getDictLabel('order_type', item.value)
  if (item.label === t('cardProductBusiness.orderSearch.isUrgent'))
    return item.value
      ? t('cardProductBusiness.orderSearch.yes')
      : t('cardProductBusiness.orderSearch.no')
  if (item.label === t('cardProductBusiness.orderSearch.deliveryMethod'))
    return getDictLabel('mail_mode', item.value)
  if (item.label === t('cardProductBusiness.orderSearch.orderSource'))
    return orderSourceList[item.value]
  if (item.label === t('cardProductBusiness.orderSearch.customerCreateOrder'))
    return customerNameList.value && customerNameList.value[item.value]
  if (item.label === t('cardProductBusiness.orderSearch.orderTotalPrice'))
    return formatMoneyDigitsEx(item.value, coinType.value)
  if (item.label === t('cardProductBusiness.orderSearch.customerOrderReceiveTime'))
    return dayjs(item.value).format('YYYY-MM-DD')
  if (item.label === t('cardProductBusiness.orderSearch.deliveryTime'))
    return dayjs(item.value).format('YYYY-MM-DD')
  return item.value
}
const customerNameList = ref()
const getCustomerName = async () => {
  const res = await fastSearchApi()
  customerNameList.value = res
}
onMounted(() => {
  int()
  getCustomerName()

  nextTick(async () => {
    if (orderId && orderId != null) {
      const orgs = await orderService.getProductOrgs(orderId.toString()) //获取下单产品卡组织
      const logs = await orderService.getProductStatusLogs(orderId.toString()) //获取下单产品日志信息
      productOrgs.value = orgs
      productLogs.value = logs
    }
  })
})
const download = (url) => {
  window.open(url)
}

const getTitle = (i) => {
  let str = t('cardProductBusiness.orderSearch.ordered')
  if (i && i.split(',')[1]) str = i && i.split(',')[1].split('。')[0]
  return str
}

const productionStatusList = ref([
  {
    code: 'NONE',
    name: t('cardProductService.productOrder.batchOrderDetail.statusNone')
  },
  {
    code: 'REVIEW_DOING',
    name: t('cardProductService.productOrder.batchOrderDetail.statusNone')
  },

  {
    code: 'STOCKING',
    name: t('cardProductService.productOrder.batchOrderDetail.statusStocking')
  },

  {
    code: 'MAKING',
    name: t('cardProductService.productOrder.batchOrderDetail.statusMaking')
  },
  {
    code: 'MAKED',
    name: t('cardProductService.productOrder.batchOrderDetail.statusMaked')
  },
  {
    code: 'SHIPPED',
    name: t('cardProductService.productOrder.batchOrderDetail.statusShipped')
  },
  {
    code: 'COMPLETED',
    name: t('cardProductService.productOrder.batchOrderDetail.statusShipped')
  }
])

const displayProductStatus = (statusCode) => {
  const status = productionStatusList.value.filter((item) => item.code == statusCode)
  return status && status[0] ? status[0].name : t('cardProductBusiness.orderSearch.noStatus')
}
//获取下单产品的完成时间
const displayProductCompletionDate = (productId) => {
  if (!productId || !productLogs.value) {
    return '-'
  }

  const results = productLogs.value.filter(
    (item) =>
      item.orderDetailId == productId &&
      (item.productStatus == 'MAKED' ||
        item.productStatus == 'SHIPPED' ||
        item.productStatus == 'COMPLETED')
  )
  if (results[0]) {
    return dayjs(results[0].createTime).format('YYYY-MM-DD')
  }
  return '-'
}
//显示卡组织数据
const displayProductOrgName = (productId) => {
  if (!productId || !productOrgs.value) {
    return '-'
  }
  const orgs = productOrgs.value[productId]
  if (orgs) {
    return orgs.map((x) => x.name_cn).join('; ')
  }
  return ''
}
</script>
<style lang="less" scoped>
h2 {
  font-weight: bold;
  padding-bottom: 10px;
}
.table {
  margin-bottom: 10px;
}
.link {
  margin-right: 10px;
}

h1 {
  font-weight: bold;
  padding-bottom: 30px;
}
.title {
  font-size: 18px;
  font-weight: bold;
}
.time {
  color: var(--el-text-color-secondary);
}
.pro-img {
  width: 122px;
  // height: auto;
  min-height: 70px;
  display: flex;
  align-items: center;
}
.flex-index {
  display: flex;
  align-items: center;
}
.img-name {
  width: 190px;
  margin-left: 24px;
  margin-right: 24px;
}
.product-warp {
  margin: 52px 0;
}
</style>
