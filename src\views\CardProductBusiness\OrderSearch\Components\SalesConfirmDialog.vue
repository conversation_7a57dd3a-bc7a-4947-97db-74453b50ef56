<template>
  <el-dialog
    :title="t('cardProductBusiness.orderSearch.saleConfirm')"
    :model-value="visible"
    :width="ifEn ? '65%' : '65%'"
    min-width="800px"
    @close="onClose"
    :scroll="true"
    :close-on-click-modal="false"
  >
    <el-form ref="fromEl" :label-width="ifEn ? 180 : 110" class="mt-20px">
      <el-row :gutter="20">
        <el-col :span="10" :offset="0">
          <el-form-item :label="t('cardProductBusiness.orderSearch.UMVOrderCode')">
            <el-text class="mx-1" size="large">{{ order?.orderCode }}</el-text>
          </el-form-item>
        </el-col>
        <el-col :span="14" :offset="0">
          <el-form-item :label="t('cardProductBusiness.orderSearch.customerName')">
            <el-text class="mx-1" size="large">{{ order?.customerName }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16" :offset="0">
          <el-form-item
            :label="t('cardProductBusiness.orderSearch.deliveryMethod')"
            prop="deliveryMethod"
          >
            <ElSelect
              class="w-60"
              v-model="deliveryMethod"
              :placeholder="t('cardProductBusiness.orderSearch.deliveryMethod')"
              clearable
            >
              <ElOption
                v-for="dict in mailModeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </ElSelect>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16" :offset="0">
          <el-form-item :label="t('cardProductBusiness.orderSearch.remarkNote')">
            <el-input
              type="text"
              :rows="2"
              v-model="remark"
              :placeholder="t('cardProductBusiness.proxyCustomerToOrder.remarkPlaceholder')"
              :maxlength="-1"
              :show-word-limit="false"
              :autosize="{ minRows: 2, maxRows: 4 }"
            /> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" :offset="0">
          <el-table :data="products" border stripe>
            <el-table-column
              prop="name"
              :label="t('cardProductBusiness.proxyCustomerToOrder.productName')"
              min-width="150"
            />
            <el-table-column
              prop="clientProductUniqueCode"
              :label="t('cardProductBusiness.proxyCustomerToOrder.customerProuductCode')"
              min-width="150"
            />
            <el-table-column
              prop="companyCode"
              :label="t('cardProductBusiness.proxyCustomerToOrder.cardCode')"
              min-width="150"
            />
            <el-table-column
              prop="amount"
              :label="t('cardProductBusiness.proxyCustomerToOrder.orderNum')"
              width="150"
            />
            <el-table-column
              prop="unitPrice"
              :label="t('cardProductBusiness.proxyCustomerToOrder.unitPrice')"
              width="180"
            >
              <template #default="{ row }">
                <el-input
                  v-model="row.unitPrice"
                  size="normal"
                  type="number"
                  @change="onUnitPriceChange(row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="total"
              :label="t('cardProductBusiness.proxyCustomerToOrder.totalPrice')"
              width="150"
            />
          </el-table>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ t('common.close') }}</el-button>
      <el-button type="primary" @click="onSubmit">{{ t('common.submitForm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
export interface IOrder {
  orderId: string
  orderCode: string
  customerName: string
}
</script>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { saleConfirmOrderApi } from '@/api/order'
import { useOrderService } from '../hooks/userOrderSerivce'
import { IOrderProduct } from '../types/orderProduct'
import { formatMoneyDigitsEx } from '@/utils/formatMoney'

const { t, ifEn } = useI18n()
const visible = ref<boolean>(false)
const orderService = useOrderService()
const { customers } = orderService
const order = ref<IOrder | undefined>()
const products = ref<IOrderProduct[]>([]) //产品列表
const fromEl = ref<FormInstance>()
const coinType = ref<string>('')

//邮寄方式
const mailModeOptions = computed(() => {
  return getStrDictOptions('mail_mode')
})
const deliveryMethod = ref('')
const remark = ref('')
const emits = defineEmits<{
  (e: 'submit'): boolean
}>()

//获取产品列表数据
async function loadProducts(orderInfo) {
  if (!orderInfo?.orderDetailExt?.productionList) {
    console.error('产品列表数据为空')
    throw new Error('产品列表数据为空')
  }
  coinType.value = orderInfo.orderExt.coinType
  orderInfo.orderDetailExt.productionList.forEach((item) => {
    const total = formatMoneyDigitsEx(item.oneTotalPrice, coinType.value)
    console.log('load product price ', total)
    products.value.push({
      orderDetailId: item.orderDetailId,
      name: item.saleName ?? item.productionName,
      cardCode: item.companyCode ?? item.cardCode,
      companyCode: item.companyCode,
      amount: item.cardCount,
      unitPrice: item.unitPrice,
      total: total,
      clientProductUniqueCode: item.clientProductUniqueCode
    })
  })
}

//显示Dialog
async function show(orderId: string) {
  const orderInfo = await orderService.getOrderInfo(orderId)
  const customerName = unref(customers)[orderInfo.customerId]
  await loadProducts(orderInfo) //加载产品数据
  order.value = {
    orderId: orderInfo.orderId,
    orderCode: orderInfo.orderCode,
    customerName: customerName || ''
  }
  console.log(orderInfo)
  visible.value = true
}

//关闭Dialog
async function onClose() {
  order.value = undefined
  products.value = []
  visible.value = false
}

//提交更新产品信息
async function onSubmit() {
  if (!deliveryMethod.value) {
    ElMessage.error(t('cardProductBusiness.proxyCustomerToOrder.deliveryMethodNotNull'))
    return
  }
  console.log(order.value)
  if (!order.value || !order.value?.orderId) {
    ElMessage.error(t('cardProductBusiness.orderSearch.orderCodeNullTip'))
    return
  }
  //需要提交的产品数据
  const productionList = products.value.map((item) => {
    return {
      orderDetailId: item.orderDetailId,
      unitPrice: item.unitPrice
    }
  })

  const data = {
    orderId: order.value.orderId,
    deliveryMethod: deliveryMethod.value,
    remark: remark.value,
    productionList: productionList
  }
  console.log(data)
  const result = await saleConfirmOrderApi(data)
  if (result) {
    ElMessage.success(t('cardProductBusiness.orderApproval.submitSuccess'))
    deliveryMethod.value = ''
    emits('submit')
    onClose()
  }
}

//总价计算
function onUnitPriceChange(product: IOrderProduct) {
  const index = product.total.lastIndexOf('.')
  let decmaliLen = 3 //默认小数点为
  if (index > -1) {
    decmaliLen = product.total.substring(index + 1).length //获取小数位数
  }
  const productTotal = (product.amount * product.unitPrice).toFixed(decmaliLen)

  product.total = formatMoneyDigitsEx(productTotal.toString(), coinType.value)
}

defineExpose({ show })
</script>

<style scoped></style>
../types/orderProductPirce
