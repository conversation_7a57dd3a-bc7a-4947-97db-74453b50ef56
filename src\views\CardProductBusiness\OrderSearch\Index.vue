<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-23 10:16:01
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-11-06 11:58:00
 * @Description: 卡产品业务-订单查询
-->
<script setup lang="ts">
defineOptions({
  name: 'OrderSearch'
})

import { reactive, ref, onMounted } from 'vue'
import router from '@/router'
import type { FormRules } from 'element-plus'
import { batchAndSamplePageApi, fastSearchApi, cancelApi } from '@/api/order'
import { getDictLabel, getDictOptions } from '@/utils/dict'
import { cloneDeep } from 'lodash-es'
import { orderSourceList } from './lib'
import { formatMoneyDigitsEx } from '@/utils/formatMoney'
import SalesConfirmDialog from './Components/SalesConfirmDialog.vue'

import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'

import { useOrderService } from './hooks/userOrderSerivce'
const orderService = useOrderService()

const { t, ifEn } = useI18n()
const tableLoading = ref<boolean>(false)
interface IqueryData {
  customerId: string
  orderSource: string
  orderType: string
  orderStatus: string
  orderCode: string
  vname: string
  createUserName: string
  taskCode: string
  selfOnly: boolean | undefined
  createOrderTime: []
  saleUserName: string | undefined
  completionDates: []
}

const queryData = reactive<IqueryData>({
  customerId: '',
  orderSource: '',
  orderType: '',
  orderStatus: '',
  orderCode: '',
  vname: '',
  createUserName: '',
  taskCode: '',
  selfOnly: false,
  createOrderTime: [],
  saleUserName: '',
  completionDates: []
})
const rules = reactive<FormRules>({
  customerId: [
    {
      required: false,
      message: t('cardProductBusiness.orderSearch.selectCustomerPlaceholder'),
      trigger: 'change'
    }
  ]
})
const defaultTime = ref<[Date, Date]>([
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59)
])
const isExpand = ref<boolean>(true)
const expand = () => {
  isExpand.value = !isExpand.value
}

// 订单类型
const orderTypeOptions = ref(getDictOptions('order_type'))

// 订单来源
const orderSourceEnum = [
  t('cardProductBusiness.orderSearch.management'),
  t('cardProductBusiness.orderSearch.customer'),
  t('cardProductBusiness.orderSearch.sale')
]

// 订单来源-对象
const orderSource = {
  management: orderSourceEnum[0],
  customer: orderSourceEnum[1],
  sale: orderSourceEnum[2]
}

const tableData = ref([])
const pagination = reactive({
  pageNo: 1,
  pageSize: 10
})
const total = ref(0)
const getList = async () => {
  try {
    tableLoading.value = true
    const res = await batchAndSamplePageApi(queryData, pagination)
    tableData.value = res?.list?.map((item) => {
      let productIdMaps = {}
      item.productTypeCount = 0
      item.orderDetailExt?.productionList?.forEach((product) => {
        ++item.productTypeCount
        if (undefined === productIdMaps[product.productId]) {
          productIdMaps[product.productId] = product.productId
        }
      })

      return item
    })
    total.value = res?.total || 0
  } catch (error) {
  } finally {
    tableLoading.value = false
  }
}

import type { FormInstance } from 'element-plus'
import { routerPush } from '@/utils/authutil'
import { ArrowDown } from '@element-plus/icons-vue'
const fromEl = ref<FormInstance>()
const search = () => {
  fromEl.value &&
    fromEl.value.validate(async (valid) => {
      if (!valid) {
        return
      }
      getList()
    })
}
const reset = (fromEl: FormInstance | undefined): void => {
  if (!fromEl) return
  fromEl.resetFields()
}
const handle = (e) => {
  router.push({
    name: 'CardOrderDetails',
    query: { orderId: e.orderId }
  })

  //   if (e.orderType === 'SAMPLE_ORDER') {
  //     //样卡订单详情
  //     router.push({
  //       name: 'SampleCardDetail',
  //       query: { orderId: e.orderId }
  //     })
  //   } else {
  //     //批卡订单详情
  //     router.push({
  //       name: 'BatchCardDetail',
  //       query: { orderId: e.orderId }
  //     })
  //   }
}
const cancel = (e) => {
  ElMessageBox.confirm(
    t('cardProductBusiness.orderSearch.revocationOrderConfirm'),
    t('cardProductBusiness.orderSearch.tip'),
    {
      confirmButtonText: t('cardProductBusiness.orderSearch.yes'),
      cancelButtonText: t('cardProductBusiness.orderSearch.no'),
      type: 'warning'
    }
  )
    .then(async () => {
      const res = await cancelApi(e.orderId, e.orderType)
      res.code &&
        ElMessage({
          message: t('cardProductBusiness.orderSearch.orderRevocationSuccess'),
          type: 'success'
        })
      getList()
    })
    .catch(() => {})
}

//跳转-代客下单
let goValetLoading = ref(false)
const goValet = async (param: string, row?: object) => {
  if (undefined === row) {
    row = { orderId: '' }
  }
  try {
    goValetLoading.value = true
    await router.push({
      path: '/CardProductBusiness/ProxyCustomerToOrder',
      query: { orderId: row.orderId }
    })
    goValetLoading.value = false
  } finally {
  }
}

const salesConfirmDialogRef = ref()
const selectOrder = ref(null)
const customerName = ref('')
//
const onSalesConfirmDialog = async (data) => {
  salesConfirmDialogRef.value?.show(data.orderId)
}

// 设置序号
const indexMethod = (index: number): number => {
  return (pagination.pageNo - 1) * pagination.pageSize + index + 1
}
const customerNameList = ref()
const getCustomerName = async () => {
  const res = await fastSearchApi()
  customerNameList.value = res
  //   if (res && Object.keys(res).length > 0 && queryData.customerId === '') {
  //     queryData.customerId = Object.keys(res)[0]
  //     getList()
  //   }
}
let orderStatusList = cloneDeep(getDictOptions('batch_order_status'))

/**导出操作 */
async function onExport() {
  if (!queryData.createOrderTime || !queryData.createOrderTime.length) {
    ElMessage.warning(t('cardProductBusiness.orderSearch.createTimeNotNull'))
    return
  }

  const data = toRaw(queryData)
  orderService.exportOrders(data)

  // const params = convertQueryParams(queryParams)
  // const res = await plmOrderApi.exportList(params)
  // const fileName = decodeURI(res['headers']['content-disposition']).split('filename=')[1]
  // download.excel(res.data, fileName)
}
//显示订单的完成时间
function dispalyCompletionDate(order) {
  const completionStatus = ['MAKED', 'ALL_SHIP', 'COMPLETED']
  if (completionStatus.includes(order.orderStatus)) {
    return order.updateTime
  }
  return '-'
}

Array.isArray(orderStatusList) &&
  orderStatusList.unshift({ label: t('cardProductBusiness.orderSearch.all'), value: '' })
onMounted(() => {
  getList()
  getCustomerName()
})

onActivated(() => {
  if (!tableLoading.value) {
    getList()
  }
})
</script>
<template>
  <ContentWrap ifTable>
    <template #search>
      <ElForm
        ref="fromEl"
        label-position="right"
        label-width="auto"
        :model="queryData"
        :rules="rules"
      >
        <ElRow :gutter="20">
          <ElCol :md="10" :lg="8" :xl="6" v-show="isExpand">
            <ElFormItem
              :label="t('cardProductBusiness.orderSearch.customerName')"
              prop="customerId"
            >
              <ElSelect
                v-model="queryData.customerId"
                clearable
                filterable
                style="width: 100%; min-width: 100%"
              >
                <ElOption
                  v-for="(item, key) in customerNameList"
                  :label="item"
                  :value="key"
                  :key="key"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :md="10" :lg="8" :xl="6" v-show="isExpand">
            <ElFormItem
              :label="t('cardProductBusiness.orderSearch.orderSource')"
              prop="orderSource"
            >
              <ElSelect
                v-model="queryData.orderSource"
                clearable
                style="width: 100%; min-width: 100%"
              >
                <ElOption
                  v-for="(item, key) in orderSourceList"
                  :label="item"
                  :value="key"
                  :key="key"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :md="10" :lg="8" :xl="6" v-show="isExpand">
            <ElFormItem :label="t('cardProductBusiness.orderSearch.UMVOrderCode')" prop="orderCode">
              <ElInput
                v-model="queryData.orderCode"
                :placeholder="t('cardProductBusiness.orderSearch.UMVOrderCodePlaceholder')"
                clearable
              />
            </ElFormItem>
          </ElCol>
          <ElCol :md="10" :lg="8" :xl="6" v-show="isExpand">
            <ElFormItem :label="t('cardProductBusiness.orderSearch.orderType')" prop="orderType">
              <ElSelect
                v-model="queryData.orderType"
                clearable
                style="width: 100%; min-width: 100%"
              >
                <ElOption
                  v-for="item in orderTypeOptions"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :md="10" :lg="8" :xl="6">
            <ElFormItem
              :label="t('cardProductBusiness.orderSearch.OrderStatus')"
              prop="orderStatus"
            >
              <ElSelect
                v-model="queryData.orderStatus"
                clearable
                style="width: 100%; min-width: 100%"
              >
                <ElOption
                  v-for="item in orderStatusList"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :md="10" :lg="8" :xl="6">
            <ElFormItem :label="t('cardProductBusiness.orderSearch.productName')" prop="vname">
              <SearchWildcardInput
                v-model="queryData.vname"
                :placeholder="t('cardProductBusiness.orderSearch.productNamePlaceholder')"
                maxlength="40"
                clearable
              />
            </ElFormItem>
          </ElCol>
          <ElCol :md="10" :lg="8" :xl="6" v-show="isExpand">
            <ElFormItem :label="t('cardProductBusiness.orderSearch.creator')" prop="createUserName">
              <SearchWildcardInput
                v-model="queryData.createUserName"
                :placeholder="
                  t('cardProductBusiness.orderApproval.placeholderTextTip', {
                    placeholder: t('cardProductBusiness.orderSearch.creator')
                  })
                "
                maxlength="40"
                clearable
              />
            </ElFormItem>
          </ElCol>
          <ElCol :md="10" :lg="8" :xl="6" v-show="isExpand">
            <ElFormItem :label="t('cardProductBusiness.orderSearch.taskCode')" prop="taskCode">
              <SearchWildcardInput
                v-model="queryData.taskCode"
                :placeholder="
                  t('cardProductBusiness.orderApproval.placeholderTextTip', {
                    placeholder: t('cardProductBusiness.orderSearch.taskCode')
                  })
                "
                maxlength="40"
                clearable
              />
            </ElFormItem>
          </ElCol>

          <el-col :span="24" :md="10" :lg="8" :xl="6">
            <el-form-item
              :label="t('cardProductBusiness.orderSearch.createTime')"
              prop="createOrderTime"
            >
              <el-date-picker
                v-model="queryData.createOrderTime"
                type="daterange"
                range-separator="-"
                :start-placeholder="t('common.startTimeText')"
                :end-placeholder="t(t('common.endTimeText'))"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="defaultTime"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <!-- <el-col :span="24" :md="10" :lg="8" :xl="6">
            <el-form-item :label="t('cardProductBusiness.orderSearch.salesman')">
              <SearchWildcardInput
                v-model="queryData.saleUserName"
                :placeholder="
                  t('cardProductBusiness.orderApproval.placeholderTextTip', {
                    placeholder: t('cardProductBusiness.orderSearch.salesman')
                  })
                "
                maxlength="40"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="24" :md="10" :lg="8" :xl="6">
            <el-form-item label="t('cardProductBusiness.orderSearch.completionDate')" prop="completionDates">
              <el-date-picker
                v-model="queryData.completionDates"
                type="daterange"
                range-separator="-"
                :start-placeholder="t('common.startTimeText')"
                :end-placeholder="t(t('common.endTimeText'))"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="defaultTime"
                style="width: 100%"
              />
            </el-form-item>
          </el-col> -->

          <ElCol :span="24" :md="24" :lg="8" :xl="6">
            <ElCheckbox
              v-model="queryData.selfOnly"
              :label="t('cardProductBusiness.orderSearch.viewMyself')"
            />
            <ElButton type="primary" v-track:click.btn @click="search" style="margin-left: 20px">{{
              t('common.query')
            }}</ElButton>
            <ElButton @click="reset(fromEl)" color="#E6A23C" class="reset">{{
              t('common.reset')
            }}</ElButton>
            <el-button type="primary" size="default" @click="onExport">{{
              t('common.export')
            }}</el-button>

            <span class="expand" @click="expand">
              <template v-if="isExpand">
                {{ t('common.shrink') }}<ElIcon><ArrowUp /></ElIcon>
              </template>
              <template v-else
                >{{ t('common.expand') }}<ElIcon><ArrowDown /></ElIcon>
              </template>
            </span>
          </ElCol>
        </ElRow>
      </ElForm>
    </template>
    <div class="tableTitle mt-10px">
      <span>{{ t('cardProductBusiness.orderSearch.cardProductList') }}</span>
      <ElButton
        type="primary"
        v-track:click.btn
        @click="goValet('add')"
        :loading="goValetLoading"
        >{{ t('cardProductBusiness.orderSearch.orderWrite') }}</ElButton
      >
    </div>
    <ElTable
      v-horizontal-scroll
      v-loading="tableLoading"
      :data="tableData"
      max-height="880px"
      style="width: 100%"
    >
      <ElTableColumn :label="t('tableDemo.index')" width="70" type="index" :index="indexMethod" />
      <ElTableColumn
        prop="createUser"
        :label="t('cardProductBusiness.orderSearch.customerName')"
        :minWidth="ifEn ? '200' : '110'"
      >
        <template #default="{ row }">
          {{ customerNameList && customerNameList[row.customerId] }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="orderCode"
        :label="t('cardProductBusiness.orderSearch.UMVOrderCode')"
        min-width="250"
        show-overflow-tooltip
      />
      <!-- <ElTableColumn
        prop="taskCode"
        :label="t('cardProductBusiness.orderSearch.taskCode')"
        min-width="150"
        show-overflow-tooltip
      /> -->
      <ElTableColumn
        prop="productionInfo"
        :label="t('cardProductBusiness.orderSearch.productName')"
        min-width="180"
      >
        <template #default="scope">
          <div v-if="scope.row?.orderDetailExt?.productionList?.length > 0">
            <div
              class="info-item flex-between"
              v-for="(item, index) in scope.row?.orderDetailExt?.productionList"
              :key="item.productionName"
              v-show="index < 2"
            >
              <div class="card-warp flex-index">
                <ElPopover placement="right-start" width="355" v-if="item?.imgList?.length > 0">
                  <ElImage :src="item.imgList[0]?.imageUrl" alt="" class="popover-img"
                    ><template #error> </template
                  ></ElImage>
                  <template #reference>
                    <ElImage
                      :src="item.imgList[0]?.imageUrl"
                      v-if="item.imgList[0]?.imageUrl"
                      alt=""
                      class="card-img"
                      ><template #error> </template
                    ></ElImage>
                  </template>
                </ElPopover>

                <span class="production-name">{{ item.productionName || '-' }}</span>
              </div>
              <!-- <div class="card-num">{{ item.cardCount && ` *${item.cardCount}` }}</div> -->
            </div>
            <p v-if="scope.row?.orderDetailExt?.productionList?.length > 2"
              >{{ t('cardProductBusiness.orderSearch.more') }}...</p
            >
          </div>

          <span v-else>-</span>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="productTypeCount"
        :label="t('cardProductBusiness.orderSearch.productNum')"
        :minWidth="ifEn ? '150' : '80'"
      >
        <template #default="{ row }"> {{ row.productTypeCount }} </template>
      </ElTableColumn>
      <ElTableColumn
        prop="orderTotalPrice"
        :label="t('cardProductBusiness.orderSearch.orderTotalPrice')"
        min-width="150"
      >
        <template #default="{ row }">
          {{ formatMoneyDigitsEx(row.orderTotalPrice) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="orderType"
        :label="t('cardProductBusiness.orderSearch.orderType')"
        :minWidth="ifEn ? '150' : '100'"
      >
        <template #default="{ row }">
          {{ getDictLabel('order_type', row.orderType) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="orderSource"
        :label="t('cardProductBusiness.orderSearch.source')"
        width="80"
      >
        <template #default="scope">
          <span>
            {{ scope.row?.orderSource ? orderSource[scope.row?.orderSource] : '-' }}
          </span>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="saleUserName"
        :label="t('cardProductBusiness.orderSearch.salesman')"
        width="80"
      />
      <ElTableColumn
        prop="createUserName"
        :label="t('cardProductBusiness.orderSearch.creator')"
        width="80"
      />

      <ElTableColumn
        prop="orderSource"
        :label="t('cardProductBusiness.orderSearch.completionDate')"
        width="160"
      >
        <template #default="scope">
          <span>
            {{ dispalyCompletionDate(scope.row) }}
          </span>
        </template>
      </ElTableColumn>

      <ElTableColumn
        prop="updateTime"
        :label="t('cardProductBusiness.orderSearch.updateTime')"
        min-width="160"
      />
      <ElTableColumn
        prop="orderStatus"
        :label="t('cardProductBusiness.orderSearch.OrderStatus')"
        :width="ifEn ? '120' : '80'"
      >
        <template #default="{ row }">
          {{ getDictLabel('batch_order_status', row.orderStatus) }}
        </template>
      </ElTableColumn>
      <ElTableColumn
        align="center"
        prop="operations"
        :label="t('common.operate')"
        min-width="180"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            type="danger"
            link
            v-track:click.btn
            @click="onSalesConfirmDialog(row)"
            v-if="row.orderStatus === 'SALE_CONFIRMED'"
            >{{ t('cardProductBusiness.orderSearch.saleConfirm') }}</el-button
          >

          <ElButton type="primary" link size="small" v-track:click.btn @click="handle(row)">{{
            t('cardProductBusiness.orderSearch.view')
          }}</ElButton>
          <ElButton
            v-if="row.orderSource === 'sale' && row.orderStatus === 'WAIT_SUBMIT'"
            type="primary"
            link
            size="small"
            v-track:click.btn
            @click="goValet('edit', row)"
            >{{ t('common.edit') }}</ElButton
          >
          <!-- <ElButton
            v-if="getDictLabel('batch_order_status', row.orderStatus) !== '已取消'"
            type="primary"
            link
            size="small"
            @click="cancel(row)"
            >{{ t('cardProductBusiness.orderSearch.revocation') }}</ElButton
          > -->
        </template>
      </ElTableColumn>
    </ElTable>
    <template #pagination>
      <Pagination
        v-model:page="pagination.pageNo"
        v-model:limit="pagination.pageSize"
        :total="total"
        @pagination="getList"
      />
    </template>
  </ContentWrap>
  <SalesConfirmDialog
    ref="salesConfirmDialogRef"
    :order="selectOrder"
    :customer="customerName"
    @submit="getList"
  />
</template>

<style lang="less" scoped>
h2 {
  font-size: 18px;
  font-weight: bold;
}
.el-table__body {
  .el-button + .el-button {
    margin-left: 0;
  }
  .operate {
    color: #409eff;
    width: auto;
  }
}
.reset {
  color: #fff;
}
.expand {
  padding: 0 20px;
  font-size: 13px;
  color: #606266;
  width: auto;
  cursor: pointer;
  user-select: none;
  .el-icon {
    margin-left: 2px;
    vertical-align: middle;
  }
}
.tableTitle {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  & > span {
    font-weight: bold;
  }
}
.flex {
  display: flex;
  justify-content: center;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style lang="less">
.Order_DIYOrder_ElDialog {
  .el-dialog__header {
    border: none;
  }
  .el-dialog__body {
    padding-top: 0;
  }
}
.card-warp {
  margin-top: 10px;
  display: flex;
  align-items: center;
  &:first-of-type {
    margin-top: 0;
  }
}
.info-item {
  margin-bottom: 15px;
  &:last-of-type {
    margin-bottom: 0;
  }
}
.popover-img {
  width: 330px;
  height: auto;
}
:deep(.el-image__error) {
  font-size: 8px;
}
.flex-index {
  display: flex;
  align-items: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-img {
  width: 50px;
  height: auto;
  min-height: 31px;
  margin-right: 19px;
}
</style>
