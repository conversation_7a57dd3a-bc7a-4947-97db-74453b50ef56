import { batchOrderProductStatus } from '@/api/order/types/batchOrderProductStatus.d'

import { getProductOrganizations } from '@/api/order'
import { batchDetailsApi, fastSearchApi, exportList } from '@/api/order'
import { getBatchOrderProductStatusLogs } from '@/api/order/log.api'
import download from '@/utils/download'

export function useOrderService() {
  const customers = ref([])
  /**
   * @description 查询客户信息
   */
  async function getCustomers() {
    const res = await fastSearchApi()
    customers.value = res
  }

  /**
   * @description 获取订单信息
   * @param {string} orderId 订单ID
   * @return {*}  {Promise<any>}
   */
  async function getOrderInfo(orderId: string): Promise<any> {
    const orderInfo = await batchDetailsApi(orderId)
    console.log(orderInfo)
    return orderInfo
  }

  /**导出操作 */
  async function exportOrders(queryParams: any) {
    const res = await exportList(queryParams)
    const fileName =
      decodeURI(res['headers']['content-disposition']).split('filename=')[1] ?? `${Date.now()}.xls`
    download.excel(res.data, fileName)
  }

  /**
   * @description 根据订单Id查询下单产品状态数据，并按照产品ID分组后返回
   * @param {string} orderId
   * @return {*}  {Promise<{ [key: string]: batchOrderProductStatus[] }>} 按产品Id分组后的状态日志记录 字典数据类型
   */
  async function getProductStatusLogs(orderId: string): Promise<batchOrderProductStatus[]> {
    const allLogs: batchOrderProductStatus[] = await getBatchOrderProductStatusLogs(orderId)
    return allLogs
  }
  /**
   * @description 根据订单ID获取产品相关的卡组织信息
   * @param {string} orderId
   * @return {*}
   */
  async function getProductOrgs(orderId: string): Promise<any[]> {
    const allProductOrgs = await getProductOrganizations(orderId)
    return allProductOrgs
  }

  onMounted(async () => {
    await getCustomers()
  })

  return {
    getOrderInfo,
    exportOrders,
    customers,

    getProductStatusLogs,
    getProductOrgs
  }
}
