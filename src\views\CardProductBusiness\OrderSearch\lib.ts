/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-23 10:16:01
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-11-10 09:48:29
 * @Description:
 */
const { t } = useI18n()

export const orderData = [
  {
    label: t('cardProductBusiness.orderSearch.orderCode'),
    key: 'orderCode',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.OrderStatus'),
    key: 'orderStatus',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.createTime'),
    key: 'createTime',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.orderType'),
    key: 'orderType',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.orderSource'),
    key: 'orderSource',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.updateTime'),
    key: 'updateTime',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.isUrgent'),
    key: 'urgentSign',
    value: ''
  },
  // {
  //   label: t('cardProductBusiness.orderSearch.deliveryMethod'),
  //   key: 'orderExt.mailMode',
  //   value: ''
  // },
  {
    label: t('cardProductBusiness.orderSearch.deliveryTime'),
    key: 'orderExt.deliveryTime',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.remarkNote'),
    key: 'note',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.orderTotalPrice'),
    key: 'orderTotalPrice',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.creator'),
    key: 'createUserName',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.creatorAcc'),
    key: 'createUserAccount',
    value: ''
  }
]

export const sampleOrderData = [
  {
    label: t('cardProductBusiness.orderSearch.orderCode'),
    key: 'orderCode',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.OrderStatus'),
    key: 'orderStatus',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.createTime'),
    key: 'createTime',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.orderType'),
    key: 'orderType',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.orderSource'),
    key: 'orderSource',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.updateTime'),
    key: 'updateTime',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.isUrgent'),
    key: 'urgentSign',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.deliveryMethod'),
    key: 'orderExt.mailMode',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.deliveryTime'),
    key: 'orderExt.deliveryTime',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.remarkNote'),
    key: 'note',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.orderTotalPrice'),
    key: 'orderTotalPrice',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.creator'),
    key: 'createUserName',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.creatorAcc'),
    key: 'createUserAccount',
    value: ''
  }
]

export const customData = [
  {
    label: t('cardProductBusiness.orderSearch.customerCreateOrder'),
    key: 'customerId',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.customerOrderReceiveTime'),
    key: 'receiveAt',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.customerOrderCode'),
    key: 'customerOrderCode',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.addressOfPeople'),
    key: 'orderExt.contact',
    value: ''
  },
  {
    label: t('cardProductBusiness.orderSearch.tel'),
    key: 'orderExt.tel',
    value: ''
  }
]

export const orderSourceList = {
  management: t('cardProductBusiness.orderSearch.management'),
  customer: t('cardProductBusiness.orderSearch.customer'),
  sale: t('cardProductBusiness.orderSearch.sale')
}
export const deliveryMethodEnum = {
  storage: t('cardProductBusiness.orderSearch.storage'),
  customerPick: t('cardProductBusiness.orderSearch.customerPick'),
  mail: t('cardProductBusiness.orderSearch.mail')
}

// EXCEL模板信息表头---导入产品
export const excelHeaderInfo = [
  t('cardProductBusiness.orderSearch.excelHeaderOfProductName'),
  t('cardProductBusiness.orderSearch.excelHeaderOfNum'),
  t('cardProductBusiness.orderSearch.excelHeaderOfType'),
  t('cardProductBusiness.orderSearch.excelHeaderOfDeliveryTime'),
  t('cardProductBusiness.orderSearch.excelHeaderOfDeliveryMethod'),
  t('cardProductBusiness.orderSearch.excelHeaderOfPackageMode'),
  t('cardProductBusiness.orderSearch.excelHeaderOfInnerBox'),
  t('cardProductBusiness.orderSearch.excelHeaderOfOuterBox'),
  t('cardProductBusiness.orderSearch.excelHeaderOfProductPrice'),
  t('cardProductBusiness.orderSearch.excelHeaderOfWrite31'),
  t('cardProductBusiness.orderSearch.excelHeaderOfCompanyCode'),
  t('cardProductBusiness.orderSearch.excelHeaderOfCustomerCode'),
  t('cardProductBusiness.orderSearch.excelHeaderOfRemark')
]
