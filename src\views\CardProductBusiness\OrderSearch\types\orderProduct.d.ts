/**
 * @description 下单产品数量
 * @export
 * @interface IOrderProduct
 */
export interface IOrderProduct {
  /**
   * @description 下单产品详情ID
   * @type {string}
   * @memberof IOrderProduct
   */
  orderDetailId: string

  /**
   * @description 产品名称
   * @type {string}
   * @memberof IOrderProduct
   */
  name: string

  /**
   * @description 卡款编号(GSC卡号)
   * @type {string}
   * @memberof IOrderProduct
   */
  cardCode: string

  /**
   * @description 下单数量
   * @type {number}
   * @memberof IOrderProduct
   */
  amount: number

  /**
   * @description 单价价格
   * @type {number}
   * @memberof IOrderProduct
   */
  unitPrice: number

  /**
   * @description  总价
   * @type {string}
   * @memberof IOrderProduct
   */
  total: string

  clientProductUniqueCode: string

  companyCode: string
}
