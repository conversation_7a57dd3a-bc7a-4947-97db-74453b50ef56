:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 0 !important;
}
:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 0 !important;
}
:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 0 !important;
}
.btn-style {
  height: 48px;
  letter-spacing: 1px;
  box-sizing: border-box;
  border-radius: 10px;
  color: #ffffff;
  font-size: 18px;
  background: linear-gradient(114deg, #eecd91, #d5a147);
}
.btn-style-plain {
  height: 48px;
  letter-spacing: 1px;
  box-sizing: border-box;
  border-radius: 10px;
  color: #E2A32C;
  font-size: 18px;
  border: 1px solid #E2A32C;
}
.input-bg {
  :deep(.el-input__wrapper) {
    background: #f6f6f6;
    border-radius: 10px;
    box-shadow: none;
  }
  :deep(.el-input__count-inner) {
    background: #f6f6f6;
  }
}
.form-item-h {
  :deep(.el-form-item__label) {
    height: 48px;
    line-height: 48px;
  }
}
.textarea-placeholder-h {
  :deep(.el-textarea__inner) {
    padding: 13px 11px;
  }
}
.textarea-bg{
  :deep(.el-textarea__inner) {
    background: #f6f6f6;
    border-radius: 10px;
    box-shadow: none;
    height: 126px;
  }
  :deep(.el-input__count) {
    background: #f6f6f6;
  }
}
button:focus, input:focus {
  outline: none;
}
.msg-box {
  :deep(.el-descriptions__body) {
    background: #f6f6f6;
    border: 1px solid #e6e6ea;
    padding: 32px 30px 20px 30px;
    font-size: 16px;
  }
  :deep(.el-descriptions__label) {
    color: #666666;
  }
  :deep(.el-descriptions__content) {
    color: #333333;
  }
}

.msg-box-no-bg {
  :deep(.el-descriptions__label) {
    color: #666666;
  }
  :deep(.el-descriptions__content) {
    color: #333333;
  }
}

.color-gold {
  color: #E2A32C;
}

.color-blue {
  color: blue;
}
.color-red {
  color: red;
}
.cursor-pointer {
  cursor: pointer;
}
.break-all {
  word-wrap: break-word;
  word-break: break-all;
}



// 设计师详情页面样式
.table-image-wrap {
  .imageName {
    width: 230px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}