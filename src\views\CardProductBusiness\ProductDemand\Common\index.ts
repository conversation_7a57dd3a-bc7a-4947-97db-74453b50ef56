import { ElMessageBox } from 'element-plus'
import router from '@/router/index'
import { dialogEnum, IMsg, diaMsg } from './type'
import { useDictStoreWithOut } from '@/store/modules/dict'
const { t } = useI18n()
// 是否需求关闭
export const isDemandClose = (type: number) => {
  return type === 14
}

// 现有阶段对应之前阶段划分版本
export const makeCardArr = [0, 4] // 之前的 0
export const designArr = [1, 5, 6, 8] // 之前的 1
export const draftArr = [2, 9, 10, 11] // 之前的 2
export const sampleCardArr = [3, 12, 13] // 之前的 3

// 获取对应阶段值
export const getOldDemandValve = (type: number) => {
  const value = {
    label: '--',
    phase: undefined
  } as any
  if (makeCardArr.includes(type)) {
    value.label = t('cardProductService.productDemand.common.cardRequirements')
    value.phase = 0
  }
  if (designArr.includes(type)) {
    value.label = t('cardProductService.productDemand.common.designScheme')
    value.phase = 1
  }
  if (draftArr.includes(type)) {
    value.label = t('cardProductService.productDemand.common.sampleScheme')
    value.phase = 2
  }
  if (sampleCardArr.includes(type)) {
    value.label = t('cardProductService.productDemand.common.sampleCardApplication')
    value.phase = 3
  }
  if (isDemandClose(type)) {
    value.label = t('cardProductService.productDemand.common.requirementClosure')
    value.phase = 4
  }
  return value
}

// 多文件分隔符
export const spliceText = '|%fgf%|'

// 多文件数组转字符串
export const getFileString = (fileList) => {
  const obj = {
    nameStr: '',
    urlStr: ''
  }
  const nameArr: string[] = []
  const urlArr: string[] = []
  fileList.forEach((item) => {
    if (item.status === 'success' && item.url) {
      nameArr.push(item.name)
      urlArr.push(item.url)
    }
  })
  obj.nameStr = nameArr.join(spliceText)
  obj.urlStr = urlArr.join(spliceText)
  return obj
}
// 提示
export const tipConfirm = () => {
  return new Promise((resolve, reject) => {
    const { t } = useI18n()
    ElMessageBox.confirm(
      t('cardProductService.productDemand.common.doYouWantToPerformThisOperation'),
      t('cardProductService.productDemand.common.prompt'),
      {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    ).then(async () => {
      try {
        resolve('')
      } catch (e) {
        reject(e)
      }
    })
  })
}

// 打开弹窗
export const getOpenInfo = (type: string, obj: any = {}, msg: IMsg = diaMsg) => {
  const openInfo = {
    isDiaLogShow: false,
    diaLogTitle: '',
    diaConfirmTip: '',
    diaData: {},
    openType: ''
  }
  switch (type) {
    // 查看3D外链
    case 'viewOutLink':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.view_3DFiles')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.viewOutLink
      break
    // 查看3D文件
    case 'view3D':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.view_3DFiles')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.view3D
      break
    // 提示弹窗
    case 'tipConfirm':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = msg.title
      openInfo.diaConfirmTip = msg.tip
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.tipConfirm
      break
    // 确认设计方案
    case 'verifyScheme':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.confirmThePlan')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.verifyScheme
      break
    // 确认稿样方案
    case 'verifyDraftScheme':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.confirmThePlan')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.verifyDraftScheme
      break
    // 确认样卡方案
    case 'verifySampleScheme':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.confirmThePlan')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.verifySampleScheme
      break
    // 查看设计方案
    case 'viewDesign':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.viewDesignProposal')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.viewDesign
      break
    // 查看稿样方案
    case 'viewDraft':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.viewDraftProposal')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.viewDraft
      break
    // 查看回执信息
    case 'viewBackMsg':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.viewReceiptInformation')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.backMsg
      break
    // 新增
    case 'add':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t(
        'cardProductService.productDemand.common.initiateProductRequirements'
      )
      openInfo.openType = dialogEnum.demand
      break
    // 编辑
    case 'edit':
      openInfo.isDiaLogShow = true
      openInfo.diaLogTitle = t('cardProductService.productDemand.common.editProductRequirements')
      openInfo.diaData = obj
      openInfo.openType = dialogEnum.editDemand
      break
    // 订单详情
    case 'orderDetail':
      router.push({
        // path: '/makeCardService/sampleCardDetail',
        name: 'ProductDemandOrderDetail',
        query: {
          orderId: obj.orderId
        }
      })
      break
    // 查看
    case 'view':
      router.push({
        //path: '/makeCardService/detail',
        name: 'ProductDemandDetail',
        query: {
          makeCardRequirementInfoId: obj.makeCardRequirementInfoId,
          activeName: obj.activeName
        }
      })
      break
  }
  return openInfo
}

const dataURLtoBlob = (dataUrl) => {
  const arr = dataUrl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

const blobToFile = (theBlob, fileName) => {
  theBlob.lastModifiedDate = new Date() // 文件最后修改日期
  theBlob.name = fileName
  return new File([theBlob], fileName, { type: theBlob.type, lastModified: Date.now() })
}

export const base64ToFile = (base64, fileName) => {
  const blob = dataURLtoBlob(base64)
  const file = blobToFile(blob, fileName)
  return file
}

// 生成图片名称
export const createImgName = () => {
  const date = new Date()
  return `img-${date?.getFullYear()}-${
    date?.getMonth() + 1
  }-${date?.getDate()}-${Date.now().toString(36)}`
}

/**
 * 根据数据获取数据字段中的内容
 * @param mapKey 数据字典中的dictType
 * @param value dictType中的value
 */
export const getDistMapData = (mapKey, value?) => {
  const dictStore = useDictStoreWithOut()
  if (value !== undefined) {
    const arr = dictStore.getDictMap[mapKey]?.find((item) => {
      return item.value === value
    })
    return arr?.label || '---'
  } else {
    return dictStore.getDictMap[mapKey]
  }
}
