import { productType } from '@/api/makeCardService/types'
const { t } = useI18n()
export interface IMsg {
  title: string
  tip: string
}

export const diaMsg: IMsg = {
  title: t('cardProductService.productDemand.common.prompt'),
  tip: t('cardProductService.productDemand.common.doYouWantToPerformThisOperation')
}

// 需求来源
export const demandSource = [
  {
    value: 1,
    label: t('cardProductService.productDemand.common.client')
  },
  {
    value: 2,
    label: t('cardProductService.productDemand.common.managementPlatform')
  }
]

// 弹窗枚举
export enum dialogEnum {
  backMsg = 'backMsg', // 客户回执
  viewDesign = 'viewDesign', // 查看设计
  demand = 'demand', // 需求列表
  editDemand = 'editDemand', // 编辑需求列表
  viewDraft = 'viewDraft', // 查看稿样文件
  verifyScheme = 'verifyScheme', // 确认设计方案
  verifyDraftScheme = 'verifyDraftScheme', // 确认稿样方案
  verifySampleScheme = 'verifySampleScheme', // 确认样卡方案
  tipConfirm = 'tipConfirm', // 确认弹窗
  view3D = 'view3D', // 查看3D文件
  viewOutLink = 'viewOutLink' // 查看3D外链
}

// 制卡需求
import { useDictStoreWithOut } from '@/store/modules/dict'

const getDistMapData = (mapKey) => {
  const dictStore = useDictStoreWithOut()
  return dictStore.getDictMap[mapKey]
}
export const makeCardRankData = [
  {
    label: t('cardProductService.productDemand.common.unionPay'),
    id: 1,
    value: getDistMapData('makecard_organ_unionpay_type')
  },
  {
    label: 'VISA',
    id: 2,
    value: getDistMapData('makecard_organ_visa_type')
  },
  {
    label: 'MC',
    id: 3,
    value: getDistMapData('makecard_organ_mc_type')
  },
  {
    label: 'AMEX',
    id: 4,
    value: getDistMapData('makecard_organ_amex_type')
  },
  {
    label: 'JCB',
    id: 5,
    value: getDistMapData('makecard_organ_jcb_type')
  },
  {
    label: t('cardProductService.productDemand.common.other'),
    id: 6,
    value: getDistMapData('makecard_organ_other_type')
  }
]

/**
 * @description 产品需求用于选择产品对象
 * @export
 * @interface prodcut
 */
export interface demandProdcut {
  /**
   * @description 产品Id
   * @type {string}
   * @memberof prodcut
   */
  id: string

  /**
   * @description 产品名称
   * @type {string}
   * @memberof prodcut
   */
  name: string

  /**
   * @description 产品类型
   * @type {productType}
   * @memberof prodcut
   */
  type: productType
}

/**
 * @description 产品类型数组定义
 * @export
 * @enum {Array}
 */
export const productTypeList = [
  {
    value: productType.batch,
    label: t('cardProductService.productDemand.common.batchCardProducts')
  },
  { value: productType.diy, label: t('cardProductService.productDemand.common.diyProducts') }
]
