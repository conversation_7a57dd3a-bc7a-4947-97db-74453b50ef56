<template>
  <el-descriptions class="mt-20px msg-box" :column="1" v-loading="loading">
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.requirementNumber')
        }}</div>
        <div class="ml-20px content"
          >{{ props.list.makeCardNumber }}
          <!-- <el-button
            type="primary"
            @click="chatService"
            :loading="isImLoading"
            :disabled="isDemandClose(props.list.makeCardRequirementInfoPhase)"
            v-if="visibleIMButton"
          >
            在线沟通
          </el-button> -->
        </div>
      </div>
    </el-descriptions-item>

    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.customerName')
        }}</div>
        <div class="ml-20px content break-all">{{ props.list.makeCardRequirementInfoCname }}</div>
      </div>
    </el-descriptions-item>

    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.customerAccount')
        }}</div>
        <div class="ml-20px content break-all">{{
          props.list.makeCardRequirementInfoUserName
        }}</div>
      </div>
    </el-descriptions-item>
    <!-- <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.productType')
        }}</div>
        <div class="ml-20px content break-all">
          <el-tag
            :type="props.list.productType == productType.batch ? 'success' : 'danger'"
            size="small"
            >{{ productTypeFormater(props.list.productType) }}
          </el-tag>
        </div>
      </div>
    </el-descriptions-item> -->
    <!-- <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.productName')
        }}</div>
        <div class="ml-20px content break-all">
          <el-text tag="ins">{{ props.list.productName }}</el-text>
        </div>
      </div>
    </el-descriptions-item> -->
    <!-- <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">产品主题</div>
        <div class="ml-20px content break-all">
          <el-text tag="ins">{{ props.list.themeName }}</el-text>
        </div>
      </div>
    </el-descriptions-item> -->
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.requirementTitle')
        }}</div>
        <div class="ml-20px content break-all">{{ props.list.makeCardRequirementInfoTitle }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.requirementDescription')
        }}</div>
        <div class="ml-20px content break-all">{{
          props.list.makeCardRequirementInfoRemark || '--'
        }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.cardBin')
        }}</div>
        <div class="ml-20px content break-all">{{ props.list.makeCardRequirementBin || '--' }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.customerUploadsFiles')
        }}</div>
        <div class="ml-20px content break-all">
          <el-button
            class="mb-2"
            type="primary"
            v-if="props?.list?.makeCardRequirementInfoAttachmentsArr?.length > 1"
            @click="downLoadAll(props.list.makeCardRequirementInfoAttachmentsArr)"
            >{{
              t('cardProductService.productDemand.components.dialogModule.downloadAll')
            }}</el-button
          >
          <div
            v-for="(item, index) in props.list.makeCardRequirementInfoAttachmentsArr"
            :key="'file' + index"
          >
            <span
              style="color: rgb(226, 163, 44); cursor: pointer"
              @click="perviewImage(item.url)"
              >{{ item.name }}</span
            >
            <el-button
              class="color-gold ml-20px cursor-pointer"
              v-if="item.url"
              link
              :loading="downBtnLoading.includes(index)"
              @click="downFile(item.url, item.name, index)"
              >{{ t('cardProductService.productDemand.components.download') }}
            </el-button>
          </div>
          <div v-if="props?.list?.makeCardRequirementInfoAttachmentsArr?.length == 0">--</div>
        </div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.submissionTime')
        }}</div>
        <div class="ml-20px content">{{ props.list.makeCardRequirementInfoCreateDate }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.expectedSubmissionDate')
        }}</div>
        <div class="ml-20px content">{{ props.list.makeCardRequirementInfoWithdraw || '--' }}</div>
      </div>
    </el-descriptions-item>
  </el-descriptions>

  <!-- IM即时通讯 -->
  <keep-alive>
    <IMessage
      v-if="isIMShow"
      :requirementId="props.list.makeCardRequirementInfoId"
      :isIMShow="isIMShow"
      @handle-close="closeIM"
    />
  </keep-alive>
</template>

<script lang="ts" setup>
import { downloadFileApi } from '@/api/makeCardService/index'
import IMessage from './IMessage/IMessage.vue'
import { isDemandClose } from '../Common/index'
import { useRequirementService } from '../hooks/useRequirementService'
import { productType } from '@/api/makeCardService/types'
//import * as designApi from '@/api/makeCardService/design/index'

const { productTypeFormater } = useRequirementService()
const { t } = useI18n()
const props = defineProps({
  list: {
    type: Object,
    default: () => {}
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 是否显示IM
let isIMShow = ref(false)

// IM按钮Loading
let isImLoading = ref(false)

// 在线沟通
const chatService = async () => {
  try {
    isImLoading.value = true
    // // 判断当前用户是否在允许聊天范围
    // await designApi.isImScope({
    //   makeCardDesignSchemeInfoNumber: props.list.makeCardRequirementInfoId,
    //   makeCardDesignInfoIdStylistType: 0
    // })
    isIMShow.value = true
  } catch (err) {
  } finally {
    isImLoading.value = false
  }
}
const closeIM = () => {
  isIMShow.value = false
}
//是否显示在线沟通按钮
const visibleIMButton = computed<boolean>(() => {
  const { makeCardRequirementInfoPhase } = props.list //产品需求的阶段值
  console.log(makeCardRequirementInfoPhase)
  //产品需的状态判断
  return (
    makeCardRequirementInfoPhase === 0 || //需求提交
    makeCardRequirementInfoPhase === 4 || //需求待确认
    makeCardRequirementInfoPhase === 5 || //设计需求待分配
    makeCardRequirementInfoPhase === 9 //稿样需求待分配
  )
})

let downBtnLoading = ref<number[]>([])

// 下载数据
const downFile = async (fileUrl, name, index) => {
  let fileName = ''
  if (name) {
    fileName = name
  } else {
    fileName = await fileNameFormatter(fileUrl)
  }
  try {
    downBtnLoading.value.push(index)
    const formData: FormData = new FormData()
    formData.append('makeCardFileName', fileUrl)
    const res = await downloadFileApi(formData)
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    downBtnLoading.value.splice(downBtnLoading.value.indexOf(index), 1)
  }
}

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.lastIndexOf('-') + 1, fileName.length)
}

// 预览
import envController from '@/controller/envController'

const perviewImage = (url) => {
  if (url == '') return
  let fileType = url.split('.').pop()
  let fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf', 'webp']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning('只支持图片和pdf预览')
  }
  window.open(`${envController.getOssUrl()}/${url}`, '_blank')
}
// 全部下载
const downLoadAll = (data) => {
  const downLoadList = JSON.parse(JSON.stringify(data))
  console.log('data===', data)
  downLoadList.forEach((item, index) => {
    downFile(item.url, item.name, index)
  })
}
</script>

<style lang="less" scoped>
@import url('../Common/common.less');
.desc-column {
  width: 200px;
  text-align: right;
  color: #666666;
}
.content {
  color: #333333;
  flex: 1;
}
</style>
