<template>
  <div class="make-card-demand form-item-h">
    <el-form
      ref="basicDemandRef"
      :model="basicDemand"
      :label-width="ifEn ? '200px' : '80px'"
      :rules="rules"
    >
      <!-- <el-form-item label="产品类型">
        <el-radio-group v-model="basicDemand.productType" @change="onProductTypeChanged">
          <el-radio :label="1">批卡产品</el-radio>
          <el-radio :label="2">DIY产品</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item
        :label="t('cardProductService.productDemand.components.demand.requirementType')"
        prop="makeCardRequirementRtype"
      >
        <el-radio-group v-model="basicDemand.makeCardRequirementRtype">
          <el-radio label="0">{{
            t('cardProductService.productDemand.components.demand.designRequirements')
          }}</el-radio>
          <el-radio label="1">
            {{
              t('cardProductService.productDemand.components.demand.sampleRequirements')
            }}</el-radio
          >
        </el-radio-group>
      </el-form-item>

      <!-- <el-form-item label="产品名称">
        <el-select
          v-model="selectdProdcut"
          value-key="id"
          allow-create
          filterable
          remote
          reserve-keyword
          default-first-option
          :placeholder="
            t('cardProductService.productDemand.components.demand.pleaseEnterTheProductSelection')
          "
          :remote-method="queryProducts"
          class="input input-bg"
          style="height: 32px !important"
        >
          <el-option v-for="item in products" :key="item.id" :label="item.name" :value="item" />
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="产品主题" prop="themeName">
        <el-input
          v-model="basicDemand.themeName"
          class="input input-bg"
          clearable
          maxlength="50"
          show-word-limit
          placeholder="请输入产品主题"
        />
      </el-form-item> -->

      <el-form-item
        :label="t('cardProductBusiness.orderApproval.customerName')"
        prop="makeCardRequirementInfoCId"
      >
        <el-select
          v-model="basicDemand.makeCardRequirementInfoCId"
          :placeholder="t('productsShow.batchCardProduct.customerNamePlaceholder')"
          filterable
          clearable
          :loading="loadingCustom"
          style="width: 400px"
          @change="customChange"
          @clear="customChange"
        >
          <el-option
            v-for="item in optionsCustom"
            :key="item.customerId"
            :label="item.customerName"
            :value="item.customerId"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        :label="t('cardProductService.productDemand.components.demand.requirementTitle')"
        prop="makeCardRequirementInfoTitle"
      >
        <el-input
          v-model="basicDemand.makeCardRequirementInfoTitle"
          class="input input-bg"
          clearable
          maxlength="50"
          show-word-limit
          :placeholder="t('cardProductService.productDemand.components.demand.pleaseEnterATitle')"
        />
      </el-form-item>
      <el-form-item
        :label="t('cardProductService.productDemand.components.demand.requirementDescription')"
        prop="makeCardRequirementInfoRemark"
      >
        <el-input
          type="textarea"
          v-model="basicDemand.makeCardRequirementInfoRemark"
          class="textarea textarea-bg break-all textarea-placeholder-h"
          clearable
          maxlength="1000"
          show-word-limit
          resize="none"
          :placeholder="
            t(
              'cardProductService.productDemand.components.demand.pleaseEnterARequirementDescription'
            )
          "
        />
      </el-form-item>

      <el-form-item
        :label="t('cardProductService.productDemand.components.demand.cardBin')"
        prop="makeCardRequirementBin"
      >
        <el-input
          v-model="basicDemand.makeCardRequirementBin"
          class="input input-bg"
          clearable
          maxlength="50"
          show-word-limit
          :placeholder="
            t('cardProductService.productDemand.components.demand.pleaseEnterTheCardBin')
          "
        />
      </el-form-item>
      <el-form-item
        :label="t('cardProductService.productDemand.components.demand.customerDocuments')"
        prop="makeCardRequirementInfoAttachments"
      >
        <Upload
          ref="uploadBasicRef"
          :showFileList="showFileList"
          :btnText="t('cardProductService.productDemand.components.demand.selectFile')"
          :uploadTip="t('cardProductService.productDemand.components.demand.uploadTips')"
          :limit="8"
          :multiple="true"
          accept="*"
          :limitFormat="['*']"
          :maxSize="1024 * 1024 * 100"
        />
      </el-form-item>
    </el-form>
    <div class="footer mt-40px">
      <el-button size="large" @click="handleClose">{{ t('common.cancel') }}</el-button>
      <el-button type="primary" size="large" :loading="isAddDemandLoading" @click="handleSubmit">{{
        t('common.ok')
      }}</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import Upload from '../Upload.vue'
import * as makeCardApi from '@/api/makeCardService/index'
import type { makeCardListAddType } from '@/api/makeCardService/types'
import { productType } from '@/api/makeCardService/types'
import * as otherApi from '@/api/makeCardService/other'

import { dialogEnum, demandProdcut } from '../../Common/type'
import { spliceText, getFileString } from '../../Common/index'
import { useRequirementService } from '../../hooks/useRequirementService'
const { searchProdcuts } = useRequirementService()

const { t, ifEn } = useI18n()
const props = defineProps({
  diaData: {
    type: Object,
    default: () => {}
  },
  openType: {
    type: String,
    default: ''
  }
})
// 解构传入的数据
const { openType } = toRefs(props)

const emit = defineEmits(['cancel', 'get-list'])
const handleClose = () => {
  resetForm()
  emit('cancel')
}

const basicDemandRef = ref<FormInstance>()

const uploadBasicRef = ref()

// 客户名称
const optionsCustom = ref<any>([])
let loadingCustom = ref(false)

const remoteGetCustom = async (query: string) => {
  loadingCustom.value = true
  try {
    const { data } = await otherApi.getCustomerName(query, 1)
    optionsCustom.value = data
  } finally {
    loadingCustom.value = false
  }
}

// 选择客户
const customChange = (val) => {
  if (val) {
    let obj = optionsCustom.value.find((item) => {
      return item.customerId === val
    })
    basicDemand.makeCardRequirementInfoCname = obj.customerName
    basicDemand.makeCardRequirementInfoCcode = obj.customerCode
  } else {
    basicDemand.makeCardRequirementInfoCname = ''
    basicDemand.makeCardRequirementInfoCcode = ''
  }
}

// 校验规则
const rules = reactive<FormRules>({
  makeCardRequirementInfoCId: [
    {
      required: true,
      message: t('cardProductService.productDemand.components.demand.rulesTips'),
      trigger: 'blur'
    }
  ],
  makeCardRequirementInfoTitle: [
    {
      required: true,
      message: t('cardProductService.productDemand.components.demand.rulesTips'),
      trigger: 'blur'
    }
  ],
  makeCardRequirementRtype: [
    {
      required: true,
      message: t('cardProductService.productDemand.components.demand.rulesTips'),
      trigger: 'blur'
    }
  ],
  makeCardRequirementInfoRemark: [
    {
      required: true,
      message: t('cardProductService.productDemand.components.demand.rulesTips'),
      trigger: 'blur'
    }
  ],
  makeCardRequirementInfoAttachments: [{ required: false, message: '', trigger: 'blur' }],
  themeName: [
    {
      required: true,
      message: t('cardProductService.productDemand.components.demand.rulesTips'),
      trigger: 'blur'
    }
  ]
})

let isAddDemandLoading = ref(false)

const handleSubmit = async () => {
  if (!basicDemandRef.value) return
  await basicDemandRef.value.validate(async (valid) => {
    if (valid) {
      isAddDemandLoading.value = true
      try {
        const loading = ElLoading.service({
          lock: true,
          text: `Loading`,
          background: 'rgba(255, 255, 255, 0.3)'
        })
        await uploadBasicRef.value
          .submitFile(loading)
          .then(async (res) => {
            uploadSuccess(res)
            if (openType.value === dialogEnum.editDemand) {
              let editBasicDemand = {
                makeCardRequirementInfoId: props.diaData.makeCardRequirementInfoId,
                ...basicDemand
              }
              await makeCardApi.editMakeCardApi(editBasicDemand)
              ElMessage.success(
                t('cardProductService.productDemand.components.demand.editSuccessful')
              )
            } else if (openType.value === dialogEnum.demand) {
              await makeCardApi.addMakeCardApi(basicDemand)
              ElMessage.success(
                t('cardProductService.productDemand.components.demand.successfullyAdded')
              )
            }
            loading.close()
            handleClose()
            emit('get-list')
          })
          .catch((_err) => {
            loading.close()
          })
      } finally {
        isAddDemandLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  if (!basicDemandRef.value) return
  basicDemandRef.value.resetFields()
}

const uploadSuccess = (fileList) => {
  let obj = getFileString(fileList)
  basicDemand.makeCardRequirementInfoAttachments = obj.nameStr
  basicDemand.makeCardRequirementInfoAttachmentseos = obj.urlStr
}

// 需求表单
let basicDemand = reactive<makeCardListAddType>({
  makeCardRequirementInfoSource: 2,
  makeCardRequirementInfoTitle: '',
  makeCardRequirementInfoRemark: '',
  makeCardRequirementInfoAttachments: '',
  makeCardRequirementInfoAttachmentseos: '',
  makeCardRequirementBin: '',
  makeCardRequirementRtype: '0', // 字符串类型 0为设计需求，1为稿样需求，该字段必选，默认为设计需求

  productId: '',
  productName: '',
  productType: productType.batch,
  themeName: '',
  makeCardRequirementInfoCId: '',
  makeCardRequirementInfoCname: '',
  makeCardRequirementInfoCcode: '',
  imTenantId: ''
})

let showFileList = ref<any>([])

// 更新数据
const upData = () => {
  Object.keys(basicDemand).forEach((key) => {
    basicDemand[key] = props.diaData[key]
  })

  if (props.diaData.makeCardRequirementInfoAttachments) {
    props.diaData.makeCardRequirementInfoAttachments.split(spliceText).forEach((item) => {
      let file = {
        name: item,
        url: '',
        status: 'success',
        placeholder: t('cardProductService.productDemand.components.demand.uploadSuccessful')
      }
      showFileList.value.push(file)
    })
  }
  if (props.diaData.makeCardRequirementInfoAttachmentseos) {
    props.diaData.makeCardRequirementInfoAttachmentseos.split(spliceText).forEach((item, index) => {
      showFileList.value[index].url = item
    })
  }
}

// 监听页面展示 清空校验规则等
onMounted(() => {
  if (openType.value === dialogEnum.editDemand) {
    upData()
  }
  remoteGetCustom('')
})
//产品类型变更
function onProductTypeChanged() {
  products.value = []
  basicDemand.productName = ''
  basicDemand.productId = ''
}

//需求选择的产品
// let selectdProdcut = computed<demandProdcut>({
//   get() {
//     const product: demandProdcut = {
//       id: basicDemand.productId ?? '',
//       name: basicDemand.productName ?? '',
//       type: basicDemand.productType ?? productType.batch
//     }
//     if (products.value.some((item) => item.id == product.id) == false) {
//       console.log('products', products.value)
//       products.value.push(product)
//     }
//     return product
//   },
//   set(val: demandProdcut | string) {
//     if (typeof val === 'string') {
//       products.value.push({ id: '', name: val, type: basicDemand.productType })
//       basicDemand.productId = undefined
//       basicDemand.productName = val
//     } else {
//       basicDemand.productId = val.id
//       basicDemand.productName = val.name
//     }
//   }
// })
//产品列表
const products = ref<demandProdcut[]>([])
//查询产品数据
async function queryProducts(query: string) {
  products.value = await searchProdcuts(basicDemand.productType, query)
}

/** 子组件数据暴露start */
defineExpose({
  resetForm,
  upData
})
/** 子组件数据暴露end */
</script>

<style scoped lang="less">
@import url('../../Common/common.less');
.make-card-demand {
  .table-checkbox {
    flex-wrap: wrap;
  }
  .footer {
    display: flex;
    justify-content: flex-end;
  }
  .btn-submit {
    width: 122px;
  }
  .input {
    width: 100%;
    height: 48px;
  }
  .textarea {
    width: 100%;
    height: 126px;
  }
}
</style>
