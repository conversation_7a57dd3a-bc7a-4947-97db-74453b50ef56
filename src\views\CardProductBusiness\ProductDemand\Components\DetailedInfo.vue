<template>
  <el-descriptions class="mt-20px msg-box" :column="1">
    <!-- <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.relatedProjects')
        }}</div>
        <div class="ml-20px content break-all">{{
          props.list.makeCardRequirementInfoRelevancName || '--'
        }}</div>
      </div>
    </el-descriptions-item> -->
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.requirementType')
        }}</div>
        <div class="ml-20px content break-all">{{
          makeCardRequirementRtypeEnum[props.list.makeCardRequirementRtype]
        }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.expectedSubmissionDate')
        }}</div>
        <div class="ml-20px content">{{ props.list.makeCardRequirementInfoWithdraw || '--' }}</div>
      </div>
    </el-descriptions-item>
    <template v-if="false">
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.components.cardTypes')
          }}</div>
          <div class="ml-20px content break-all">{{
            props.list.makeCardRequirementInfoType || '--'
          }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.components.cardType')
          }}</div>
          <div class="ml-20px content break-all">{{
            props.list.makeCardRequirementInfoSpecies || '--'
          }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.components.cardOrganizationAndCardLevel')
          }}</div>
          <div
            class="ml-20px content break-all"
            v-if="props?.list?.makeCardRequirementInfoRankArr?.length > 0"
          >
            <div
              v-for="(item, index) in props.list.makeCardRequirementInfoRankArr"
              :key="'rank' + index"
            >
              {{ item.label }}: <span class="ml-10px">{{ item.value }}</span>
            </div>
          </div>
          <div class="ml-20px content" v-else> -- </div>
        </div>
      </el-descriptions-item>
    </template>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.otherInstructions')
        }}</div>
        <div class="ml-20px content break-all">{{
          props.list.makeCardRequirementInfoOtherRemark || '--'
        }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item label-align="center">
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.otherAttachments')
        }}</div>
        <div class="ml-20px content break-all">
          <el-button
            class="mb-2"
            type="primary"
            v-if="props?.list?.makeCardRequirementInfoOtherAttachmentsArr?.length > 1"
            @click="downLoadAll(props.list.makeCardRequirementInfoOtherAttachmentsArr)"
            >{{
              t('cardProductService.productDemand.components.dialogModule.downloadAll')
            }}</el-button
          >
          <div
            v-for="(item, index) in props.list.makeCardRequirementInfoOtherAttachmentsArr"
            :key="'file' + index"
          >
            <span
              style="color: rgb(226, 163, 44); cursor: pointer"
              @click="perviewImage(item.url)"
              >{{ item.name }}</span
            >
            <el-button
              class="color-gold ml-20px cursor-pointer"
              v-if="item.url"
              link
              :loading="downBtnLoading.includes(index)"
              @click="downFile(item.url, item.name, index)"
              >{{ t('cardProductService.productDemand.components.download') }}
            </el-button>
          </div>
          <div v-if="props?.list?.makeCardRequirementInfoOtherAttachmentsArr?.length == 0">--</div>
        </div>
      </div>
    </el-descriptions-item>
  </el-descriptions>
</template>

<script lang="ts" setup>
import { downloadFileApi } from '@/api/makeCardService/index'
const { t } = useI18n()
const props = defineProps({
  list: {
    type: Object,
    default: () => {}
  },
  loading: {
    type: Boolean,
    default: false
  }
})

let downBtnLoading = ref<number[]>([])

// 需求类型枚举
const makeCardRequirementRtypeEnum = [
  t('cardProductService.productDemand.components.designRequirements'),
  t('cardProductService.productDemand.components.sampleRequirements')
]

// 下载数据
const downFile = async (fileUrl, name, index) => {
  let fileName = ''
  if (name) {
    fileName = name
  } else {
    fileName = await fileNameFormatter(fileUrl)
  }
  try {
    downBtnLoading.value.push(index)
    const formData: FormData = new FormData()
    formData.append('makeCardFileName', fileUrl)
    const res = await downloadFileApi(formData)
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    downBtnLoading.value.splice(downBtnLoading.value.indexOf(index), 1)
  }
}

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.lastIndexOf('-') + 1, fileName.length)
}

// 预览
import envController from '@/controller/envController'
const perviewImage = (url) => {
  if (url == '') return
  let fileType = url.split('.').pop()
  let fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf', 'webp']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning('只支持图片和pdf预览')
  }
  window.open(`${envController.getOssUrl()}/${url}`, '_blank')
}
// 全部下载
const downLoadAll = (data) => {
  const downLoadList = JSON.parse(JSON.stringify(data))
  console.log('data===', data)
  downLoadList.forEach((item, index) => {
    downFile(item.url, item.name, index)
  })
}
</script>
<style lang="less" scoped>
@import url('../Common/common.less');
.desc-column {
  width: 200px; //120px;
  text-align: right;
  color: #666666;
}
.content {
  color: #333333;
  flex: 1;
}
</style>
