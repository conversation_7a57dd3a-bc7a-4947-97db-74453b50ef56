<template>
  <el-dialog
    :title="props.diaLogTitle"
    :model-value="isDiaLogShow"
    :before-close="handleClose"
    :close-on-click-modal="false"
    append-to-body
    :top="props.top"
    :style="props.diaStyle"
  >
    <!-- 需求列表 -->
    <div v-if="openType === dialogEnum.demand || openType === dialogEnum.editDemand">
      <Demand
        ref="DemandRef"
        @cancel="handleClose"
        @get-list="getList"
        :diaData="props.diaData"
        :openType="props.openType"
      />
    </div>

    <!-- 回执信息 -->
    <div v-if="openType === dialogEnum.backMsg">
      <BackMsg :diaData="props.diaData" @cancel="handleClose" />
    </div>
    <!-- 查看设计方案 -->
    <div v-if="openType === dialogEnum.viewDesign">
      <ViewDesign
        :diaData="props.diaData"
        :makeCardDetail="props.makeCardDetail"
        @get-list="getList"
        @cancel="handleClose"
      />
    </div>
    <!-- 查看稿样方案 -->
    <div v-if="openType === dialogEnum.viewDraft">
      <ViewDraft
        :diaData="props.diaData"
        :makeCardDetail="props.makeCardDetail"
        @get-list="getList"
        @cancel="handleClose"
      />
    </div>

    <!-- 确认设计方案 -->
    <div v-if="openType === dialogEnum.verifyScheme">
      <VerifyScheme
        :makeCardDetail="props.makeCardDetail"
        :diaData="props.diaData"
        @get-list="getList"
        @cancel="handleClose"
      />
    </div>
    <!-- 确认稿样方案 -->
    <div v-if="openType === dialogEnum.verifyDraftScheme">
      <VerifyDraftScheme
        :makeCardDetail="props.makeCardDetail"
        :diaData="props.diaData"
        @get-list="getList"
        @cancel="handleClose"
      />
    </div>
    <!-- 确认样卡方案 -->
    <div v-if="openType === dialogEnum.verifySampleScheme">
      <VerifySampleScheme
        :makeCardDetail="props.makeCardDetail"
        :diaData="props.diaData"
        @get-list="getList"
        @cancel="handleClose"
      />
    </div>

    <!-- 确认弹窗 -->
    <div v-if="openType === dialogEnum.tipConfirm">
      <TipConfirm
        :diaConfirmTip="props.diaConfirmTip"
        @cancel="handleClose"
        @tip-confirm="tipConfirm"
      />
    </div>

    <!-- 查看3D文件 -->
    <div v-if="openType === dialogEnum.view3D">
      <View3D :diaData="props.diaData" @cancel="handleClose" />
    </div>
    <!-- 查看3D外链 -->
    <div v-if="openType === dialogEnum.viewOutLink">
      <ViewOutLink :diaData="props.diaData" @cancel="handleClose" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import Demand from './Demand/Index.vue'
import BackMsg from './DialogModule/BackMsg.vue'
import ViewDesign from './DialogModule/ViewDesign.vue'
import ViewDraft from './DialogModule/ViewDraft.vue'
import VerifyScheme from './DialogModule/VerifyScheme.vue'
import VerifyDraftScheme from './DialogModule/VerifyDraftScheme.vue'
import VerifySampleScheme from './DialogModule/VerifySampleScheme.vue'
import TipConfirm from './DialogModule/TipConfirm.vue'
import View3D from './DialogModule/View3D.vue'
import ViewOutLink from './DialogModule/ViewOutLink.vue'
import { dialogEnum } from '../Common/type'

const props = defineProps({
  isDiaLogShow: {
    type: Boolean,
    default: false,
    required: true
  },
  diaLogTitle: {
    type: String,
    default: ''
  },
  diaConfirmTip: {
    type: String,
    default: ''
  },
  openType: {
    type: String,
    default: ''
  },
  top: {
    type: String,
    default: ''
  },
  diaStyle: {
    type: String,
    default: 'width: 1000px; min-width: 800px'
  },
  diaData: {
    type: Object,
    default: () => {}
  },
  makeCardDetail: {
    type: Object,
    default: () => {}
  }
})
// 解构传入的弹窗开关
const { isDiaLogShow, openType } = toRefs(props)

const DemandRef = ref()

const emit = defineEmits(['handle-close', 'tip-confirm', 'get-list'])
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

const handleClose = () => {
  wsCache.delete('quickstart')
  resetForm()
  emit('handle-close')
}

const tipConfirm = () => {
  emit('tip-confirm', props.diaData.makeCardRequirementInfoId)
}

const getList = () => {
  emit('get-list')
}

const resetForm = () => {
  if (!DemandRef.value) return
  DemandRef.value.resetForm()
}
</script>

<style scoped lang="less"></style>
