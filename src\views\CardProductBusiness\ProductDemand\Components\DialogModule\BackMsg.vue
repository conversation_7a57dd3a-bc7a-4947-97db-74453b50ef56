<template>
  <el-descriptions class="ml-20px msg-box-no-bg" :column="1">
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.dialogModule.receiptInformation')
        }}</div>
        <div class="ml-20px content break-all" v-if="props.diaData.designSchemeInfoReceipt">{{
          props.diaData.designSchemeInfoReceipt
        }}</div>
        <div
          class="ml-20px content break-all"
          v-if="props.diaData.makecardDraftschemeInfoReceipt"
          >{{ props.diaData.makecardDraftschemeInfoReceipt }}</div
        >
        <div class="ml-20px content break-all" v-if="props.diaData.diySamplecardInfoReceipt">{{
          props.diaData.diySamplecardInfoReceipt
        }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.dialogModule.submissionTime')
        }}</div>
        <div class="ml-20px content" v-if="props.diaData.designSchemeInfoReceiptUpdate">{{
          props.diaData.designSchemeInfoReceiptUpdate
        }}</div>
        <div class="ml-20px content" v-if="props.diaData.makecardDraftschemeInfoReceiptdate">{{
          props.diaData.makecardDraftschemeInfoReceiptdate
        }}</div>
        <div class="ml-20px content" v-if="props.diaData.diySamplecardInfoUpdatedate">{{
          props.diaData.diySamplecardInfoUpdatedate
        }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.dialogModule.attachment')
        }}</div>
        <div class="ml-20px content break-all" v-if="props.diaData.designSchemeInfoReceipt">
          <el-button
            class="mb-2"
            type="primary"
            v-if="designArr.length > 1"
            @click="downLoadAll(designArr)"
            >{{
              t('cardProductService.productDemand.components.dialogModule.downloadAll')
            }}</el-button
          >
          <div v-for="(item, index) in designArr" :key="'design' + index">
            <span
              :class="{ 'color-gold pointer': item.name != '--' }"
              @click="perviewImage(item.url)"
              >{{ item.name }}</span
            >
            <el-button
              class="color-gold ml-20px cursor-pointer"
              v-if="item.url"
              link
              :loading="downBtnLoading.includes(index)"
              @click="downFile(item.url, item.name, index)"
              >{{ t('cardProductService.productDemand.components.dialogModule.download') }}
            </el-button>
          </div>
        </div>
        <div class="ml-20px content break-all" v-if="props.diaData.makecardDraftschemeInfoReceipt">
          <el-button
            class="mb-2"
            type="primary"
            v-if="draftArr.length > 1"
            @click="downLoadAll(draftArr)"
            >{{
              t('cardProductService.productDemand.components.dialogModule.downloadAll')
            }}</el-button
          >
          <div v-for="(item, index) in draftArr" :key="'draft' + index">
            <span
              :class="{ 'color-gold pointer': item.name != '--' }"
              @click="perviewImage(item.url)"
              >{{ item.name }}</span
            >
            <el-button
              class="color-gold ml-20px cursor-pointer"
              v-if="item.url"
              link
              :loading="downBtnLoading.includes(index)"
              @click="
                downFile(
                  item.url,
                  item.name,
                  index,
                  props.diaData.makecardDraftschemeInfoReceiptFileSource
                )
              "
              >{{ t('cardProductService.productDemand.components.dialogModule.download') }}
            </el-button>
          </div>
        </div>
        <div class="ml-20px content break-all" v-if="props.diaData.diySamplecardInfoReceipt">
          <el-button
            class="mb-2"
            type="primary"
            v-if="sampleCardArr.length > 1"
            @click="downLoadAll(sampleCardArr)"
            >{{
              t('cardProductService.productDemand.components.dialogModule.downloadAll')
            }}</el-button
          >
          <div v-for="(item, index) in sampleCardArr" :key="'sampleCard' + index">
            <span
              :class="{ 'color-gold pointer': item.name != '--' }"
              @click="perviewImage(item.url)"
              >{{ item.name }}</span
            >
            <el-button
              class="color-gold ml-20px cursor-pointer"
              v-if="item.url"
              link
              :loading="downBtnLoading.includes(index)"
              @click="downFile(item.url, item.name, index)"
              >{{ t('cardProductService.productDemand.components.dialogModule.download') }}
            </el-button>
          </div>
        </div>
      </div>
    </el-descriptions-item>
  </el-descriptions>
  <!-- 操作区 -->
  <div class="flex justify-end mt-20px">
    <el-button type="primary" size="large" @click="back">{{ t('common.ok') }}</el-button>
  </div>
</template>

<script setup lang="ts">
import { spliceText } from '../../Common/index'
import { downloadFileApi } from '@/api/makeCardService/index'
const { t } = useI18n()
let props = defineProps({
  diaData: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['cancel'])

const back = () => {
  emit('cancel')
}

const designArr = ref<any>([])
const draftArr = ref<any>([])
const sampleCardArr = ref<any>([])

const upData = (name, eos) => {
  let arrChange: any = []
  if (name) {
    name.split(spliceText).forEach((item) => {
      arrChange.push({
        name: item,
        url: ''
      })
    })
  } else {
    arrChange.push({
      name: '--',
      url: ''
    })
  }
  if (eos) {
    eos.split(spliceText).forEach((item, index) => {
      arrChange[index].url = item
    })
  }
  return arrChange
}

let downBtnLoading = ref<number[]>([])

// 下载数据
const downFile = async (fileUrl, name, index, source = '') => {
  let fileName = ''
  if (name) {
    fileName = name
  } else {
    fileName = await fileNameFormatter(fileUrl)
  }
  try {
    downBtnLoading.value.push(index)
    const formData: FormData = new FormData()
    formData.append('source', source)
    if (source === '1') {
      formData.append('makeCardFileName', name)
      formData.append('makeCardFileUrl', fileUrl)
    } else {
      formData.append('makeCardFileName', fileUrl)
    }
    const res = await downloadFileApi(formData)
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    downBtnLoading.value.splice(downBtnLoading.value.indexOf(index), 1)
  }
}

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.lastIndexOf('-') + 1, fileName.length)
}

// 监听页面展示
watch(
  () => props.diaData,
  () => {
    designArr.value = upData(
      props.diaData.designSchemeInfoReceiptfu,
      props.diaData.designSchemeInfoReceiptfueos
    )
    draftArr.value = upData(
      props.diaData.makecardDraftschemeInfoReceiptfu,
      props.diaData.makecardDraftschemeInfoReceiptfueos
    )
    sampleCardArr.value = upData(
      props.diaData.diySamplecardInfoReceiptfu,
      props.diaData.diySamplecardInfoReceiptfueos
    )
  },
  { immediate: true, deep: true }
)

// 预览
import envController from '@/controller/envController'

const perviewImage = (url) => {
  if (url == '') return
  let fileType = url.split('.').pop()
  let fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf', 'webp']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning('只支持图片和pdf预览')
  }

  window.open(`${envController.getOssUrl()}/${url}`, '_blank')
}

// 全部下载
const downLoadAll = (data) => {
  data.forEach((item) => {
    downFile(item.url, item.name)
  })
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.desc-column {
  width: 150px;
  text-align: right;
  color: #666666;
}
.content {
  color: #333333;
  flex: 1;
}

.btn {
  width: 122px;
}
.pointer {
  cursor: pointer;
}
</style>
