<template>
  <div class="tip mt-60px mb-65px">
    <el-icon color="#E2A32C" :size="22">
      <WarningFilled />
    </el-icon>
    <span class="ml-8px">{{ props.diaConfirmTip }}</span>
  </div>
  <!-- 操作区 -->
  <div class="flex justify-end mt-20px">
    <el-button size="large" @click="back">{{ t('common.cancel') }}</el-button>
    <el-button type="primary" size="large" @click="confirm">{{ t('common.ok') }}</el-button>
  </div>
</template>

<script setup lang="ts">
import { WarningFilled } from '@element-plus/icons-vue'

const { t } = useI18n()
const props = defineProps({
  diaConfirmTip: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['cancel', 'tip-confirm'])

const back = () => {
  emit('cancel')
}

const confirm = () => {
  emit('tip-confirm')
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.btn {
  width: 122px;
}
.tip {
  color: #303133;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
