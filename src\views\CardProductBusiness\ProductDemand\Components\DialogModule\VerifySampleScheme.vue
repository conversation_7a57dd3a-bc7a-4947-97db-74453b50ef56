<template>
  <div class="tip mb-20px">
    <el-icon color="#E2A32C" :size="22">
      <WarningFilled />
    </el-icon>
    <span class="ml-8px">{{
      t('cardProductService.productDemand.components.dialogModule.msgTips')
    }}</span>
  </div>
  <el-form
    ref="verifySchemeRef"
    :model="queryParams"
    :label-width="ifEn ? '180px' : '80px'"
    :rules="rules"
  >
    <el-form-item
      :label="t('cardProductService.productDemand.components.dialogModule.receiptInformation')"
      prop="diySamplecardInfoReceipt"
    >
      <el-input
        type="textarea"
        v-model="queryParams.diySamplecardInfoReceipt"
        class="textarea textarea-bg break-all"
        clearable
        maxlength="500"
        show-word-limit
        resize="none"
        :placeholder="
          t(
            'cardProductService.productDemand.components.dialogModule.pleaseEnterReceiptInformation'
          )
        "
      />
    </el-form-item>
    <el-form-item
      :label="t('cardProductService.productDemand.components.dialogModule.uploadAttachments')"
      prop="diySamplecardInfoReceiptfueos"
    >
      <Upload
        ref="uploadRef"
        :btnText="t('cardProductService.productDemand.components.demand.selectFile')"
        :uploadTip="t('cardProductService.productDemand.components.demand.uploadTips')"
        :limit="8"
        :multiple="true"
        accept="*"
        :limitFormat="['*']"
        :maxSize="1024 * 1024 * 100"
      />
    </el-form-item>
  </el-form>
  <!-- 操作区 -->
  <div class="flex justify-end mt-20px">
    <el-button size="large" @click="back">{{ t('common.cancel') }}</el-button>
    <el-button type="primary" size="large" :loading="loading" @click="verifyScheme">{{
      t('common.ok')
    }}</el-button>
  </div>
</template>

<script setup lang="ts">
import Upload from '../Upload.vue'
import { getFileString } from '../../Common/index'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { WarningFilled } from '@element-plus/icons-vue'
import { getSampleEditApi } from '@/api/makeCardService/sampleCard/index'
import type { verifySchemeType } from '@/api/makeCardService/sampleCard/type'
const { t, ifEn } = useI18n()
let props = defineProps({
  diaData: {
    type: Object,
    default: () => {}
  },
  makeCardDetail: {
    type: Object,
    default: () => {}
  }
})

// 校验规则
const verifySchemeRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  diySamplecardInfoReceipt: [
    {
      required: true,
      message: t('cardProductService.productDemand.components.demand.rulesTips'),
      trigger: 'blur'
    }
  ]
})

const queryParams = reactive<verifySchemeType>({
  diySamplecardInfoId: undefined,
  diySamplecardInfoIdList: undefined,
  diySamplecardInfoReceipt: '',
  diySamplecardInfoCardnumber: '',
  diySamplecardInfoStatus: 1,
  diySamplecardInfoReceiptfu: '',
  diySamplecardInfoReceiptfueos: ''
})

const emit = defineEmits(['cancel', 'get-list'])

const back = () => {
  emit('cancel')
}

// 上传文件
const uploadRef = ref()

const uploadSuccess = (fileList) => {
  let obj = getFileString(fileList)
  queryParams.diySamplecardInfoReceiptfu = obj.nameStr
  queryParams.diySamplecardInfoReceiptfueos = obj.urlStr
}

const loading = ref(false)

const verifyScheme = async () => {
  if (!verifySchemeRef.value) return
  await verifySchemeRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const loading = ElLoading.service({
          lock: true,
          text: `Loading`,
          background: 'rgba(255, 255, 255, 0.3)'
        })
        await uploadRef.value
          .submitFile(loading)
          .then(async (res) => {
            uploadSuccess(res)

            queryParams.diySamplecardInfoCardnumber = props.makeCardDetail.makeCardRequirementInfoId

            if (props.diaData?.type === 'multiple') {
              queryParams.diySamplecardInfoIdList = props.diaData?.multipleSelection
                .map((item: any) => item.diySamplecardInfoId)
                .filter((l) => l != undefined)
            } else {
              queryParams.diySamplecardInfoId = props.diaData.diySamplecardInfoId
            }
            await getSampleEditApi(queryParams)
            ElMessage.success(
              t('cardProductService.productDemand.components.dialogModule.confirmedSuccessful')
            )
            emit('cancel')
            emit('get-list')
            resetForm(verifySchemeRef.value)

            loading.close()
          })
          .catch((_err) => {
            loading.close()
          })
      } finally {
        loading.value = false
      }
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.btn {
  width: 122px;
}
.tip {
  color: #303133;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
