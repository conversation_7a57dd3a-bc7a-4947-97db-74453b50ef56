<template>
  <model-gltf
    v-if="isShow"
    class="view-3d"
    :src="url3D"
    :backgroundColor="0xf6f6f6"
    :rotation="rotation"
    @load="onLoad"
    @progress="onSuccess"
    v-loading="loading"
  >
    <template #progress-bar>
      <el-progress :stroke-width="3" :percentage="progress" :show-text="false" />
    </template>
  </model-gltf>
  <!-- 操作区 -->
  <div class="flex justify-end mt-20px">
    <el-button type="primary" size="large" @click="back">{{ t('common.ok') }}</el-button>
  </div>
</template>

<script lang="ts" setup>
import { ModelGltf } from 'vue-3d-model'
import { downloadFileApi } from '@/api/makeCardService/index'
const { t } = useI18n()
let props = defineProps({
  diaData: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['cancel'])

const back = () => {
  emit('cancel')
}

let url3D = ref('')
let loading = ref(false)

// 下载数据
const downFile = async (fileUrl, name, source = '') => {
  try {
    loading.value = true
    const formData: FormData = new FormData()
    formData.append('source', source)
    if (source === '1') {
      formData.append('makeCardFileName', name)
      formData.append('makeCardFileUrl', fileUrl)
    } else {
      formData.append('makeCardFileName', fileUrl)
    }
    const res = await downloadFileApi(formData)
    const blob = new Blob([res.data], { type: res.data.type })
    const href = URL.createObjectURL(blob)
    url3D.value = href
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  downFile(
    props.diaData?.makecardDraftschemeInfoThreedshoweos,
    props.diaData?.makecardDraftschemeInfoThreedshow,
    props.diaData?.makecardDraftschemeInfoThreedshowSource
  )
})

let isShow = ref(false)

const rotation = reactive({
  x: 0,
  y: 0,
  z: 0
})

const onLoad = () => {
  // rotate()
}
const rotate = () => {
  requestAnimationFrame(rotate)
  rotation.z += 0.01
}

let progress = ref(0)

const onSuccess = (e) => {
  progress.value = Number(((e.loaded / e.total) * 100).toFixed(0))
}

onMounted(() => {
  nextTick(() => {
    isShow.value = true
  })
})
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.btn {
  width: 122px;
}
.view-3d {
  width: 100%;
  height: 500px !important;
  border-radius: 10px;
  position: relative !important;
  z-index: 999 !important;
}
</style>
