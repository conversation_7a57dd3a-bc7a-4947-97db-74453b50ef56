<template>
  <el-descriptions class="ml-20px msg-box-no-bg view-design-detail" :column="1">
    <el-descriptions-item>
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.demandDetail.schemeName')
        }}</div>
        <div class="ml-20px content">{{ props.diaData.designSchemeInfoSchemename }}</div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item v-if="props.diaData.designSchemeInfoVideosname">
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.dialogModule.videoFile')
        }}</div>
        <div class="ml-20px content break-all">
          <div class="image-wrap" style="color: rgb(226, 163, 44)">
            <div class="left" @click="perviewImage(props.diaData.designSchemeInfoVideos)">{{
              t('cardProductService.productDemand.components.dialogModule.viewVideoFiles')
            }}</div>
            <!-- <div
              class="right"
              @click="
                downFile(
                  props.diaData.designSchemeInfoVideos,
                  props.diaData.designSchemeInfoVideosname
                )
              "
              >{{ t('cardProductService.productDemand.components.dialogModule.download') }}</div
            > -->
          </div>
        </div>
      </div>
    </el-descriptions-item>
    <el-descriptions-item v-if="props.diaData.designSchemeInfoThreedfiles">
      <div class="flex">
        <div class="desc-column">{{
          t('cardProductService.productDemand.components.dialogModule.tDAddress')
        }}</div>
        <div
          class="ml-20px content break-all"
          style="color: rgb(226, 163, 44); cursor: pointer"
          @click="openDialog('viewOutLink', props.diaData)"
          >{{ t('cardProductService.productDemand.components.dialogModule.view3DDisplay') }}</div
        >
      </div>
    </el-descriptions-item>
  </el-descriptions>
  <div v-for="(item, index) in draftHistoryList" :key="item.makecardDraftschemeInfoId">
    <div class="info-wrap">
      <div
        >{{ t('cardProductService.productDemand.components.dialogModule.round') }}：{{
          draftHistoryList.length - index
        }}</div
      >
      <div
        >{{ t('cardProductService.productDemand.components.dialogModule.CreateTime') }}：{{
          item.makecardDraftschemeInfoAppenddate
        }}</div
      >
      <el-tag :type="infoTagColorList[item.makecardDshistoryInfoStatus]">{{
        dshistoryInfoStatusList[item.makecardDshistoryInfoStatus]
      }}</el-tag>
    </div>
    <div class="mb-6px info-content" :key="item.makecardDraftschemeInfoId">
      <!-- 按钮 -->
      <div class="info-button">
        <template v-if="item.makecardDshistoryInfoStatus == 0">
          <el-button type="primary" size="small" @click="changeProgrammeStatus(0, item)">{{
            t('cardProductService.productDemand.components.dialogModule.NeedToModify')
          }}</el-button>
          <el-button type="primary" size="small" @click="changeProgrammeStatus(1, item)">{{
            t('cardProductService.productDemand.components.dialogModule.confirmPlan')
          }}</el-button>
        </template>
        <el-button
          type="primary"
          v-if="item.makecardDshistoryInfoStatus != 0"
          size="small"
          @click="changeProgrammeStatus(2, item)"
          >{{ t('cardProductService.productDemand.components.dialogModule.feedback') }}</el-button
        >
        <el-button
          v-if="item.makecardDshistoryInfoStatus == 1"
          type="primary"
          size="small"
          @click="
            showViewReceiptsDlg(
              item.commonFeedbackInfoRespVOList.find((item) => item.commonFeedbackInfoType == 1)
            )
          "
          >{{
            t('cardProductService.productDemand.components.dialogModule.viewReceipts')
          }}</el-button
        >
      </div>
      <el-descriptions class="ml-10px" :column="1">
        <el-descriptions-item>
          <div class="flex">
            <div class="desc-column">{{
              t('cardProductService.productDemand.components.dialogModule.designScheme')
            }}</div>
            <div class="ml-20px content break-all">
              <!-- <span>{{ item.makecardDraftschemeInfoName }}</span>
                <el-button
                  class="color-gold ml-20px cursor-pointer"
                  v-if="item.makecardDraftschemeInfoName"
                  link
                  :loading="downBtnLoading.includes(index)"
                  @click="
                    downFile(
                      item.makecardDraftschemeInfoEosname,
                      item.makecardDraftschemeInfoName,
                      item.makecardDraftschemeInfoOnfSource,
                      index
                    )
                  "
                  >下载
                </el-button> -->
              <el-button
                class="mb-2"
                type="primary"
                v-if="item.imageList.length > 1"
                @click="downLoadAll(item)"
                >{{
                  t('cardProductService.productDemand.components.dialogModule.downloadAll')
                }}</el-button
              >
              <div
                class="image-wrap"
                style="color: rgb(226, 163, 44)"
                v-for="(dataItem, idx) in item.imageList"
                :key="`image_detail_${idx}`"
              >
                <div class="left" @click="perviewImage(item.imageEosList[idx])">{{ dataItem }}</div>
                <div class="right" @click="downFile(item.imageEosList[idx], dataItem, '', idx)">{{
                  t('cardProductService.productDemand.components.dialogModule.download')
                }}</div>
              </div>
            </div>
          </div>
        </el-descriptions-item>
        <!-- <el-descriptions-item>
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.components.dialogModule.display')
              }}</div>
              <div
                class="ml-20px content break-all"
                v-if="item.makecardDraftschemeInfoThreedshoweos"
              >
                <span>{{ item.makecardDraftschemeInfoThreedshow }}</span>
                <span
                  class="color-gold ml-20px cursor-pointer"
                  @click="openDialog('view3D', item)"
                  >{{ t('cardProductService.productDemand.components.dialogModule.check') }}</span
                >
              </div>
              <div class="ml-20px content" v-else>--</div>
            </div>
          </el-descriptions-item> -->
        <el-descriptions-item>
          <div class="flex">
            <div class="desc-column">{{
              t('cardProductService.productDemand.components.dialogModule.designDescription')
            }}</div>
            <div class="ml-20px content break-all">{{
              item.makecardDraftschemeInfoSremark || '--'
            }}</div>
          </div>
        </el-descriptions-item>

        <el-descriptions-item v-if="item.commonFeedbackInfoRespVOList.length > 0">
          <div class="flex">
            <div class="desc-column">{{
              t('cardProductService.productDemand.components.dialogModule.customerFeedback')
            }}</div>
            <div class="ml-20px content break-all">
              <el-table :data="item.commonFeedbackInfoRespVOList" style="width: 100%">
                <el-table-column
                  type="index"
                  :label="t('cardProductService.productDemand.components.dialogModule.number')"
                  width="60"
                  center
                />
                <el-table-column
                  prop="commonFeedbackInfoContent"
                  :label="
                    t(
                      'cardProductService.productDemand.components.dialogModule.feedbackInformation'
                    )
                  "
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="commonFeedbackInfoFilename"
                  :label="
                    t(
                      'cardProductService.productDemand.components.dialogModule.attachmentInformation'
                    )
                  "
                >
                  <template #default="scope">
                    <div
                      style="cursor: pointer; color: rgb(226, 163, 44)"
                      v-for="(item, index) in scope.row.commonFeedbackInfoFilename?.split(
                        spliceText
                      )"
                      :key="index"
                      @click="showViewReceiptsDlg(scope.row)"
                      >{{ item }}</div
                    ></template
                  >
                </el-table-column>
                <el-table-column
                  prop="commonFeedbackInfoAddtime"
                  :label="
                    t('cardProductService.productDemand.components.dialogModule.feedbackTime')
                  "
                />
              </el-table>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <span v-if="isShowHr(index)"></span>
    </div>
  </div>
  <!-- 操作区 -->
  <div class="flex justify-end mt-20px">
    <el-button type="primary" size="large" @click="back">{{ t('common.ok') }}</el-button>
  </div>
  <!-- 各种弹窗 -->
  <DialogInfo
    :isDiaLogShow="isDiaLogShow"
    :diaLogTitle="diaLogTitle"
    :openType="openType"
    :diaData="diaDataInfo"
    :diaStyle="'width: 1000px; width: 800px;'"
    @handle-close="handleClose"
  />
  <!-- 方案确认-仍需修改弹框 -->
  <programme-dialog
    v-model:isDiaLogShow="isShowDlg"
    :isDlgType="isDlgType"
    :dlgObj="dlgObj"
    :openType="openType"
    @get-list="getList"
  />
  <!-- 查看回执弹框 -->
  <view-receipts v-model:show="isShowViewReceipts" :dlgObj="receiptsObj" @get-list="getList" />
</template>

<script setup lang="ts">
import { downloadFileApi } from '@/api/makeCardService/index'
import DialogInfo from '../DialogInfo.vue'
import { getOpenInfo, spliceText } from '../../Common/index'
import { getDraftHistoryApi } from '@/api/makeCardService/draft/index'
// 方案确认-仍需修改弹框
import programmeDialog from './programmeDialog.vue'
// 查看回执弹框
import viewReceipts from './viewReceipts.vue'
const { t } = useI18n()
const props = defineProps({
  diaData: {
    type: Object,
    default: () => {}
  },
  makeCardDetail: {
    type: Object,
    default: () => {}
  }
})

// 弹窗状态
const isDiaLogShow = ref(false)
// 弹窗标题
const diaLogTitle = ref('')
// 弹窗数据
const diaDataInfo = ref({})
// 打开方式（类型，例如打开回执信息 backMsg）
const openType = ref('')
// 关闭弹窗
const handleClose = () => {
  isDiaLogShow.value = false
  openType.value = ''
}

// 弹窗
const openDialog = (type: string, obj?: object) => {
  const openInfo = getOpenInfo(type, obj)
  diaLogTitle.value = openInfo.diaLogTitle
  openType.value = openInfo.openType
  diaDataInfo.value = openInfo.diaData
  isDiaLogShow.value = openInfo.isDiaLogShow
}

// 页面loading状态
const loading = ref(false)
// 稿样历史文件数据
const draftHistoryList = ref<any[]>([])

// 获取数据
const getList = async () => {
  loading.value = true
  try {
    const { data } = await getDraftHistoryApi({
      makecardDraftschemeInfoId: props.diaData.designSchemeInfoId,
      makecardDshistoryInfoType: 0 // 查询历史列表类型 0 设计 1 稿样
    })
    data.forEach((item) => {
      if (item.makecardDraftschemeInfoName) {
        item.imageList = item.makecardDraftschemeInfoName?.split(spliceText) || []
        item.imageEosList = item.makecardDraftschemeInfoEosname?.split(spliceText) || []
      }
    })
    draftHistoryList.value = data
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getList()
})
const isShowHr = (index) => {
  return index < draftHistoryList.value?.length - 1
}
const downBtnLoading = ref(false)

// 下载数据
const downFile = async (fileUrl, name) => {
  let fileName = ''
  if (name) {
    fileName = name
  } else {
    fileName = await fileNameFormatter(fileUrl)
  }
  try {
    downBtnLoading.value = true
    const formData: FormData = new FormData()
    formData.append('makeCardFileName', fileUrl)
    const res = await downloadFileApi(formData)
    downBtnLoading.value = false
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  }
}

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.lastIndexOf('-') + 1, fileName.length)
}

const emit = defineEmits(['cancel', 'getList'])

const back = () => {
  emit('cancel')
  emit('getList')
}

const perview3d = (url) => {
  window.open(url)
}
const perview3dLink = (url) => {
  window.open(url)
}
// 预览
import envController from '@/controller/envController'
const perviewImage = (url) => {
  if (url == '') return
  const fileType = url.split('.').pop()
  const fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf', 'mp4']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning('只支持图片、pdf、mp4文件预览')
  }

  window.open(`${envController.getOssUrl()}/${url}`, '_blank')
}

// 全部下载
const downLoadAll = (data) => {
  const imageList = data.imageList
  const imageEosList = data.imageEosList
  imageList.forEach((item, index) => {
    downFile(imageEosList[index], item)
  })
}

// 方案确认、驳回接口
const isShowDlg = ref(false) // 是否显示弹窗
const isDlgType = ref(1) // 0 仍需修改 1确认 2.追加
const dlgObj = ref({})
const dshistoryInfoStatusList = [
  t('cardProductService.productDemand.components.dialogModule.toBeConfirmed'),
  t('cardProductService.productDemand.components.dialogModule.confirmed'),
  t('cardProductService.productDemand.components.dialogModule.NeedToModify')
] // 方案状态映射
const infoTagColorList = ['primary', 'success', 'danger']
const changeProgrammeStatus = (type, item) => {
  isDlgType.value = type
  openType.value = 'design'
  isShowDlg.value = true
  dlgObj.value = JSON.parse(JSON.stringify(item))
  dlgObj.value.makeCardDetail = props.makeCardDetail
}

// 查看回执相关
const isShowViewReceipts = ref(false)
const receiptsObj = ref({})
const showViewReceiptsDlg = (row) => {
  receiptsObj.value = JSON.parse(JSON.stringify(row))
  isShowViewReceipts.value = true
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.desc-column {
  // width: 94px;
  text-align: right;
  color: #666666;
}
.content {
  color: #333333;
  flex: 1;
}
.btn {
  width: 122px;
}

.image-wrap .left {
  padding-right: 20px;
}
.view-design-detail {
  .image-box {
    width: 200px;
    // background: red;
  }
}
.image-wrap {
  display: flex;
  justify-content: flex-start;

  .left {
    width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .right {
    cursor: pointer;
    margin-left: 10px;
  }
}

.info-wrap {
  padding: 0 12px 0 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.info-content {
  position: relative;
  border: 1px solid #d4d4d4;
  border-radius: 10px;
  overflow: hidden;
  padding: 10px 0 0;
  margin-bottom: 15px;

  .info-button {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}
</style>
