<!--log日志查看-->
<template>
  <div class="draft-history view-design-detail" v-loading="loading">
    <el-descriptions class="ml-10px msg-box-no-bg" :column="1">
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.components.dialogModule.cardPaymentCode')
          }}</div>
          <div class="ml-20px content break-all">{{
            props.diaData.makecardDraftschemeInfoCardid
          }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.components.dialogModule.cardName')
          }}</div>
          <div class="ml-20px content break-all">{{
            props.diaData.makecardDraftschemeInfoCardname
          }}</div>
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <div v-for="(item, index) in draftHistoryList" :key="item.makecardDraftschemeInfoId">
      <div class="info-wrap">
        <div
          >{{ t('cardProductService.productDemand.components.dialogModule.round') }}：{{
            draftHistoryList.length - index
          }}</div
        >
        <div
          >{{ t('cardProductService.productDemand.components.dialogModule.CreateTime') }}：{{
            item.makecardDraftschemeInfoAppenddate
          }}</div
        >
        <el-tag :type="infoTagColorList[item.makecardDshistoryInfoStatus]">{{
          dshistoryInfoStatusList[item.makecardDshistoryInfoStatus]
        }}</el-tag>
      </div>
      <div class="mb-6px info-content" :key="item.makecardDraftschemeInfoId">
        <!-- 按钮 -->
        <div class="info-button">
          <template v-if="item.makecardDshistoryInfoStatus == 0">
            <el-button type="primary" size="small" @click="changeProgrammeStatus(0, item)">{{
              t('cardProductService.productDemand.components.dialogModule.NeedToModify')
            }}</el-button>
            <el-button type="primary" size="small" @click="changeProgrammeStatus(1, item)">{{
              t('cardProductService.productDemand.components.dialogModule.confirmPlan')
            }}</el-button>
          </template>
          <el-button
            type="primary"
            v-if="item.makecardDshistoryInfoStatus != 0"
            size="small"
            @click="changeProgrammeStatus(2, item)"
            >{{ t('cardProductService.productDemand.components.dialogModule.feedback') }}</el-button
          >
          <el-button
            v-if="item.makecardDshistoryInfoStatus == 1"
            type="primary"
            size="small"
            @click="
              showViewReceiptsDlg(
                item.commonFeedbackInfoRespVOList.find((item) => item.commonFeedbackInfoType == 1)
              )
            "
            >{{
              t('cardProductService.productDemand.components.dialogModule.viewReceipts')
            }}</el-button
          >
        </div>
        <el-descriptions class="ml-10px" :column="1">
          <el-descriptions-item>
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.components.dialogModule.sampleFile')
              }}</div>
              <div class="ml-20px content break-all">
                <!-- <span>{{ item.makecardDraftschemeInfoName }}</span>
                <el-button
                  class="color-gold ml-20px cursor-pointer"
                  v-if="item.makecardDraftschemeInfoName"
                  link
                  :loading="downBtnLoading.includes(index)"
                  @click="
                    downFile(
                      item.makecardDraftschemeInfoEosname,
                      item.makecardDraftschemeInfoName,
                      item.makecardDraftschemeInfoOnfSource,
                      index
                    )
                  "
                  >下载
                </el-button> -->
                <el-button
                  class="mb-2"
                  type="primary"
                  v-if="item.imageList.length > 1"
                  @click="downLoadAll(item)"
                  >{{
                    t('cardProductService.productDemand.components.dialogModule.downloadAll')
                  }}</el-button
                >
                <div
                  class="image-wrap"
                  style="color: rgb(226, 163, 44)"
                  v-for="(dataItem, idx) in item.imageList"
                  :key="`image_detail_${idx}`"
                >
                  <div class="left" @click="perviewImage(item.imageEosList[idx])">{{
                    dataItem
                  }}</div>
                  <div class="right" @click="downFile(item.imageEosList[idx], dataItem, '', idx)">{{
                    t('cardProductService.productDemand.components.dialogModule.download')
                  }}</div>
                </div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.components.dialogModule.otherFile')
              }}</div>
              <div class="ml-20px content break-all">
                <el-button
                  class="mb-2"
                  type="primary"
                  v-if="item.imageListOther.length > 1"
                  @click="downLoadAllOther(item)"
                  >{{
                    t('cardProductService.productDemand.components.dialogModule.downloadAll')
                  }}</el-button
                >
                <div
                  class="image-wrap"
                  style="color: rgb(226, 163, 44)"
                  v-for="(dataItem, idx) in item.imageListOther"
                  :key="`image_detail_${idx}`"
                >
                  <div class="left" @click="perviewImage(item.imageEosListOther[idx])">{{
                    dataItem
                  }}</div>
                  <div
                    class="right"
                    @click="downFile(item.imageEosListOther[idx], dataItem, '', idx)"
                    >{{
                      t('cardProductService.productDemand.components.dialogModule.download')
                    }}</div
                  >
                </div>
              </div>
            </div>
          </el-descriptions-item>
          <!-- <el-descriptions-item>
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.components.dialogModule.display')
              }}</div>
              <div
                class="ml-20px content break-all"
                v-if="item.makecardDraftschemeInfoThreedshoweos"
              >
                <span>{{ item.makecardDraftschemeInfoThreedshow }}</span>
                <span
                  class="color-gold ml-20px cursor-pointer"
                  @click="openDialog('view3D', item)"
                  >{{ t('cardProductService.productDemand.components.dialogModule.check') }}</span
                >
              </div>
              <div class="ml-20px content" v-else>--</div>
            </div>
          </el-descriptions-item> -->
          <el-descriptions-item>
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.components.dialogModule.remarks')
              }}</div>
              <div class="ml-20px content break-all">{{
                item.makecardDraftschemeInfoSremark || '--'
              }}</div>
            </div>
          </el-descriptions-item>

          <el-descriptions-item v-if="item.commonFeedbackInfoRespVOList.length > 0">
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.components.dialogModule.customerFeedback')
              }}</div>
              <div class="ml-20px content break-all">
                <el-table :data="item.commonFeedbackInfoRespVOList" style="width: 100%">
                  <el-table-column
                    type="index"
                    :label="t('cardProductService.productDemand.components.dialogModule.number')"
                    width="60"
                    center
                  />
                  <el-table-column
                    prop="commonFeedbackInfoContent"
                    :label="
                      t(
                        'cardProductService.productDemand.components.dialogModule.feedbackInformation'
                      )
                    "
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="commonFeedbackInfoFilename"
                    :label="
                      t(
                        'cardProductService.productDemand.components.dialogModule.attachmentInformation'
                      )
                    "
                  >
                    <template #default="scope">
                      <div
                        style="cursor: pointer; color: rgb(226, 163, 44)"
                        v-for="(item, index) in scope.row.commonFeedbackInfoFilename?.split(
                          spliceText
                        )"
                        :key="index"
                        @click="showViewReceiptsDlg(scope.row)"
                        >{{ item }}</div
                      ></template
                    >
                  </el-table-column>
                  <el-table-column
                    prop="commonFeedbackInfoAddtime"
                    :label="
                      t('cardProductService.productDemand.components.dialogModule.feedbackTime')
                    "
                  />
                </el-table>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
        <span v-if="isShowHr(index)"></span>
      </div>
    </div>
  </div>
  <!-- 操作区 -->
  <div class="flex justify-end mt-20px">
    <el-button type="primary" size="large" @click="back">{{ t('common.ok') }}</el-button>
  </div>
  <!-- 各种弹窗 -->
  <DialogInfo
    :isDiaLogShow="isDiaLogShow"
    :diaLogTitle="diaLogTitle"
    :openType="openType"
    :diaData="diaDataInfo"
    :diaStyle="'width: 1000px; width: 800px;'"
    @handle-close="handleClose"
  />

  <!-- 方案确认-仍需修改弹框 -->
  <programme-dialog
    v-model:isDiaLogShow="isShowDlg"
    :isDlgType="isDlgType"
    :dlgObj="dlgObj"
    @get-list="getList"
  />

  <!-- 查看回执弹框 -->
  <view-receipts v-model:show="isShowViewReceipts" :dlgObj="receiptsObj" @get-list="getList" />
</template>

<script setup lang="ts">
import DialogInfo from '../DialogInfo.vue'
import { downloadFileApi } from '@/api/makeCardService/index'
import { getDraftHistoryApi } from '@/api/makeCardService/draft/index'
import { getOpenInfo, spliceText } from '../../Common/index'

// 方案确认-仍需修改弹框
import programmeDialog from './programmeDialog.vue'
// 查看回执弹框
import viewReceipts from './viewReceipts.vue'
const { t } = useI18n()
const props = defineProps({
  diaData: {
    type: Object,
    default: () => {}
  },
  makeCardDetail: {
    type: Object,
    default: () => {}
  }
})

// 弹窗状态
const isDiaLogShow = ref(false)
// 弹窗标题
const diaLogTitle = ref('')
// 弹窗数据
const diaDataInfo = ref({})
// 打开方式（类型，例如打开回执信息 backMsg）
const openType = ref('')
// 关闭弹窗
const handleClose = () => {
  isDiaLogShow.value = false
  openType.value = ''
}

// 弹窗
const openDialog = (type: string, obj?: object) => {
  const openInfo = getOpenInfo(type, obj)
  diaLogTitle.value = openInfo.diaLogTitle
  openType.value = openInfo.openType
  diaDataInfo.value = openInfo.diaData
  isDiaLogShow.value = openInfo.isDiaLogShow
}

const isShowHr = (index) => {
  return index < draftHistoryList.value?.length - 1
}

// 页面loading状态
const loading = ref(false)
// 稿样历史文件数据
const draftHistoryList = ref<any[]>([])

// 获取数据
const getList = async () => {
  loading.value = true
  try {
    const { data } = await getDraftHistoryApi({
      makecardDraftschemeInfoId: props.diaData.makecardDraftschemeInfoId,
      makecardDshistoryInfoType: 1 // 查询历史列表类型 0 设计 1 稿样
    })
    data.forEach((item) => {
      if (item.makecardDraftschemeInfoName) {
        item.imageList = item.makecardDraftschemeInfoName.split(spliceText)
        item.imageEosList = item.makecardDraftschemeInfoEosname.split(spliceText)
      }
      if (item.makecardDshistoryInfoOtherfilename) {
        item.imageListOther = item.makecardDshistoryInfoOtherfilename.split(spliceText)
        item.imageEosListOther = item.makecardDshistoryInfoOtherfileeosname.split(spliceText)
      } else {
        item.imageListOther = []
        item.imageEosListOther = []
      }
    })
    draftHistoryList.value = data
  } finally {
    loading.value = false
  }
}

const downBtnLoading = ref<number[]>([])

// 下载数据
const downFile = async (fileUrl, name, source = '', index) => {
  const fileName = name ? name : fileNameFormatter(fileUrl)
  try {
    downBtnLoading.value.push(index)
    const formData: FormData = new FormData()
    formData.append('source', source)
    if (source === '1') {
      formData.append('makeCardFileName', name)
      formData.append('makeCardFileUrl', fileUrl)
    } else {
      formData.append('makeCardFileName', fileUrl)
    }
    const res = await downloadFileApi(formData)
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    downBtnLoading.value.splice(downBtnLoading.value.indexOf(index), 1)
  }
}

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.lastIndexOf('-') + 1, fileName.length)
}

const emit = defineEmits(['cancel', 'getList'])

const back = () => {
  emit('cancel')
  emit('getList')
}

onMounted(() => {
  getList()
})

// 预览
import envController from '@/controller/envController'

const perviewImage = (url) => {
  if (url == '') return
  const fileType = url.split('.').pop()
  const fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf', 'webp']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning('只支持图片和pdf预览')
  }

  window.open(`${envController.getOssUrl()}/${url}`, '_blank')
}

// 全部下载
const downLoadAll = (data) => {
  const imageList = data.imageList
  const imageEosList = data.imageEosList
  imageList.forEach((item, index) => {
    downFile(imageEosList[index], item, '', index)
  })
}
const downLoadAllOther = (data) => {
  const imageList = data.imageListOther
  const imageEosList = data.imageEosListOther
  imageList.forEach((item, index) => {
    downFile(imageEosList[index], item, '', index)
  })
}

// 方案确认、驳回接口
const isShowDlg = ref(false) // 是否显示弹窗
const isDlgType = ref(1) // 0 仍需修改 1确认 2.追加
const dlgObj = ref({})
const dshistoryInfoStatusList = [
  t('cardProductService.productDemand.components.dialogModule.toBeConfirmed'),
  t('cardProductService.productDemand.components.dialogModule.confirmed'),
  t('cardProductService.productDemand.components.dialogModule.NeedToModify')
] // 方案状态映射
const infoTagColorList = ['primary', 'success', 'danger']
const changeProgrammeStatus = (type, item) => {
  isDlgType.value = type
  isShowDlg.value = true
  dlgObj.value = JSON.parse(JSON.stringify(item))
  dlgObj.value.makeCardDetail = props.makeCardDetail
}

// 查看回执相关
const isShowViewReceipts = ref(false)
const receiptsObj = ref({})
const showViewReceiptsDlg = (row) => {
  receiptsObj.value = JSON.parse(JSON.stringify(row))
  isShowViewReceipts.value = true
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.btn {
  width: 122px;
}
.desc-column {
  width: 70px; //94px;
  text-align: right;
  color: #666666;
}
.content {
  color: #333333;
  flex: 1;
}
.draft-history {
  max-height: 500px;
  overflow: auto;
}

.view-design-detail {
  .image-box {
    width: 200px;
    // background: red;
  }
  .image-wrap {
    display: flex;
    justify-content: flex-start;

    .left {
      width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
    .right {
      cursor: pointer;
      margin-left: 10px;
    }
  }
}

.info-wrap {
  padding: 0 12px 0 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.info-content {
  position: relative;
  border: 1px solid #d4d4d4;
  border-radius: 10px;
  overflow: hidden;
  padding: 10px 0 0;
  margin-bottom: 15px;

  .info-button {
    position: absolute;
    right: 10px;
    top: 10px;
  }
}
</style>
