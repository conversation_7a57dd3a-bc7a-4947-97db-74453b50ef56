<template>
  <div>
    <iframe
      id="audio_iframe"
      :src="diaData.designSchemeInfoThreedfiles"
      allow="microphone"
      width="100%"
      height="460px"
      pointer-events="none"
      scrolling="no"
      style="
        position: static;
        right: 0;
        bottom: 0;
        background: transparent;
        border: none;
        z-index: 999;
      "
    ></iframe>
  </div>
  <!-- 操作区 -->
  <div class="flex justify-end mt-20px">
    <el-button type="primary" size="large" @click="back">{{ t('common.ok') }}</el-button>
  </div>
</template>

<script setup lang="ts">
import { spliceText } from '../../Common/index'
import { downloadFileApi } from '@/api/makeCardService/index'
const { t } = useI18n()
let props = defineProps({
  diaData: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['cancel'])

const back = () => {
  emit('cancel')
}

const designArr = ref<any>([])
const draftArr = ref<any>([])
const sampleCardArr = ref<any>([])

const upData = (name, eos) => {
  let arrChange: any = []
  if (name) {
    name.split(spliceText).forEach((item) => {
      arrChange.push({
        name: item,
        url: ''
      })
    })
  } else {
    arrChange.push({
      name: '--',
      url: ''
    })
  }
  if (eos) {
    eos.split(spliceText).forEach((item, index) => {
      arrChange[index].url = item
    })
  }
  return arrChange
}

let downBtnLoading = ref<number[]>([])

// 下载数据
const downFile = async (fileUrl, name, index, source = '') => {
  let fileName = ''
  if (name) {
    fileName = name
  } else {
    fileName = await fileNameFormatter(fileUrl)
  }
  try {
    downBtnLoading.value.push(index)
    const formData: FormData = new FormData()
    formData.append('source', source)
    if (source === '1') {
      formData.append('makeCardFileName', name)
      formData.append('makeCardFileUrl', fileUrl)
    } else {
      formData.append('makeCardFileName', fileUrl)
    }
    const res = await downloadFileApi(formData)
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    downBtnLoading.value.splice(downBtnLoading.value.indexOf(index), 1)
  }
}

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.lastIndexOf('-') + 1, fileName.length)
}

// 监听页面展示
watch(
  () => props.diaData,
  () => {
    designArr.value = upData(
      props.diaData.designSchemeInfoReceiptfu,
      props.diaData.designSchemeInfoReceiptfueos
    )
    draftArr.value = upData(
      props.diaData.makecardDraftschemeInfoReceiptfu,
      props.diaData.makecardDraftschemeInfoReceiptfueos
    )
    sampleCardArr.value = upData(
      props.diaData.diySamplecardInfoReceiptfu,
      props.diaData.diySamplecardInfoReceiptfueos
    )
  },
  { immediate: true, deep: true }
)

// 预览
import envController from '@/controller/envController'
const perviewImage = (url) => {
  if (url == '') return
  let fileType = url.split('.').pop()
  let fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf', 'webp']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning('只支持图片和pdf预览')
  }

  window.open(`${envController.getOssUrl()}/${url}`, '_blank')
}

// 全部下载
const downLoadAll = (data) => {
  data.forEach((item) => {
    downFile(item.url, item.name)
  })
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.desc-column {
  width: 150px;
  text-align: right;
  color: #666666;
}
.content {
  color: #333333;
  flex: 1;
}

.btn {
  width: 122px;
}
.pointer {
  cursor: pointer;
}
</style>
