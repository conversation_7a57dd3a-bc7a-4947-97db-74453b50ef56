<template>
  <el-dialog
    :title="titleList[isDlgType].title"
    :model-value="isDiaLogShow"
    :before-close="back"
    :close-on-click-modal="false"
    append-to-body
    :top="props.top"
    :style="props.diaStyle"
    v-if="isDiaLogShow"
  >
    <div class="tip mb-20px" v-if="titleList[isDlgType].tips">
      <el-icon color="#E2A32C" :size="22">
        <WarningFilled />
      </el-icon>
      <span class="ml-8px">{{ titleList[isDlgType].tips }}</span>
    </div>
    <el-form
      ref="verifySchemeRef"
      :model="queryParams"
      :label-width="ifEn ? '180px' : '80px'"
      :rules="rules"
    >
      <el-form-item :label="titleList[isDlgType].textAreaTitle" prop="commonFeedbackInfoContent">
        <el-input
          type="textarea"
          v-model="queryParams.commonFeedbackInfoContent"
          class="textarea textarea-bg break-all"
          clearable
          maxlength="500"
          show-word-limit
          resize="none"
          :placeholder="`${t(
            'cardProductService.productDemand.components.dialogModule.pleaseEnter'
          )} ${titleList[isDlgType].textAreaTitle}`"
        />
      </el-form-item>
      <el-form-item
        :label="t('cardProductService.productDemand.components.dialogModule.uploadAttachments')"
        prop="commonFeedbackInfoFile"
      >
        <Upload
          ref="uploadRef"
          :btnText="t('cardProductService.productDemand.components.demand.selectFile')"
          :uploadTip="t('cardProductService.productDemand.components.demand.uploadTips')"
          :limit="8"
          :multiple="true"
          accept="*"
          :limitFormat="['*']"
          :maxSize="1024 * 1024 * 100"
        />
      </el-form-item>
    </el-form>
    <!-- 操作区 -->
    <div class="flex justify-end mt-20px">
      <el-button size="large" @click="back">{{ t('common.cancel') }}</el-button>
      <el-button
        type="primary"
        size="large"
        :loading="loading"
        v-if="openType == 'design'"
        @click="verifySchemeDesign"
        >{{ t('common.ok') }}</el-button
      >
      <el-button type="primary" size="large" :loading="loading" v-else @click="verifyScheme">{{
        t('common.ok')
      }}</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import Upload from '../Upload.vue'
import { getFileString } from '../../Common/index'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { WarningFilled } from '@element-plus/icons-vue'
import { sampleSchemeApi } from '@/api/makeCardService/draft/index'
import { acceptDesignApi } from '@/api/makeCardService/design/index'

const { t, ifEn } = useI18n()
const props = defineProps({
  diaData: {
    type: Object,
    default: () => {}
  },
  makeCardDetail: {
    type: Object,
    default: () => {}
  },
  isDiaLogShow: {
    type: Boolean,
    default: false,
    required: true
  },
  openType: {
    type: String,
    default: ''
  },
  top: {
    type: String,
    default: ''
  },
  diaStyle: {
    type: String,
    default: 'width: 1000px; min-width: 800px'
  },
  isDlgType: {
    type: Number,
    default: 0
  },
  dlgObj: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
// 解构传入的弹窗开关
const { isDiaLogShow, openType, isDlgType } = toRefs(props)
const emit = defineEmits(['cancel', 'get-list', 'update:isDiaLogShow'])

// 弹框标题
const titleList = ref([
  {
    title: t('cardProductService.productDemand.components.dialogModule.NeedToModify'),
    tips: `${t('cardProductService.productDemand.components.dialogModule.pleaseEnter')} ${t(
      'cardProductService.productDemand.components.dialogModule.suggestion'
    )}`,
    textAreaTitle: t('cardProductService.productDemand.components.dialogModule.suggestion')
  },
  {
    title: t('cardProductService.productDemand.components.dialogModule.confirmPlan'),
    tips: t('cardProductService.productDemand.components.dialogModule.confirmPlanTips'),
    textAreaTitle: t('cardProductService.productDemand.components.dialogModule.receiptInformation')
  },
  {
    title: t('cardProductService.productDemand.components.dialogModule.feedback'),
    tips: '',
    textAreaTitle: t('cardProductService.productDemand.components.dialogModule.feedback')
  },
  {
    title: t('cardProductService.productDemand.common.viewReceiptInformation'),
    tips: '',
    textAreaTitle: ''
  }
])

// 校验规则
const verifySchemeRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  commonFeedbackInfoContent: [
    {
      required: true,
      message: t('cardProductService.productDemand.components.demand.rulesTips'),
      trigger: 'blur'
    }
  ]
})

const queryParams = reactive({
  makecardDraftschemeInfoCardnumber: '',
  makecardDraftschemeInfoId: undefined,
  makecardDraftschemeInfoIdList: undefined,
  makecardDraftschemeInfoReceipt: '',
  makecardDraftschemeInfoStatus: 1,
  commonFeedbackInfoFile: '',
  commonFeedbackInfoFilename: '',
  commonFeedbackInfoContent: ''
})

const back = () => {
  resetForm(verifySchemeRef.value)
  emit('update:isDiaLogShow', false)
}

// 上传文件
const uploadRef = ref()

const uploadSuccess = (fileList) => {
  const obj = getFileString(fileList)
  queryParams.commonFeedbackInfoFilename = obj.nameStr
  queryParams.commonFeedbackInfoFile = obj.urlStr
}

const loading = ref(false)

const verifyScheme = async () => {
  console.log('verifyScheme')

  if (!verifySchemeRef.value) return
  await verifySchemeRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const loading = ElLoading.service({
          lock: true,
          text: `Loading`,
          background: 'rgba(255, 255, 255, 0.3)'
        })
        await uploadRef.value
          .submitFile(loading)
          .then(async (res) => {
            uploadSuccess(res)

            const params = {
              commonFeedbackInfoContent: queryParams.commonFeedbackInfoContent,
              commonFeedbackInfoFile: queryParams.commonFeedbackInfoFile,
              commonFeedbackInfoFilename: queryParams.commonFeedbackInfoFilename,
              commonFeedbackInfoType: props.isDlgType,
              makecardDshistoryInfoId: props.dlgObj.makecardDshistoryInfoId,

              makecardDshistoryInfoCardid: props.dlgObj.makecardDraftschemeInfoCardid,
              makecardDshistoryInfoVersion: props.dlgObj.makecardDshistoryInfoVersion,
              makecardDraftschemeInfoId: props.dlgObj.makecardDraftschemeInfoId,
              makecardDraftschemeInfoCardtitle:
                props.dlgObj?.makeCardDetail.makeCardRequirementInfoTitle,
              makecardDraftschemeInfoCardnumber:
                props.dlgObj?.makeCardDetail.makeCardRequirementInfoId
            }

            await sampleSchemeApi(params)
            ElMessage.success(
              t('cardProductService.productDemand.components.dialogModule.confirmedSuccessful')
            )
            back()
            emit('get-list')

            loading.close()
          })
          .catch((_err) => {
            loading.close()
          })
      } finally {
        loading.value = false
      }
    }
  })
}

const verifySchemeDesign = async () => {
  console.log('verifySchemeDesign')
  console.log('verifySchemeDesign props', props)
  if (!verifySchemeRef.value) return
  await verifySchemeRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const loading = ElLoading.service({
          lock: true,
          text: `Loading`,
          background: 'rgba(255, 255, 255, 0.3)'
        })
        await uploadRef.value
          .submitFile(loading)
          .then(async (res) => {
            uploadSuccess(res)
            const statusMap = {
              0: 2, // 仍需修改
              1: 1 // 确认方案
              // 追加反馈返回原始状态
            }
            const params = {
              designSchemeInfoReceipt: queryParams.commonFeedbackInfoContent,
              designSchemeInfoReceiptfueos: queryParams.commonFeedbackInfoFile,
              designSchemeInfoReceiptfu: queryParams.commonFeedbackInfoFilename,
              designSchemeInfoId: props.dlgObj.makecardDraftschemeInfoId,
              makeCardDesignSchemeInfoNumber: props.dlgObj.makeCardDetail.makeCardRequirementInfoId,
              makeCardRequirementInfoPhase:
                props.dlgObj.makeCardDetail.makeCardRequirementInfoPhase,
              makecardDshistoryInfoId: props.dlgObj.makecardDshistoryInfoId, // 轮次id
              commonFeedbackInfoType: props.isDlgType, // （0：仍需修改 1：确认 2追加）
              designSchemeInfoStatus: statusMap[props.isDlgType]
                ? statusMap[props.isDlgType]
                : props.dlgObj.makecardDshistoryInfoStatus, //通过1，驳回2（反馈类型（状态 0未确认，1已经确认 2仍需修改 ）
              makecardDshistoryInfoStatus: statusMap[props.isDlgType]
                ? statusMap[props.isDlgType]
                : props.dlgObj.makecardDshistoryInfoStatus //（反馈类型（状态 0未确认，1已经确认 2仍需修改 ）
            }

            console.log('acceptDesignApi', params)
            await acceptDesignApi(params)
            ElMessage.success(
              t('cardProductService.productDemand.components.dialogModule.confirmedSuccessful')
            )
            back()
            emit('get-list')

            loading.close()
          })
          .catch((_err) => {
            loading.close()
          })
      } finally {
        loading.value = false
      }
    }
  })
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.btn {
  width: 122px;
}
.tip {
  color: #303133;
  font-size: 18px;
  display: flex;
  padding-left: 80px;
  // align-items: center;
  // justify-content: center;
}
</style>
