<template>
  <el-dialog
    v-model="show"
    :title="t('cardProductService.productDemand.common.viewReceiptInformation')"
    :style="props.diaStyle"
    :before-close="hide"
    class="page-view-receipts-dlg"
  >
    <el-form :model="props.dlgObj" label-width="auto">
      <el-form-item
        :label="t('cardProductService.productDemand.components.dialogModule.receiptInformation')"
        ><span style="word-wrap: break-word; word-break: break-all">{{
          props.dlgObj?.commonFeedbackInfoContent
        }}</span></el-form-item
      >
      <el-form-item
        :label="t('cardProductService.productDemand.components.dialogModule.submissionTime')"
        >{{ props.dlgObj?.commonFeedbackInfoAddtime }}</el-form-item
      >
      <el-form-item
        :label="t('cardProductService.productDemand.components.dialogModule.attachment')"
      >
        <div v-if="props.dlgObj?.commonFeedbackInfoFilename">
          <el-button
            v-if="props.dlgObj?.commonFeedbackInfoFilename?.split(spliceText).length > 1"
            type="primary"
            @click="
              downLoadAll(
                props.dlgObj?.commonFeedbackInfoFile,
                props.dlgObj?.commonFeedbackInfoFilename
              )
            "
            >{{
              t('cardProductService.productDemand.components.dialogModule.downloadAll')
            }}</el-button
          >
          <div
            class="image-list"
            v-for="(item, index) in props.dlgObj?.commonFeedbackInfoFilename?.split(spliceText)"
            :key="index"
          >
            <div
              class="left"
              @click="perviewImage(props.dlgObj?.commonFeedbackInfoFile?.split(spliceText)[index])"
              >{{ item }}</div
            >
            <div
              class="right"
              @click="
                downFile(
                  props.dlgObj?.commonFeedbackInfoFile?.split(spliceText)[index],
                  item,
                  index
                )
              "
              >{{ t('cardProductService.productDemand.components.dialogModule.download') }}</div
            >
          </div>
        </div>
        <div v-else>--</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="hide" size="large"> {{ t('common.ok') }} </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
const { t, ifEn } = useI18n()
import { getOpenInfo, spliceText } from '../../Common/index'
import { downloadFileApi } from '@/api/makeCardService/index'
// 接收参数
const props = defineProps({
  dlgObj: {
    type: Object,
    default: () => {}
  },
  show: {
    type: Boolean,
    default: false
  },
  diaStyle: {
    type: String,
    default: 'width: 1000px; min-width: 800px'
  }
})
const { show } = toRefs(props)
const emit = defineEmits(['update:show'])

const hide = () => {
  emit('update:show', false)
}

// 预览
import envController from '@/controller/envController'
const perviewImage = (url) => {
  if (url == '') return
  let fileType = url.split('.').pop()
  let fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf', 'webp']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning('只支持图片和pdf预览')
  }
  window.open(`${envController.getOssUrl()}/${url}`, '_blank')
}

// 下载数据
let downBtnLoading = ref<number[]>([])
const downFile = async (fileUrl, name, index) => {
  let fileName = ''
  if (name) {
    fileName = name
  } else {
    fileName = await fileNameFormatter(fileUrl)
  }
  try {
    downBtnLoading.value.push(index)
    const formData: FormData = new FormData()
    formData.append('makeCardFileName', fileUrl)
    const res = await downloadFileApi(formData)
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    downBtnLoading.value.splice(downBtnLoading.value.indexOf(index), 1)
  }
}
function fileNameFormatter(fileName) {
  return fileName.substring(fileName.lastIndexOf('-') + 1, fileName.length)
}

const downLoadAll = (urlList, nameList) => {
  let list = urlList.split(spliceText)
  let name = nameList.split(spliceText)
  list.forEach((item, index) => {
    downFile(item, name[index], index)
  })
}
</script>
<style lang="less" scoped>
.page-view-receipts-dlg {
  .image-list {
    width: 350px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #d5a147;

    .left {
      width: 300px;
      cursor: pointer;
    }

    .right {
      cursor: pointer;
    }
  }
  .el-form-item--default {
    margin-bottom: 0 !important;
  }

  .el-form-item__content {
    display: block !important;
    float: none;
  }
}
</style>
