<template>
  <div class="cropper">
    <img ref="imageRef" :src="imageSrc" alt="image" />
    <div class="send-box">
      <el-button class="btn-style send-btn" @click="saveImage">{{
        t('cardProductService.productDemand.components.iMessage.saveImage')
      }}</el-button>
      <el-button class="btn-style send-btn" @click="cropImage">{{
        t('cardProductService.productDemand.components.iMessage.sendDirectly')
      }}</el-button>
    </div>
  </div>
</template>

<script setup>
const { t } = useI18n()
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'
import { createImgName } from '../../Common/index'

const props = defineProps({
  // 图片地址
  imageSrc: {
    type: String,
    required: true
  },
  // 裁剪框宽高比
  aspectRatio: {
    type: Number,
    default: 1
  },
  // 视图控制
  viewMode: {
    type: Number,
    default: 1
  },
  // 设置裁剪区域占图片的大小，值为0-1，默认0.8，表示80%区域
  autoCropArea: {
    type: Number,
    default: 1
  }
})

const imageRef = ref(null)
let cropper = null

// 使用Cropper构造函数创建裁剪实例，并传入图片以及选项配置
onMounted(() => {
  cropper = new Cropper(imageRef.value, {
    // aspectRatio: props.aspectRatio,
    viewMode: props.viewMode,
    autoCropArea: props.autoCropArea
  })
})

const emit = defineEmits(['update-image-src', 'handle-close'])

const cancel = () => {
  emit('handle-close')
}

// 获取裁剪后的图片
const cropImage = () => {
  const canvas = cropper.getCroppedCanvas()
  const croppedImage = canvas.toDataURL()
  emit('update-image-src', croppedImage)
  cancel()
}

// 保存截图
const saveImage = () => {
  const canvas = cropper.getCroppedCanvas()
  const croppedImage = canvas.toDataURL()
  let saveLink = document.createElement('a')
  saveLink.href = croppedImage
  saveLink.setAttribute('download', createImgName())
  saveLink.click()
  cancel()
}

// 销毁
onBeforeUnmount(() => {
  cropper.destroy()
})
</script>
<style lang="less" scoped>
// @import url('../../Common/common.less');
.cropper {
  width: 100%;
  height: 100%;
}
.send-box {
  display: flex;
  justify-content: flex-end;
  .send-btn {
    width: 122px;
    margin: 20px;
  }
}
</style>
