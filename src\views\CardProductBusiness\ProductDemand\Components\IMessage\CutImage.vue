<template>
  <div>
    <el-dialog
      :model-value="isCutShow"
      :close-on-click-modal="false"
      :show-close="false"
      :before-close="cancel"
      :top="'80px'"
      style="width: 700px"
      :destroy-on-close="true"
    >
      <!-- 头部 -->
      <template #header>
        <div class="my-header flex justify-between">
          <text class="text">{{
            t('cardProductService.productDemand.components.iMessage.kjNovaClipper')
          }}</text>
          <div @click="cancel">
            <el-icon class="text cursor-pointer"><Close /></el-icon>
          </div>
        </div>
      </template>
      <div class="cropper">
        <cropper-img
          :imageSrc="props.imageSrc"
          @update-image-src="updateImageSrc"
          @handle-close="cancel"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
const { t } = useI18n()
import CropperImg from './Cropper.vue'
import { Close } from '@element-plus/icons-vue'

const props = defineProps({
  imageSrc: {
    type: String,
    required: true
  }
})

const isCutShow = ref(false)

const imageNew = ref()

const updateImageSrc = (src) => {
  imageNew.value = src
  // TODO: 发送图片
  emit('send-cut-img', src)
}

onMounted(() => {
  nextTick(() => {
    isCutShow.value = true
  })
})

const emit = defineEmits(['handle-close', 'send-cut-img'])

const cancel = () => {
  emit('handle-close')
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');

:deep(.el-dialog__header) {
  padding: 0;
  border-bottom: none;
}
:deep(.el-dialog__body) {
  padding: 0;
}
.my-header {
  width: 100%;
  height: 40px;
  padding: 0 20px;
  align-items: center;
  background: #e2a32c;

  .text {
    font-size: 18px;
    font-weight: 500;
    color: #ffffff;
  }
}
.cropper {
  width: 100%;
  height: 100%;
}
</style>
