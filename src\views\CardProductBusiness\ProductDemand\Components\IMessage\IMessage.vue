<template>
  <div>
    <el-dialog
      :model-value="isIMShow"
      :close-on-click-modal="false"
      :show-close="false"
      :before-close="cancel"
      :top="diaLogOffsetTop"
      style="width: 1000px"
    >
      <!-- 头部 -->
      <template #header>
        <div class="my-header flex justify-between">
          <text class="text">{{
            t('cardProductService.productDemand.components.iMessage.onlineCommunication')
          }}</text>
          <div @click="cancel">
            <el-icon class="text cursor-pointer"><Close /></el-icon>
          </div>
        </div>
      </template>
      <!-- 主要部分 -->
      <div class="content">
        <!-- 图片编辑区 -->
        <div class="image-tool">
          <ImageEdit @send-cut-img="sendCutImg" ref="imageEditRef" />
        </div>
        <!-- 消息区 -->
        <div class="message">
          <div class="message-list">
            <div class="status">
              {{ t('cardProductService.productDemand.components.iMessage.interactiveRecording') }}
              <span class="on-line"
                >({{ t('cardProductService.productDemand.components.iMessage.onLine') }})</span
              >
            </div>
            <div class="user">
              <div v-for="(item, index) in onlineUserList" :key="'user' + index">
                <el-tooltip
                  effect="light"
                  :content="item.nickName || item.userName"
                  placement="top-start"
                  :visible="visibleTipArr.includes(index)"
                >
                  <el-image
                    :src="userAvatarObj[item.userId] || imgUrl"
                    class="avatar avatar-online cursor-pointer"
                    :preview-src-list="[userAvatarObj[item.userId] || imgUrl]"
                    @mouseenter="visibleTipArr.push(index)"
                    @mouseleave="visibleTipArr.splice(visibleTipArr.indexOf(index), 1)"
                  />
                </el-tooltip>
                <!-- :class="[ { 'avatar-online': false }, 在线状态 { 'avatar-un-online': false }, 离线状态 { 'avatar-message': false } 有消息状态 ]" -->
              </div>
            </div>
          </div>
          <div class="message-history" ref="messageHistoryRef">
            <div class="no-more" v-if="hasReturn"
              >{{ t('cardProductService.productDemand.components.iMessage.loading') }}~</div
            >
            <div class="no-more" v-else
              >{{
                t('cardProductService.productDemand.components.iMessage.thereSNothingMoreLeft')
              }}~</div
            >
            <!-- 消息 -->
            <div v-for="(item, index) in chatRecordList" :key="'message' + index">
              <div
                class="one-message mb-10px"
                :class="[
                  { 'one-message-left': item.self === 0 },
                  { 'one-message-right': item.self === 1 }
                ]"
              >
                <!-- 头像左边 -->
                <div v-if="item.self === 0">
                  <el-image
                    :src="userAvatarObj[item.userId] || imgUrl"
                    class="avatar cursor-pointer"
                    :preview-src-list="[userAvatarObj[item.userId] || imgUrl]"
                  />
                </div>
                <!-- 名字 消息 时间 -->
                <div
                  class="break-all"
                  :class="[{ 'ml-10px': item.self === 0 }, { 'mr-10px': item.self === 1 }]"
                >
                  <div
                    class="name"
                    :class="[
                      { 'one-name-time-left': item.self === 0 },
                      { 'one-name-time-right': item.self === 1 }
                    ]"
                    >{{ item.userName || ' ' }}</div
                  >
                  <div
                    :class="[
                      { 'message-content-left': item.self === 0 },
                      { 'message-content-right': item.self === 1 }
                    ]"
                  >
                    <div class="message-content" v-if="item.messageType === 'msg'">
                      {{ item.messageContent.message || ' ' }}
                    </div>
                    <div v-if="item.messageType === 'img'">
                      <div
                        v-for="(imageItem, imgIndex) in item.messageContent.files"
                        :key="'image' + imgIndex"
                      >
                        <el-image
                          class="single-img"
                          fit="contain"
                          :src="imageItem.photoUrl"
                          :preview-src-list="[imageItem.photoUrl]"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    class="time mt-10px"
                    :class="[
                      { 'one-name-time-left': item.self === 0 },
                      { 'one-name-time-right': item.self === 1 }
                    ]"
                    >{{ item.createTime }}</div
                  >
                </div>
                <!-- 头像右边 -->
                <div v-if="item.self === 1">
                  <el-image
                    :src="userAvatarObj[item.userId] || imgUrl"
                    class="avatar cursor-pointer"
                    :preview-src-list="[userAvatarObj[item.userId] || imgUrl]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 消息框区域 -->
      <div class="footer">
        <div>
          <el-input
            type="textarea"
            v-model="messageText"
            class="message-text break-all"
            clearable
            maxlength="1000"
            show-word-limit
            resize="none"
            :placeholder="t('cardProductService.productDemand.components.iMessage.pleaseInput')"
            @keyup.enter.exact="sendMessageFn"
            @keydown.enter.exact="preventEnterEv"
          />
        </div>
        <div class="file-box">
          <Upload
            ref="uploadIMRef"
            :limit="1"
            accept=".jpg,.png,.gif,.bmp"
            :limitFormat="['jpg', 'png', 'gif', 'bmp']"
            :maxSize="1024 * 1024 * 10"
          >
            <template #btn>
              <el-tooltip
                effect="light"
                :content="t('cardProductService.productDemand.components.iMessage.sendPictures')"
                placement="top-start"
              >
                <el-icon class="cursor-pointer" size="18">
                  <FolderRemove />
                </el-icon>
              </el-tooltip>
            </template>
          </Upload>
        </div>
        <div class="send-box">
          <el-button class="btn-style send-btn" @click="fileChange">{{
            t('cardProductService.productDemand.components.iMessage.send')
          }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { FolderRemove } from '@element-plus/icons-vue'
import Upload from '../Upload.vue'
import ImageEdit from './ImageEdit.vue'
import { Close } from '@element-plus/icons-vue'
import imgUrl from '@/assets/imgs/avatar.png'
import { base64ToFile } from '../../Common/index'
import { uploadFileApi } from '@/api/makeCardService/index'

import { getAccessToken, getTenantId } from '@/utils/auth'
import type { Socket } from 'socket.io-client'
import io from 'socket.io-client'
import { useUserStoreWithOut } from '@/store/modules/user'
import { createImgName } from '../../Common/index'
import { uniqBy } from 'lodash-es'
const { t } = useI18n()
let mySocket: Socket

const props = defineProps({
  isIMShow: {
    type: Boolean,
    default: false
  },
  requirementId: {
    type: String,
    default: ''
  },
  type: {
    // （设计'0'，稿样'1'）
    type: String,
    default: ''
  }
})

const { isIMShow } = toRefs(props)

const emit = defineEmits(['handle-close'])

let messageText = ref('')

const cancel = () => {
  emit('handle-close')
}

const uploadIMRef = ref()
const messageHistoryRef = ref()
const imageEditRef = ref()

const diaLogOffsetTop = ref('80px')
// 笔记本适配
if (window.screen.height <= 768) diaLogOffsetTop.value = '10px'

// 发送裁剪图片
const sendCutImg = async (src) => {
  if (!connectedError()) return
  let file = base64ToFile(src, createImgName() + '.png')
  const formData: FormData = new FormData()
  formData.append('fileList', file)
  formData.append('number', '1')
  formData.append('restrict', '10')
  try {
    const onUploadProgress = () => {}
    const { data } = await uploadFileApi(formData, onUploadProgress)
    let imgFrom: any[] = []
    data.forEach((item) => {
      let obj = {
        fileName: item.name,
        photoUrl: item.path
      }
      imgFrom.push(obj)
    })
    // 发送图片
    sendMessage('', imgFrom, 'img')
  } catch (e) {
    ElMessage.error(t('cardProductService.productDemand.components.iMessage.fileUploadFailed'))
  }
}

onMounted(() => {
  nextTick(() => {
    messageHistoryRef.value.onscroll = pageScroll
  })
  initWS()
})

// 群组ID
const groupId = ref(props.requirementId)

// 用户信息-获取用户ID
const userStore = useUserStoreWithOut()
const userInfo = userStore.getUser
const userId = ref(userInfo.id)

// 租户ID
const tenantId = getTenantId()

// 登录TOKEN
const token = ref('')
if (getAccessToken()) {
  token.value = getAccessToken() // 让每个请求携带自定义token
}

// 请求地址
import envController from '@/controller/envController'
//由于仅此组件使用,暂时写在组件中,不抽离
const getModeMapUrl = () => {
  const modeMapUrl = new Map<string, string>()
  modeMapUrl.set('dev', import.meta.env.VITE_IM_URL_DEV)
  modeMapUrl.set('sit', import.meta.env.VITE_IM_URL_SIT)
  modeMapUrl.set('uat', import.meta.env.VITE_IM_URL_UAT)
  modeMapUrl.set('prod', import.meta.env.VITE_IM_URL_PROD)
  return modeMapUrl
}

const base_url = getModeMapUrl().get(envController.getEnvironment())

// 聊天数据
let chatRecordList = ref<any>([])

// 用户列表
let chatUserList = ref<any>([])

// 用户列表 -- 离线
let offLineUsersList = ref<any>([])

// 判断是否滚动到最后
let isFirstGetList = true

// 判断是否还有内容
let hasReturn = ref(false)

// 记录窗口位置
let scrollHeight = 0

// 窗口偏移差值
const windowDiffValue = 800

// 是否成功登录聊天室
let isLoginSuccess = ref(false)

// 头像集合
const userAvatarObj = computed(() => {
  let res = {}
  let allUserList = chatUserList.value.concat(offLineUsersList.value)
  allUserList.forEach((item) => {
    res[item?.userId] = item?.photoUrl || imgUrl
  })
  return res
})
const onlineUserList = computed(() => {
  let res: any = uniqBy(chatUserList.value, 'userId')
  return res
})

// 控制tooltip显示---解决打开图片预览时，tooltip会不关闭的问题
const visibleTipArr = ref<number[]>([])

// 链接错误状态
const connectedError = (): Boolean => {
  if (!isLoginSuccess.value) {
    ElMessage.error(t('cardProductService.productDemand.components.iMessage.errrotTips1'))
    return false
  }
  if (!mySocket.connected) {
    ElMessage.error(t('cardProductService.productDemand.components.iMessage.errrotTips2'))
    return false
  }
  return true
}

const initWS = () => {
  mySocket = io(base_url, {
    timeout: 20000,
    transports: ['websocket'],
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 3,
    reconnectionDelay: 4000,
    query: {
      requirementId: groupId.value
    } // 链接参数
  })

  // 链接成功
  mySocket.on('connect', () => {
    console.log('socket event 【connect】===')
    const loginParams = {
      token: token.value,
      requirementId: groupId.value,
      tenantId: tenantId,
      type: props.type
    }
    console.log('emit login', loginParams)
    // 登录
    mySocket.emit('login', loginParams)
  })

  // 重连失败
  mySocket.on('reconnect_failed', () => {
    console.log('socket event 【reconnect_failed】===')

    hasReturn.value = false
    ElMessage.error(t('cardProductService.productDemand.components.iMessage.errrotTips3'))
  })

  // 链接失败
  mySocket.on('connect_error', () => {
    console.log('socket event 【connect_error】===')

    hasReturn.value = false
  })

  // 正在重连
  mySocket.on('reconnecting', () => {
    console.log('socket event 【reconnecting】===')

    hasReturn.value = false
    ElMessage.warning(t('cardProductService.productDemand.components.iMessage.errrotTips4'))
  })

  // 断开链接
  mySocket.on('disconnect', () => {
    console.log('socket event 【disconnect】===')

    hasReturn.value = false
  })

  // 登录 -- 在线用户
  mySocket.on('login', (res) => {
    console.log('socket event 【login】===', res)

    chatUserList.value = res.data
    isLoginSuccess.value = true
    onGetMsgList('')
  })

  // 获取 ---离线用户
  mySocket.on('offLineUsers', (res) => {
    console.log('socket event 【offLineUsers】===', res)

    if (res.data?.length > 0) {
      offLineUsersList.value = res.data
    }
  })

  // 接收自己发送的数据
  mySocket.on('BroadcastSelf', (res) => {
    console.log('socket event 【BroadcastSelf】===', res)

    if (res?.data?.frontVerify) {
      for (let i = chatRecordList.value?.length - 1; i >= 0; i--) {
        if (chatRecordList.value[i]?.frontVerify === res.data?.frontVerify) {
          chatRecordList.value[i] = res.data
          break
        }
      }
    } else {
      chatRecordList.value.push(res?.data)
      // 加载新数据保持在当前位置 或不动
      isScrollToBottom()
    }
  })

  // 用户上线下线通知
  mySocket.on('BroadcastUser', (res) => {
    console.log('socket event 【BroadcastUser】===', res)

    if (res.data.action == 'login') {
      chatUserList.value.push(res.data.data)
      offLineUsersList.value.splice(
        offLineUsersList.value.findIndex((item) => item.userId === res.data.data.userId),
        1
      )
    }
    if (res.data.action == 'disconnect') {
      offLineUsersList.value.push(res.data.data)
      chatUserList.value.splice(
        chatUserList.value.findIndex((item) => item.userId === res.data.data.userId),
        1
      )
    }
  })

  // 接收广播消息
  mySocket.on('Broadcast', (res) => {
    console.log('socket event 【Broadcast】===', res)

    chatRecordList.value.push(res.data)
    // 加载新数据保持在当前位置 或不动
    isScrollToBottom()
  })

  // 接收消息列表数据
  mySocket.on('list', (res) => {
    console.log('socket event 【list】===', res)

    scrollHeight = messageHistoryRef.value.scrollHeight
    hasReturn.value = res.data.records.length > 9 ? true : false
    chatRecordList.value = res.data.records.concat(chatRecordList.value)

    nextTick(() => {
      messageHistoryRef.value.scrollTop = isFirstGetList
        ? messageHistoryRef.value.scrollHeight
        : messageHistoryRef.value.scrollHeight - scrollHeight
    })

    isFirstGetList = false
  })
}

onActivated(() => {
  nextTick(() => {
    messageHistoryRef.value.scrollTop = messageHistoryRef.value.scrollHeight
  })
})

// 判断是否滚动
// 滚动距离 与 区域高度 在一定差值内，说明处于接近底部，新消息则应该推上去显示
// 滚动距离 与 区域高度 不在一定差值内，说明用户在看聊天记录，新消息则不应该改变试图显示区域
const isScrollToBottom = () => {
  if (messageHistoryRef.value.scrollHeight - messageHistoryRef.value.scrollTop < windowDiffValue) {
    nextTick(() => {
      messageHistoryRef.value.scrollTop = messageHistoryRef.value.scrollHeight
    })
  }
}

// 销毁
onBeforeUnmount(() => {
  mySocket.close()
  imageEditRef.value.removeCanvas()
})

// 页面滚动监听
const pageScroll = () => {
  let scrollTop = messageHistoryRef.value.scrollTop
  if (scrollTop <= 0 && hasReturn.value) {
    let messageId = ''
    if (chatRecordList.value?.length > 0) {
      messageId = chatRecordList.value[0].messageId
    }
    onGetMsgList(messageId)
  }
}

// 发送消息
const sendMessage = (message, photos, type) => {
  const randomValue = Math?.random()?.toString(36)?.substring(2, 5)
  let frontVerify = type + Date.now().toString(36) + randomValue
  mySocket.emit('message', {
    message,
    photos,
    type,
    requireId: groupId.value,
    userId: userId.value,
    // 前端验证ID，监听的接收消息事件可以关联的发送的这条数据，后续或许有其他作用
    frontVerify
  })
  // 开始计时
  sendMessageTimeout(message, photos, type, frontVerify)
}

// 发送消息超时
const sendMessageTimeout = (message, photos, type, frontVerify) => {
  chatRecordList.value.push(createMessage(message, photos, type, frontVerify))
  if (type === 'msg') {
    // 消息类型清空消息
    messageText.value = ''
  }
  // 加载新数据保持在当前位置 或不动
  isScrollToBottom()
}

// 创建一条发送中的数据
const createMessage = (message, photos, type, frontVerify) => {
  return {
    createTime: '发送中...',
    messageContent: { message: message, files: photos },
    messageType: type,
    self: 1,
    userName: '...',
    frontVerify
  }
}

// 获取消息列表
const onGetMsgList = (messageId) => {
  mySocket.emit('list', {
    messageId: messageId
  })
}

// 上传文件
const uploadSuccess = (fileList) => {
  if (fileList?.length < 1) {
    if (!messageText.value)
      return ElMessage.warning(
        t('cardProductService.productDemand.components.iMessage.pleaseInput')
      )
  } else {
    let imgFrom: any[] = []
    fileList.forEach((item) => {
      if (item.status === 'success' && item.fullUrlPath) {
        let obj = {
          fileName: item.name,
          photoUrl: item.fullUrlPath
        }
        imgFrom.push(obj)
      }
    })
    // 发送图片
    sendMessage('', imgFrom, 'img')
    // 图片类型清空文件
    nextTick(() => {
      uploadIMRef.value.clearFile()
    })
  }
  // 发送文字
  if (messageText.value) return sendMessage(messageText.value, [], 'msg')
}
const fileChange = async () => {
  if (!connectedError()) return
  const loading = ElLoading.service({
    lock: true,
    text: `Loading`,
    background: 'rgba(255, 255, 255, 0.3)'
  })
  try {
    await uploadIMRef.value.submitFile(loading).then(async (res) => {
      uploadSuccess(res)
    })
  } finally {
    loading.close()
  }
}

// 回车发送消息
const sendMessageFn = () => {
  if (!connectedError()) return
  if (!messageText.value)
    return ElMessage.warning(t('cardProductService.productDemand.components.iMessage.pleaseInput'))
  sendMessage(messageText.value, [], 'msg')
}

// 按下回车键，阻止事件默认行为，如需要回车，使用 shift + enter
const preventEnterEv = (e) => {
  e.preventDefault()
  return false
}
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');

:deep(.el-dialog__header) {
  padding: 0;
  border-bottom: none;
}
:deep(.el-dialog__body) {
  padding: 0;
}
.my-header {
  width: 100%;
  height: 40px;
  padding: 0 20px;
  align-items: center;
  background: #e2a32c;

  .text {
    font-size: 18px;
    font-weight: 500;
    color: #ffffff;
  }
}
@media screen and (max-height: 768px) {
  .content {
    height: 371px !important;
    .image-tool {
    }
  }
}
.content {
  height: 571px;
  display: flex;
  .image-tool {
    width: 600px;
    height: 100%;
    border-right: 1px solid #e6e6e6;
  }
  .message {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .message-list {
      width: 100%;
      height: 130px;
      padding: 13px 18px;
      .status {
        font-size: 18px;
        color: #333333;
        .on-line {
          font-size: 14px;
          color: #777777;
        }
      }
      .user {
        height: 60px;
        display: flex;
        align-items: center;
        flex-flow: row nowrap;
        overflow: auto;
        margin-top: 13px;
        .avatar {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          margin-right: 10px;
          padding: 3px;
          box-sizing: border-box;
        }
        .avatar-online {
          border: 2px solid #ffa409;
        }

        .avatar-un-online {
          filter: grayscale(60%) opacity(60%);
        }

        .avatar-message {
          animation: border 2s infinite;
          animation-delay: 1s;
        }
        @keyframes border {
          0% {
            transform: scale(1);
          }
          25% {
            transform: scale(0.9);
          }
          50% {
            transform: scale(1);
          }
        }
      }
    }
    .message-history {
      flex: 1;
      overflow-y: auto;
      border-top: 1px solid #e6e6e6;
      padding: 13px 18px;
      .no-more {
        width: 100%;
        font-size: 12px;
        color: #777777;
        line-height: 30px;
        text-align: center;
      }
      .one-message-left {
        text-align: left;
      }
      .one-message-right {
        text-align: right;
        justify-content: flex-end;
      }
      .one-message {
        display: flex;
        .single-img {
          width: 160px;
          height: 160px;
        }

        .image-box {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          .message-img {
            width: 36px;
            height: 36px;
            margin-right: 4px;
            margin-top: 7px;
            border-radius: 4px;
          }
        }
        .avatar {
          width: 32px;
          height: 32px;
          border-radius: 5px;
        }
        .name,
        .time {
          font-size: 12px;
          color: #777777;
          min-height: 20px;
          width: 180px;
        }

        .message-content {
          white-space: pre-wrap;
        }
        .one-name-time-left {
          display: flex;
          justify-content: flex-start;
          text-align: left;
        }
        .one-name-time-right {
          display: flex;
          justify-content: flex-end;
          text-align: left;
        }
        .message-content-left {
          max-width: 180px;
          min-height: 40px;
          background: #f7f6f5;
          padding: 10px;
          margin-left: 10px;
          border-radius: 4px;
          position: relative;
          display: inline-block;
        }
        .message-content-left::after {
          content: '';
          width: 0;
          height: 0;
          border-right: 10px solid #f7f6f5;
          border-top: 10px solid transparent;
          border-bottom: 10px solid transparent;
          border-left: 10px solid transparent;
          position: absolute;
          left: -18px;
          top: 8px;
        }
        .message-content-right {
          max-width: 180px;
          min-height: 40px;
          background: rgba(255, 164, 9, 0.1);
          padding: 10px;
          margin-right: 10px;
          border-radius: 4px;
          position: relative;
          text-align: left;
          display: inline-block;
        }
        .message-content-right::after {
          content: '';
          width: 0;
          height: 0;
          border-right: 10px solid transparent;
          border-top: 10px solid transparent;
          border-bottom: 10px solid transparent;
          border-left: 10px solid rgba(255, 164, 9, 0.1);
          position: absolute;
          right: -20px;
          top: 8px;
        }
      }
    }
  }
}

.footer {
  width: 100%;
  height: 182px;
  position: relative;
  border-top: 1px solid #e6e6e6;
  .send-box {
    position: absolute;
    bottom: 20px;
    right: 20px;
    .send-btn {
      width: 122px;
    }
  }
  .file-box {
    position: absolute;
    left: 20px;
    bottom: 10px;
  }
  .message-text {
    :deep(.el-textarea__inner) {
      padding: 20px 17px;
      box-shadow: none;
      height: 90px;
    }
  }
}
</style>
