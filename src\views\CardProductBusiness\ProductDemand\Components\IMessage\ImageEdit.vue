<template>
  <div class="image-edit">
    <div id="down" ref="imageWrapper">
      <div class="tip" v-if="isTipTextShow">{{
        t('cardProductService.productDemand.components.iMessage.imageEditingArea')
      }}</div>
      <!-- 图片层 -->
      <div class="canvas-img-box" :style="rotateDegStyle">
        <div
          v-if="imageEditUrl"
          class="canvas-img"
          :style="
            'background: url(' +
            imageEditUrl +
            ');background-repeat: no-repeat;background-size: contain;background-position: center;'
          "
        ></div>
      </div>
      <!-- 文字层 -->
      <p
        @mousedown="down"
        @mouseup="end"
        @mouseleave="end"
        v-for="(item, index) in textArr"
        :key="'p' + index"
        :class="item.className"
        :style="item.style"
        class="cursor-move break-all overflow-hidden"
      >
        {{ item.text }}
      </p>
      <!-- 矩形层 -->
      <div
        @mousedown="down"
        @mouseup="end"
        @mouseleave="end"
        v-for="(item, index) in squareArr"
        :key="'square' + index"
        :class="item.className"
        :style="item.style"
        class="cursor-move"
      ></div>
      <!-- 圆形层 -->
      <div
        @mousedown="down"
        @mouseup="end"
        @mouseleave="end"
        v-for="(item, index) in circleArr"
        :key="'circle' + index"
        :class="item.className"
        :style="item.style"
        class="cursor-move"
      ></div>
      <canvas
        id="canvas"
        :class="curType"
        ref="canvasRef"
        :width="config.canvasWidth"
        :height="config.canvasHeight"
        @mousedown.prevent.stop="MouDown"
        @mousemove.prevent.stop="MouMove"
        @mouseup.prevent.stop="isDraw = false"
        @mouseleave.prevent.stop="isDraw = false"
      ></canvas>
    </div>
    <!-- 选择框 -->
    <div class="choosebox" v-show="isShow" ref="chooseBoxRef">
      <div class="delate" @click="removeChild">
        <img class="svg" src="@/assets/imgs/makeCard/IMClose.svg" />
      </div>
      <div class="route" @mousedown.stop="w_down" @mouseup.stop="end" @mouseleave="end">
        <img class="svg" src="@/assets/imgs/makeCard/IMRoute.svg" />
      </div>
    </div>
    <div class="tools">
      <!-- 文字输入 -->
      <div class="enter-text" v-if="isWriteTextShow">
        <el-input
          style="width: 500px"
          v-model="writeTextValue"
          clearable
          maxlength="50"
          show-word-limit
          class="input input-bg"
          :placeholder="
            t('cardProductService.productDemand.components.iMessage.pleaseEnterTheContent')
          "
        />
        <el-button class="ml-20px" @click="submitText">{{ t('common.ok') }}</el-button>
      </div>
      <div class="file-edit">
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.empty')"
          placement="top-start"
        >
          <img src="@/assets/imgs/makeCard/clear.png" class="tool cursor-pointer" @click="clear" />
        </el-tooltip>
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.rubberEraser')"
          placement="top-start"
        >
          <img
            src="@/assets/imgs/makeCard/rubber.png"
            class="tool cursor-pointer"
            @click="writeClear"
          />
        </el-tooltip>
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.graffitiPen')"
          placement="top-start"
        >
          <img
            src="@/assets/imgs/makeCard/pen.png"
            class="tool-big cursor-pointer"
            @click="writePen"
          />
        </el-tooltip>
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.characters')"
          placement="top-start"
        >
          <img
            src="@/assets/imgs/makeCard/text.png"
            class="tool cursor-pointer"
            @click="writeText"
          />
        </el-tooltip>
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.rectangle')"
          placement="top-start"
        >
          <img
            src="@/assets/imgs/makeCard/square.png"
            class="tool cursor-pointer"
            @click="writeSquare"
          />
        </el-tooltip>
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.rotundity')"
          placement="top-start"
        >
          <img
            src="@/assets/imgs/makeCard/round.png"
            class="tool cursor-pointer"
            @click="writeCircle"
          />
        </el-tooltip>
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.rotateImage')"
          placement="top-start"
        >
          <img
            src="@/assets/imgs/makeCard/rotate.png"
            class="rotate cursor-pointer"
            @click="writeRotate"
          />
        </el-tooltip>
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.preserve')"
          placement="top-start"
        >
          <img
            src="@/assets/imgs/makeCard/download.png"
            class="tool cursor-pointer"
            @click="writeDown('down')"
          />
        </el-tooltip>
        <el-tooltip
          effect="light"
          :content="t('cardProductService.productDemand.components.iMessage.complete')"
          placement="top-start"
        >
          <img
            src="@/assets/imgs/makeCard/finish.svg"
            class="tool cursor-pointer"
            @click="writeShort"
          />
        </el-tooltip>
      </div>
      <div class="file-btn">
        <div class="operate_save cursor-pointer" @click="clearImg">{{
          t('cardProductService.productDemand.components.iMessage.clearImage')
        }}</div>
        <Upload
          class="upload-box"
          ref="uploadIMEditRef"
          :limit="1"
          :isShowFileList="false"
          accept=".jpg,.png,.gif,.bmp"
          :limitFormat="['jpg', 'png', 'gif', 'bmp']"
          :maxSize="1024 * 1024 * 10"
          @file-change="fileChange"
        >
          <template #btn>
            <div class="operate_save cursor-pointer">
              {{ t('cardProductService.productDemand.components.iMessage.uploadImages') }}</div
            >
          </template>
        </Upload>
      </div>
    </div>
  </div>
  <CutImage
    v-if="isCutShow"
    :isCutShow="isCutShow"
    :imageSrc="imageSrc"
    @handle-close="handleClose"
    @send-cut-img="sendCutImg"
  />
</template>

<script lang="ts" setup>
import Upload from '../Upload.vue'
import html2canvas from 'html2canvas'
import CutImage from './CutImage.vue'
import { createImgName } from '../../Common/index'
const { t } = useI18n()
const emit = defineEmits(['send-cut-img'])

let imageEditUrl = ref('')

// 是否显示提示语（图片编辑区）
let isTipTextShow = ref(true)

// canvas DOM
const canvasRef = ref<any>()
// canvas 绘制
let ctx = ref<any>()
// 是否画图
let isDraw = ref<any>(false)

// 画布绘制区域配置
let curType = ref('') //鼠标样式
// let canvasBox = ref('') //canvas缓存
let config = reactive({
  gloabal: 'source-over',
  isWrite: false, // 是否开启画图
  // lastWriteSpeed: 1, //书写速度 [Number] 可选
  // lastWriteWidth: 2, //下笔的宽度 [Number] 可选
  lineCap: 'round', //线条的边缘类型 [butt]平直的边缘 [round]圆形线帽 [square]	正方形线帽
  lineJoin: 'round', //线条交汇时边角的类型  [bevel]创建斜角 [round]创建圆角 [miter]创建尖角。
  canvasWidth: 600, //canvas宽高 [Number] 可选
  canvasHeight: 532, //高度  [Number] 可选
  // isShowBorder: false, //是否显示边框 [可选]
  // bgColor: '#fcc', //背景色 [String] 可选
  // borderWidth: 1, // 网格线宽度  [Number] 可选
  // borderColor: '#ff787f', //网格颜色  [String] 可选
  writeWidth: 5, //基础轨迹宽度  [Number] 可选
  // maxWriteWidth: 250, // 写字模式最大线宽  [Number] 可选
  // minWriteWidth: 2, // 写字模式最小线宽  [Number] 可选
  writeColor: '#000' // 轨迹颜色  [String] 可选
  // isSign: false, //签名模式 [Boolean] 默认为非签名模式,有线框, 当设置为true的时候没有任何线框
  // imgType: 'png' //下载的图片格式  [String] 可选为 jpeg  canvas本是透明背景的
})
// 笔记本适配
if (window.screen.height <= 768) {
  // config.canvasWidth = 400
  config.canvasHeight = 332
}
let pointes = reactive<any>({
  first: { x: '', y: '' }, //第一个点
  allPoint: [], // 所有点
  last: { x: '', y: '' } // 最后一个点
})

// 鼠标按下
function MouDown(mou) {
  clearOptions()
  if (config.isWrite) {
    isDraw.value = true
    let point = { x: '', y: '' }
    point.x = mou.offsetX
    point.y = mou.offsetY
    pointes.first.x = mou.offsetX
    pointes.first.y = mou.offsetY
    pointes.allPoint.push(point)
  }
}

// 鼠标移动
function MouMove(mou) {
  if (isDraw.value && config.isWrite) {
    let point = { x: '', y: '' }
    point.x = mou.offsetX
    point.y = mou.offsetY
    pointes.last.x = mou.offsetX
    pointes.last.y = mou.offsetY
    pointes.allPoint.push(point)
    lineDraw()
  }
}

// 画线
function lineDraw() {
  ctx.value.beginPath()
  ctx.value.moveTo(pointes.first.x, pointes.first.y)
  ctx.value.lineTo(pointes.last.x, pointes.last.y)
  ctx.value.strokeStyle = config.writeColor
  ctx.value.lineWidth = config.writeWidth
  ctx.value.lineCap = config.lineCap
  ctx.value.lineJoin = config.lineJoin
  ctx.value.stroke()
  // 将最后一个点作为下一个点的起点
  pointes.first.x = pointes.last.x
  pointes.first.y = pointes.last.y
}

// 清除
function clear() {
  ctx.value.height = config.canvasHeight
  ctx.value.clearRect(0, 0, config.canvasWidth, config.canvasHeight)
  config.isWrite = false
  curType.value = ''
  squareArr.value = []
  circleArr.value = []
  textArr.value = []
  isRubber.value = false
  isPen.value = false
  getDefault()
}

// 橡皮檫
let isRubber = ref(false)
function writeClear() {
  clearOptions()
  isRubber.value = !isRubber.value
  if (isRubber.value) {
    isPen.value = false
    config.isWrite = true
    canvasRef.value.style.zIndex = 998
    curType.value = 'cur-rubber'
    config.writeWidth = 10
    config.gloabal = 'destination-out'
    ctx.value.globalCompositeOperation = config.gloabal
  } else {
    curType.value = ''
    config.isWrite = false
    getDefault()
  }
}

// 画笔
let isPen = ref(false)
function writePen() {
  clearOptions()
  isPen.value = !isPen.value
  if (isPen.value) {
    isRubber.value = false
    config.isWrite = true
    canvasRef.value.style.zIndex = 998
    curType.value = 'cur-pen'
    config.writeWidth = 5
    config.gloabal = 'source-over'
    ctx.value.globalCompositeOperation = config.gloabal
  } else {
    curType.value = ''
    config.isWrite = false
    getDefault()
  }
}

// 画正方形
interface ISquareArr {
  className: string
  style: string
}
let squareArr = ref<ISquareArr[]>([])
function writeSquare() {
  config.isWrite = false
  curType.value = ''
  isRubber.value = false
  isPen.value = false
  getDefault()
  getCanvasItem('square')
}

// 添加元素
const getCanvasItem = (type) => {
  //生成位置随机数
  let left = random(1, 60)
  let top = random(1, 40)
  function random(min, max) {
    return Math.floor(Math.random() * (max - min)) + min
  }
  switch (type) {
    case 'text':
      let textNum = 1
      textNum = sameName(textNum, 'text', textArr)
      let textData = returnClass(textNum, 'text', top, left)
      textArr.value.push({ text: writeTextValue.value, ...textData })
      break
    case 'square':
      let squareNum = 1
      squareNum = sameName(squareNum, 'square', squareArr)
      let squareData = returnClass(squareNum, 'square', top, left)
      squareArr.value.push(squareData)
      break
    case 'circle':
      let circleNum = 1
      circleNum = sameName(circleNum, 'circle', circleArr)
      let circleData = returnClass(circleNum, 'circle', top, left)
      circleArr.value.push(circleData)
      break
  }
}

function sameName(num, type, arr) {
  let className = type + num
  for (let i = 0; i < arr.value.length; i++) {
    if (className == arr.value[i].className) {
      num++
      return sameName(num, type, arr)
    }
  }
  return num
}
function returnClass(num, type, top, left) {
  let styleClass = ''
  switch (type) {
    case 'text':
      styleClass = '%;width:200px;padding: 0 10px;font-size:18px;z-index:'
      break
    case 'square':
      styleClass = '%;width:100px;height:100px;border:1px solid black;z-index:'
      break
    case 'circle':
      styleClass = '%;width:100px;height:100px;border-radius:50%;border:1px solid black;z-index:'
      break
  }
  return {
    className: type + num,
    style: 'position:absolute;top:' + top + '%;left:' + left + styleClass + (num + 1)
  }
}

const w_down = (e) => {
  let moveDiv = document.getElementsByClassName(className.value)[0] as HTMLElement
  if (!moveDiv) return
  let x = e.clientX
  let y = e.clientY
  let ww = document.documentElement.clientWidth
  let wh = window.innerHeight
  let cw = moveDiv.clientWidth
  let cy = moveDiv.clientHeight
  chooseZIndex.value = moveDiv.style.zIndex
  moveDiv.style.zIndex = '990'

  document.onselectstart = () => {
    return false
  }

  document.onmousemove = function (ev) {
    let event = ev || window.event
    event.preventDefault()
    if (event.clientY < 0 || event.clientX < 0 || event.clientY > wh || event.clientX > ww) {
      return false
    }
    let nx = event.clientX - x //移动距离X
    let ny = event.clientY - y //移动距离Y
    let cxPum = cw + nx
    let cyPum = cy + ny
    if (cxPum > 30 && cyPum > 30) {
      moveDiv.style.width = cxPum + 'px'
      moveDiv.style.height = cyPum + 'px'
      chooseBoxRef.value.style.width = cxPum - ((scale.value - 1) * moveDiv.clientWidth) / 2 + 'px'
      chooseBoxRef.value.style.height =
        cyPum - ((scale.value - 1) * moveDiv.clientHeight) / 2 + 'px'
    }
  }
}

const down = (e) => {
  config.isWrite = false
  curType.value = ''
  chooseClass(e)
  if (!className.value) return

  let x = e.clientX
  let y = e.clientY
  let moveDiv = document.getElementsByClassName(className.value)[0] as HTMLElement
  let dx = moveDiv.offsetLeft //物体初始位置距左边
  let dy = moveDiv.offsetTop //物体初始位置距顶部
  let ww = document.documentElement.clientWidth
  let wh = window.innerHeight
  chooseZIndex.value = moveDiv.style.zIndex
  moveDiv.style.zIndex = '990'

  document.onselectstart = () => {
    return false
  }

  document.onmousemove = function (ev) {
    let event = ev || window.event
    event.preventDefault()
    if (event.clientY < 0 || event.clientX < 0 || event.clientY > wh || event.clientX > ww) {
      return false
    }
    if (moveDiv) {
      let nx = event.clientX - x //移动距离X
      let ny = event.clientY - y //移动距离Y
      let xPum = dx + nx
      let yPum = dy + ny
      moveDiv.style.left = xPum + 'px'
      moveDiv.style.top = yPum + 'px'
      chooseBoxRef.value.style.left = xPum - ((scale.value - 1) * moveDiv.clientWidth) / 2 + 'px'
      chooseBoxRef.value.style.top = yPum - ((scale.value - 1) * moveDiv.clientHeight) / 2 + 'px'
    }
  }
}

//鼠标释放时候的函数
const end = () => {
  let moveDiv = document.getElementsByClassName(className.value)[0] as HTMLElement
  if (moveDiv) {
    moveDiv.style.zIndex = chooseZIndex.value
  }
  // 清空鼠标移动监听
  document.onmousemove = null
  // 允许鼠标选中页面数据
  document.onselectstart = () => {
    return true
  }
}

let isShow = ref(false)
let className = ref('')
let scale = ref(1)
let deg = ref(0)
let chooseBoxRef = ref()
let chooseZIndex = ref('')
const chooseClass = (e) => {
  e.stopPropagation() //防止冒泡
  let cName = e.target.className
  let tagName = e.target.tagName
  if (tagName == 'P' || tagName == 'p') {
    isWriteTextShow.value = true
    writeTextValue.value = e.target.innerHTML.trim()
  } else {
    isWriteTextShow.value = false
    writeTextValue.value = ''
  }
  if (cName) {
    isShow.value = true
    className.value = cName
    chooseBoxRef.value.style.left =
      e.target.offsetLeft - ((scale.value - 1) * (e.target.clientWidth + 2)) / 2 + 'px'
    chooseBoxRef.value.style.border = '1px solid red'
    chooseBoxRef.value.style.top =
      e.target.offsetTop - ((scale.value - 1) * (e.target.clientHeight + 2)) / 2 + 'px'
    chooseBoxRef.value.style.width = (e.target.clientWidth + 2) * scale.value + 'px'
    chooseBoxRef.value.style.height = (e.target.clientHeight + 2) * scale.value + 'px'
    chooseBoxRef.value.style.transform = 'rotate(' + deg.value + 'deg)'
  }
}

//删除元素
const removeChild = () => {
  var div = document.getElementsByClassName(className.value)[0] as HTMLElement
  div?.parentNode?.removeChild(div)
  getDefault()
}

// 旋转
let rotateDegStyle = ref('')
let rotateDeg = ref(0)
function writeRotate() {
  if (!imageEditUrl.value) return
  config.isWrite = false
  curType.value = ''
  isRubber.value = false
  isPen.value = false
  getDefault()
  rotateDeg.value = rotateDeg.value + 90
  if ((rotateDeg.value / 90) % 2 != 0) {
    rotateDegStyle.value =
      'transform: rotate(' + rotateDeg.value + 'deg) scale(0.88);transition: all 0.8s;'
  } else {
    rotateDegStyle.value = 'transform: rotate(' + rotateDeg.value + 'deg);transition: all 0.8s;'
  }
}

// 下载
let imageWrapper = ref()
const writeDown = (type) => {
  const loading = ElLoading.service({
    lock: true,
    text: `Loading`,
    background: 'rgba(255, 255, 255, 0.3)'
  })
  isTipTextShow.value = false
  nextTick(() => {
    html2canvas(imageWrapper.value, {
      useCORS: true,
      scale: 1,
      logging: false,
      backgroundColor: null,
      allowTaint: true
    })
      .then((canvas) => {
        loading.close()
        isTipTextShow.value = true
        const base64 = canvas.toDataURL().replace(/^data:image\/(png|jpg);base64,/, '')
        let base64Img = `data:image/png;base64,${base64}`
        if (type === 'cut') {
          imageSrc.value = base64Img
          isCutShow.value = true
          return
        }
        let saveLink = document.createElement('a')
        saveLink.href = base64Img
        saveLink.setAttribute('download', createImgName())
        saveLink.click()
      })
      .catch((e) => {
        loading.close()
        isTipTextShow.value = true
        console.log('error', e)
      })
  })
}

// 裁剪
const isCutShow = ref(false)
const imageSrc = ref('')
const handleClose = () => {
  isCutShow.value = false
}
const writeShort = () => {
  writeDown('cut')
}

// 清空图片
const clearImg = () => {
  imageEditUrl.value = ''
  rotateDeg.value = 0
  rotateDegStyle.value = 'transform: rotate(' + rotateDeg.value + 'deg);'
  uploadIMEditRef.value.clearFile()
}

// 发送裁剪图片
const sendCutImg = (src) => {
  clearImg()
  clear()
  emit('send-cut-img', src)
}

// 圆圈
interface ICircleArr {
  className: string
  style: string
}
let circleArr = ref<ICircleArr[]>([])
const writeCircle = () => {
  config.isWrite = false
  curType.value = ''
  isRubber.value = false
  isPen.value = false
  getDefault()
  getCanvasItem('circle')
}

// 文本
interface ITextArr {
  className: string
  style: string
  text: string
}
let textArr = ref<ITextArr[]>([])
const writeTextValue = ref('')
const isWriteTextShow = ref(false)
const writeText = () => {
  getDefault()
  config.isWrite = false
  curType.value = ''
  isRubber.value = false
  isPen.value = false
  writeTextValue.value = ''
  isWriteTextShow.value = !isWriteTextShow.value
  config.writeWidth = 5
  config.gloabal = 'source-over'
  ctx.value.globalCompositeOperation = config.gloabal
}

const submitText = () => {
  if (!writeTextValue.value)
    return ElMessage.error(
      t('cardProductService.productDemand.components.iMessage.pleaseEnterText')
    )
  if (className.value) {
    var style_texts = document.getElementsByClassName(className.value)[0] as HTMLElement
    style_texts.innerText = writeTextValue.value
  } else {
    getCanvasItem('text')
  }
  clearOptions()
}

// 默认样式
const getDefault = () => {
  config.writeWidth = 5
  config.gloabal = 'source-over'
  ctx.value.globalCompositeOperation = config.gloabal
  canvasRef.value.style.zIndex = 1
  clearOptions()
}

const clearOptions = () => {
  writeTextValue.value = ''
  isWriteTextShow.value = false
  className.value = ''
  isShow.value = false
}

onMounted(() => {
  ctx.value = canvasRef.value.getContext('2d')
})

// 销毁
const removeCanvas = () => {
  canvasRef.value.remove()
}

// 上传文件
const uploadIMEditRef = ref()
const uploadSuccess = (data) => {
  imageEditUrl.value = ''
  rotateDeg.value = 0

  rotateDegStyle.value = 'transform: rotate(' + rotateDeg.value + 'deg);'
  nextTick(() => {
    imageEditUrl.value = data[data?.length - 1].imgBase64
    uploadIMEditRef.value.clearFile()
  })
}
const fileChange = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: `Loading`,
    background: 'rgba(255, 255, 255, 0.3)'
  })
  await uploadIMEditRef.value
    .submitFileBase64()
    .then(async (res) => {
      uploadSuccess(res)
      loading.close()
    })
    .catch((_err) => {
      loading.close()
    })
}

defineExpose({
  removeCanvas
})
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.image-edit {
  width: 100%;
  height: 100%;
  position: relative;
  background: #e6e6e6;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  @media screen and (max-height: 768px) {
    #down,
    .canvas-img-box {
      // width: 400px !important;
      height: 330px !important;
    }
    .canvas-img {
      height: 320px !important;
    }
    #canvas {
      // width: 400px !important;
      height: 332px !important;
    }
  }
  #down {
    width: 100%;
    height: 530px;
  }
  .canvas-img-box {
    width: 600px;
    height: 530px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: absolute;
    z-index: 0;
  }
  .canvas-img {
    width: 100%;
    height: 520px;
  }
  #canvas {
    width: 600px;
    height: 532px;
    position: absolute;
    z-index: 1;
  }
  .tip {
    width: 100px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    position: absolute;
    top: calc(50% - 13px);
    left: calc(50% - 50px);
    color: #cccccc;
    font-size: 14px;
    z-index: 0;
  }
  .tools {
    width: 100%;
    height: 40px;
    position: absolute;
    bottom: 0;
    background: #ffffff;
    display: flex;
    align-items: center;
    padding-left: 10px;
    z-index: 999;
    justify-content: space-between;
    .file-edit {
      width: 320px;
      display: flex;
      align-items: center;
    }
    .file-btn {
      flex: 1;
      display: flex;
      justify-content: space-between;
      margin-right: 10px;
      .upload-box {
        width: 120px;
      }
      .operate_save {
        width: 120px;
        height: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        font-size: 13px;
        color: #ffa409;
        border: 1px solid #ffa409;
        border-radius: 4px;
      }
    }
    .tool {
      margin-right: 16px;
      width: 15px;
      height: 15px;
    }
    .tool-big {
      margin-right: 16px;
      width: 21px;
      height: 16px;
    }
    .rotate {
      margin-right: 16px;
      width: 14px;
      height: 16px;
    }
    .enter-text {
      width: 100%;
      height: 50px;
      overflow: hidden;
      position: absolute;
      top: -50px;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      border-bottom: 1px solid #f7f6f5;
    }
  }
  .choosebox {
    position: absolute;
    z-index: 998;
    pointer-events: none;

    .delate .svg {
      width: 44px;
      height: 44px;
      position: absolute;
      left: -22px;
      bottom: -22px;
      pointer-events: auto !important;
      cursor: pointer;
    }

    .route .svg {
      width: 44px;
      height: 44px;
      bottom: -22px;
      right: -22px;
      position: absolute;
      pointer-events: auto !important;
      cursor: row-resize;
    }
  }
}
.cur-rubber {
  cursor: url(@/assets/imgs/makeCard/rubber.png) 0 30, auto;
}
.cur-pen {
  // cursor: url(@/assets/imgs/makeCard/pen.png), auto;
  cursor: crosshair;
}
</style>
