<template>
  <el-form ref="makeCardList" :model="queryParams" :inline="true">
    <el-form-item prop="makeCardRequirementInfoTitle">
      <el-select
        class="search-input input-bg"
        v-model="queryParams.makeCardRequirementInfoTitle"
        :placeholder="t('cardProductService.productDemand.components.searchRequirementTitle')"
        filterable
        remote
        :loading="loading"
        clearable
        @clear="titleClear"
      >
        <template #prefix>
          <el-icon :size="22">
            <Search />
          </el-icon>
        </template>
        <el-option
          v-for="item in options"
          :key="item.makeCardRequirementInfoTitle"
          :label="item.makeCardRequirementInfoTitle"
          :value="item.makeCardRequirementInfoTitle"
        />
      </el-select>
    </el-form-item>
    <!-- <el-form-item>
      <el-input
        class="search-input"
        v-model="queryParams.productName"
        :placeholder="t('cardProductService.productDemand.components.pleaseEnterTheProductName')"
        clearable
      />
    </el-form-item> -->

    <!-- 需求类型 -->
    <el-form-item>
      <el-select
        v-model="queryParams.makeCardRequirementRtype"
        :placeholder="
          t('cardProductService.productDemand.components.pleaseEnterTheRequirementType')
        "
        clearable
        style="width: 240px"
        @clear="selectClear"
      >
        <el-option :label="t('cardProductService.productDemand.components.whole')" value="" />
        <el-option
          :label="t('cardProductService.productDemand.components.designRequirements')"
          value="0"
        />
        <el-option
          :label="t('cardProductService.productDemand.components.sampleRequirements')"
          value="1"
        />
      </el-select>
    </el-form-item>

    <el-form-item>
      <el-button type="primary" v-track:click.btn @click="onSubmit">{{
        t('common.query')
      }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import * as makeCardApi from '@/api/makeCardService/index'
import type { makeCardListReqType, makeCardListType } from '@/api/makeCardService/types'
const { t } = useI18n()
const emits = defineEmits(['getList'])

/** 配置表单数据 */
const queryParams: makeCardListReqType = reactive({
  makeCardRequirementInfoTitle: '',
  productName: '',
  makeCardRequirementRtype: ''
})

const options = ref<makeCardListType[]>([])

const makeCardList = ref<FormInstance>()
/** 配置表单数据结束 */

let loading = ref(false)

const remoteGetTitle = async (query: string) => {
  loading.value = true
  try {
    const { data } = await makeCardApi.getMakeCardTitleApi({
      makeCardRequirementInfoTitle: query,
      productName: ''
    })
    options.value = data
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  remoteGetTitle('')
})

/** 子组件数据暴露start */
defineExpose({
  queryParams
})
/** 子组件数据暴露end */

/** 弹框方法（重置、确定）start */
const onSubmit = () => {
  emits('getList', queryParams)
}
/** 弹框方法（重置、确定）start */

/** 下拉框清除按钮 */
const selectClear = () => {
  queryParams.makeCardRequirementRtype = ''
}
const titleClear = () => {
  queryParams.makeCardRequirementInfoTitle = ''
}
</script>

<style lang="less" scoped>
@import url('../Common/common.less');
.search-input {
  width: 250px;
}
</style>
