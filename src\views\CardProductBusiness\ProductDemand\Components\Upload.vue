<template>
  <div class="flex box-tip">
    <el-upload
      :multiple="props.multiple"
      :disabled="props.disabled"
      :limit="props.limit"
      :accept="props.accept"
      :show-file-list="props.isShowFileList"
      :auto-upload="props.autoUpload"
      v-model:file-list="fileList"
      ref="uploadModuleRef"
      :before-upload="beforeUpload"
      :on-remove="onRemove"
      :on-change="fileChange"
      :on-exceed="onExceedHandle"
      class="upload"
    >
      <slot name="btn">
        <!-- <el-button class="btn-style btn-upload"> -->
        <el-button type="primary" size="large">
          {{ props.btnText }}
        </el-button>
      </slot>
      <template #tip>
        <div class="el-upload__tip">{{ props.uploadTip }}</div>
      </template>
      <template #file="uploadFileSlot">
        <div class="file-list">
          <div class="icon">
            <el-icon size="12" v-if="uploadFileSlot.file.isImage">
              <Picture />
            </el-icon>
            <el-icon size="12" v-else>
              <Document />
            </el-icon>
          </div>
          <div class="content">
            <div class="flex">
              <div class="name" @click="preViewImage(uploadFileSlot.file)">{{
                uploadFileSlot.file.name
              }}</div>
              <div
                class="slot-button text cursor-pointer"
                @click="onSlotRemove(uploadFileSlot.file.uid)"
              >
                {{ t('common.delete') }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { Document, Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as makeCardApi from '@/api/makeCardService/index'

const uploadModuleRef = ref()

const fileList = ref<any>([])
const { t } = useI18n()
const props = defineProps({
  showFileList: {
    type: Array,
    default: () => []
  },
  uploadTip: {
    type: String,
    default: ''
  },
  btnText: {
    type: String,
    default: '上传文件'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 8
  },
  multiple: {
    type: Boolean,
    default: false
  },
  uploadData: {
    type: Object,
    default: () => {}
  },
  //是否自动上传
  autoUpload: {
    type: Boolean,
    default: false
  },
  //上传最大字节限制
  maxSize: {
    type: Number,
    default: 1024 * 1024
  },
  mergeUpload: {
    type: Boolean,
    default: false
  },
  isShowFileList: {
    type: Boolean,
    default: true
  },
  accept: {
    type: String,
    default: ''
  },
  limitFormat: {
    type: Array,
    default: () => []
  }
})

const { maxSize } = toRefs(props)

// 监听页面展示
watch(
  () => props.showFileList,
  () => {
    // 如果存在已上传的图片列表，进行回显
    if (props.showFileList && props.showFileList.length) {
      let showFileList: any[] = []
      props.showFileList.forEach((file: any) => {
        const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
        let isImage = ['png', 'gif', 'jpg'].includes(fileType)
        showFileList.push({
          name: file.name,
          url: file.url,
          status: file.status,
          placeholder: file.placeholder,
          isImage
        })
      })
      fileList.value = showFileList
    }
  },
  { immediate: true, deep: true }
)

const emit = defineEmits(['success', 'file-change', 'delete-file'])

function fileChange(_file, fileArr) {
  fileList.value = fileArr
  if (!props.autoUpload) {
    let canUpload = verifyFile(_file)
    if (!canUpload) {
      fileArr.forEach((v: any, i) => {
        if (v.uid === _file.uid) {
          fileList.value.splice(i, 1)
        }
      })
    }
  }
  emit('file-change', fileList.value)
}

function onSlotRemove(uid) {
  let index: any = 0
  fileList.value.forEach((v: any, i) => {
    if (v.uid == uid) {
      index = i
    }
  })
  fileList.value.splice(index, 1)
  emit('delete-file', fileList.value)
}

const submitFile = async (loading) => {
  return new Promise(async (resolve, reject) => {
    const loadingUpFileList: any = []
    const formData: FormData = new FormData()
    fileList.value.forEach((file) => {
      if (file.status !== 'success' && !file.url) {
        loadingUpFileList.push(file)
      }
    })
    if (loadingUpFileList?.length < 1) {
      return resolve(fileList.value)
    }
    loadingUpFileList.forEach((file) => {
      formData.append('fileList', file.raw)
    })
    const makeCardFileVerifyIds = loadingUpFileList.map((item) => {
      return item.uid
    })
    formData.append('makeCardFileVerifyIds', makeCardFileVerifyIds)
    formData.append('number', props.limit.toString())
    formData.append('restrict', (props.maxSize / 1024 / 1024).toString())
    try {
      const onUploadProgress = (res) => {
        let progress = Number(((res.loaded / res.total) * 100).toFixed(0))
        loading.setText(`Loading ${progress}%`)
      }
      const data = await makeCardApi.uploadFileApi(formData, onUploadProgress)
      data.data.forEach((element) => {
        fileList.value.forEach((item, fileIndex) => {
          if (element.makeCardFileVerifyId == item.uid) {
            fileList.value[fileIndex].status = 'success'
            fileList.value[fileIndex].placeholder = t(
              'cardProductService.productDemand.components.uploadSuccessful'
            )
            fileList.value[fileIndex].url = element.eosName
            fileList.value[fileIndex].fullUrlPath = element.path
          }
        })
      })
      return resolve(fileList.value)
    } catch (e) {
      ElMessage.error(t('cardProductService.productDemand.components.fileUploadFailed'))
      return reject(false)
    }
  })
}

const submitFileBase64 = () => {
  return new Promise(async (resolve, reject) => {
    try {
      if (fileList?.value?.length < 1) {
        return reject()
      }
      await fileList.value.forEach(async (item, index) => {
        fileList.value[index].imgBase64 = await fileToBase64(item.raw)
        if (index === fileList?.value?.length - 1) {
          return resolve(fileList.value)
        }
      })
    } catch (e) {
      ElMessage.error(t('cardProductService.productDemand.components.fileUploadFailed'))
      return reject(false)
    }
  })
}

const fileToBase64 = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      const base64 = reader.result
      resolve(base64)
    }
    reader.readAsDataURL(file)
  })
}

const onRemove = (file, fileArr) => {
  ElMessage.warning(
    t('cardProductService.productDemand.components.uploadTips1') + '【' + file.name + '】'
  )
  emit('delete-file', fileArr)
}

// 上传之前钩子
const beforeUpload = (file) => {
  return verifyFile(file)
}
function verifyFile(file) {
  if (file.size > maxSize.value) {
    ElMessage.error(t('cardProductService.productDemand.components.uploadTips2'))
    return false
  }
  // 校验是否重复
  let uploadFileList = JSON.parse(JSON.stringify(fileList.value))
  let nameSize = 0 // ==1 表示第一次出现 ==2表示出现多次
  for (let i = 0; i < uploadFileList.length; i++) {
    if (uploadFileList[i].name == file.name) {
      nameSize = nameSize + 1
    }
  }
  if (nameSize >= 2) {
    ElMessage.error(t('cardProductService.productDemand.components.fileRepeat'))
    return false
  }
  if (props.limitFormat.includes('*')) {
    return true
  }
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
  let fileSuffix = props.limitFormat.includes(fileType)
  if (!fileSuffix) {
    ElMessage.error(t('cardProductService.productDemand.components.uploadTips3'))
  }
  return fileSuffix
}
// 超出上传数量
const onExceedHandle = () => {
  ElMessage.error(t('cardProductService.productDemand.components.uploadTips5'))
}

// 检验是否上传完成
const checkComplete = () => {
  return new Promise<boolean>((resolve, reject) => {
    if (fileList.value.length > 0) {
      let arr = fileList.value.map((item) => {
        return item.url
      })
      if (arr.includes(undefined)) {
        ElMessage.warning(t('cardProductService.productDemand.components.uploadTips4'))
        reject(false)
      } else {
        resolve(true)
      }
    } else {
      resolve(true)
    }
  })
}

// 清空待上传文件
const clearFile = () => {
  fileList.value.splice(0, fileList?.value?.length)
}

// 预览文件
import envController from '@/controller/envController'

const preViewImage = async (file) => {
  let fileType = file.name.split('.').pop()
  let fileList = ['jpg', 'png', 'gif', 'jpeg', 'pdf', 'webp']
  if (!fileList.includes(fileType)) {
    return ElMessage.warning('只支持图片和pdf预览')
  }

  if (file.raw) {
    const _file = file.raw
    let blob = new Blob([_file], { type: _file.type })
    let resultUrl = window.URL.createObjectURL(blob)
    window.open(`${resultUrl}`, '_blank')
  } else {
    window.open(`${envController.getOssUrl()}/${file.url}`, '_blank')
  }
}

defineExpose({
  fileList,
  clearFile,
  checkComplete,
  submitFile,
  submitFileBase64
})
</script>

<style lang="less" scoped>
@import url('../Common/common.less');
.box-tip {
  align-items: center;
  .el-upload__tip {
    line-height: 16px;
    color: darkgray;
    width: 520px;
    position: absolute;
    top: 10px;
    left: 180px;
  }
  .btn-upload {
    width: 162px;
  }
  .upload {
    width: 300px;
    margin-right: 20px;
    position: relative;
  }
}
.file-list {
  min-width: 300px;
  height: 32px;
  display: flex;
  align-items: center;
  .icon {
    width: 12px;
    height: 12px;
    display: flex;
    margin-left: 10px;
  }
  .content {
    flex: 1;
    margin-left: 8px;
    .name {
      width: 220px;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;
    }
    .text {
      font-size: 14px;
      color: #e2a32c;
      margin-left: 10px;
    }
    .slot-button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
