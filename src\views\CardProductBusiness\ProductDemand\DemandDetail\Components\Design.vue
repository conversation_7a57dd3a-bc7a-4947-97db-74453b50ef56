<template>
  <el-descriptions class="mt-20px msg-box" :column="1">
    <el-descriptions-item
      :label="t('cardProductService.productDemand.demandDetail.designManager')"
      >{{ designList?.split(',')[0] || '--' }}</el-descriptions-item
    >
  </el-descriptions>
  <!-- <el-button
    type="primary"
    size="large"
    style="margin-top: 20px"  
    @click="confirmCase"
    :disabled="isDemandClose(props.makeCardDetail.makeCardRequirementInfoPhase)"
  >
    {{ t('cardProductService.productDemand.demandDetail.confirmThePlan') }}
  </el-button> -->
  <el-button
    type="primary"
    size="large"
    style="margin-top: 20px"
    @click="chatService"
    :loading="isImLoading"
    :disabled="isDemandClose(props.makeCardDetail.makeCardRequirementInfoPhase)"
    v-if="designList"
  >
    {{ t('cardProductService.productDemand.demandDetail.onlineCommunication') }}
  </el-button>
  <el-form ref="makeCardList" :model="queryParams" :inline="false" class="filter-box" v-if="false">
    <el-row :gutter="10">
      <!-- 选择设计师 -->
      <el-col :md="10" :lg="10" :xl="10">
        <el-form-item label="" prop="designSchemeInfoStylist" style="margin-bottom: 0 !important">
          <el-select
            class="search-input"
            v-model="queryParams.designSchemeInfoStylist"
            :placeholder="t('cardProductService.productDemand.designRecommend.pleaseSelect')"
            filterable
            remote
            :loading="loading"
            clearable
          >
            <template #prefix>
              <el-icon :size="22" />
            </template>
            <el-option
              v-for="item in designList?.split(',')"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <!-- 更新日期 -->
      <el-col :md="10" :lg="10" :xl="10">
        <el-form-item
          label=""
          prop="designSchemeInfoUpdateDate"
          style="margin-bottom: 0 !important"
        >
          <el-date-picker
            v-model="queryParams.designSchemeInfoUpdateDate"
            type="date"
            :placeholder="t('cardProductService.productDemand.designRecommend.pleaseSelect')"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :md="4" :lg="4" :xl="4">
        <el-button class="" @click="filterHandler">
          {{ t('cardProductService.productDemand.demandDetail.query') }}
        </el-button>
      </el-col>
    </el-row>
  </el-form>
  <!-- 表格 -->
  <el-table
    v-loading="loading"
    :data="tableData"
    :header-cell-style="{
      background: '#F8F8F8',
      color: '#333333',
      height: '65px'
    }"
    :row-style="{ height: '65px' }"
    style="width: 100%; margin-top: 20px"
    max-height="650px"
    @selection-change="handleSelectionChange"
    class="table-image-wrap"
    border
    resizable
  >
    <!-- <el-table-column type="selection" width="55" :selectable="checkSelectSet" /> -->
    <el-table-column
      :label="t('cardProductService.productDemand.demandDetail.number')"
      width="100px"
    >
      <template #default="scope">
        {{ scope.$index + 1 }}
      </template>
    </el-table-column>
    <el-table-column
      prop="designSchemeInfoSchemename"
      :label="t('cardProductService.productDemand.demandDetail.schemeName')"
      min-width="200"
    />
    <el-table-column
      prop="remark"
      :label="t('cardProductService.productDemand.demandDetail.designScheme')"
      min-width="300"
    >
      <template #default="scope">
        <!-- <span class="cursor-pointer" @click="openDialog('viewDesign', scope.row)">{{
          scope.row.designSchemeInfoName
        }}</span> -->
        <div
          class="imageName"
          style="color: rgb(226, 163, 44)"
          @click="openDialog('viewDesign', scope.row)"
          v-for="(item, ind) in scope.row.imageList"
          :key="item"
        >
          <el-tooltip
            :key="`imageList_${ind}`"
            class="box-item"
            effect="dark"
            :content="`${item}`"
            placement="top-start"
          >
            {{ item }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <!-- <el-table-column
      prop="designSchemeInfoStylist"
      :label="t('cardProductService.productDemand.demandDetail.designer')"
      width="200"
    /> -->
    <el-table-column
      prop="designSchemeInfoUpdateDate"
      :label="t('cardProductService.productDemand.demandDetail.updateTime')"
      width="200"
    />
    <!-- <el-table-column
      prop="designSchemeInfoStatus"
      :label="t('cardProductService.productDemand.demandDetail.state')"
      width="120"
    >
      <template #default="scope">
        <span>
          {{ designSchemeInfoStatusEnum[scope.row.designSchemeInfoStatus] }}
        </span>
      </template>
    </el-table-column> -->
    <!-- <el-table-column
      prop="designSchemeInfoReceipt"
      :label="t('cardProductService.productDemand.demandDetail.receiptInformation')"
      width="320"
      show-overflow-tooltip
    >
      <template #default="scope">
        <span>{{ scope.row.designSchemeInfoReceipt || '--' }}</span>
      </template>
    </el-table-column> -->
    <!-- <el-table-column fixed="right" :label="t('common.operate')" width="200">
      <template #default="scope">
        <el-button
          style="color: #e2a32c"
          link
          @click="openDialog('viewBackMsg', scope.row)"
          v-if="scope.row.designSchemeInfoStatus === 1"
        >
          {{ t('cardProductService.productDemand.demandDetail.viewReceipts') }}
        </el-button>
        <el-button
          style="color: #e2a32c"
          link
          @click="openDialog('verifyScheme', scope.row)"
          v-if="scope.row.designSchemeInfoStatus === 0"
          :disabled="isDemandClose(props.makeCardDetail.makeCardRequirementInfoPhase)"
        >
          {{ t('cardProductService.productDemand.demandDetail.confirmThePlan') }}
        </el-button>
      </template>
    </el-table-column> -->
  </el-table>
  <!-- 各种弹窗 -->
  <DialogInfo
    :isDiaLogShow="isDiaLogShow"
    :diaLogTitle="diaLogTitle"
    :openType="openType"
    :diaData="diaData"
    :makeCardDetail="props.makeCardDetail"
    :diaStyle="'width: 1000px;'"
    @handle-close="handleClose"
    @get-list="getList"
  />
  <!-- IM即时通讯 -->
  <keep-alive>
    <IMessage
      v-if="isIMShow"
      :requirementId="props.makeCardDetail.makeCardRequirementInfoId"
      :isIMShow="isIMShow"
      type="0"
      @handle-close="closeIM"
    />
  </keep-alive>
</template>

<script lang="ts" setup>
import DialogInfo from '../../Components/DialogInfo.vue'
import IMessage from '../../Components/IMessage/IMessage.vue'
import { getOpenInfo, isDemandClose } from '../../Common/index'
import * as designApi from '@/api/makeCardService/design/index'
import { spliceText } from '../../Common/index'
import { cloneDeep } from 'lodash-es'

const { t } = useI18n()
const designSchemeInfoStatusEnum = [
  t('cardProductService.productDemand.demandDetail.toBeConfirmed'),
  t('cardProductService.productDemand.demandDetail.confirmed'),
  t('cardProductService.productDemand.demandDetail.reject')
]

const props = defineProps({
  makeCardDetail: {
    type: Object,
    default: () => {}
  }
})

const queryParams = reactive({
  designSchemeInfoStylist: '',
  designSchemeInfoUpdateDate: ''
})

let tableDataOrigin = []

const filterHandler = (queryValue: string, propKey: string) => {
  console.log('designSchemeInfoStylist', queryParams.designSchemeInfoStylist)
  console.log('designSchemeInfoUpdateDate', queryParams.designSchemeInfoUpdateDate)
  let allList = JSON.parse(JSON.stringify(tableDataOrigin))

  if (queryParams.designSchemeInfoStylist) {
    allList = allList.filter((item) => {
      return item['designSchemeInfoStylist']
        ?.replaceAll(spliceText, '')
        ?.toLowerCase()
        ?.includes(queryParams?.designSchemeInfoStylist?.toLowerCase())
    })
  }
  if (queryParams.designSchemeInfoUpdateDate) {
    allList = allList.filter((item) => {
      return item['designSchemeInfoUpdateDate']
        ?.replaceAll(spliceText, '')
        ?.toLowerCase()
        ?.includes(queryParams?.designSchemeInfoUpdateDate?.toLowerCase())
    })
  }
  tableData.value = allList
}
// 是否显示IM
const isIMShow = ref(false)

// IM按钮Loading
const isImLoading = ref(false)

// 在线沟通
const chatService = async () => {
  try {
    isImLoading.value = true
    if (props.makeCardDetail.makeCardRequirementRtype === '1') {
      return ElMessage.warning(t('cardProductService.productDemand.demandDetail.errorTips1'))
    }
    // 判断当前用户是否在允许聊天范围
    await designApi.isImScope({
      makeCardDesignSchemeInfoNumber: props.makeCardDetail.makeCardRequirementInfoId,
      makeCardDesignInfoIdStylistType: 0 //查找设计人员
    })
    isIMShow.value = true
  } catch (err) {
  } finally {
    isImLoading.value = false
  }
}
const closeIM = () => {
  isIMShow.value = false
}

// 弹窗状态
const isDiaLogShow = ref(false)
// 弹窗数据
const diaData = ref({})
// 弹窗标题
const diaLogTitle = ref('')
// 打开方式（类型，例如打开回执信息 backMsg）
const openType = ref('')
// 关闭弹窗
const handleClose = () => {
  isDiaLogShow.value = false
  openType.value = ''
}
// 弹窗
const openDialog = (type: string, obj?: object) => {
  const openInfo = getOpenInfo(type, obj)
  diaLogTitle.value = openInfo.diaLogTitle
  openType.value = openInfo.openType
  diaData.value = openInfo.diaData
  isDiaLogShow.value = openInfo.isDiaLogShow
}

// 表格多选
const multipleSelection = ref([])

// 表格数据选中变更
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 批量确认方案
const confirmCase = () => {
  if (multipleSelection.value?.length < 1)
    return ElMessage.warning(t('cardProductService.productDemand.demandDetail.errorTips2'))
  openDialog('verifyScheme', { type: 'multiple', multipleSelection: multipleSelection.value })
}

// 禁用多选
const checkSelectSet = (row) => {
  return row.designSchemeInfoStatus === 0
  // return true
}

// 表格状态
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取数据
const getList = async () => {
  loading.value = true
  try {
    const { data } = await designApi.getDesignListApi({
      makeCardDesignSchemeInfoNumber: props.makeCardDetail.makeCardRequirementInfoId
    })
    const makecardDraft = JSON.parse(JSON.stringify(data)) || []
    makecardDraft.forEach((item) => {
      if (item.designSchemeInfoName) {
        item.imageList = item.designSchemeInfoName.split(spliceText)
        item.imageEosList = item.designSchemeInfoEosName.split(spliceText)
      } else {
        item.imageList = []
        item.imageEosList = []
      }
    })
    console.log('makecardDraft===', makecardDraft)
    tableData.value = makecardDraft
    tableDataOrigin = cloneDeep(makecardDraft)
  } finally {
    loading.value = false
  }
}
// 获取分配设计师
const designList = ref()
const getDesign = async () => {
  const { data } = await designApi.getDesignApi({
    makeCardDesignInfoIdNumber: props.makeCardDetail.makeCardRequirementInfoId,
    makeCardDesignInfoIdStylistType: 0
  })
  designList.value = data.makeCardDesignInfoIdStylistName
}

onMounted(() => {
  getList()
  getDesign()
})

/** 子组件数据暴露start */
defineExpose({
  chatService
})
/** 子组件数据暴露end */
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.btm-chat {
  width: 162px;
}

.filter-box {
  display: flex;
  align-items: center;
  height: 90px;
  float: right;
}

.filter-box :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-select-input-focus-border-color) inset !important;
}
.filter-box :deep(.el-select .el-input.is-focus .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-select-input-focus-border-color) inset !important;
}
.filter-box :deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-select-input-focus-border-color) inset !important;
}
</style>
