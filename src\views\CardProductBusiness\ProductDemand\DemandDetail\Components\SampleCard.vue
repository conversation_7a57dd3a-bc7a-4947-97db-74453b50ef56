<template>
  <!-- 列表 -->
  <el-table
    v-loading="loading"
    :data="tableData"
    height="100%"
    :header-cell-style="{ background: '#F7F7F9', color: '#606266' }"
    style="width: 100%; margin-top: 20px"
    class="table-view"
  >
    <el-table-column
      prop="applyCode"
      :label="t('cardProductService.productDemand.demandDetail.applicationFormNumber')"
    >
      <template #default="{ row }">
        {{ row.applyCode }}
      </template>
    </el-table-column>
    <el-table-column
      prop="saleUserName"
      :label="t('cardProductService.productDemand.demandDetail.salesman')"
    />
    <el-table-column
      prop="status"
      :label="t('cardProductService.productDemand.demandDetail.applicationStatus')"
    >
      <template #default="{ row }">
        <span class="badge" :style="{ background: statusColorMapper(row.status) }">
          {{ dispalyStatus(row.status) }}</span
        >
      </template>
    </el-table-column>
    <el-table-column
      prop="createDate"
      :label="t('cardProductService.productDemand.demandDetail.creationTime')"
    />
  </el-table>
  <!-- 各种弹窗 -->
  <DialogInfo
    :isDiaLogShow="isDiaLogShow"
    :diaLogTitle="diaLogTitle"
    :openType="openType"
    :diaData="diaData"
    :makeCardDetail="props.makeCardDetail"
    :diaStyle="'width: 1000px;'"
    @handle-close="handleClose"
    @get-list="getList"
  />
</template>

<script lang="ts" setup>
import DialogInfo from '../../Components/DialogInfo.vue'
import { getOpenInfo, isDemandClose } from '../../Common/index'
import { getSampleListApi, getOrderListApi } from '@/api/makeCardService/sampleCard/index'
import { useDictStoreWithOut } from '@/store/modules/dict'
const { t } = useI18n()
const sampleSchemeInfoStatusEnum = [
  t('cardProductService.productDemand.demandDetail.toBeConfirmed'),
  t('cardProductService.productDemand.demandDetail.confirmed')
]

let props = defineProps({
  makeCardDetail: {
    type: Object,
    default: () => {}
  }
})

// 弹窗状态
let isDiaLogShow = ref(false)
// 弹窗数据
let diaData = ref({})
// 弹窗标题
let diaLogTitle = ref('')
// 打开方式（类型，例如打开回执信息 backMsg）
let openType = ref('')
// 关闭弹窗
const handleClose = () => {
  isDiaLogShow.value = false
  openType.value = ''
}

// 弹窗
const openDialog = (type: string, obj?: object) => {
  const openInfo = getOpenInfo(type, obj)
  diaLogTitle.value = openInfo.diaLogTitle
  openType.value = openInfo.openType
  diaData.value = openInfo.diaData
  isDiaLogShow.value = openInfo.isDiaLogShow
}

// 表格多选
let multipleSelection = ref([])

// 表格数据选中变更
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 批量确认方案
const confirmCase = () => {
  if (multipleSelection.value?.length < 1)
    return ElMessage.warning(t('cardProductService.productDemand.demandDetail.errorTips2'))
  openDialog('verifySampleScheme', { type: 'multiple', multipleSelection: multipleSelection.value })
}

// 禁用多选
const checkSelectSet = (row) => {
  return row.diySamplecardInfoStatus === 0
  // return true
}

// 表格状态
let loading = ref(false)
// 表格数据
let tableData = ref([])

// 获取数据
const getList = async () => {
  loading.value = true
  try {
    const { data } = await getSampleListApi({
      diySamplecardInfoCardnumber: props.makeCardDetail.makeCardNumber
    })
    tableData.value = data
    getOrderList()
  } finally {
    loading.value = false
  }
}

enum orderApplicationStatusEnum {
  /**
   * 取消
   */
  CANCEL = 'CANCEL',
  /**
   * 待提交
   */
  WAIT_SUBMIT = 'WAIT_SUBMIT',
  /**
   * 销售审核
   */
  SALE_AUDIT = 'SALE_AUDIT',
  /**
   * 销售经理审核
   */
  MANAGE_AUDIT = 'MANAGE_AUDIT',
  /**
   * 已完成
   */
  FINISH = 'FINISH'
}
const orderApplicationStatusArray = [
  {
    value: orderApplicationStatusEnum.CANCEL,
    label: '样卡取消',
    color: '#F56C6C'
  },
  {
    value: orderApplicationStatusEnum.FINISH,
    label: '样卡制卡',
    color: '#67C23A'
  },
  {
    value: [
      orderApplicationStatusEnum.MANAGE_AUDIT,
      orderApplicationStatusEnum.SALE_AUDIT,
      orderApplicationStatusEnum.WAIT_SUBMIT
    ].toString(),
    label: '样卡需求评审',
    color: '#E6A23C'
  }
]
//显示申请类型名称
function dispalyStatus(value: orderApplicationStatusEnum): string {
  console.log('value', value)
  const result = orderApplicationStatusArray.filter((item) => item.value.indexOf(value) != -1)[0]
  if (result) {
    return result.label
  }
  return ''
}
//显示申请状态对应的颜色
function statusColorMapper(value: orderApplicationStatusEnum): string {
  const result = orderApplicationStatusArray.filter((item) => item.value.indexOf(value) != -1)[0]
  if (result) {
    return result.color
  }
  return ''
}

let getOrderLoading = ref(false)

const getOrderData = ref([])

// 获取数据
const getOrderList = async () => {
  loading.value = true
  try {
    const { data } = await getOrderListApi({
      makeCardNumber: props.makeCardDetail.makeCardRequirementInfoId
    })
    getOrderData.value = data

    // 获取订单状态
    tableData.value.forEach((element: any) => {
      element.diySamplecardInfoOrderstatusText = '---'
      let orderItem: any = getOrderData.value.find((item: any) => {
        return element?.diySamplecardInfoOrdernumber === item?.orderCode
      })
      if (orderItem) {
        element.orderId = orderItem.orderId

        element.diySamplecardInfoOrderstatusText = getDistMapData(
          'order_status',
          orderItem?.orderStatus
        )
      }
    })
  } finally {
    getOrderLoading.value = false
  }
}

/**
 * 根据数据获取数据字段中的内容
 * @param mapKey 数据字典中的dictType
 * @param value dictType中的value
 */
const getDistMapData = (mapKey, value) => {
  const dictStore = useDictStoreWithOut()
  let arr = dictStore.getDictMap[mapKey]?.find((item) => {
    return item.value === value
  })
  return arr?.label || '---'
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');
.btm-chat {
  width: 162px;
}
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999;
  border-radius: 10px;
}
</style>
