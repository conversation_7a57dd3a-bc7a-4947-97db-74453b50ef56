<template>
  <el-descriptions class="mt-20px msg-box" :column="1">
    <el-descriptions-item
      :label="t('cardProductService.productDemand.demandDetail.samplePersonnel')"
      >{{ designList || '--' }}</el-descriptions-item
    >
  </el-descriptions>
  <!-- <el-button
    type="primary"
    size="large"
    style="margin-top: 20px"
    @click="confirmCase"
    :disabled="isDemandClose(props.makeCardDetail.makeCardRequirementInfoPhase)"
    v-if="tableData.length"
  >
    {{ t('cardProductService.productDemand.demandDetail.confirmThePlan') }}
  </el-button> -->
  <el-button
    type="primary"
    size="large"
    style="margin-top: 20px"
    @click="chatService"
    :loading="isImLoading"
    :disabled="isDemandClose(props.makeCardDetail.makeCardRequirementInfoPhase)"
    v-if="designList"
  >
    {{ t('cardProductService.productDemand.demandDetail.onlineCommunication') }}
  </el-button>
  <el-form ref="makeCardList" :model="queryParams" :inline="false" class="filter-box">
    <el-row :gutter="10">
      <!-- 卡款代码 -->
      <el-col :md="12" :lg="12" :xl="12">
        <el-form-item
          label=""
          prop="makecardDraftschemeInfoCardid"
          style="margin-bottom: 0 !important"
        >
          <el-input
            v-model="queryParams.makecardDraftschemeInfoCardid"
            :placeholder="t('cardProductService.productDemand.demandDetail.cardPaymentCodeSearch')"
            clearable
            @input="filterByCardCode"
          />
        </el-form-item>
      </el-col>
      <!-- 卡款名称 -->
      <el-col :md="12" :lg="12" :xl="12">
        <el-form-item
          label=""
          prop="makecardDraftschemeInfoCardname"
          style="margin-bottom: 0 !important"
        >
          <el-input
            v-model="queryParams.makecardDraftschemeInfoCardname"
            :placeholder="t('cardProductService.productDemand.demandDetail.cardNameSearch')"
            clearable
            @input="filterByCardName"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <!-- 表格 -->
  <el-table
    v-loading="loading"
    :data="tableData"
    :header-cell-style="{
      background: '#F8F8F8',
      color: '#333333',
      height: '65px'
    }"
    :row-style="{ height: '65px' }"
    style="width: 100%; margin-top: 20px"
    max-height="650px"
    @selection-change="handleSelectionChange"
    class="table-image-wrap"
    border
    resizable
  >
    <!-- <el-table-column type="selection" width="55" :selectable="checkSelectSet" /> -->
    <el-table-column :label="t('cardProductService.productDemand.demandDetail.number')" width="100">
      <template #default="scope">
        {{ scope.$index + 1 }}
      </template>
    </el-table-column>
    <el-table-column
      prop="makecardDraftschemeInfoCardid"
      :label="t('cardProductService.productDemand.demandDetail.cardPaymentCode')"
    >
      <template #default="scope">
        <div
          v-for="(item, index) in scope.row.makecardDraftschemeInfoCardid.split(spliceText)"
          :key="`makecardDraftschemeInfoCardid_${index}`"
          >{{ item }}</div
        >
      </template>
    </el-table-column>
    <el-table-column
      prop="makecardDraftschemeInfoCardname"
      :label="t('cardProductService.productDemand.demandDetail.cardName')"
    >
      <template #default="scope">
        <div
          v-for="(item, index) in scope.row.makecardDraftschemeInfoCardname.split(spliceText)"
          :key="`makecardDraftschemeInfoCardid_${index}`"
          >{{ item }}</div
        >
      </template>
    </el-table-column>
    <el-table-column
      prop="makecardDraftschemeInfoName"
      :label="t('cardProductService.productDemand.demandDetail.sampleScheme')"
    >
      <template #default="scope">
        <!-- <span class="cursor-pointer" @click="openDialog('viewDraft', scope.row)">{{
          scope.row.makecardDraftschemeInfoName
        }}</span> -->
        <div
          class="imageName"
          style="color: rgb(226, 163, 44)"
          @click="openDialog('viewDraft', scope.row)"
          v-for="(item, ind) in scope.row.imageList"
          :key="item"
        >
          <el-tooltip
            :key="`imageList_${ind}`"
            class="box-item"
            effect="dark"
            :content="`${item}`"
            placement="top-start"
          >
            {{ item }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <!-- <el-table-column
      prop="makecardDraftschemeInfoThreedshow"
      :label="t('cardProductService.productDemand.demandDetail.demonstration')"
    >
      <template #default="scope">
        <span
          class="color-gold cursor-pointer"
          @click="openDialog('view3D', scope.row)"
          v-if="
            scope.row.makecardDraftschemeInfoThreedshoweos &&
            scope.row.makecardDraftschemeInfoThreedshoweos.slice(-4) == '.glb'
          "
          >【{{ t('common.see') }}】</span
        >
        <span v-else>--</span>
      </template>
    </el-table-column> -->
    <el-table-column
      prop="makecardDraftschemeInfoReceiptdate"
      :label="t('cardProductService.productDemand.demandDetail.updateTime')"
    />
    <!-- <el-table-column
      prop="makecardDraftschemeInfoStatus"
      :label="t('cardProductService.productDemand.demandDetail.state')"
      width="100"
    >
      <template #default="scope">
        <span>{{ draftSchemeInfoStatusEnum[scope.row.makecardDraftschemeInfoStatus] }}</span>
      </template>
    </el-table-column> -->
    <!-- <el-table-column
      prop="makecardDraftschemeInfoReceipt"
      :label="t('cardProductService.productDemand.demandDetail.receiptInformation')"
      width="300"
      show-overflow-tooltip
    >
      <template #default="scope">
        <span>{{ scope.row.makecardDraftschemeInfoReceipt || '--' }}</span>
      </template>
    </el-table-column> -->
    <!-- <el-table-column fixed="right" :label="t('common.operate')" width="200">
      <template #default="scope">
        <el-button
          style="color: #e2a32c"
          link
          @click="openDialog('viewBackMsg', scope.row)"
          v-if="scope.row.makecardDraftschemeInfoStatus === 1"
          >{{ t('cardProductService.productDemand.demandDetail.viewReceipts') }}
        </el-button>
        <el-button
          style="color: #e2a32c"
          link
          @click="openDialog('verifyDraftScheme', scope.row)"
          v-if="scope.row.makecardDraftschemeInfoStatus === 0"
          :disabled="isDemandClose(props.makeCardDetail.makeCardRequirementInfoPhase)"
          >{{ t('cardProductService.productDemand.demandDetail.confirmThePlan') }}</el-button
        >
      </template>
    </el-table-column> -->
  </el-table>
  <!-- 各种弹窗 -->
  <DialogInfo
    :isDiaLogShow="isDiaLogShow"
    :diaLogTitle="diaLogTitle"
    :openType="openType"
    :diaData="diaData"
    :makeCardDetail="props.makeCardDetail"
    :diaStyle="'width: 1000px;'"
    @handle-close="handleClose"
    @get-list="getList"
  />

  <!-- IM即时通讯 -->
  <keep-alive>
    <IMessage
      v-if="isIMShow"
      :requirementId="props.makeCardDetail.makeCardRequirementInfoId"
      :isIMShow="isIMShow"
      type="1"
      @handle-close="closeIM"
    />
  </keep-alive>
</template>

<script lang="ts" setup>
import DialogInfo from '../../Components/DialogInfo.vue'
// import { ElMessage } from 'element-plus'
import IMessage from '../../Components/IMessage/IMessage.vue'
import { getOpenInfo, isDemandClose } from '../../Common/index'
import * as draftApi from '@/api/makeCardService/draft/index'
import * as designApi from '@/api/makeCardService/design/index'
import { spliceText } from '../../Common/index'
import { cloneDeep } from 'lodash-es'

const { t } = useI18n()
const draftSchemeInfoStatusEnum = [
  t('cardProductService.productDemand.demandDetail.toBeConfirmed'),
  t('cardProductService.productDemand.demandDetail.confirmed')
]

const queryParams = reactive({
  makecardDraftschemeInfoCardid: '',
  makecardDraftschemeInfoCardname: ''
})

let tableDataOrigin = []

const filterByCardCode = (value) => {
  filterHandler(value, 'makecardDraftschemeInfoCardid')
}

const filterByCardName = (value) => {
  filterHandler(value, 'makecardDraftschemeInfoCardname')
}

const filterHandler = (queryValue: string, propKey: string) => {
  console.log('makecardDraftschemeInfoCardid', queryParams.makecardDraftschemeInfoCardid)
  console.log('makecardDraftschemeInfoCardname', queryParams.makecardDraftschemeInfoCardname)
  let allList = JSON.parse(JSON.stringify(tableDataOrigin))

  if (queryParams.makecardDraftschemeInfoCardid) {
    allList = allList.filter((item) => {
      return item['makecardDraftschemeInfoCardid']
        ?.replaceAll(spliceText, '')
        ?.toLowerCase()
        ?.includes(queryParams?.makecardDraftschemeInfoCardid?.toLowerCase())
    })
  }
  if (queryParams.makecardDraftschemeInfoCardname) {
    allList = allList.filter((item) => {
      return item['makecardDraftschemeInfoCardname']
        ?.replaceAll(spliceText, '')
        ?.toLowerCase()
        ?.includes(queryParams?.makecardDraftschemeInfoCardname?.toLowerCase())
    })
  }
  tableData.value = allList
}

let props = defineProps({
  makeCardDetail: {
    type: Object,
    default: () => {}
  }
})

// 是否显示IM
let isIMShow = ref(false)

// IM按钮Loading
let isImLoading = ref(false)

// 在线沟通
const chatService = async () => {
  try {
    isImLoading.value = true
    // if (props.makeCardDetail.makeCardRequirementRtype === '1') {
    //   return ElMessage.warning('稿样需求不支持在线沟通，请联系管理员！')
    // }
    // 判断当前用户是否在允许聊天范围
    await designApi.isImScope({
      makeCardDesignSchemeInfoNumber: props.makeCardDetail.makeCardRequirementInfoId,
      makeCardDesignInfoIdStylistType: 1 //查找稿样人员
    })
    isIMShow.value = true
  } catch (err) {
  } finally {
    isImLoading.value = false
  }
}
const closeIM = () => {
  isIMShow.value = false
}

// 弹窗状态
let isDiaLogShow = ref(false)
// 弹窗数据
let diaData = ref({})
// 弹窗标题
let diaLogTitle = ref('')
// 打开方式（类型，例如打开回执信息 backMsg）
let openType = ref('')
// 关闭弹窗
const handleClose = () => {
  isDiaLogShow.value = false
  openType.value = ''
}

// 弹窗
const openDialog = (type: string, obj?: object) => {
  const openInfo = getOpenInfo(type, obj)
  diaLogTitle.value = openInfo.diaLogTitle
  openType.value = openInfo.openType
  diaData.value = openInfo.diaData
  isDiaLogShow.value = openInfo.isDiaLogShow
}

// 表格多选
let multipleSelection = ref([])

// 表格数据选中变更
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 批量确认方案
const confirmCase = () => {
  if (multipleSelection.value?.length < 1)
    return ElMessage.warning(t('cardProductService.productDemand.demandDetail.errorTips2'))
  openDialog('verifyDraftScheme', { type: 'multiple', multipleSelection: multipleSelection.value })
}

// 禁用多选
const checkSelectSet = (row) => {
  return row.makecardDraftschemeInfoStatus === 0
  // return true
}

// 表格状态
let loading = ref(false)

// 表格数据
let tableData = ref([])

// 获取数据
const getList = async () => {
  loading.value = true
  try {
    const { data } = await draftApi.getDraftListApi({
      makecardDraftschemeInfoCardnumber: props.makeCardDetail.makeCardRequirementInfoId
    })
    let makecardDraft = JSON.parse(JSON.stringify(data)) || []
    makecardDraft.forEach((item) => {
      if (item.makecardDraftschemeInfoName) {
        item.imageList = item.makecardDraftschemeInfoName.split(spliceText)
        item.imageEosList = item.makecardDraftschemeInfoEosname.split(spliceText)
      } else {
        item.imageList = []
        item.imageEosList = []
      }
    })
    tableData.value = makecardDraft
    tableDataOrigin = cloneDeep(makecardDraft)
  } finally {
    loading.value = false
  }
}

// 获取分配设计师
const designList = ref()
const getDesign = async () => {
  const { data } = await designApi.getDesignApi({
    makeCardDesignInfoIdNumber: props.makeCardDetail.makeCardRequirementInfoId,
    makeCardDesignInfoIdStylistType: 1
  })
  designList.value = data.makeCardDesignInfoIdStylistName
}

onMounted(() => {
  getList()
  getDesign()
})
</script>

<style lang="less" scoped>
@import url('../../Common/common.less');

.btm-chat {
  width: 162px;
}

.filter-box {
  display: flex;
  align-items: center;
  height: 90px;
  float: right;
}

.filter-box :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
}
</style>
