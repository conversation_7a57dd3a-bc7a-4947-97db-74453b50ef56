<template>
  <div class="content">
    <div class="data-list">
      <div class="title">{{ t('cardProductService.productDemand.common.cardRequirements') }}</div>

      <!-- 关闭需求 -->
      <div v-if="isDemandClose(tableData.makeCardRequirementInfoPhase)">
        <div class="mt-20px title-tip basics">{{
          t('cardProductService.productDemand.demandDetail.closeDemand')
        }}</div>
        <el-descriptions class="mb-20px mt-20px msg-box" :column="3" v-loading="loading">
          <el-descriptions-item width="400">
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.demandDetail.operationTime')
              }}</div>
              <div class="ml-20px content-close break-all">{{
                tableData.makeCardRequirementInfoEditDate
              }}</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item width="400">
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.demandDetail.operator')
              }}</div>
              <div class="ml-20px content-close break-all">{{
                tableData.makeCardRequirementInfoReserv2
              }}</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item min-width="400">
            <div class="flex">
              <div class="desc-column">{{
                t('cardProductService.productDemand.demandDetail.closingInstructions')
              }}</div>
              <div class="ml-20px content-close break-all">{{
                tableData.makeCardRequirementInfoReserv1
              }}</div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="mt-20px title-tip basics">{{
        t('cardProductService.productDemand.demandDetail.basicNeeds')
      }}</div>
      <BasicInfo :list="tableData" :loading="loading" />
      <!-- 详细需求 -->
      <!--      <div>-->
      <!--        <div class="mt-20px title-tip detail">{{-->
      <!--          t('cardProductService.productDemand.demandDetail.detailedRequirements')-->
      <!--        }}</div>-->
      <!--        <DetailedInfo :list="tableData" :loading="loading" />-->
      <!--      </div>-->
      <!-- tabs -->
      <div
        v-if="
          tableData.makeCardRequirementInfoPhase > 0 &&
          !makeCardArr.includes(tableData.makeCardRequirementInfoPhase)
        "
      >
        <el-tabs v-model="activeName" class="mt-20px">
          <el-tab-pane
            :label="t('cardProductService.productDemand.demandDetail.designScheme')"
            :name="tabsNameList[0]"
          >
            <Design
              ref="designRef"
              :makeCardDetail="tableData"
              v-if="activeName === tabsNameList[0]"
            />
          </el-tab-pane>
          <el-tab-pane
            :label="t('cardProductService.productDemand.demandDetail.sampleScheme')"
            :name="tabsNameList[1]"
          >
            <SampleManuscript :makeCardDetail="tableData" v-if="activeName === tabsNameList[1]" />
          </el-tab-pane>
          <el-tab-pane
            :label="t('cardProductService.productDemand.common.sampleCardApplication')"
            :name="tabsNameList[2]"
          >
            <SampleCard :makeCardDetail="tableData" v-if="activeName === tabsNameList[2]" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { makeCardRankData } from '../Common/type'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useRouter } from 'vue-router'
import BasicInfo from '../Components/BasicInfo.vue'
import DetailedInfo from '../Components/DetailedInfo.vue'
import Design from './Components/Design.vue'
import SampleManuscript from './Components/SampleManuscript.vue'
import SampleCard from './Components/SampleCard.vue'
import * as makeCardApi from '@/api/makeCardService/index'

import { spliceText, isDemandClose, makeCardArr } from '../Common/index'
const { t } = useI18n()
const tabsNameList: any = ['design', 'sampleManuscript', 'sampleCard']

const activeName = ref(tabsNameList[0])

const router = useRouter()

const tableData = ref<any>({})
const loading = ref(false)

// 在线沟通实例
const designRef = ref()

onMounted(() => {
  if (
    router.currentRoute.value.query.activeName &&
    tabsNameList.includes(router.currentRoute.value.query.activeName)
  ) {
    activeName.value = router.currentRoute.value.query.activeName
  }
  const tagsViewStore = useTagsViewStore() // tab store
  let visitedViews = tagsViewStore.getVisitedViews // 当前tab页信息
  visitedViews.forEach((item) => {
    if (item.path === '/CardProductService/ProductDemand/DemandDetail') {
      item.query.makeCardRequirementInfoId =
        router.currentRoute.value.query.makeCardRequirementInfoId
      item.query.activeName = activeName.value
    }
  })

  nextTick(() => {
    getList()
  })
})
// 获取数据
const getList = async () => {
  console.log('getlist')
  loading.value = true
  const queryParams: any = router.currentRoute.value.query.makeCardRequirementInfoId
  try {
    const data: any = await makeCardApi.findMakeCardApi({ makeCardRequirementInfoId: queryParams })
    if (data.data.makeCardRequirementInfoSpecies) {
      data.data.makeCardRequirementInfoSpecies = JSON.parse(
        data.data.makeCardRequirementInfoSpecies
      ).join('、')
    }
    if (data.data.makeCardRequirementInfoRank) {
      let res: any = []
      let arr = JSON.parse(data.data.makeCardRequirementInfoRank)
      console.log(arr)

      makeCardRankData.forEach((item) => {
        let resObj: any = {
          label: item.label,
          value: []
        }
        item.value.forEach((list) => {
          if (arr.includes(list.label)) {
            resObj.value.push(list.label)
          }
        })
        if (resObj.value.length > 0) {
          resObj.value = resObj.value.join('、')
          res.push(resObj)
        }
      })
      data.data.makeCardRequirementInfoRankArr = res
    }
    data.data.makeCardRequirementInfoAttachmentsArr = []
    if (data.data.makeCardRequirementInfoAttachments) {
      data.data.makeCardRequirementInfoAttachments.split(spliceText).forEach((item) => {
        let file = {
          name: item,
          url: ''
        }
        data.data.makeCardRequirementInfoAttachmentsArr.push(file)
      })
    } else {
      data.data.makeCardRequirementInfoAttachmentsArr = []
    }
    if (data.data.makeCardRequirementInfoAttachmentseos) {
      data.data.makeCardRequirementInfoAttachmentseos.split(spliceText).forEach((item, index) => {
        data.data.makeCardRequirementInfoAttachmentsArr[index].url = item
      })
    }
    data.data.makeCardRequirementInfoOtherAttachmentsArr = []
    if (data.data.makeCardRequirementInfoOtherAttachments) {
      data.data.makeCardRequirementInfoOtherAttachments.split(spliceText).forEach((item) => {
        let file = {
          name: item,
          url: ''
        }
        data.data.makeCardRequirementInfoOtherAttachmentsArr.push(file)
      })
    } else {
      // let file = {
      //   name: '--',
      //   url: ''
      // }
      data.data.makeCardRequirementInfoOtherAttachmentsArr = []
    }
    if (data.data.makeCardRequirementInfoOtherAttachmentseos) {
      data.data.makeCardRequirementInfoOtherAttachmentseos
        .split(spliceText)
        .forEach((item, index) => {
          data.data.makeCardRequirementInfoOtherAttachmentsArr[index].url = item
        })
    }
    tableData.value = data.data

    // 是否打开即时通讯页面
    openChat()
  } finally {
    loading.value = false
  }
}

// 打开即时通讯页面
const openChat = () => {
  if (
    activeName.value === tabsNameList[0] &&
    router.currentRoute.value.query.isChat === '1' &&
    tableData.value.makeCardRequirementInfoPhase > 0 &&
    !makeCardArr.includes(tableData.value.makeCardRequirementInfoPhase)
  ) {
    nextTick(() => {
      designRef.value.chatService()
    })
  }
}
</script>

<style lang="less" scoped>
@import url('../Common/common.less');
.desc-column {
  width: 200px; //94px;
  text-align: right;
  color: #666666;
}
.content-close {
  color: #333333;
  flex: 1;
}
.content {
  :deep(.el-tabs__item.is-active) {
    color: #e2a32c;
  }
  :deep(.el-tabs__item:hover) {
    color: #e2a32c;
  }
  :deep(.el-tabs__active-bar) {
    background-color: #e2a32c;
  }
  .data-list {
    .title,
    .title-tip {
      color: #333333;
      font-size: 16px;
      font-weight: 400;
      display: flex;
      align-items: center;
    }
    .title::before {
      content: '';
      display: block;
      width: 6px;
      height: 22px;
      background: #e2a32c;
      border-radius: 0 3px 3px 0;
      margin-right: 16px;
    }
    .basics::before {
      content: '';
      display: block;
      width: 18px;
      height: 18px;
      background-image: url('../../../assets/imgs/makeCard/basicsDesc.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-right: 14px;
    }
    .detail::before {
      content: '';
      display: block;
      width: 18px;
      height: 18px;
      background-image: url('../../../assets/imgs/makeCard/detailDesc.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-right: 14px;
    }
  }
}
</style>
