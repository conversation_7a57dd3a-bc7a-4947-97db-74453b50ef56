<template>
  <ContentWrap ifTable>
    <template #search>
      <!-- 查询 -->
      <div class="flex">
        <Search ref="searchWrapRef" @get-list="getSearchList" />
        <el-button type="primary" v-track:click.btn @click="openDialog('add')">
          {{ t('cardProductService.productDemand.demandList.newProductRequirements') }}
        </el-button>
      </div>
    </template>
    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      max-height="550px"
      :header-cell-style="{
        background: '#F8F8F8',
        color: '#333333',
        height: '65px'
      }"
      :row-style="{ height: '65px' }"
      style="width: 100%"
    >
      <el-table-column
        :label="t('cardProductService.productDemand.demandDetail.number')"
        width="100px"
      >
        <template #default="scope">
          {{ scope.$index + 1 + (current - 1) * row }}
        </template>
      </el-table-column>
      <el-table-column
        prop="makeCardNumber"
        :label="t('cardProductService.productDemand.components.requirementNumber')"
        width="200"
      />
      <el-table-column
        prop="makeCardRequirementInfoTitle"
        :label="t('cardProductService.productDemand.components.demand.requirementTitle')"
        min-width="200"
      />
      <el-table-column
        prop="makeCardRequirementInfoSource"
        :label="t('cardProductService.productDemand.demandList.sourceOfDemand')"
        min-width="200"
      >
        <template #default="scope">
          <span>
            {{ makeCardRequirementInfoSourceEnum[scope.row.makeCardRequirementInfoSource] }}
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="productName" label="产品名称" min-width="200" />
      <el-table-column prop="productType" label="产品类型" width="100">
        <template #default="scope"> {{ productTypeFormater(scope.row.productType) }}</template>
      </el-table-column> -->

      <el-table-column
        prop="makeCardRequirementInfoCreateDate"
        :label="t('cardProductService.productDemand.components.submissionTime')"
        width="200"
      />
      <el-table-column
        prop="makeCardRequirementInfoPhaseText"
        :label="t('cardProductService.productDemand.demandList.currentStage')"
        min-width="200"
      />
      <el-table-column fixed="right" :label="t('common.operate')" width="220">
        <template #default="scope">
          <el-button
            style="color: #e2a32c"
            link
            v-track:click.btn
            @click="viewDetail('view', scope.row)"
            >{{ t('common.see') }}</el-button
          >
          <el-button
            style="color: #e2a32c"
            link
            v-track:click.btn
            @click="openDialog('edit', scope.row)"
            v-if="
              scope.row.makeCardRequirementInfoSource === 0 &&
              makeCardArr.includes(scope.row.makeCardRequirementInfoPhase)
            "
            >{{ t('common.edit') }}</el-button
          >
          <el-button
            style="color: #e2a32c"
            link
            v-track:click.btn
            @click="openDialog('tipConfirm', scope.row)"
            v-if="
              scope.row.makeCardRequirementInfoSource === 0 &&
              makeCardArr.includes(scope.row.makeCardRequirementInfoPhase)
            "
            >{{ t('common.withdraw') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <template #pagination>
      <Pagination
        v-model:page="current"
        v-model:limit="row"
        :total="total"
        @pagination="getPaginationList"
      />
    </template>
  </ContentWrap>
  <!-- 弹窗 -->
  <DialogInfo
    :isDiaLogShow="isDiaLogShow"
    :diaLogTitle="diaLogTitle"
    :openType="openType"
    :diaConfirmTip="diaConfirmTip"
    :diaData="diaData"
    :diaStyle="'width: 1000px;'"
    @handle-close="handleClose"
    @tip-confirm="tipConfirm"
    @get-list="getList"
  />
</template>

<script setup lang="ts">
defineOptions({
  name: 'ProductDemand'
})

import { ElMessage } from 'element-plus'
import Search from '../Components/Search.vue'
import DialogInfo from '../Components/DialogInfo.vue'
// import Pagination from '../Components/Pagination.vue'
import { getOpenInfo, getOldDemandValve, makeCardArr } from '../Common/index'
import { IMsg } from '../Common/type'
import * as makeCardApi from '@/api/makeCardService/index'
import type {
  makeCardListReqType,
  makeCardListType,
  makeCardListPageReqType
} from '@/api/makeCardService/types'
import { useRequirementService } from '../hooks/useRequirementService'
const { productTypeFormater } = useRequirementService()

const { t } = useI18n()
const makeCardRequirementInfoSourceEnum = [
  t('cardProductService.productDemand.common.client'),
  t('cardProductService.productDemand.common.managementPlatform'),
  t('cardProductService.productOrder.batchOrderDetail.sale')
]

// 弹窗相关
let isDiaLogShow = ref(false)
// 弹窗数据
let diaData = ref({})
// 弹窗标题
let diaLogTitle = ref('')
// 弹窗提示 确认类弹窗
let diaConfirmTip = ref('')
// 打开方式（类型，例如打开回执信息 backMsg）
let openType = ref('')
// 关闭弹窗
const handleClose = () => {
  isDiaLogShow.value = false
  openType.value = ''
}
// 打开弹窗
const openDialog = (type: string, obj?: object, msg?: IMsg) => {
  const openInfo = getOpenInfo(type, obj, msg)
  isDiaLogShow.value = openInfo.isDiaLogShow
  diaLogTitle.value = openInfo.diaLogTitle
  diaConfirmTip.value = openInfo.diaConfirmTip
  diaData.value = openInfo.diaData
  openType.value = openInfo.openType
}

// 查看详情
const viewDetail = (type: string, obj) => {
  const tabsNameList = ['', 'design', 'sampleManuscript', 'sampleCard']
  let phase = getOldDemandValve(obj.makeCardRequirementInfoPhase).phase || 0
  let activeName = tabsNameList[phase]
  getOpenInfo(type, { activeName, ...obj })
}

// 表格等待状态
let loading = ref(false)
// 表格数据
const tableData = ref<makeCardListType[]>([])

// 分页器
const current = ref(1) // 当前页数
let total: any = ref(0) // 总页数
const row = ref(10) // 每页多少条

// 分页触发获取列表
const getPaginationList = (res) => {
  current.value = res.page
  row.value = res.limit
  getList()
}

// 搜索触发获取列表
const getSearchList = (res) => {
  current.value = 1
  getList(res)
}
const route = useRoute()
import { useTagsViewStore } from '@/store/modules/tagsView'
onMounted(() => {
  getList()
  nextTick(() => {
    const tagsViewStore = useTagsViewStore()
    tagsViewStore.delCachedView()
    console.log('tagsViewStore.getCachedViews1', tagsViewStore.getCachedViews)
  })
  if (route.query.type == 'add') {
    openDialog('add')
  }
})

// 获取数据
const getList = async (queryParams: makeCardListReqType = queryParamsSave) => {
  queryParamsSave = JSON.parse(JSON.stringify(queryParams))
  let getListQueryParams: makeCardListPageReqType = {
    pageNum: current.value,
    pageSize: row.value,
    ...queryParams
  }
  loading.value = true
  try {
    const { data } = await makeCardApi.getMakeCardListApi(getListQueryParams)
    total.value = data.total
    tableData.value = data.list
    tableData.value?.forEach((item) => {
      item.makeCardRequirementInfoPhaseText = getOldDemandValve(
        item.makeCardRequirementInfoPhase
      ).label
    })
  } finally {
    loading.value = false
  }
}

let queryParamsSave: makeCardListReqType = { makeCardRequirementInfoTitle: '', productName: '' }

// 删除数据
const tipConfirm = async (makeCardInfoId) => {
  try {
    await makeCardApi.delMakeCardApi(makeCardInfoId)
    ElMessage.success(t('common.delSuccess'))
    getList()
  } finally {
    handleClose()
  }
}
</script>

<style lang="less" scoped>
@import url('../Common/common.less');
.btn-add {
  width: 162px;
}
</style>
