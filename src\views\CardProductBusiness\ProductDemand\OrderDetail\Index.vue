<template>
  <div v-loading="loading" class="break-all">
    <div v-loading="logisticsLoading" class="mb-20px" v-if="logisticsInfo?.mailNo">
      <div class="title">{{
        t('cardProductService.productDemand.orderDetail.logisticsInformation')
      }}</div>
      <div class="wl-header mt-20px wl-title">
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.logisticsCompany')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.expressTypeText }}</div>
        </div>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.waybillNumber')
          }}</div>
          <div class="ml-20px content">{{ logisticsInfo?.mailNo }}</div>
        </div>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.shippingMethod')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.orderExt?.mailMode }}</div>
        </div>
      </div>
      <div class="wl-trace">
        <div class="wl-info">
          <div class="mb-20px">{{
            t('cardProductService.productDemand.orderDetail.logisticsTracking')
          }}</div>
          <el-timeline>
            <el-timeline-item
              :color="index === 0 ? '#e2a32c' : ''"
              v-for="(item, index) in logisticsList"
              :key="'wl' + index"
              class="time-line"
            >
              <div class="flex ml-10px" :style="index === 0 ? 'color: #e2a32c' : ''">
                <div class="logistics-text"> {{ item?.opTime }} {{ item?.remark }} </div>
                <div class="link-mar" v-if="index === 0">
                  <el-link :underline="false" @click="showMoreChange">
                    <span class="mr-5px">{{
                      !showMore ? t('common.expand') : t('common.shrink')
                    }}</span>
                    <el-icon>
                      <ArrowUpBold v-show="showMore" />
                      <ArrowDownBold v-show="!showMore" />
                    </el-icon>
                  </el-link>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <div class="wl-user-info">
          <div class="put">{{ t('cardProductService.productDemand.orderDetail.shou') }}</div>
          <div>
            {{ sampleCardInfo?.orderExt?.receivingInfo?.contact || '--' }}，

            {{ sampleCardInfo?.orderExt?.receivingInfo?.tel || '--' }}，
            {{ sampleCardInfo?.orderExt?.receivingInfo?.fullAddress || '--' }}
          </div>
        </div>
      </div>
    </div>

    <div class="title">{{
      t('cardProductService.productDemand.orderDetail.orderInformation')
    }}</div>
    <el-descriptions class="mt-20px msg-box" :column="5">
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.orderNumber')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.orderCode }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.orderStatus')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.diySamplecardInfoOrderstatusText }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.creationTime')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.createTime }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.orderType')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.orderTypeText }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.orderSource')
          }}</div>
          <div class="ml-20px content">{{ orderSource[sampleCardInfo?.orderSource] }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.updateTime')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.updateTime }}</div>
        </div>
      </el-descriptions-item>
      <!-- <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">是否加急</div>
          <div class="ml-20px content">{{ isUrgentEnum[sampleCardInfo?.isUrgent] }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">交付方式</div>
          <div class="ml-20px content">{{
            deliveryMethod[sampleCardInfo?.orderExt?.deliveryMethod]
          }}</div>
        </div>
      </el-descriptions-item> -->
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.estimatedDeliveryTime')
          }}</div>
          <div class="ml-20px content">{{
            sampleCardInfo?.orderDetailExt?.productionList[0]?.deliveryTime
          }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.totalOrderPrice')
          }}</div>
          <div class="ml-20px content"
            ><!--￥ 隐藏价格标识-->{{ sampleCardInfo?.orderTotalPrice?.toFixed(3) }}</div
          >
        </div>
      </el-descriptions-item>
      <el-descriptions-item :span="1">
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.operator')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.operateName }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item :span="4">
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.operatorAccount')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.operateUsername }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item :span="5">
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.remarksDescription')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.note }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item :span="5">
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.orderVoucher')
          }}</div>
          <div class="ml-20px content">
            <div v-for="(item, index) in fileListValue" :key="'pz' + index">
              {{ item.fileName }}
              <el-button
                class="color-gold ml-20px cursor-pointer"
                v-if="item.eosUrl"
                link
                :loading="downBtnLoading.includes(index)"
                @click="downFile(item.saveFileName, item.fileName, index)"
                >{{ t('cardProductService.productDemand.orderDetail.download') }}
              </el-button>
            </div>
          </div>
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <div class="title mt-20px">{{
      t('cardProductService.productDemand.orderDetail.customerInformation')
    }}</div>
    <el-descriptions class="mt-20px msg-box" :column="24">
      <el-descriptions-item width="300">
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.orderingCustomers')
          }}</div>
          <div class="ml-20px content">{{
            sampleCardInfo?.orderDetailExt?.productionList[0]?.customerName
          }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item width="300">
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.contacts')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.orderExt?.contact }}</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item width="300">
        <div class="flex">
          <div class="desc-column">{{
            t('cardProductService.productDemand.orderDetail.contactPhoneNumber')
          }}</div>
          <div class="ml-20px content">{{ sampleCardInfo?.orderExt?.tel }}</div>
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <div class="title mt-20px">{{
      t('cardProductService.productDemand.orderDetail.productInformation')
    }}</div>
    <el-descriptions class="mt-20px" :column="5" direction="vertical" border>
      <el-descriptions-item
        :label="t('cardProductService.productDemand.orderDetail.productInformation')"
        min-width="180"
      >
        {{ sampleCardInfo?.orderDetailExt?.productionList[0]?.cardCode }} -
        {{ sampleCardInfo?.orderDetailExt?.productionList[0]?.cardName }}
      </el-descriptions-item>
      <el-descriptions-item
        :label="t('cardProductService.productDemand.orderDetail.referencePrice')"
        min-width="100"
        ><!--￥ 隐藏价格标识-->{{
          sampleCardInfo?.orderDetailExt?.productionList[0]?.unitPrice?.toFixed(3)
        }}</el-descriptions-item
      >
      <el-descriptions-item
        :label="t('cardProductService.productDemand.orderDetail.quantity')"
        min-width="100"
        >{{ sampleCardInfo?.orderDetailExt?.productionList[0]?.cardCount }}</el-descriptions-item
      >
      <el-descriptions-item
        :label="t('cardProductService.productDemand.orderDetail.personalizedMaterials')"
        min-width="120"
        >{{ isIndividualEnum[sampleCardInfo?.isIndividual] }}</el-descriptions-item
      >
    </el-descriptions>

    <div class="title mt-20px">{{
      t('cardProductService.productDemand.orderDetail.mailingInformation')
    }}</div>
    <el-descriptions class="mt-20px" :column="4" direction="vertical" border>
      <el-descriptions-item
        :label="t('cardProductService.productDemand.orderDetail.addressName')"
        min-width="200"
        >{{ sampleCardInfo?.orderExt?.receivingInfo?.addressName || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        :label="t('cardProductService.productDemand.orderDetail.address')"
        min-width="400"
        >{{ sampleCardInfo?.orderExt?.receivingInfo?.fullAddress || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        :label="t('cardProductService.productDemand.orderDetail.recipients')"
        min-width="150"
        >{{ sampleCardInfo?.orderExt?.receivingInfo?.contact || '--' }}</el-descriptions-item
      >
      <el-descriptions-item
        :label="t('cardProductService.productDemand.orderDetail.contactPhoneNumber')"
        min-width="150"
        >{{ sampleCardInfo?.orderExt?.receivingInfo?.tel || '--' }}</el-descriptions-item
      >
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ProductDemandOrderDetail'
})

/** 卡产品服务-产品需求-订单详情 **/
/** 原菜单路径: @/views/MakeCardService/sampleCard/detail.vue  **/
import { ArrowUpBold, ArrowDownBold } from '@element-plus/icons-vue'
import { useDictStoreWithOut } from '@/store/modules/dict'
import { useTagsViewStore } from '@/store/modules/tagsView'
import {
  getOrderDetailApi,
  downloadFileApi,
  searchTraceMailNo
} from '@/api/CardProductService/ProductDemand/SampleCard/index'
const { t } = useI18n()
// 详情数据
const sampleCardInfo = ref<any>({})

// 路由
const router = useRouter()

// 交互方式
const orderPayTypeEnum = [
  t('cardProductService.productDemand.orderDetail.mail'),
  t('cardProductService.productDemand.orderDetail.inventoryAndProxyStorage'),
  t('cardProductService.productDemand.orderDetail.customerSelfPickup')
]

// 交互方式-对象
const deliveryMethod = {
  mail: orderPayTypeEnum[0],
  storage: orderPayTypeEnum[1],
  customerPick: orderPayTypeEnum[2]
}

// 订单来源
const orderSourceEnum = [
  t('cardProductService.productDemand.common.managementPlatform'),
  t('cardProductService.productDemand.orderDetail.managementPlatform')
]

// 订单来源-对象
const orderSource = {
  management: orderSourceEnum[0],
  customer: orderSourceEnum[1]
}

// 是否加急
const isUrgentEnum = [
  t('cardProductService.productDemand.orderDetail.no'),
  t('cardProductService.productDemand.orderDetail.yes')
]

// 是否写入个人化物料
const isIndividualEnum = [
  t('cardProductService.productDemand.orderDetail.no'),
  t('cardProductService.productDemand.orderDetail.yes')
]

// 是否显示更多物流
let showMore = ref(false)

// 物流信息
let logisticsList = ref<any>([])

// 收起展开按钮
const showMoreChange = () => {
  showMore.value = !showMore.value
  logisticsList.value = []
  showMore.value
    ? (logisticsList.value = logisticsInfo.value?.routeList)
    : logisticsList.value.push(logisticsInfo.value?.routeList[0])
}

onMounted(() => {
  // 获取工单ID
  orderId.value = router.currentRoute.value.query.orderId

  // 防止切换tab时数据丢失
  const tagsViewStore = useTagsViewStore() // tab store
  let visitedViews = tagsViewStore.getVisitedViews // 当前tab页信息
  visitedViews.forEach((item) => {
    if (item.path === '/CardProductService/ProductDemand/OrderDetail') {
      item.query.orderId = orderId.value
    }
  })

  if (!orderId.value)
    return ElMessage.error(t('cardProductService.productDemand.orderDetail.errorTips1'))

  getList()
})

// 物流获取状态
let logisticsLoading = ref(false)

// 物流信息
let logisticsInfo = ref<any>({})

// 获取物流数据
const getLogisticsTraces = async () => {
  if (!sampleCardInfo.value?.orderDetailExt?.productionList[0]?.mailNo) return
  logisticsLoading.value = true
  try {
    const { data } = await searchTraceMailNo({
      mailNo: sampleCardInfo.value?.orderDetailExt?.productionList[0]?.mailNo,
      expressType: sampleCardInfo.value?.orderDetailExt?.productionList[0]?.expressType
    })

    logisticsInfo.value = data

    if (logisticsInfo.value?.routeList?.length > 0) {
      logisticsInfo.value.routeList.reverse()
      logisticsList.value = [logisticsInfo.value.routeList[0]]
    }
  } catch (e) {
  } finally {
    logisticsLoading.value = false
  }
}

// 工单ID
let orderId = ref<any>('')

// 等待状态
let loading = ref(false)

// 凭证列表
const fileListValue = ref<{ fileName: string; saveFileName: string; eosUrl: string }[]>([])

// 获取数据
const getList = async () => {
  loading.value = true
  try {
    const { data } = await getOrderDetailApi({
      orderId: orderId.value
    })

    sampleCardInfo.value = data

    // 获取物流信息
    if (sampleCardInfo.value?.orderExt?.deliveryMethod === 'mail') getLogisticsTraces()

    sampleCardInfo.value.diySamplecardInfoOrderstatusText = getDistMapData(
      'order_status',
      sampleCardInfo.value?.orderStatus
    )
    sampleCardInfo.value.orderTypeText = getDistMapData(
      'order_type',
      sampleCardInfo.value?.orderType
    )
    sampleCardInfo.value.expressTypeText = getDistMapData(
      'express_type',
      sampleCardInfo.value?.orderDetailExt?.productionList[0]?.expressType
    )

    // 下单凭证
    fileListValue.value = sampleCardInfo.value.orderExt?.fileList || []
  } catch (err) {
  } finally {
    loading.value = false
  }
}

/**
 * 根据数据获取数据字段中的内容
 * @param mapKey 数据字典中的dictType
 * @param value dictType中的value
 */
const getDistMapData = (mapKey, value) => {
  const dictStore = useDictStoreWithOut()
  let arr = dictStore.getDictMap[mapKey]?.find((item) => {
    return item.value === value
  })
  return arr?.label || '---'
}

let downBtnLoading = ref<number[]>([])

// 下载数据
const downFile = async (saveFileName, name, index) => {
  let fileName = name ? name : fileNameFormatter(saveFileName)
  try {
    downBtnLoading.value.push(index)
    const res = await downloadFileApi({
      saveFileName,
      fileName: name
    })
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = fileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  } finally {
    downBtnLoading.value.splice(downBtnLoading.value.indexOf(index), 1)
  }
}

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.lastIndexOf('-') + 1, fileName.length)
}
</script>

<style scoped lang="less">
@import url('../Common/common.less');
.title {
  color: #333333;
  font-size: 16px;
  font-weight: 400;
  display: flex;
  align-items: center;
}
.title::before {
  content: '';
  display: block;
  width: 6px;
  height: 22px;
  background: #e2a32c;
  border-radius: 0 3px 3px 0;
  margin-right: 16px;
}
.desc-column {
  width: 94px;
  text-align: right;
  color: #666666;
}
.content {
  color: #333333;
  flex: 1;
}
.wl-header {
  height: 65px;
  background: #f6f6f6;
  display: flex;
  align-items: center;
  font-size: 16px;
}
.wl-trace {
  border: 1px solid #e6e6ea;
}
.wl-title {
  border: 1px solid #e6e6ea;
  border-bottom: none;
}
.time-line {
  margin-left: -35px;
}
.logistics-text {
  max-width: calc(100% - 150px);
}
.link-mar {
  width: 60px;
  margin-left: 30px;
  line-height: 18px;
  color: #333333;
}
.wl-info {
  padding: 20px 20px 0;
}
.wl-user-info {
  display: flex;
  align-items: center;
  border-top: 1px solid #e6e6ea;
  padding: 20px;
  color: #333333;
  font-size: 16px;
  .put {
    width: 42px;
    height: 42px;
    margin-right: 20px;
    background: #f9ebd0;
    color: #e2a32c;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }
}
</style>
