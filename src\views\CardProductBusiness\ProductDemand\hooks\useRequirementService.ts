import { makeCardListType, productType } from '@/api/makeCardService/types'

import { getProductListApi as queryBatchProducts } from '@/api/product/merchantCard/index'
import { getOutProductList as queryDiyProducts } from '@/api/diy/index'
import { useUserStoreWithOut } from '@/store/modules/user'
// import {
//   getProductListApi as queryDiyProducts, //查询DIY产品List
//   getProductDetailApi as getDiyProduct //获取指定的DIY产品信息
// } from '@/api/product/diyCard/index'

//import { addOrUpdateProductApi } from '@/api/product/productInfo/index'

//import router from '@/router/index'

import { demandProdcut, productTypeList } from '../Common/type'

// import {
//   tipConfirm,
//   getOpenInfo,
//   getDistMapData,
//   isDemandMakeCard,
//   isDemandMakeCardCanDo,
//   isDemandDesign,
//   isDemandDraft,
//   isDemandSampleCard,
//   isDemandGetDesign
// } from '../index'

/**
 * @description 卡产品需求列表相关操作Service
 * @export
 */
export function useRequirementService() {
  const userStore = useUserStoreWithOut()
  //   /**
  //    * @description 进入查看页面
  //    * @param {makeCardListType} data 卡产品需求
  //    */
  //   async function goToView(data: makeCardListType): Promise<void> {
  //     const requirementId = data.makeCardRequirementInfoId //需求ID
  //     const phase: number = data.makeCardRequirementInfoPhase //需求当前阶段标识
  //     let activeTag: detailPageTags | undefined = undefined //
  //     if (isDemandDesign(phase)) activeTag = detailPageTags.design
  //     if (isDemandDraft(phase)) activeTag = detailPageTags.draft
  //     if (isDemandSampleCard(phase)) activeTag = detailPageTags.sampleCardApply
  //     router.push({
  //       path: '/makeCardService/detail', ///demandDetail/index
  //       query: {
  //         makeCardRequirementInfoId: requirementId,
  //         activeName: activeTag
  //       }
  //     })
  //   }

  /**
   * @description 查询卡产品数据操作
   * @param {productType} type 产品类型
   * @param {string} name 产品名称
   * @return {*}  {demandProdcut[]} 可供选择的产品数据
   */
  async function searchProdcuts(type: productType, name: string): Promise<demandProdcut[]> {
    let prodcuts: demandProdcut[] = []
    const customerId: string = userStore.getCustomerInfo?.customerId || null
    if (!customerId) {
      return prodcuts
    }

    if (type == productType.batch) {
      //查询批卡产品
      const result = await queryBatchProducts({ saleName: name, customerId: customerId })
      const { list } = result
      prodcuts = list.map((item) => {
        return { id: item.productId, name: item.saleName, type: productType.batch }
      })
    } else {
      //查询DIY卡产品
      const result = await queryDiyProducts({ cardAliasC: name, customerId: customerId })
      const { data } = result
      prodcuts = data.map((item) => {
        return { id: item.cardId, name: item.cardAliasC ?? item.cardName, type: productType.diy }
      })
    }
    return prodcuts
  }

  /**
   * @description 显示产品类型名称
   * @param {productType} type
   * @return {*}  {string}
   */
  function productTypeFormater(type: productType): string {
    return productTypeList.filter((item) => item.value == type)[0]?.label ?? ''
  }

  //   /**
  //    * @description 获取指定的产品详情信息
  //    * @param {productType} type 产品类型
  //    * @param {string} productId 产品Id
  //    */
  //   async function getProduct(type: productType, productId: string): Promise<productModel> {
  //     const product: productModel = {
  //       id: '',
  //       name: '',
  //       type: productType.batch,
  //       code: '',
  //       // frontImg: undefined,
  //       // reverseImg: undefined,
  //       // maskImg: undefined,
  //       status: 1,
  //       cardCode: '',
  //       cardName: '',
  //       imgList: undefined
  //     }
  //     if (type == productType.batch) {
  //       const result = await getBatcProduct(productId)
  //       const { cardCode, cardName, imgList, productCode, saleName, status } = result
  //       product.id = productId
  //       product.cardCode = cardCode
  //       product.cardName = cardName
  //       product.code = productCode
  //       product.name = saleName
  //       product.status = !status || status.statusCode === 'under_review' ? 0 : 1
  //       product.type = productType.batch

  //       if (imgList) {
  //         product.imgList = imgList
  //       }
  //     }
  //     if (type == productType.diy) {
  //       const result = await getDiyProduct(productId)
  //       const { cardAliasC, cardAliasOut, cardCode, cardName, imageList, statusCode, productCode } =
  //         result
  //       product.id = productId
  //       product.cardCode = cardCode
  //       product.cardName = cardName
  //       product.code = productCode
  //       product.name = cardAliasC ?? cardAliasOut
  //       product.status = statusCode === 'await_submit' ? 0 : 1
  //       product.type = productType.diy
  //       // if (imageList) {
  //       //   const frontImg = imageList.filter((item) => item.diyImageTypeCode == 'FRONT_IMAGE')[0]
  //       //   product.frontImg = {
  //       //     id: frontImg?.imageId,
  //       //     type: 'front',
  //       //     name: frontImg?.imageName,
  //       //     url: frontImg?.imageUrl
  //       //   }

  //       //   const reverseImg = imageList.filter((item) => item.diyImageTypeCode == 'BACK_IMAGE')[0]
  //       //   product.reverseImg = {
  //       //     id: reverseImg?.imageId,
  //       //     type: 'reverse',
  //       //     name: reverseImg?.imageName,
  //       //     url: reverseImg?.imageUrl
  //       //   }

  //       //   const maskImg = imageList.filter((item) => item.diyImageTypeCode == 'MASK_IMAGE')[0]
  //       //   product.maskImg = {
  //       //     id: maskImg?.diyImageId,
  //       //     type: 'mask',
  //       //     name: maskImg?.diyImageFileJson.fileName,
  //       //     url: maskImg?.diyImageFileJson.fileUrl
  //       //   }
  //       // }
  //     }
  //     return product
  //   }

  //   /**
  //    * @description 更新批卡产品信息
  //    * @param {productModel} product
  //    * @param {*} frontFileList
  //    * @param {*} reverseFileList
  //    */
  //   async function updateBatchProdcut(
  //     product: productModel,
  //     frontFileList: any,
  //     reverseFileList: any
  //   ) {
  //     if (frontFileList.length === 0) {
  //       ElMessage.error('请上传产品正面图片！')
  //       return
  //     }
  //     if (reverseFileList.length === 0) {
  //       ElMessage.error('请上传产品背面图片！')
  //       return
  //     }

  //     const data = {
  //       productId: product.id,
  //       productCode: product.code,
  //       productName: product.name,
  //       cardCode: product.cardCode,
  //       cardName: product.cardName,
  //       saleName: product.name,
  //       statusCode: product.status
  //     }
  //     const formData = new FormData()

  //     formData.append('data', JSON.stringify(data))
  //     frontFileList.forEach((file) => {
  //       formData.append('frontImages', file.raw)
  //     })
  //     reverseFileList.forEach((file) => {
  //       formData.append('reverseImages', file.raw)
  //     })

  //     try {
  //       const res = await addOrUpdateProductApi('/product/out/v1/updateClientProduct', formData)
  //       if (res) {
  //         ElMessage.success('保存产品成功！')
  //       }
  //     } catch (e) {
  //       console.error('更新产品失败：', e)
  //     }
  //   }

  //   /**
  //    * @description 查询卡款数据
  //    * @param {string} cardName
  //    * @return {*}
  //    */
  //   async function searchMerchantCardList(cardName: string) {
  //     const params = {
  //       cardName: cardName || ''
  //     }
  //     try {
  //       return (await getMerchantCardListApi(params)) || []
  //     } catch (e) {
  //       console.error('获取卡款列表异常：', e)
  //       return []
  //     }
  //   }

  return {
    // goToView,
    searchProdcuts,
    //  getProduct
    productTypeFormater
    // updateBatchProdcut,

    // searchMerchantCardList
  }
}
