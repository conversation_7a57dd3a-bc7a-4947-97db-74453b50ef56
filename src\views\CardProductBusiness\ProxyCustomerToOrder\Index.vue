<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-23 10:16:01
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-12-08 14:01:45
 * @Description: 卡产品业务-代客下单
-->
<script setup lang="ts">
defineOptions({
  name: 'ProxyCustomerToOrder'
})

import { first, filter } from 'lodash-es'
import { getDictOptions, getStrDictOptions } from '@/utils/dict'
import SelectAddressDialog from './components/SelectAddressDialog.vue'
import AddAddressDialog from './components/AddAddressDialog.vue'
import ProductAddDialog from './components/ProductAddDialog.vue' // 添加产品弹窗
/**上传 */
import { batchDetailsApi, createApi, getNamesApi, pricesApi, saveApi, uploadApi } from '@/api/order'
import { getSaleCustomerListApi, getSampleCustomerListApi } from '@/api/CustomList'
import { useRouter } from 'vue-router'
import { formatMoneyDigitsEx } from '@/utils/formatMoney'
import { useXlsx } from '@/hooks/web/useXlsx'
import Upload from './components/Upload.vue'
import { excelHeaderInfo } from './common/lib'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import { ElMessageBox, FormRules } from 'element-plus'
import { useValidator } from '@/hooks/web/useValidator'
//选择商品
import ProductListDialog from './components/ProductListDialog.vue' // 引入预览插件
import FilePreviewDialog from './components/FilePreviewDialog.vue'
//根据客户id查询客户信息
import { getCustomerDetailApi } from '@/api/makeCardService/other'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useCache } from '@/hooks/web/useCache'
import { cloneDeep } from 'lodash-es'

import { useCustomerService } from '@/hooks/common/useCustomerService'
const { loadCoinType } = useCustomerService()

dayjs.extend(customParseFormat)

const { analyzeExcel, exportExcel, getExcelHeader } = useXlsx()
const { t, ifEn } = useI18n()
const message = useMessage()
const router = useRouter()

interface dataValue {
  data: any
  code: number
  msg: string
}

// 产品最大数量
const PRODUCT_NUM = 9999900

interface QueryDataType {
  orderType: string
  orderSource: string
  customerId: string
  terminalCustomerId: string | undefined //终端客户
  receiveAt: string
  customerOrderCode: string
  orderDetailExt: {
    productionList: any
  }
  urgentSign: boolean
  urgentReason: string
  orderExt: {
    deliveryTime: string
    contact: string
    tel: string
    deliveryMethod: string
    receivingInfo: any
    fileList: any
    originFileList: any
    productionCount: string
    numberCount: string
    mailMode: string
    coinType: string
  }
  note: string
  saleUserId: string
  draftUserId: string
}

const orderTypeOptions = ref(getDictOptions('order_type'))
const onSelectOrderType = async (x) => {
  // if (['INVENTORY_SAMPLE', 'FREE_SAMPLE_ORDER'].includes(x)) {
  //   queryData.orderExt.deliveryMethod = undefined
  // } else {
  //   queryData.orderExt.deliveryMethod = 'mail'
  // }
}
const queryData = reactive<QueryDataType>({
  orderType: orderTypeOptions.value?.[0]?.value,
  orderSource: 'sale',
  customerId: '',
  terminalCustomerId: '',
  receiveAt: '',
  customerOrderCode: '',
  orderDetailExt: {
    productionList: []
  },
  urgentSign: false,
  urgentReason: '',
  orderExt: {
    deliveryTime: '',
    contact: '',
    tel: '',
    deliveryMethod: 'mail',
    receivingInfo: [],
    fileList: [],
    originFileList: [], // 附件的原件
    productionCount: '',
    numberCount: '',
    mailMode: '', //邮寄方式
    coinType: 'CNY' //币种类型
  },
  note: '',
  saleUserId: '',
  draftUserId: ''
})

const forwarders: Ref<string[]> = ref([])

const validator = useValidator()

// 是否禁用终端用户选择
let disableTerminalSelect = ref(true)

const checkTerminalInfo = (_rule: any, value: any, callback: any) => {
  return disableTerminalSelect.value || !!value
    ? callback()
    : callback(new Error(t('cardProductBusiness.proxyCustomerToOrder.terminalSelectIsNull')))
}

const rules = reactive<FormRules>({
  orderType: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.orderTypeNotNull'),
      trigger: 'blur'
    }
  ],
  receiveAt: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.customerOrderReceiveTimeNotNull'),
      trigger: 'change'
    }
  ],
  urgentSign: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.urgentNotNull'),
      trigger: 'blur'
    }
  ],
  urgentReason: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.urgentExplainNotNull'),
      trigger: 'blur'
    }
  ],
  customerId: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.customerNameNotNull'),
      trigger: 'change'
    }
  ],
  terminalCustomerId: [{ validator: checkTerminalInfo, trigger: 'blur' }],
  // saleUserId: [{ required: true, message: '业务员（销售）不能为空', trigger: 'change' }],
  // draftUserId: [{ required: true, message: '稿样不能为空', trigger: 'change' }],
  deliveryTime: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.deliveryTimeNotNull'),
      trigger: 'change'
    }
  ],
  contact: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.userNotNull'),
      trigger: 'change'
    }
  ],
  tel: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.noNullForPhone'),
      trigger: 'change'
    },
    {
      validator: validator.validatePhone,
      trigger: 'change'
    }
  ],
  deliveryMethod: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.deliveryMethodNotNull'),
      trigger: 'change'
    }
  ],
  fileList: [
    {
      required: false,
      message: t('cardProductBusiness.proxyCustomerToOrder.fileListNotNull'),
      trigger: 'change'
    }
  ]
})
const fromEl = ref()
const createLoading = ref(false)

const cancel = () => {
  router.push({
    name: 'OrderSearch'
  })
  closeCurrentPage()
}

//保存
const create = async (type) => {
  try {
    createLoading.value = true
    let isValid = false
    fromEl.value &&
      (await fromEl.value.validate((valid) => {
        isValid = valid
      }))
    if (!isValid)
      return message.notifyError(t('cardProductBusiness.proxyCustomerToOrder.pleaseInputRequired'))
    // 检查下单产品数量
    if (!queryData.orderDetailExt.productionList.length)
      return message.notifyError(t('cardProductBusiness.proxyCustomerToOrder.productNotNull'))
    let hasEmptyCount = queryData.orderDetailExt.productionList.some(
      (x) => x.cardCount === undefined || x.cardCount <= 0
    )

    if (hasEmptyCount) {
      return message.notifyError(t('cardProductBusiness.proxyCustomerToOrder.productNumNotNull'))
    }
    if (
      !['INVENTORY_SAMPLE', 'FREE_SAMPLE_ORDER'].includes(queryData.orderType) &&
      !queryData.orderExt.mailMode
    ) {
      return message.notifyError(
        t('cardProductBusiness.proxyCustomerToOrder.deliveryMethodNotNull')
      )
    }
    // console.log('queryData.orderExt.deliveryMethod', queryData.orderExt.deliveryMethod)

    if (queryData.orderExt.deliveryMethod === 'mail') {
      let check = true
      //遍历下单产品
      queryData.orderDetailExt.productionList.forEach((item) => {
        // let total = 0
        item['receivingList'] = []
        //receivingInfo 邮寄地址信息
        queryData.orderExt.receivingInfo.forEach((item1, index1) => {
          let { productionInfo, ...objRest } = item1
          item1?.productionInfo[item.productId] > 0 &&
            item['receivingList'].push({
              ...objRest,
              cardCount: item1?.productionInfo[item.productId]
            })
          //   total = item1?.productionInfo[item.productId] + total
          //   if (queryData.orderExt.receivingInfo.length - 1 == index1 && total !== item.cardCount) {
          //     check = false
          //     message.notifyError(`商品"${item.productionName}"数量与地址栏商品数量不相等`)
          //   }
        })
      })
      if (!check) return
    }
    const params = cloneDeep(queryData)
    // 下拉列表清空后undefined数据库没变
    Object.keys(params).forEach((key) => {
      if (params[key] === undefined) {
        params[key] = ''
      }
    })
    params.orderDetailExt.productionList.forEach((item) => {
      if (item.productId.includes('Add')) {
        item.productId = ''
      }
    })
    params.orderExt.receivingInfo.forEach((item) => {
      item['productionInfo'] = {}
    })
    params.orderExt.fileList.forEach((item) => {
      delete item.id
    })
    params.orderExt.originFileList.forEach((item) => {
      delete item.id
    })

    const saleUser = saleCustomerList.value.find((item: any) => {
      return item.id == queryData.saleUserId
    })
    if (saleUser) {
      params['saleUserAccount'] = saleUser['username']
      params['saleUserId'] = saleUser['id']
      params['saleUserName'] = saleUser['nickname']
    }

    const draftUser = sampleCustomerList.value.find((item: any) => {
      return item.id == queryData.draftUserId
    })
    if (draftUser) {
      params['draftUserAccount'] = draftUser['username']
      params['draftUserId'] = draftUser['id']
      params['draftUserName'] = draftUser['nickname']
    }

    console.log(params)
    let res: dataValue | undefined = undefined
    if (type === 'create') {
      res = await createApi(params)
    }
    if (type === 'save') {
      res = await saveApi(params)
    }
    if (res && res.code === 0) {
      ElMessage({
        message: t('cardProductBusiness.proxyCustomerToOrder.createSuccess'),
        type: 'success'
      })
      router.push({
        name: 'OrderSearch'
      })
      clearTempCache()
      closeCurrentPage()
    }
  } finally {
    createLoading.value = false
  }
}

/*const tempSave = () => {
  const { wsCache } = useCache()
  wsCache.add('tempOrderData', queryData)
  ElMessageBox.confirm(
    t('cardProductBusiness.proxyCustomerToOrder.orderSaveSuccess'),
    t('common.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel')
    }
  )
    .then(() => {
      cancel()
    })
    .catch(() => {})
}*/
const checkTempCache = () => {
  const { wsCache } = useCache()
  const cache = wsCache.get('tempOrderData')
  if (cache) {
    ElMessageBox.confirm(
      t('cardProductBusiness.proxyCustomerToOrder.hasSaveOrder'),
      t('common.tip'),
      {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel')
      }
    )
      .then(() => {
        Object.assign(queryData, cache)
      })
      .catch(() => console.info('操作取消'))
  }
}
const clearTempCache = () => {
  const { wsCache } = useCache()
  wsCache.delete('tempOrderData')
}
const del = (row) => {
  queryData.orderDetailExt.productionList.forEach((item, index) => {
    if (item.productId === row.productId) {
      queryData.orderDetailExt.productionList.splice(index, 1)
    }
  })
  queryData.orderExt.receivingInfo.forEach((item) => {
    delete item.productionInfo[row.productionName]
  })
}
const tableLoading = ref(false)

const addDialog = ref(false)
const addressDialog = ref(false)

const selectAddress = () => {
  if (!queryData.customerId)
    return message.notifyError(t('productsShow.batchCardProduct.customerNamePlaceholder'))
  addressDialog.value = true
}
const addAddress = () => {
  if (!queryData.customerId)
    return message.notifyError(t('productsShow.batchCardProduct.customerNamePlaceholder'))
  addDialog.value = true
  addressInfo.value = {}
}

//用于查询产品的客户id 优先终端客户
const searchProductByCustomerId = computed(() => {
  if (queryData.terminalCustomerId) return queryData.terminalCustomerId
  return queryData.customerId
})
let productListDialogRef = ref()
const addProduct = () => {
  if (!queryData.customerId && !queryData.terminalCustomerId)
    return message.notifyError(t('productsShow.batchCardProduct.customerNamePlaceholder'))
  productListDialogRef.value.getReceivingList()
}
const productName = ref('')
//选择商品
const productSelection = async (val) => {
  console.log(val)
  //获取商品价格
  let param = val?.length
    ? val.map((item) => {
        return {
          productId: item.productId,
          purchaseQuantity: 100
        }
      })
    : ''
  const res = await pricesApi(param)
  //整理productionList数据，用户当前从产品库选择的产品
  let productionList =
    val && val.length
      ? val.map((item, i) => {
          return {
            customerId: queryData.customerId,
            productionName: item.saleName,
            // key: saleName 后端真正使用的产品名称字段
            saleName: item.saleName,
            cardName: item.saleName,
            imgList: item.imgList,
            unitPrice: res?.[i]?.price || 0,
            productId: item.productId,
            cardCount: undefined,
            productCode: item.productCode,
            cardCode: item.cardCode,
            isIndividual: 1,
            productType: 1, // 产品类型（1-卡产品，2-非卡产品）
            companyCode: '', // 我司代码
            customerCode: '', // 客户唯一代码
            remark: '', // 备注
            manualDeliveryAt: queryData.orderExt.deliveryTime, // 交付时间
            deliveryType: '' // 交付方式
          }
        })
      : []
  // 将已经添加过的商品过滤
  // let newProductionList = productionList.filter((item) => {
  //   return (
  //     queryData.orderDetailExt.productionList.findIndex(
  //       (item1) => item.productId === item1.productId
  //     ) < 0
  //   )
  // })
  queryData.orderDetailExt.productionList =
    queryData.orderDetailExt.productionList.concat(productionList)
  queryData.orderExt.productionCount = queryData.orderDetailExt.productionList.length
}
const handleChange = async (val, row) => {
  if (row.productId.includes('Add')) {
    // 新增的产品不触发请求
    return
  }

  const res = await pricesApi([{ productId: row.productId, purchaseQuantity: val }])
  queryData.orderDetailExt.productionList.forEach((item) => {
    if (item.productId === row.productId) {
      item['unitPrice'] = res?.[0]?.price || 0
    }
  })
}
const selection = (val) => {
  queryData.orderExt.receivingInfo = val || []
  queryData.orderExt.receivingInfo.forEach((item) => {
    item['reveivingCount'] = 0
    item['productionInfo'] = {}
    queryData.orderDetailExt.productionList.forEach((item1) => {
      item['productionInfo'][item1.productId] = 0
    })
  })
}
const addressInfo = ref()
const editAddress = (row) => {
  addDialog.value = true
  addressInfo.value = row
}
const getCardCount = (row) => {
  let total = 0
  for (let key in row.productionInfo) {
    total = row.productionInfo[key] + total
  }
  row.reveivingCount = total
  return total
}
const productNum = computed(() => {
  let total = 0
  queryData.orderDetailExt.productionList.forEach((item) => {
    total = item['cardCount'] ? item['cardCount'] + total : total
  })
  return total
})

watch(
  productNum,
  (val) => {
    queryData.orderExt.numberCount = String(val)
  },
  {
    immediate: true
  }
)
const productTotal = computed(() => {
  let totalPrice = 0
  // if (queryData.orderExt.deliveryMethod === 'mail') {
  //   queryData.orderDetailExt.productionList.forEach((item) => {
  //     queryData.orderExt.receivingInfo.forEach((item1) => {
  //       totalPrice = item1?.productionInfo[item.productId] * item.unitPrice + totalPrice
  //     })
  //   })
  // } else {
  queryData.orderDetailExt.productionList.forEach((item) => {
    let cardCount = item?.cardCount ?? 0
    totalPrice = cardCount * item.unitPrice + totalPrice
  })
  // }
  return totalPrice.toFixed(2)
})

let uploadRef = ref()
const fileList = ref()
const acceptFileTypes = '.doc,.docx,.pdf,.xls,.xlsx,.jpg,.jpeg,.png,.txt'
const fileUpload: any = async (f) => {
  const formData = new FormData()
  const renameFile = new File([f.file], f.file.name.split(/[\t\r\f\n\s]*/g).join(''), {
    type: f.file.type
  }) // 这里创建一个新的file对象，并且将文件名称的空格进行过滤，否则上传带有空格的文件名称会有问题
  formData.append('files', renameFile)

  try {
    const { data } = await uploadApi(formData)
    if (data.length) {
      data[0].id = f.file.id
    }
    queryData.orderExt.fileList.push(...data)
    queryData.orderExt.originFileList.push(...data)
    fileList.value.forEach((item) => {
      item.name = item.name.split(/[\t\r\f\n\s]*/g).join('')
      if (item.raw?.id == f.file.id) {
        item.src = data?.[0]?.eosUrl
      }
    })
  } catch (e) {
    // fileList.value = []
    //todo: 为何需要调用handleRemove，上传异常时没有调用queryData.orderExt.fileList的push方法
    uploadRef.value.handleRemove(f.file)
    ElMessage.error(t('cardProductBusiness.proxyCustomerToOrder.uploadErr'))
  }
}
const beforeUpload = (file) => {
  let maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error(t('cardProductBusiness.proxyCustomerToOrder.uploadLimitReUpload'))
    return false
  }

  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
  let fileSuffix = acceptFileTypes.includes(fileType)
  if (!fileSuffix) {
    ElMessage.error(t('cardProductBusiness.proxyCustomerToOrder.checkFileFormat'))
    return false
  }
  file.id = generateId() // 给这个file赋值一个唯一ID，后面用来标记这个文件
}
//todo : 已上传到服务器的文件未删除
const handleRemove = (uploadFile, uploadFiles) => {
  queryData.orderExt.fileList = queryData.orderExt.fileList.filter((item) => {
    if (item.id !== uploadFile?.raw?.id) return item
  })
  queryData.orderExt.originFileList = queryData.orderExt.originFileList.filter((item) => {
    if (item.id !== uploadFile?.raw?.id) return item
  })
}

const fileViewerRef = ref<any>(null)
let dialogDoc: any = ref(false)
const currentFileId = ref('') // 当前查看的file的id
const handlePreview = (file) => {
  dialogDoc.value = true
  let data = file
  currentFileId.value = file.raw.id
  console.log('handlePreview')
  console.log(file)
  const suffixArray = data.name.split('.')
  if (null == suffixArray || suffixArray.length == 0) return
  const suffix = suffixArray[suffixArray.length - 1]
  if (suffix == 'docx') {
    fileViewerRef.value?.viewDocx(data)
  } else if (suffix == 'xlsx') {
    fileViewerRef.value?.viewXlsx(data)
  } else if (suffix == 'jpg' || suffix == 'jpeg' || suffix == 'png') {
    fileViewerRef.value?.viewImg(data)
  } else if (suffix == 'pdf') {
    fileViewerRef.value?.viewPdf(data)
  } else if (suffix == 'txt') {
    fileViewerRef.value?.viewTxt(data)
  }
}
// 将修改涂抹后的图片或者PDF重新上传并更新到文件列表
const editImgFile = async (file) => {
  const formData = new FormData()
  formData.append('files', file)

  try {
    const { data } = await uploadApi(formData)
    fileList.value.forEach((item) => {
      if (item.raw.id == currentFileId.value) {
        item.src = data?.[0]?.eosUrl
      }
    })
    queryData.orderExt.fileList.forEach((item, index) => {
      if (item.id == currentFileId.value) {
        queryData.orderExt.fileList[index] = {
          id: currentFileId.value,
          ...data[0]
        }
      }
    })
  } catch (e) {
    ElMessage.error(t('cardProductBusiness.proxyCustomerToOrder.uploadErr'))
  }
}

let customer = ref<any>({})
const getCustomerDetail = async (customerId) => {
  try {
    // queryData.orderExt.contact = customer.value.contact
    // queryData.orderExt.tel = customer.value.contactPhone
    //清空邮寄方式和产品列表
    queryData.orderExt.mailMode = ''
    queryData.orderDetailExt.productionList = []
    queryData.orderExt.receivingInfo = []
    forwarders.value = []
    if (customerId) {
      customer.value = await getCustomerDetailApi(customerId)
      disableTerminalSelect.value = customer.value.k3CustomerCode !== '03.101'
      if (disableTerminalSelect.value) {
        queryData.terminalCustomerId = undefined
      }
      forwarders.value = customer.value.forwarders
    } else {
      queryData.terminalCustomerId = undefined
      disableTerminalSelect.value = true
    }
  } catch (e) {
    queryData.orderExt.mailMode = ''
    forwarders.value = []
  }
}

// 邮寄方式，如果forwarders里面没有值，就取默认的字典值，如果有，则根据forwarders获取字典值
const mailModeOptions = computed(() => {
  if (!forwarders.value || JSON.stringify(forwarders.value) === '[]') {
    return sortByLabel(getStrDictOptions('mail_mode'))
  } else {
    return sortByLabel(getStrDictOptions('mail_mode')).filter(
      (e) => forwarders?.value.indexOf(e?.value as string) !== -1
    )
  }
})
/**
 * 根据数组元素的label属性对数组进行排序
 *
 * @param array 要排序的数组，数组元素必须包含label属性
 * @returns 排序后的数组
 */
const sortByLabel = (array) => {
  return array.sort((a, b) => {
    // 将label转换为字符串，以确保比较的是字符串
    var labelA = String(a.label).toUpperCase()
    var labelB = String(b.label).toUpperCase()

    // 如果a的label小于b的label，则返回负数，表示a应该排在b前面
    if (labelA < labelB) {
      return -1
    }

    // 如果a的label大于b的label，则返回正数，表示a应该排在b后面
    if (labelA > labelB) {
      return 1
    }

    // 如果label相等，则返回0，表示a和b的顺序不变
    return 0
  })
}

const customerList = ref()
const saleCustomerList = ref()
const sampleCustomerList = ref()
onMounted(async () => {
  customerList.value = await getNamesApi()

  let retData = await getSaleCustomerListApi()
  saleCustomerList.value = retData.data

  console.log('saleCustomerList.value', saleCustomerList.value)

  //  saleCustomerList.value = arr.map((item) => {
  //     return {
  //       saleUserAccount: item.username,
  //       saleUserId: item.id,
  //       saleUserName: item.nickname
  //     }
  //   })
  retData = await getSampleCustomerListApi()
  sampleCustomerList.value = retData.data

  //加载编辑数据
  let orderId = route.query?.orderId

  if (orderId !== '' && orderId !== undefined) {
    //查询批卡订单详情
    const data = await batchDetailsApi(orderId)
    // 加载客户信息 重置交付方式
    getCustomerDetail(data.customerId)
    //加载批卡订单详情数据
    Object.assign(queryData, data)
    fileList.value = []
    data.orderExt.fileList.forEach((item) => {
      let id = generateId()
      item.id = id
      fileList.value?.push({
        name: item.fileName,
        src: item.eosUrl,
        status: 'success',
        raw: { id }
      })
    })
    data.orderExt.originFileList = cloneDeep(data.orderExt.fileList)
  }
  // checkTempCache()
})

const getImg = (row) => {
  let imageUrl = undefined
  row?.imgList?.[0].imageUrl
  row?.imgList &&
    row?.imgList.forEach((item) => {
      if (item.imageType === 'front') imageUrl = item.imageUrl
    })
  return imageUrl
}
const selectAddressDialogRef = ref()
const updateList = () => {
  selectAddressDialogRef.value && selectAddressDialogRef.value.getReceivingList()
}
const disabledDate = (time) => {
  // return time.getTime() > Date.now() // 可选历史天、可选当前天、不可选未来天
  // return time.getTime() > Date.now() - 8.64e7;  // 可选历史天、不可选当前天、不可选未来天
  // return time.getTime() < Date.now() - 8.64e7;  // 不可选历史天、可选当前天、可选未来天
  return time.getTime() < Date.now() // 不可选历史天、不可选当前天、可选未来天
}
const handleBlur = (e, i, productId) => {
  queryData.orderExt.receivingInfo[i].productionInfo[productId] = e.target?.valueAsNumber
}
const productionBlur = (e, i) => {
  queryData.orderDetailExt.productionList[i].cardCount = e.target?.valueAsNumber
}

const showAddProductDialog = ref(false)
const productDetail: any = ref(null)
const createProduct = (row = null) => {
  if (!queryData.customerId)
    return message.notifyError(t('productsShow.batchCardProduct.customerNamePlaceholder'))
  productDetail.value = row
  showAddProductDialog.value = true
}
// 新增产品，修改产品，提交数据回显
const productAddSubmit = (product) => {
  const newProduct = {
    customerId: queryData.customerId,
    productionName: product.productName,
    saleName: product.productName,
    cardName: product.productName,
    imgList: [],
    unitPrice: product.price,
    productId: generateId(),
    cardCount: product.quantity,
    productCode: '',
    cardCode: '',
    isIndividual: product.write31material ? 1 : 0,
    productType: product.productType,
    companyCode: product.companyCode,
    customerCode: product.customerCode,
    remark: product.remark,
    manualDeliveryAt: product.manualDeliveryAt || queryData.orderExt.deliveryTime, // 交付时间
    deliveryType: product.deliveryType // 交付方式
  }
  if (product.productId) {
    // 修改
    let index = queryData.orderDetailExt.productionList.findIndex(
      (item) => item.productId == product.productId
    )
    if (index != -1) {
      queryData.orderDetailExt.productionList[index] = {
        ...newProduct,
        productId: product.productId,
        imgList: productDetail.value?.imgList,
        productCode: productDetail.value?.productCode,
        cardCode: productDetail.value?.cardCode
      }
    }
  } else {
    // 新增
    queryData.orderDetailExt.productionList.push(newProduct)
  }
}
// 生成唯一ID
const generateId = () => {
  return 'Add_' + Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
}

const TagsViewStore = useTagsViewStore()
const route = useRoute()
// 关闭当前页面
const closeCurrentPage = () => {
  const index = TagsViewStore.visitedViews.findIndex((item) => {
    return item.name === route.name
  })
  TagsViewStore.visitedViews.splice(index, 1)
}

// 导入模板 解析数据
const uploadExcelRef = ref()

const fileChange = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: `Loading`,
    background: 'rgba(255, 255, 255, 0.3)'
  })
  const res = await uploadExcelRef.value.submitFileBase64()
  resolveExcel(res)
  loading.close()
}

// 解析excel数据
const resolveExcel = async (res) => {
  try {
    tableLoading.value = true
    let headers = await getExcelHeader(res[0].raw)
    excelHeaderInfo.forEach((item, index) => {
      if (item !== headers[index]) {
        throw Error(t('cardProductBusiness.proxyCustomerToOrder.importFileErr'))
      }
    })
    let result = await analyzeExcel(res[0].raw)
    batchImportProduct(result)
  } catch (error) {
    ElMessage.error(t('cardProductBusiness.proxyCustomerToOrder.importFileErr'))
  } finally {
    tableLoading.value = false
    nextTick(() => {
      uploadExcelRef.value.clearFile()
    })
  }
}

// 批量导出创建产品
const batchImportProduct = (result) => {
  try {
    let newProductList: any = []
    result.forEach((item) => {
      const regexNum = /^\d+$/
      if (item[excelHeaderInfo[1]] < 1 || item[excelHeaderInfo[1]] > PRODUCT_NUM)
        throw new Error(
          t('cardProductBusiness.proxyCustomerToOrder.checkOrderNum', {
            maxNum: PRODUCT_NUM
          })
        )
      if (!item[excelHeaderInfo[1]])
        throw new Error(t('cardProductBusiness.proxyCustomerToOrder.inputNumInfo'))

      //免费样卡 入库样卡不判断单价
      if (!['INVENTORY_SAMPLE', 'FREE_SAMPLE_ORDER'].includes(queryData.orderType)) {
        if (!item[excelHeaderInfo[8]] && item[excelHeaderInfo[8]] !== 0)
          throw new Error(t('cardProductBusiness.proxyCustomerToOrder.inputPriceInfo'))
        const regexNumAndPoint = /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/
        if (!regexNumAndPoint.test(item[excelHeaderInfo[8]]) && item[excelHeaderInfo[8]] !== 0) {
          throw new Error(t('cardProductBusiness.proxyCustomerToOrder.priceNoTrue'))
        }
      }
      if (!regexNum.test(item[excelHeaderInfo[1]])) {
        throw new Error(t('cardProductBusiness.proxyCustomerToOrder.numNoTrue'))
      }

      if (item[excelHeaderInfo[3]]) {
        const manualDeliveryAtReg = dayjs(item[excelHeaderInfo[3]]).isValid()
        if (!manualDeliveryAtReg) {
          throw new Error(t('cardProductBusiness.proxyCustomerToOrder.deliveryTimeFormatNoTrue'))
        }
      }
      // console.log('xlsxDateFormater', xlsxDateFormater(item[excelHeaderInfo[3]]))

      const newProduct = {
        customerId: queryData.customerId,
        productionName: item[excelHeaderInfo[0]] || '',
        saleName: item[excelHeaderInfo[0]] || '',
        cardName: item[excelHeaderInfo[0]] || '',
        imgList: [],
        unitPrice: item[excelHeaderInfo[8]] ? item[excelHeaderInfo[8]] : 0,
        productId: generateId(),
        cardCount: item[excelHeaderInfo[1]] || 0,
        productCode: '',
        cardCode: '',
        isIndividual:
          item[excelHeaderInfo[9]] === t('cardProductBusiness.orderSearch.excelWrite') ? 1 : 0, // 是否写入个人化（1-写入，2-不写入）
        productType:
          item[excelHeaderInfo[2]] === t('cardProductBusiness.orderSearch.excelCardProduct')
            ? 1
            : item[excelHeaderInfo[2]] === t('cardProductBusiness.orderSearch.excelNonCardProduct')
            ? 2
            : '', // 产品类型（1-卡产品，2-非卡产品）
        companyCode: item[excelHeaderInfo[10]] || '',
        customerCode: item[excelHeaderInfo[11]] || '',
        remark: item[excelHeaderInfo[12]] ? item[excelHeaderInfo[12]]?.toString() : '',
        manualDeliveryAt: xlsxDateFormater(item[excelHeaderInfo[3]]), //交付时间
        // ? dayjs(item[excelHeaderInfo[3]]).format('YYYY-MM-DD HH:mm:ss')
        // : '', // 交付时间
        deliveryType: item[excelHeaderInfo[4]] || '',
        packageType: item[excelHeaderInfo[5]] || '',
        inBox: item[excelHeaderInfo[6]] || '',
        outBox: item[excelHeaderInfo[7]] || ''
      }
      newProductList.push(newProduct)
    })
    queryData.orderDetailExt.productionList.push(...newProductList)
  } catch (error: any) {
    ElMessage.error(error.message)
  } finally {
  }
}

//格式化xls的日期
const xlsxDateFormater = (sourceDate) => {
  if (!sourceDate) {
    return ''
  }
  if (typeof sourceDate === 'object') {
    return dayjs(sourceDate).add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
  }
  return dayjs(sourceDate).format('YYYY-MM-DD HH:mm:ss')
}

const downloadTemplateProductFile = () => {
  exportExcel(
    excelHeaderInfo,
    [],
    t('cardProductBusiness.proxyCustomerToOrder.exportOrderFileName')
  )
}

//币种类型
//const coinType = ref<string>('')
watch(
  [() => queryData.terminalCustomerId, () => queryData.customerId],
  async () => {
    const customerId: string = queryData.terminalCustomerId || queryData.customerId
    const customer = first(filter(customerList.value, (item) => item.customerId === customerId))
    if (!customer) {
      return ''
    }
    const type = await loadCoinType(customer.customerCode) //根据租户ID获取币种类型
    queryData.orderExt.coinType = type
    //coinType.value = type
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <ElForm
    ref="fromEl"
    label-position="right"
    label-width="auto"
    class="form"
    :rules="rules"
    :model="queryData"
  >
    <ElFormItem :label="t('cardProductBusiness.orderSearch.orderType')" prop="orderType">
      <el-select
        v-model="queryData.orderType"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.selectOrderType')"
        filterable
        clearable
        style="width: 350px"
        @change="onSelectOrderType"
      >
        <el-option
          v-for="item in orderTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </ElFormItem>
    <ElFormItem :label="t('cardProductBusiness.orderSearch.customerName')" prop="customerId">
      <el-select
        v-model="queryData.customerId"
        :placeholder="t('productsShow.batchCardProduct.customerNamePlaceholder')"
        filterable
        clearable
        style="width: 350px"
        @change="getCustomerDetail"
      >
        <el-option
          v-for="item in customerList"
          :key="item.customerId"
          :label="item.customerName"
          :value="item.customerId"
        />
      </el-select>
    </ElFormItem>
    <ElFormItem
      :label="t('cardProductBusiness.proxyCustomerToOrder.endCustomer')"
      prop="terminalCustomerId"
    >
      <el-select
        v-model="queryData.terminalCustomerId"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.endCustomerPlaceholder')"
        filterable
        clearable
        :disabled="disableTerminalSelect"
        style="width: 350px"
      >
        <el-option
          v-for="item in customerList"
          :key="item.customerId"
          :label="item.customerName"
          :value="item.customerId"
        />
      </el-select>
    </ElFormItem>
    <ElFormItem
      :label="t('cardProductBusiness.orderSearch.customerOrderReceiveTime')"
      prop="receiveAt"
    >
      <ElDatePicker
        v-model="queryData.receiveAt"
        type="date"
        value-format="YYYY-MM-DD HH:mm:ss"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.selectTime')"
        style="width: 350px"
      />
    </ElFormItem>

    <ElFormItem :label="t('cardProductBusiness.proxyCustomerToOrder.salesman')" prop="saleUserId">
      <el-select
        v-model="queryData.saleUserId"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.salesmanTip')"
        filterable
        clearable
        style="width: 350px"
      >
        <el-option
          v-for="item in saleCustomerList"
          :key="String(item.id)"
          :label="item.nickname"
          :value="String(item.id)"
        />
      </el-select>
    </ElFormItem>

    <ElFormItem
      :label="t('cardProductBusiness.proxyCustomerToOrder.cardAddPersion')"
      prop="draftUserId"
    >
      <el-select
        v-model="queryData.draftUserId"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.cardAddPersionTip')"
        filterable
        clearable
        style="width: 350px"
      >
        <el-option
          v-for="item in sampleCustomerList"
          :key="String(item.id)"
          :label="item.nickname"
          :value="String(item.id)"
        />
      </el-select>
    </ElFormItem>

    <ElFormItem
      :label="t('cardProductBusiness.orderSearch.deliveryMethod')"
      prop="orderExt.deliveryMethod"
    >
      <!-- <ElRadioGroup
        v-model="queryData.orderExt.deliveryMethod"
        class="mr-3"
        @change="
          (val) => {
            if (val != 'mail') {
              queryData.orderExt.mailMode = ''
            }
          }
        "
      >
        <ElRadio value="storage">入库代存</ElRadio>
        <ElRadio value="customerPick">自提</ElRadio>
        <ElRadio value="mail">邮寄</ElRadio>
      </ElRadioGroup> -->
      <template v-if="queryData.orderExt.deliveryMethod === 'mail'">
        <ElSelect
          v-model="queryData.orderExt.mailMode"
          style="width: 350px"
          :placeholder="t('makeCard.sampleOrder.mailModePlaceholder')"
          clearable
        >
          <ElOption
            v-for="dict in mailModeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </ElSelect>
        <!--        <ElButton type="primary" @click="selectAddress" class="add">选择地址</ElButton>
        <ElButton type="primary" @click="addAddress" class="add">新增地址</ElButton>
        <ElTable
          style="width: 100%; margin-top: 20px"
          :data="queryData.orderExt.receivingInfo"
          :headerCellStyle="{ background: '#F8F8F8', height: '45px' }"
          v-loading="tableLoading"
        >
          <ElTableColumn prop="addressName" label="地址名称" width="120" />
          <ElTableColumn prop="addressDetail" label="地址" width="200" />
          <ElTableColumn prop="contact" label="收件人" width="120" />
          <ElTableColumn prop="tel" label="联系电话" width="120" />
          <ElTableColumn prop="reveivingCount" label="数量" width="120">
            <template #default="{ row }">
              {{ getCardCount(row) }}
            </template>
          </ElTableColumn>
          <ElTableColumn
            v-for="item in queryData.orderDetailExt.productionList"
            :key="item"
            :label="item.productionName + ''"
            width="150"
          >
            <template #header>
              <el-tooltip :content="item.productionName" placement="top">
                <div class="long_title">{{ item.productionName }} ({{ item.cardCount }})</div>
              </el-tooltip>
            </template>
            <template #default="{ row, $index }">
              <ElInputNumber
                class="!w-full"
                v-model="row.productionInfo[item.productId]"
                @blur="(val) => handleBlur(val, $index, item.productId)"
                :max="item.cardCount"
                :value-on-clear="0"
                :controls="false"
              />
            </template>
          </ElTableColumn>
        </ElTable> -->
      </template>
    </ElFormItem>
    <ElFormItem
      :label="t('cardProductBusiness.orderSearch.deliveryTime')"
      prop="orderExt.deliveryTime"
    >
      <ElDatePicker
        v-model="queryData.orderExt.deliveryTime"
        type="date"
        :disabled-date="disabledDate"
        value-format="YYYY-MM-DD HH:mm:ss"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.selectTime')"
        style="width: 350px"
      />
    </ElFormItem>

    <ElFormItem
      :label="t('cardProductBusiness.orderSearch.fileList')"
      prop="orderExt.fileList"
      :rules="rules.fileList"
    >
      <ElUpload
        ref="uploadRef"
        v-model:file-list="fileList"
        style="width: 30%"
        :http-request="fileUpload"
        :before-upload="beforeUpload"
        :on-remove="handleRemove"
        :on-exceed="
          () => {
            message.notifyError(t('cardProductBusiness.proxyCustomerToOrder.moreTip'))
          }
        "
        :accept="acceptFileTypes"
        :limit="8"
        multiple
        :on-preview="handlePreview"
      >
        <template #trigger>
          <ElButton v-track:click.btn type="primary">{{
            t('makeCard.common.selectFile')
          }}</ElButton>
        </template>
        <template #tip>
          <div class="el-upload__tip">
            {{
              t('makeCard.common.uploadFileNoFormatTip', {
                maxNum: 8,
                maxSize: '10M'
              })
            }}
          </div>
        </template>
      </ElUpload>
    </ElFormItem>
    <ElFormItem :label="t('cardProductBusiness.orderSearch.isUrgent')" prop="urgentSign">
      <ElRadioGroup
        v-model="queryData.urgentSign"
        @change="
          (_val) => {
            queryData.urgentReason = ''
          }
        "
      >
        <ElRadio :value="true">{{ t('cardProductBusiness.orderSearch.yes') }}</ElRadio>
        <ElRadio :value="false">{{ t('cardProductBusiness.orderSearch.no') }}</ElRadio>
      </ElRadioGroup>
    </ElFormItem>
    <ElFormItem
      :label="t('cardProductBusiness.proxyCustomerToOrder.urgentExplain')"
      prop="urgentReason"
      v-if="queryData.urgentSign === true"
    >
      <ElInput
        type="textarea"
        :rows="2"
        v-model="queryData.urgentReason"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.urgentExplainPlaceholder')"
        maxlength="1000"
        show-word-limit
        clearable
        style="width: 350px"
      />
    </ElFormItem>
    <ElFormItem :label="t('cardProductBusiness.proxyCustomerToOrder.remark')" prop="note">
      <ElInput
        type="textarea"
        :rows="2"
        v-model="queryData.note"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.remarkPlaceholder')"
        maxlength="1000"
        show-word-limit
        clearable
      />
    </ElFormItem>
    <ElFormItem
      :label="t('cardProductBusiness.orderSearch.customerOrderCode')"
      prop="customerOrderCode"
    >
      <ElInput
        v-model="queryData.customerOrderCode"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.customerCodePlaceholder')"
        maxlength="100"
        clearable
        style="width: 350px"
      />
    </ElFormItem>
    <ElFormItem
      :label="t('cardProductBusiness.proxyCustomerToOrder.addOrderProduct')"
      prop="orderDetailExt.productionList"
    >
      <ElInput
        v-model="productName"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.inputCardNamePlaceholder')"
        maxlength="40"
        clearable
        style="width: 350px"
      />
      <ElButton type="primary" v-track:click.btn @click="addProduct" class="add">
        <!-- {{ t('common.add') }} -->
        {{ t('cardProductBusiness.proxyCustomerToOrder.chooseProduct') }}
      </ElButton>
      <ElButton type="primary" @click="createProduct()" class="add">
        <!-- 录入产品 -->
        {{ t('cardProductBusiness.proxyCustomerToOrder.writeProduct') }}
      </ElButton>
      <ElTable
        :data="queryData.orderDetailExt.productionList"
        class="mt-5"
        style="width: 100%"
        :max-height="400"
        :headerCellStyle="{ background: '#F8F8F8', height: '45px' }"
        v-loading="tableLoading"
      >
        <ElTableColumn type="index" :label="t('tableDemo.index')" :width="70" />
        <ElTableColumn
          prop="cardName"
          :label="t('cardProductBusiness.orderSearch.productName')"
          :min-width="400"
        >
          <template #default="{ row }">
            <ElImage
              class="imgBox"
              v-if="row.imgList?.[0]?.imageUrl"
              style="width: 100px; height: 100px; margin-right: 10px"
              :preview-teleported="true"
              :src="getImg(row)"
              :preview-src-list="[row.imgList[0].imageUrl]"
              fit="contain"
            />
            <span>{{ row.cardName }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="companyCode"
          :label="t('cardProductBusiness.proxyCustomerToOrder.companyCode')"
          :min-width="ifEn ? 180 : 120"
        >
          <template #default="{ row }">
            {{ row.companyCode }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="customerCode"
          :label="t('cardProductBusiness.proxyCustomerToOrder.customerCode')"
          :min-width="ifEn ? 180 : 120"
        >
          <template #default="{ row }">
            {{ row.customerCode }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="unitPrice"
          :label="t('cardProductBusiness.proxyCustomerToOrder.productUnitPrice')"
          :min-width="ifEn ? 180 : 80"
        >
          <template #default="{ row }">
            {{ formatMoneyDigitsEx(row.unitPrice, queryData.orderExt.coinType) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="manualDeliveryAt"
          :label="t('cardProductBusiness.orderSearch.deliveryTime')"
          :min-width="ifEn ? 200 : 180"
        >
          <template #default="{ row }">
            <ElDatePicker
              v-model="row.manualDeliveryAt"
              type="date"
              value-format="YYYY-MM-DD HH:mm:ss"
              :placeholder="t('cardProductBusiness.proxyCustomerToOrder.selectTime')"
              style="width: 150px"
            />
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="cardCount"
          :label="t('cardProductBusiness.proxyCustomerToOrder.orderNum')"
          :min-width="140"
        >
          <template #default="{ row, $index }">
            <ElInputNumber
              @change="(val) => handleChange(val, row)"
              @blur="(val) => productionBlur(val, $index)"
              v-model="row.cardCount"
              :step="1"
              :controls="false"
              step-strictly
              :min="0"
              :max="PRODUCT_NUM"
              :value-on-clear="0"
              style="width: 100px"
            />
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="productType"
          :label="t('cardProductBusiness.proxyCustomerToOrder.type')"
          :min-width="80"
        >
          <template #default="{ row }">
            {{
              row.productType === 1
                ? t('cardProductBusiness.proxyCustomerToOrder.cardProduct')
                : t('cardProductBusiness.proxyCustomerToOrder.nonCardProduct')
            }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="isIndividual"
          :label="t('cardProductBusiness.proxyCustomerToOrder.write31Materiel')"
          :min-width="180"
        >
          <template #default="{ row }">
            <el-radio-group v-model="row.isIndividual">
              <el-radio :value="1" size="large">{{
                t('cardProductBusiness.proxyCustomerToOrder.write')
              }}</el-radio>
              <el-radio :value="0" size="large">{{
                t('cardProductBusiness.proxyCustomerToOrder.nonWrite')
              }}</el-radio>
            </el-radio-group>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="remark"
          :label="t('cardProductBusiness.orderSearch.excelHeaderOfRemark')"
          :min-width="100"
        >
          <template #default="{ row }">
            {{ row.remark }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="packageType"
          :label="t('makeCard.sampleOrder.packageMode')"
          :min-width="ifEn ? 140 : 80"
        >
          <template #default="{ row }">
            {{ row.packageType }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="inBox"
          :label="t('cardProductBusiness.orderSearch.excelHeaderOfInnerBox')"
          :min-width="ifEn ? 120 : 80"
        >
          <template #default="{ row }">
            {{ row.inBox }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="outBox"
          :label="t('makeCard.sampleOrder.outerBox')"
          :min-width="ifEn ? 120 : 80"
        >
          <template #default="{ row }">
            {{ row.outBox }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="status" :label="t('common.oper')" width="160">
          <template #default="{ row }">
            <ElButton type="primary" v-track:click.btn @click="createProduct(row)">{{
              t('common.edit')
            }}</ElButton>
            <ElButton type="danger" v-track:click.btn @click="del(row)">{{
              t('common.remove')
            }}</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElFormItem>
  </ElForm>
  <ElRow class="footer flex flex-col content-end">
    <ElCol class="flex items-center mr-5">
      {{ t('makeCard.sampleOrder.product') }}: {{ queryData.orderDetailExt.productionList?.length
      }}{{ t('makeCard.sampleOrder.unitO') }}
    </ElCol>
    <ElCol class="flex items-center mr-5">
      {{ t('makeCard.sampleOrder.cardCount') }}: {{ productNum
      }}{{ t('makeCard.sampleOrder.unitP') }}
    </ElCol>
    <ElCol class="flex items-center mr-5">
      {{ t('makeCard.sampleOrder.price') }}:
      {{ formatMoneyDigitsEx(productTotal, queryData.orderExt.coinType) }}
    </ElCol>
    <ElCol class="flex items-center mr-5">
      <ElButton type="primary" v-track:click.btn @click="cancel" class="add">{{
        t('common.cancel')
      }}</ElButton>
      <ElButton
        type="primary"
        v-track:click.btn
        @click="create('save')"
        class="add"
        :loading="createLoading"
        >{{ t('common.save') }}</ElButton
      >
      <ElButton
        type="primary"
        v-track:click.btn
        @click="create('create')"
        class="add"
        :loading="createLoading"
      >
        {{ t('cardProductBusiness.proxyCustomerToOrder.createOrder') }}
      </ElButton>
    </ElCol>
  </ElRow>
  <SelectAddressDialog
    ref="selectAddressDialogRef"
    v-model:addressDialog="addressDialog"
    v-model:addDialog="addDialog"
    :customer="customer"
    @selection="selection"
    @edit-address="editAddress"
    @add-address="addAddress"
  />
  <AddAddressDialog
    v-model:addDialog="addDialog"
    :customer="customer"
    :addressInfo="addressInfo"
    @update-list="updateList"
  />
  <ProductListDialog
    ref="productListDialogRef"
    :customerId="searchProductByCustomerId"
    :productName="productName"
    @product-selection="productSelection"
  />
  <ProductAddDialog
    v-model="showAddProductDialog"
    :productDetail="productDetail"
    @submit="productAddSubmit"
  />
  <!-- 查看 -->
  <FilePreviewDialog ref="fileViewerRef" editMode @edit-img-file="editImgFile" />
</template>

<style lang="less" scoped>
.form {
  padding-bottom: 50px;
}
.el-input {
  width: 26%;
  min-width: 268px;
}

.add {
  margin-left: 20px;
}
.footer {
  background-color: #ccc;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  z-index: 9;
}
.imgBox {
  background: #f5f7fa;
  border-radius: 10px;
  vertical-align: middle;
}
</style>
