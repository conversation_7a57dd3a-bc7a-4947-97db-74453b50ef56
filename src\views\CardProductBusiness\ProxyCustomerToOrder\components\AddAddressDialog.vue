<script setup lang="ts">
const { t } = useI18n()
import { createReceivingInfoApi, receivingInfoUpdateApi, getAreatTreeApi } from '@/api/order'
interface ICustomer {
  customerName: number
  customerId: number
}
const props = defineProps<{
  addDialog: boolean
  addressInfo: any
  customer: ICustomer | {}
}>()

const info = reactive({
  addressName: '',
  contact: '',
  tel: '',
  address: '',
  city: '',
  province: '',
  county: '',
  cityName: '',
  customerId: ''
})
const isAdd = computed(() => {
  return props.addressInfo && !Object.keys(props.addressInfo).length
})

watch(
  () => (props.customer as ICustomer)?.customerId,
  (newValue) => {
    info.customerId = newValue + ''
  }
)

//寄送地址tree
const customLocationProps = {
  label: 'name',
  value: 'id',
  children: 'children',
  isLeaf: 'isLeaf'
}

let treeData = ref<any>([])
const loadNode = async () => {
  let res = await getAreatTreeApi()
  treeData.value = res
}
onMounted(() => {
  loadNode()
})
//联动地址数据
let addressData = ref<any>([])
const areaChange = (val) => {
  console.log(val)

  //   info.cityName = data.parentName + data.name
  info.county = val[2] ?? ''
  info.city = val[1] ?? ''
  info.province = val[0] ?? ''
  treeData.value.find((item) => {
    if (item.id == info.province) {
      info.cityName = item.name
      //城市名称拼接
      !!info.city &&
        item.children.find((item2) => {
          if (item2.id == info.city) {
            info.cityName = info.cityName + item2.name
            //区名称拼接
            !!info.county &&
              item2.children.find((item3) => {
                if (item3.id == info.county) {
                  info.cityName = info.cityName + item3.name
                }
              })
          }
        })
    }
  })
}
const resetForm = () => {
  addressData.value = []
  info.address = ''
  fromEl.value && fromEl.value.resetFields()
}
const addDialog = computed({
  get() {
    if (props.addDialog) {
      nextTick(async () => {
        if (isAdd.value) return
        for (let key in info) {
          info[key] = props.addressInfo[key]
        }
        //地址联级选择器数据回显
        addressData.value.push(info.province)
        addressData.value.push(info.city)
        addressData.value.push(info.county)
      })
    } else {
      resetForm()
    }
    return props.addDialog
  },
  set(newValue) {
    emit('update:addDialog', newValue)
  }
})
const customerName = computed(() => {
  return (props.customer as ICustomer)?.customerName
})
const emit = defineEmits(['update:addDialog', 'updateList'])
const cancel = () => {
  emit('update:addDialog', false)
}
const loading = ref(false)
const fromEl = ref()
const rules = reactive({
  customerId: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.noNullForCustomer'),
      trigger: 'blur'
    }
  ],
  addressName: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.noNullForAddress'),
      trigger: 'blur'
    }
  ],
  contact: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.noNullForPeople'),
      trigger: 'blur'
    }
  ],
  tel: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.noNullForPhone'),
      trigger: 'blur'
    }
  ],
  cityName: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.noNullForMailAddress'),
      trigger: 'blur'
    }
  ]
})
const confirm = () => {
  fromEl.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        const res = isAdd.value
          ? await createReceivingInfoApi(info)
          : await receivingInfoUpdateApi(Object.assign(props.addressInfo, info))
        const txt = isAdd.value ? t('common.add') : t('common.edit')
        ElMessage({
          message:
            res.code === 0
              ? `${txt}${t('cardProductBusiness.proxyCustomerToOrder.addSuccess')}`
              : `${txt}${t('cardProductBusiness.proxyCustomerToOrder.addErr')}`,
          type: res.code === 0 ? 'success' : 'error'
        })
        emit('update:addDialog', false)
        emit('updateList', true)
      } finally {
        loading.value = false
      }
    }
  })
}
</script>
<template>
  <Dialog
    v-model="addDialog"
    width="650px"
    :title="
      (isAdd ? t('common.add') : t('common.edit')) +
      t('cardProductBusiness.proxyCustomerToOrder.receivingInfo')
    "
    center
    :maxHeight="'auto'"
    @close="cancel"
  >
    <ElForm
      class="pt-20px"
      ref="fromEl"
      label-position="right"
      label-width="100px"
      :rules="rules"
      :model="info"
    >
      <ElFormItem :label="t('cardProductBusiness.orderApproval.customerName')" prop="customerId">
        <ElInput v-model="customerName" disabled />
      </ElFormItem>
      <ElFormItem
        :label="t('cardProductBusiness.proxyCustomerToOrder.addressName')"
        prop="addressName"
      >
        <ElInput
          v-model="info.addressName"
          :placeholder="t('cardProductBusiness.proxyCustomerToOrder.addressNamePlaceholder')"
          clearable
          maxlength="50"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem :label="t('makeCard.sampleOrder.contact')" prop="contact">
        <ElInput
          v-model="info.contact"
          :placeholder="t('makeCard.sampleOrder.contactPlaceholder')"
          clearable
          maxlength="50"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem :label="t('cardProductBusiness.orderSearch.tel')" prop="tel">
        <ElInput
          v-model="info.tel"
          :placeholder="t('makeCard.sampleOrder.addressOfTelPlaceholder')"
          maxlength="11"
          show-word-limit
          clearable
        />
      </ElFormItem>
      <ElFormItem :label="t('cardProductBusiness.proxyCustomerToOrder.area')" prop="cityName">
        <el-cascader
          class="w-full"
          v-model="addressData"
          :options="treeData"
          :props="customLocationProps"
          @change="areaChange"
        />
        <!-- <ElTreeSelect
          v-model="info.cityName"
          :data="treeData"
          :props="customLocationProps"
          @node-click="areaChange"
          clearable
        /> -->

        <ElInput
          v-model="info.address"
          :placeholder="t('cardProductBusiness.proxyCustomerToOrder.areaPlaceholder')"
          type="textarea"
          class="mt-3"
          clearable
          maxlength="50"
          show-word-limit
        />
      </ElFormItem>
    </ElForm>
    <div class="operate">
      <ElButton @click="cancel">{{ t('common.cancel') }}</ElButton>
      <ElButton type="primary" @click="confirm" :loading="loading">{{ t('common.ok') }}</ElButton>
    </div>
  </Dialog>
</template>

<style scoped lang="less">
.operate {
  display: flex;
  justify-content: right;
}
.el-form-item__content {
  .flex {
    display: flex;
    justify-content: space-between;
    .el-select {
      width: 48%;
    }
    .el-input {
      width: 48%;
    }
  }
}
@import '@/styles/public.less';
</style>
