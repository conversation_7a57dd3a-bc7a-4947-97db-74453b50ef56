<template>
  <!-- doc -->
  <el-dialog
    v-model="dialogDocxValue"
    class="preview_dialog"
    :title="dialogTitle"
    fullscreen
    append-to-body
    lock-scroll
    destroy-on-close
    :close-on-press-escape="false"
    @close="dialogDocxClose"
  >
    <div ref="docxRef" class="word-div"></div>
    <template #footer>
      <el-button @click="dialogDocxClose">{{ t('common.close') }}</el-button>
    </template>
  </el-dialog>

  <!-- xlsx -->
  <el-dialog
    v-model="dialogXlsxValue"
    class="preview_dialog"
    :title="dialogTitle"
    fullscreen
    append-to-body
    lock-scroll
    destroy-on-close
    :close-on-press-escape="false"
    @close="dialogXlsxClose"
  >
    <div ref="xlsxRef" class="xlsx-div">
      <el-tabs v-model="activeName" type="border-card">
        <el-tab-pane
          v-for="(item, index) in excelSheet"
          :key="index"
          :label="item.name"
          :name="item.name"
        >
          <div class="table" v-html="item.html"></div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="dialogXlsxClose">{{ t('common.close') }}</el-button>
    </template>
  </el-dialog>

  <!-- 图片文件编辑&预览弹窗 -->
  <el-dialog
    v-model="dialogImgValue"
    class="tui-edit-img_dialog"
    :title="dialogTitle"
    fullscreen
    append-to-body
    lock-scroll
    destroy-on-close
    :close-on-press-escape="false"
  >
    <TuiImageEditor
      v-if="dialogImgValue && editMode"
      ref="imageEditorRef"
      :imgeUrl="fileData.src"
    />
    <div v-else class="img-div">
      <el-image :src="fileData.src" />
    </div>
    <template #footer>
      <el-button @click="dialogImgValue = false">{{ t('common.close') }}</el-button>
      <el-button v-if="editMode" type="primary" @click="saveEditImg">{{
        t('common.save')
      }}</el-button>
    </template>
  </el-dialog>

  <!-- pdf -->
  <el-dialog
    v-model="dialogPdfValue"
    class="preview_dialog"
    :title="dialogTitle"
    fullscreen
    append-to-body
    lock-scroll
    destroy-on-close
    :close-on-press-escape="false"
    @close="dialogPdfClose"
  >
    <div v-loading="pdfLoading" class="pdf-div">
      <template v-if="editMode">
        <div v-for="(item, i) in pdfImgFiles" :key="i" class="canvas_box">
          <canvas
            :id="`pdf_canvas_${item}`"
            style="border: 2px solid #c0c4cc; cursor: pointer"
            @click="editPdfImg(item)"
          ></canvas>
        </div>
      </template>
      <iframe
        v-else
        id="pdfRef"
        :src="iframeUrl"
        frameborder="0"
        style="width: 100%; height: 100%"
      ></iframe>
    </div>
    <template #footer>
      <el-button :loading="pdfLoading" :disabled="pdfLoading" @click="dialogPdfValue = false">{{
        t('common.close')
      }}</el-button>
      <el-button
        v-if="editMode"
        type="primary"
        :loading="pdfLoading"
        :disabled="pdfLoading"
        @click="saveEditPdfImg"
        >{{ t('common.save') }}</el-button
      >
    </template>
  </el-dialog>

  <!-- txt -->
  <el-dialog
    v-model="dialogTxtValue"
    class="preview_dialog"
    :title="dialogTitle"
    fullscreen
    append-to-body
    lock-scroll
    destroy-on-close
    :close-on-press-escape="false"
    @close="dialogTxtClose"
  >
    <div class="txt-div">
      {{ textContent }}
    </div>
    <template #footer>
      <el-button @click="dialogTxtClose">{{ t('common.close') }}</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import axios from 'axios'
import { renderAsync } from 'docx-preview'
import * as XLSX from 'xlsx'
const { t } = useI18n()
interface Props {
  editMode?: boolean
}
const props: any = withDefaults(defineProps<Props>(), {
  editMode: false // 是否开启图片和PDF文件的编辑模式
})
const emit = defineEmits(['editImgFile'])
const dialogDocxValue: any = ref(false)
let dialogTitle = ref('')
const fileHtml = ref('')

const docxRef = ref<any>()
// doc 文档预览
const viewDocx = (data: any) => {
  docxRef.value = ''
  dialogDocxValue.value = true
  if (data.src) {
    // 已上传的文件
    axios({
      url: data.src,
      method: 'get',
      responseType: 'blob'
    }).then((res) => {
      if (res.status == 200) {
        const content = res.data
        const blob = new Blob([content])
        nextTick(() => {
          dialogDocxValue.value = true
          renderAsync(blob, docxRef.value)
          dialogTitle.value = data.name
        })
      }
    })
  } else {
    // 本地文件
    const blob = new Blob([data.raw])
    nextTick(() => {
      dialogDocxValue.value = true
      renderAsync(blob, docxRef.value)
      dialogTitle.value = data.name
    })
  }
}
const dialogXlsxValue: any = ref(false)
const excelSheet: any = ref([])
const activeName = ref('')
const dialogDocxClose = () => {
  dialogDocxValue.value = false
  docxRef.value = ''
}
// xlsx 预览
const viewXlsx = (data: any) => {
  dialogXlsxValue.value = true
  if (data.src) {
    axios({
      url: data.src,
      method: 'get',
      responseType: 'blob'
    }).then((res) => {
      if (res.status == 200) {
        const content = res.data
        // const blob = new Blob(content);
        const reader = new FileReader()
        reader.readAsArrayBuffer(content)
        reader.onload = function (loadEvent: any) {
          const arrayBuffer = loadEvent.target['result']
          const workbook = XLSX.read(new Uint8Array(arrayBuffer), {
            type: 'array'
          })
          const list = []
          const sheetNames = workbook.SheetNames
          activeName.value = sheetNames[0]
          for (const p of sheetNames) {
            let html = ''
            try {
              html = XLSX.utils.sheet_to_html(workbook.Sheets[p])
            } catch (e) {
              html = ''
            }
            const map = {
              name: p,
              html: html
            }
            list.push(map)
          }
          excelSheet.value = list
          dialogTitle.value = data.name
        }
      }
    })
  } else {
    const blob = new Blob([data.raw])
    const reader = new FileReader()
    reader.readAsArrayBuffer(blob)
    reader.onload = function (loadEvent: any) {
      const arrayBuffer = loadEvent.target['result']
      const workbook = XLSX.read(new Uint8Array(arrayBuffer), {
        type: 'array'
      })
      const list = []
      const sheetNames = workbook.SheetNames
      activeName.value = sheetNames[0]
      for (const p of sheetNames) {
        let html = ''
        try {
          html = XLSX.utils.sheet_to_html(workbook.Sheets[p])
        } catch (e) {
          html = ''
        }
        const map = {
          name: p,
          html: html
        }
        list.push(map)
      }
      excelSheet.value = list
      dialogTitle.value = data.name
    }
  }
}
const dialogXlsxClose = () => {
  dialogXlsxValue.value = false
  excelSheet.value = ''
  activeName.value = ''
}

import TuiImageEditor from '@/components/TuiImageEditor'
const fileData: any = ref({})
const dialogImgValue: any = ref(false)
// 图片预览编辑
const viewImg = (data: any) => {
  if (data.src) {
    // 已上传的图片
    fileData.value = {
      src: data.src,
      id: new Date(),
      fileName: data.name
    }
    dialogImgValue.value = true
  } else {
    // 本地图片
    const freader = new FileReader()
    freader.readAsDataURL(data.raw)
    freader.onload = (e: any) => {
      fileData.value = {
        src: e.target.result,
        id: new Date(),
        fileName: data.name
      }
      dialogImgValue.value = true
    }
  }
  dialogTitle.value = data.name
}
const dialogImgClose = () => {
  dialogImgValue.value = false
}
// TuiImageEditor图片编辑的相关逻辑
let imageEditorRef = ref()
/** 将修改后的图片进行保存 */
async function saveEditImg() {
  dialogImgValue.value = false
  let imgDataUrl = imageEditorRef.value?.getImgDataURL()
  let pdfTypeList = '.pdf,.PDF'
  let fileType = fileData.value.fileName.slice(
    fileData.value.fileName.indexOf('.') + 1,
    fileData.value.fileName.length
  )
  if (pdfTypeList.indexOf(fileType) != -1) {
    // 是PDF类型文件里面的图片，将图片重新绘制到canvas中
    const canvas: any = document.getElementById('pdf_canvas_' + fileData.value.index)
    const ctx = canvas.getContext('2d')
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    // 创建新图片
    let img = new Image()
    // 设置图片源地址
    img.src = imgDataUrl
    // 确保图片加载完成后再绘制到canvas
    img.onload = () => {
      // 在canvas上绘制图片
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
    }
  } else {
    // 是图片类型文件，直接进行上传更新
    let file = convertBase64ToFile(imgDataUrl, fileData.value.fileName, 'img')
    emit('editImgFile', file)
  }
}

/** 将base64数据转为file */
function convertBase64ToFile(urlData, fileName, type = '') {
  let fileType = fileName.slice(fileName.indexOf('.') + 1, fileName.length)
  let arr = urlData.split(',')
  let mime = arr[0].match(/:(.*?);/)[1]
  if (type == 'img') {
    mime = `image/${fileType}`
  }
  let suffix = mime.split('/')[1]
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  let file = new File([u8arr], `${fileName.slice(0, fileName.indexOf('.'))}` + '.' + suffix, {
    type: mime
  })
  return file
}

/** PDF预览编辑 */
import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf.mjs'
import * as pdfjsWorker from 'pdfjs-dist/legacy/build/pdf.worker.mjs'
const dialogPdfValue: any = ref(false)
const pdfLoading = ref(false)
const iframeUrl: any = ref('')
const pdfRef = ref<any>()

//pdf的页数，保存在数组中
const pdfImgFiles = ref<number[]>([])
const viewPdf = (data: any) => {
  pdfImgFiles.value = []
  dialogPdfValue.value = true
  dialogTitle.value = data.name
  if (data.src) {
    axios({
      url: data.src,
      method: 'get',
      responseType: 'blob'
    })
      .then((res) => {
        if (res.status == 200) {
          if (props.editMode) {
            handlePdf(res.data)
          } else {
            // 把文件流转化为url
            iframeUrl.value = URL.createObjectURL(res.data)
          }
        }
      })
      .catch((err) => {
        pdfLoading.value = false
      })
  } else if (props.editMode) {
    handlePdf(data.raw)
  } else {
    iframeUrl.value = URL.createObjectURL(data.raw)
  }
}
onBeforeUnmount(() => {
  iframeUrl.value && URL.revokeObjectURL(iframeUrl.value)
})
const dialogPdfClose = () => {
  dialogPdfValue.value = false
}
//处理pdf，将PDF文件转为图片画在CANVAS中
const handlePdf = async (file) => {
  // 设定pdfjs的 workerSrc 参数
  pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker
  pdfLoading.value = true
  const objectURL = URL.createObjectURL(file)
  const loadingTask = pdfjsLib.getDocument({
    url: objectURL,
    // cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.5.207/cmaps/',
    cMapUrl: '/cmaps/', // 加载本地的字体文件路径,解决某些字体无法渲染的问题
    cMapPacked: true
  })
  loadingTask.promise
    .then((pdf) => {
      let pageNum = pdf.numPages
      console.log('页数:' + pageNum)

      //准备图片
      for (let i = 1; i <= pageNum; i++) {
        let one = i
        pdfImgFiles.value.push(one)
      }
      nextTick(() => {
        //处理
        for (let i = 1; i <= pageNum; i++) {
          pdf.getPage(i).then((page) => {
            const canvas: any = document.getElementById('pdf_canvas_' + i)
            const ctx = canvas.getContext('2d')
            const viewport = page.getViewport({ scale: 2 })
            const outputScale = window.devicePixelRatio || 1
            canvas.width = Math.floor(viewport.width * outputScale)
            canvas.height = Math.floor(viewport.height * outputScale)
            const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : undefined
            // 画布大小
            // canvas.height = viewport.height
            // canvas.width = viewport.width
            // 画布的dom大小
            const destWidth = document.body.clientWidth * 0.5
            canvas.style.width = destWidth + 'px'
            // 根据pdf每页的宽高比例设置canvas的高度
            canvas.style.height = destWidth * (viewport.height / viewport.width) + 'px'
            page.render({
              canvasContext: ctx,
              transform,
              viewport
            })
          })
        }
        pdfLoading.value = false
        URL.revokeObjectURL(objectURL)
      })
    })
    .catch((error) => {
      pdfLoading.value = false
      URL.revokeObjectURL(objectURL)
    })
}
// 点击编辑PDF里面的图片
const editPdfImg = (i) => {
  const canvas: any = document.getElementById('pdf_canvas_' + i)
  canvas.toBlob(
    (blob) => {
      const canvasObjectURL = URL.createObjectURL(new Blob([blob], { type: 'image/png' }))
      fileData.value = {
        index: i,
        src: canvasObjectURL,
        id: new Date(),
        fileName: dialogTitle.value
      }
      dialogImgValue.value = true
      nextTick(() => {
        URL.revokeObjectURL(canvasObjectURL)
      })
    },
    'image/png',
    1
  )
}
import jsPDF from 'jspdf'
// 将canvas转化为PDF，再转换为file文件返回
const saveEditPdfImg = () => {
  if (pdfLoading.value) {
    return
  }
  pdfLoading.value = true
  setTimeout(() => {
    const pdf = new jsPDF() // 创建一个新的jsPDF实例
    pdfImgFiles.value.forEach((item, index) => {
      const canvas: any = document.getElementById('pdf_canvas_' + item)
      const canvasData = canvas.toDataURL('image/png')
      const imgProps = pdf.getImageProperties(canvasData)
      const pdfWidth = pdf.internal.pageSize.getWidth()
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width
      pdf.addImage(canvasData, 'PNG', 0, 0, pdfWidth, pdfHeight, '', 'FAST') // FAST是开启压缩
      if (index !== pdfImgFiles.value.length - 1) {
        pdf.addPage()
      } else {
        // pdf.save('multicanvases.pdf')
        let file = convertBase64ToFile(pdf.output('dataurlstring'), dialogTitle.value)
        emit('editImgFile', file)
        pdfLoading.value = false
        dialogPdfValue.value = false
      }
    })
  }, 0)
}

const dialogTxtValue = ref(false)
const textContent = ref('')
// txt预览
const viewTxt = (data: any) => {
  dialogTitle.value = data.name
  dialogTxtValue.value = true
  if (data.src) {
    axios({
      url: data.src,
      method: 'get',
      responseType: 'blob'
    }).then((res) => {
      if (res.status == 200) {
        const reader = new FileReader()
        reader.onload = (e) => {
          textContent.value = e.target.result
        }
        reader.readAsText(res.data)
      }
    })
  } else {
    const reader = new FileReader()
    reader.onload = (e) => {
      textContent.value = e.target.result
    }
    reader.readAsText(data.raw)
  }
}
const dialogTxtClose = () => {
  dialogTxtValue.value = false
}
defineExpose({
  viewDocx,
  viewXlsx,
  viewImg,
  viewPdf,
  viewTxt
})
</script>
<style scoped lang="less">
.word-div {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
}
.xlsx-div {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
}
.img-div {
  height: 100%;
  overflow: auto;
  img {
    width: 100%;
    height: 100%;
  }
}

.pdf-div {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .canvas_box {
    display: flex;
    justify-content: center;
    margin: 10px 0;
  }
}
.txt-div {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: auto;
}
</style>
<style lang="less">
.preview_dialog {
  padding: 0 !important;

  .el-dialog__header {
    padding: 16px 16px 0 16px;
  }

  .el-dialog__body {
    padding: 0 !important;
    height: calc(100% - 134px);
  }
}

.xlsx-div {
  .table-html-wrap table {
    border-right: 1px solid #fff;
    border-bottom: 1px solid #e8eaec;
    border-collapse: collapse;
    // margin: auto;
  }

  .table-html-wrap table td {
    border-left: 1px solid #e8eaec;
    border-top: 1px solid #e8eaec;
    white-space: wrap;
    text-align: left;
    min-width: 100px;
    padding: 4px;
  }

  table {
    border-top: 1px solid #ebeef5;
    border-left: 1px solid #ebeef5;
    width: 100%;
    // overflow: auto;

    tr {
      height: 44px;
    }

    td {
      min-width: 200px;
      max-width: 400px;
      padding: 4px 8px;
      border-right: 1px solid #ebeef5;
      border-bottom: 1px solid #ebeef5;
    }
  }

  .el-tabs--border-card > .el-tabs__content {
    overflow-x: auto;
  }
}
</style>
