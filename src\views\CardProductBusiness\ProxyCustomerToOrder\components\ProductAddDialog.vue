<template>
  <Dialog
    v-model="showDialog"
    :close-on-click-modal="false"
    :title="dialogTitle"
    width="80%"
    :scroll="false"
  >
    <div class="dialog-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
        <el-row :gutter="20">
          <el-col :span="24" :offset="0">
            <el-form-item :label="t('makeCard.common.productName')" prop="productName">
              <el-input
                v-model="form.productName"
                :placeholder="t('common.inputText')"
                maxlength="100"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :offset="0">
            <el-form-item
              :label="t('cardProductBusiness.proxyCustomerToOrder.orderNum')"
              prop="quantity"
            >
              <el-input-number
                class="input__number"
                v-model="form.quantity"
                :placeholder="t('common.inputText')"
                :step="1"
                :min="0"
                :max="9999999"
                :value-on-clear="1"
                step-strictly
                :controls="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0">
            <el-form-item
              :label="t('cardProductBusiness.proxyCustomerToOrder.type')"
              prop="productType"
            >
              <el-select
                v-model="form.productType"
                :placeholder="t('common.selectText')"
                style="width: 100%; min-width: 100%"
              >
                <el-option
                  v-for="dict in productTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0">
            <el-form-item
              :label="t('cardProductBusiness.proxyCustomerToOrder.companyCode')"
              prop="companyCode"
            >
              <el-input
                v-model="form.companyCode"
                :placeholder="t('common.inputText')"
                clearable
                :maxlength="128"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8" :offset="0">
            <el-form-item :label="t('makeCard.sampleOrder.deliveryTime')" prop="manualDeliveryAt">
              <el-date-picker
                v-model="form.manualDeliveryAt"
                type="date"
                value-format="YYYY-MM-DD HH:mm:ss"
                :placeholder="t('cardProductBusiness.proxyCustomerToOrder.chooseDayTime')"
                style="width: 100%; min-width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0" prop="deliveryType">
            <el-form-item :label="t('makeCard.sampleOrder.deliveryMethod')">
              <el-select
                v-model="form.deliveryType"
                :placeholder="t('common.selectText')"
                filterable
                style="width: 100%; min-width: 100%"
              >
                <el-option
                  v-for="dict in deliveryTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0">
            <el-form-item
              :label="t('cardProductBusiness.proxyCustomerToOrder.productUnitPrice')"
              prop="price"
            >
              <el-input-number
                class="input__number"
                v-model="form.price"
                :placeholder="t('common.inputText')"
                :step="0.01"
                :min="0"
                :max="99999999"
                :value-on-clear="0"
                step-strictly
                :controls="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0">
            <el-form-item
              :label="t('cardProductBusiness.proxyCustomerToOrder.customerCode')"
              prop="customerCode"
            >
              <el-input
                v-model="form.customerCode"
                :placeholder="t('common.inputText')"
                clearable
                :maxlength="128"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0">
            <el-form-item prop="write31material">
              <el-checkbox
                v-model="form.write31material"
                :label="t('cardProductBusiness.proxyCustomerToOrder.write31Materiel')"
                >{{ t('cardProductBusiness.proxyCustomerToOrder.write31Materiel') }}</el-checkbox
              >
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24" :offset="0">
            <el-form-item :label="t('makeCard.detail.remark')" prop="remark">
              <el-input
                type="textarea"
                :rows="2"
                v-model="form.remark"
                :placeholder="t('common.inputText')"
                :maxlength="1000"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8" :offset="0">
            <el-form-item :label="t('makeCard.sampleOrder.packageMode')" prop="package">
              <el-input
                v-model="form.packageType"
                :placeholder="t('common.selectText')"
                clearable
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0">
            <el-form-item :label="t('makeCard.sampleOrder.innerBox')">
              <el-input
                v-model="form.inBox"
                :placeholder="t('common.selectText')"
                clearable
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="0">
            <el-form-item :label="t('cardProductBusiness.proxyCustomerToOrder.outerBox')">
              <el-input
                v-model="form.outBox"
                :placeholder="t('common.selectText')"
                clearable
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="cancel">{{ t('common.cancel') }}</el-button>
      <ButtonPromise
        type="primary"
        :clickPromise="submit"
        :loadingText="t('cardProductBusiness.proxyCustomerToOrder.beingSubmitted') + '...'"
        >{{ productDetail ? t('common.save') : t('common.add') }}</ButtonPromise
      >
    </template>
  </Dialog>
</template>

<script setup lang="ts">
const { t } = useI18n()
import type { FormRules } from 'element-plus'
import { getStrDictOptions } from '@/utils/dict'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  productDetail: {
    type: Object,
    default: () => null
  } // 需要编辑的产品详情
})

const showDialog = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  }
})

watch(showDialog, (val) => {
  if (!val) {
    reset()
  } else {
    initProductDetail()
  }
})
const emit = defineEmits(['update:modelValue', 'submit'])
const dialogTitle = ref(t('makeCard.sampleOrder.productInfo'))
const loading = ref(false)
const formRef = ref()
const form = ref<any>({
  companyCode: '', // 我司代码
  createBy: '', // 创建人ID
  createDate: '', // 创建日期
  createName: '', // 创建人名称
  customerCode: '', // 客户代码
  customerProductCode: '', // 客户产品编码（卡款前6位）
  deliveryType: '', // 交货方式
  inBox: '', // 内盒
  manualDeliveryAt: '', // 交付时间
  outBox: '', // 外箱
  packageType: '', // 包装方式
  price: 0, // 产品单价
  productId: '', // umv产品ID
  productName: '', // 产品名称
  productType: 1, // 产品类型（1-卡产品，2-非卡产品）
  quantity: 0, // 下单数量
  remark: '', // 产品备注
  reviewOrderId: '', // 签审订单表ID
  systemicDeliveryAt: '', // 交付日期（系统）
  umvOrderId: '', // umv批卡订单id
  updateBy: '', // 修改人ID
  updateDate: '', // 更新日期
  updateName: '', // 修改人名称
  write31material: false // 是否写入个人化
})
const rules = reactive<FormRules>({
  productName: [
    {
      required: true,
      message: t('cardProductBusiness.orderSearch.productNamePlaceholder'),
      trigger: 'blur'
    }
  ],
  quantity: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.inputOrderNumPlaceholder'),
      trigger: 'blur'
    },
    { validator: quantityRule, trigger: 'blur' }
  ]
})

function quantityRule(rule, value, callback) {
  if (value === '' || value === undefined) {
    callback(new Error(t('cardProductBusiness.proxyCustomerToOrder.inputOrderNumPlaceholder')))
  }
  if (Number(value) <= 0) {
    callback(new Error(t('cardProductBusiness.proxyCustomerToOrder.orderNumSizeTip')))
  }
  callback()
}

const deliveryTypeOptions: any = ref(getStrDictOptions('mail_mode')) // 交付方式选项
const productTypeOptions: any = ref([
  {
    label: t('cardProductBusiness.proxyCustomerToOrder.cardProduct'),
    value: 1
  },
  {
    label: t('cardProductBusiness.proxyCustomerToOrder.nonCardProduct'),
    value: 2
  }
]) // 产品类型选项

/** 表单重置 */
function reset() {
  nextTick(() => {
    formRef.value && formRef.value.resetFields()
    form.value.productName = ''
    form.value.deliveryType = ''
    form.value.companyCode = ''
    form.value.customerCode = ''
    form.value.remark = ''
    form.value.write31material = false
    form.value.quantity = 0
    form.value.price = 0
    form.value.productId = ''
    form.value.productType = 1
    form.value.manualDeliveryAt = ''
  })
}

function cancel() {
  showDialog.value = false
  loading.value = false
}

function initProductDetail() {
  if (props.productDetail) {
    form.value.productId = props.productDetail.productId
    form.value.productName = props.productDetail.productionName
    form.value.price = props.productDetail.unitPrice
    form.value.quantity = props.productDetail.cardCount
    form.value.write31material = props.productDetail.isIndividual === 1 ? true : false

    form.value.productType = props.productDetail.productType
    form.value.companyCode = props.productDetail.companyCode
    form.value.customerCode = props.productDetail.customerCode
    form.value.remark = props.productDetail.remark
    form.value.manualDeliveryAt = props.productDetail.manualDeliveryAt
    form.value.deliveryType = props.productDetail.deliveryType
  }
}

/** 提交按钮 */
function submit() {
  return new Promise<void>((resolve) => {
    formRef.value.validate(async (valid) => {
      if (!valid) {
        resolve()
        return
      }
      emit('submit', JSON.parse(JSON.stringify(form.value)))
      cancel()
      resolve()
    })
  })
}
</script>

<style lang="less" scoped>
.dialog-content {
  padding-top: 20px;
}

:deep(.input__number) {
  width: 100%;
  .el-input__wrapper {
    padding: 1px 7px;
  }

  .el-input__inner {
    text-align: left;
  }
}
</style>
