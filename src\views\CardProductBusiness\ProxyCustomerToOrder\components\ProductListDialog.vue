<script setup lang="ts">
const { t } = useI18n()
import { listApi } from '@/api/order'
import { formatMoneyDigitsEx } from '@/utils/formatMoney'
const message = useMessage()
const props = defineProps<{
  customerId: string
  productName: string
}>()

let productDialog = ref(false)

const total = ref(0)
const queryRef = ref()
const queryParams = ref({
  saleName: '',
  cardCode: ''
})

const pagination = reactive({
  pageNo: 1,
  pageSize: 10
})
const emit = defineEmits(['update:productDialog', 'productSelection'])
const cancel = () => {
  productDialog.value = false
}
const tableData = ref([])
const tableLoading = ref(false)
const getReceivingList = async () => {
  queryParams.value.saleName = props.productName
  queryParams.value.cardCode = props.cardCode
  selectionData.value = {}
  pagination.pageNo = 1
  getList()
}
const productTableRef = ref()
const getList = async () => {
  let currentSelection = []
  try {
    if (undefined !== selectionData.value[pagination.pageNo]) {
      currentSelection = JSON.parse(JSON.stringify(selectionData.value[pagination.pageNo]))
    }

    tableLoading.value = true
    tableData.value = []
    const data = await listApi({
      customerId: props.customerId,
      saleName: queryParams.value.saleName,
      cardCode: queryParams.value.cardCode?.replaceAll('*', '%'),
      ...pagination
    })
    tableData.value = data?.list
    total.value = data?.total
    if (data?.total === 0) {
      message.notifyWarning(t('cardProductBusiness.proxyCustomerToOrder.theCustomerNoProduct'))
      return
    }
    productDialog.value = true
  } finally {
    tableLoading.value = false
    // 跨页支持多选，勾选已选择的产品
    if (currentSelection.length > 0) {
      nextTick(() => {
        productTableRef.value.clearSelection()
        currentSelection.forEach((row) => {
          // 从现有表里面找到记录
          let selectedRow = tableData.value.find((x) => x.productId === row.productId)
          productTableRef.value.toggleRowSelection(selectedRow, true)
        })
      })
    }
  }
}
defineExpose({
  getReceivingList
})

/** 查询按钮操作 */
const handleQuery = () => {
  console.log('handleQuery')
  selectionData.value = {}
  pagination.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value.resetFields()
  handleQuery()
}

//需求：支持跨页多选
const selectionData = ref({})
const handSelectionChange = (val) => {
  selectionData.value[pagination.pageNo] = val
}
const confirm = () => {
  if (selectionData.value === undefined) {
    message.notifyWarning(t('cardProductBusiness.proxyCustomerToOrder.selectProductPlaceholder'))
    return
  }

  let selectList = []
  Object.keys(selectionData.value).forEach((key) => {
    selectList = [...selectList, ...selectionData.value[key]]
  })
  if (selectList.length === 0) {
    message.notifyWarning(t('cardProductBusiness.proxyCustomerToOrder.selectProductPlaceholder'))
    return
  }
  emit('productSelection', selectList)
  productDialog.value = false
}
const getImg = (row) => {
  let imageUrl = undefined
  row?.imgList?.[0].imageUrl
  row?.imgList &&
    row?.imgList.forEach((item) => {
      if (item.imageType === 'front') imageUrl = item.imageUrl
    })
  return imageUrl
}
// 设置序号
const indexMethod = (index: number): number => {
  return (pagination.pageNo - 1) * pagination.pageSize + index + 1
}
</script>
<template>
  <ElDialog
    v-model="productDialog"
    :title="t('cardProductBusiness.proxyCustomerToOrder.chooseProduct')"
    width="70%"
    :showClose="false"
    @close="cancel"
  >
    <el-form ref="queryRef" class="mb-20px" :model="queryParams" label-width="auto">
      <el-row :gutter="24">
        <el-col :md="8" :lg="8" :xl="6">
          <el-form-item :label="t('makeCard.common.productName')" prop="saleName">
            <el-input
              v-model="queryParams.saleName"
              :placeholder="t('common.inputText')"
              clearable
              maxlength="40"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="6">
          <el-form-item :label="t('makeCard.detail.cardDraftCode')" prop="cardCode">
            <el-input
              v-model="queryParams.cardCode"
              :placeholder="t('common.inputText')"
              clearable
              maxlength="40"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :md="8" :lg="8" :xl="6">
          <el-form-item label="">
            <el-button type="primary" @click="handleQuery">{{ t('common.query') }}</el-button>
            <el-button type="warning" @click="resetQuery">{{ t('common.reset') }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <ElTable
      ref="productTableRef"
      v-loading="tableLoading"
      class="mt-[20px]"
      :data="tableData"
      height="40vh"
      @selection-change="handSelectionChange"
    >
      <ElTableColumn type="selection" />
      <ElTableColumn
        type="index"
        :label="t('tableDemo.index')"
        :min-width="70"
        :index="indexMethod"
      />
      <ElTableColumn prop="saleName" :label="t('makeCard.common.productName')" min-width="200px">
        <template #default="{ row }">
          <ElImage
            class="imgBox"
            v-if="row.imgList?.[0]?.imageUrl"
            style="width: 100px; height: 100px"
            :preview-teleported="true"
            :src="getImg(row)"
            :preview-src-list="[row.imgList?.[0].imageUrl]"
            fit="contain"
          />
          <span>{{ row.saleName }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="cardCode" :label="t('makeCard.detail.cardDraftCode')" min-width="200px">
        <template #default="{ row }">
          <span>{{ row.cardCode }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="price"
        :label="t('cardProductBusiness.proxyCustomerToOrder.productUnitPrice')"
        v-if="false"
      >
        <template #default="{ row }">
          <span v-if="!row?.priceStage?.stage">{{
            formatMoneyDigitsEx(row?.priceStage?.price, '')
          }}</span>
          <!-- 阶梯价格 -->
          <span v-else class="flex flex-col">
            <div
              class="flex flex-row"
              v-for="(item, index) in row.priceStage.priceList"
              :key="index"
            >
              <div class="w-32" v-if="item.condition === 'lt'">
                {{ t('cardProductBusiness.proxyCustomerToOrder.lessThan')
                }}{{ item.upperLimit }}</div
              >
              <div class="w-32" v-else-if="item.condition === 'eq'">
                {{ item.lowerLimit }}-{{ item.upperLimit }}
              </div>
              <div class="w-32" v-else-if="item.condition === 'gt'">
                {{ t('cardProductBusiness.proxyCustomerToOrder.greaterThan') }}{{ item.lowerLimit }}
              </div>
              <div> {{ formatMoneyDigitsEx(item.price, item.currency) }}</div>
            </div>
          </span>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="quantity"
        :label="t('cardProductBusiness.proxyCustomerToOrder.inventory')"
        v-if="false"
      >
        <template #default="{ row }">
          {{ row?.merchantCard?.cardInventory?.replace(/\B(?=(\d{3})+\b)/g, ',') || '' }}
        </template>
      </ElTableColumn>
    </ElTable>
    <div class="flex justify-end mt-20px">
      <Pagination
        v-model:page="pagination.pageNo"
        v-model:limit="pagination.pageSize"
        @pagination="getList"
        :total="total"
      />
    </div>

    <div class="operate">
      <ElButton type="primary" @click="confirm">{{ t('common.ok') }}</ElButton>
      <ElButton @click="cancel">{{ t('common.cancel') }}</ElButton>
    </div>
  </ElDialog>
</template>

<style lang="less" scoped>
.flex {
  display: flex;
  .el-input {
    width: 30%;
    margin-right: 20px;
  }
}
.operate {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.imgBox {
  background: #f5f7fa;
  border-radius: 10px;
  vertical-align: middle;
}
@import '@/styles/public.less';
</style>
