<script setup lang="ts">
const { t } = useI18n()
import { receivingListApi, getAreaApi } from '@/api/order'
const props = defineProps<{
  addressDialog: boolean
  addDialog: boolean
  customer: { customerId: number }
}>()
const addressDialog = computed({
  get() {
    return props.addressDialog
  },
  set(newValue) {
    emit('update:addressDialog', newValue)
  }
})
const emit = defineEmits([
  'update:addressDialog',
  'selection',
  'editAddress',
  'update:addDialog',
  'addAddress'
])
const cancel = () => {
  emit('update:addressDialog', false)
}
const tableData = ref()
const tableLoading = ref()
const search = () => {
  getReceivingList()
}
const add = () => {
  emit('update:addDialog', true)
  emit('addAddress', true)
}
const edit = (row) => {
  emit('editAddress', row)
}
const queryData = reactive({
  addressName: '',
  contact: ''
})
const getReceivingList = async () => {
  try {
    tableLoading.value = true
    const data = await receivingListApi({
      customerId: props.customer?.customerId,
      addressName: queryData.addressName,
      contact: queryData.contact
    })
    tableData.value = data
  } finally {
    tableLoading.value = false
  }
}

const tableRef = ref()
watch(
  () => props.addressDialog,
  (newValue) => {
    newValue && getReceivingList()
    if (!newValue) {
      tableRef.value?.clearSelection()
    }
  }
)
const selectionData = ref([])
const handSelectionChange = (val) => {
  selectionData.value = val
}
const confirm = async () => {
  await fulleAddress()
  emit('selection', selectionData.value)
  console.log(selectionData.value, 'selectionData.value')
  emit('update:addressDialog', false)
}
const fulleAddress = () => {
  selectionData.value.forEach(async (item: { fullAddress: string }) => {
    const area = await getAreaApi({ areaId: item['city'] })
    item['fullAddress'] = area + item['address']
  })
}
defineExpose({
  getReceivingList
})
</script>

<template>
  <ElDialog
    v-model="addressDialog"
    :title="t('makeCard.dialog.chooseReceiving')"
    width="60%"
    :showClose="false"
    @close="cancel"
  >
    <div class="flex mt-[20px] mb-[20px]">
      <ElInput
        v-model="queryData.addressName"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.namePlaceholder')"
        clearable
      />
      <ElInput
        v-model="queryData.contact"
        :placeholder="t('cardProductBusiness.proxyCustomerToOrder.userPlaceholder')"
        clearable
      />
      <ElButton type="primary" @click="search">{{ t('common.search') }}</ElButton>
      <ElButton @click="add">{{ t('common.add') }}</ElButton>
    </div>
    <ElTable ref="tableRef" :data="tableData" height="60vh" @selection-change="handSelectionChange">
      <ElTableColumn type="selection" />
      <ElTableColumn prop="addressName" :label="t('makeCard.sampleOrder.name')" width="120" />
      <ElTableColumn prop="contact" :label="t('makeCard.sampleOrder.contact')" width="120" />
      <ElTableColumn prop="tel" :label="t('makeCard.sampleOrder.phone')" width="150" />
      <ElTableColumn prop="address" :label="t('makeCard.sampleOrder.address')">
        <template #default="{ row }">
          {{ row?.addressDetail }}
        </template>
      </ElTableColumn>
      <ElTableColumn :label="t('common.operate')" width="120" align="center">
        <template #default="{ row }">
          <ElButton link type="primary" size="small" @click="edit(row)">{{
            t('common.edit')
          }}</ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
    <div class="operate">
      <ElButton type="primary" @click="confirm">{{ t('common.ok') }}</ElButton>
      <ElButton @click="cancel">{{ t('common.cancel') }}</ElButton>
    </div>
  </ElDialog>
</template>

<style lang="less" scoped>
.flex {
  display: flex;
  .el-input {
    width: 30%;
    margin-right: 20px;
  }
}
.operate {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
@import '@/styles/public.less';
</style>
