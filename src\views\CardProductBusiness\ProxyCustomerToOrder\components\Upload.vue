<template>
  <div class="flex box-tip">
    <el-upload
      :multiple="props.multiple"
      :disabled="props.disabled"
      :limit="props.limit"
      :accept="props.accept"
      :show-file-list="props.isShowFileList"
      :auto-upload="props.autoUpload"
      v-model:file-list="fileList"
      ref="uploadModuleRef"
      :before-upload="beforeUpload"
      :on-remove="onRemove"
      :on-change="fileChange"
      :on-exceed="onExceedHandle"
      class="upload"
    >
      <slot name="btn">
        <el-button type="primary" class="btn-upload" :disabled="props.disabled">
          {{ props.btnText }}
        </el-button>
      </slot>
      <template #tip>
        <div class="el-upload__tip">{{ props.uploadTip }}</div>
      </template>
      <template #file="uploadFileSlot">
        <div class="file-list">
          <div class="icon">
            <el-icon size="12" v-if="uploadFileSlot.file.isImage">
              <Picture />
            </el-icon>
            <el-icon size="12" v-else>
              <Document />
            </el-icon>
          </div>
          <div class="content">
            <div class="flex">
              <div class="name">{{ uploadFileSlot.file.name }}</div>
              <el-button
                type="primary"
                link
                @click="onSlotRemove(uploadFileSlot.file.uid)"
                :disabled="props.disabled"
              >
                {{ t('common.delete') }}
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script lang="ts">
const { t } = useI18n()
</script>

<script setup lang="ts">
import { Document, Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as makeCardApi from '@/api/makeCardService/index'

const uploadModuleRef = ref()

const fileList = ref<any>([])

const props = defineProps({
  showFileList: {
    type: Array,
    default: () => []
  },
  uploadTip: {
    type: String,
    default: ''
  },
  btnText: {
    type: String,
    default: t('makeCard.common.uploadFile')
  },
  disabled: {
    type: Boolean,
    default: false
  },
  limit: {
    type: Number,
    default: 8
  },
  multiple: {
    type: Boolean,
    default: false
  },
  uploadData: {
    type: Object,
    default: () => {}
  },
  //是否自动上传
  autoUpload: {
    type: Boolean,
    default: false
  },
  //上传最大字节限制
  maxSize: {
    type: Number,
    default: 1024 * 1024
  },
  mergeUpload: {
    type: Boolean,
    default: false
  },
  isShowFileList: {
    type: Boolean,
    default: true
  },
  accept: {
    type: String,
    default: ''
  },
  limitFormat: {
    type: Array,
    default: () => []
  }
})

const { maxSize } = toRefs(props)

// 监听页面展示
watch(
  () => props.showFileList,
  () => {
    // 如果存在已上传的图片列表，进行回显
    if (props.showFileList && props.showFileList.length) {
      let showFileList: any[] = []
      props.showFileList.forEach((file: any) => {
        const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
        let isImage = ['png', 'gif', 'jpg'].includes(fileType)
        showFileList.push({
          name: file?.name,
          url: file?.url,
          status: file?.status,
          placeholder: file?.placeholder,
          isImage
        })
      })
      fileList.value = showFileList
    } else {
      fileList.value = []
    }
  },
  { immediate: true, deep: true }
)

const emit = defineEmits(['success', 'file-change', 'delete-file'])

function fileChange(_file, fileArr) {
  fileList.value = fileArr
  if (!props.autoUpload) {
    let canUpload = verifyFile(_file)
    if (!canUpload) {
      fileArr.forEach((v: any, i) => {
        if (v.uid === _file.uid) {
          fileList.value.splice(i, 1)
        }
      })
    }
  }
  emit('file-change', fileList.value)
}

function onSlotRemove(uid) {
  let index: any = 0
  fileList.value.forEach((v: any, i) => {
    if (v.uid == uid) {
      index = i
    }
  })
  fileList.value.splice(index, 1)
  emit('delete-file', fileList.value)
}

const submitFile = async (loading?) => {
  return new Promise(async (resolve, reject) => {
    const loadingUpFileList: any = []
    const formData: FormData = new FormData()
    fileList.value.forEach((file) => {
      if (file.status !== 'success' && !file.url) {
        loadingUpFileList.push(file)
      }
    })
    if (loadingUpFileList?.length < 1) {
      return resolve(fileList.value)
    }
    loadingUpFileList.forEach((file) => {
      formData.append('fileList', file.raw)
    })
    const makeCardFileVerifyIds = loadingUpFileList.map((item) => {
      return item.uid
    })
    formData.append('makeCardFileVerifyIds', makeCardFileVerifyIds)
    formData.append('number', props.limit.toString())
    formData.append('restrict', (props.maxSize / 1024 / 1024).toString())
    try {
      const onUploadProgress = (res) => {
        if (!loading) return
        let progress = Number(((res.loaded / res.total) * 100).toFixed(0))
        loading.setText(`Loading ${progress}%`)
      }
      const data = await makeCardApi.uploadFileApi(formData, onUploadProgress)
      data.data.forEach((element) => {
        fileList.value.forEach((item, fileIndex) => {
          if (element.makeCardFileVerifyId == item.uid) {
            fileList.value[fileIndex].status = 'success'
            fileList.value[fileIndex].placeholder = t('makeCard.common.uploadSuccess')
            fileList.value[fileIndex].url = element.eosName
            fileList.value[fileIndex].fullUrlPath = element.path
          }
        })
      })
      return resolve(fileList.value)
    } catch (e) {
      ElMessage.error(t('makeCard.common.uploadFileError'))
      return reject(false)
    }
  })
}

const submitFileBase64 = () => {
  return new Promise(async (resolve, reject) => {
    try {
      if (fileList?.value?.length < 1) {
        return resolve([])
      }
      await fileList.value.forEach(async (item, index) => {
        fileList.value[index].imgBase64 = await fileToBase64(item.raw)
        if (index === fileList?.value?.length - 1) {
          return resolve(fileList.value)
        }
      })
    } catch (e) {
      ElMessage.error(t('makeCard.common.uploadFileError'))
      return reject(false)
    }
  })
}

const fileToBase64 = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      const base64 = reader.result
      resolve(base64)
    }
    reader.readAsDataURL(file)
  })
}

const onRemove = (file, fileArr) => {
  ElMessage.warning(t('makeCard.common.removeTip', { fileName: file.name }))
  emit('delete-file', fileArr)
}

// 上传之前钩子
const beforeUpload = (file) => {
  return verifyFile(file)
}
function verifyFile(file) {
  if (file.size > maxSize.value) {
    ElMessage.error(t('makeCard.common.sizeOutOfLimit'))
    return false
  }
  if (props.limitFormat.includes('*')) {
    return true
  }
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
  let fileSuffix = props.limitFormat.includes(fileType)
  if (!fileSuffix) {
    ElMessage.error(t('makeCard.common.checkFileFormat'))
  }
  return fileSuffix
}
// 超出上传数量
const onExceedHandle = () => {
  ElMessage.error(t('makeCard.common.numberOutOfLimit'))
}

// 检验是否上传完成
const checkComplete = () => {
  return new Promise<boolean>((resolve, reject) => {
    if (fileList.value.length > 0) {
      let arr = fileList.value.map((item) => {
        return item.url
      })
      if (arr.includes(undefined)) {
        ElMessage.warning(t('makeCard.common.awaitUpload'))
        reject(false)
      } else {
        resolve(true)
      }
    } else {
      resolve(true)
    }
  })
}

// 清空待上传文件
const clearFile = () => {
  fileList.value.splice(0, fileList?.value?.length)
}

defineExpose({
  fileList,
  clearFile,
  checkComplete,
  submitFile,
  submitFileBase64
})
</script>

<style lang="less" scoped>
.el-upload__tip {
  line-height: 16px;
  color: darkgray;
  width: 300px;
  position: absolute;
  top: 0px;
  left: 160px;
}
.btn-upload {
  width: 140px;
  height: 40px;
}
.upload {
  width: 300px;
  margin-right: 20px;
  position: relative;
}
.file-list {
  display: flex;
  align-items: center;
  .icon {
    width: 12px;
    height: 12px;
    display: flex;
    margin-left: 10px;
  }
  .content {
    flex: 1;
    margin-left: 8px;
    .name {
      width: 220px;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .text {
      font-size: 14px;
      color: #409eff;
      margin-left: 10px;
    }
    .slot-button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
