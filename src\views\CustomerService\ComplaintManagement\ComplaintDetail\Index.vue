<template>
  <ContentWrap>
    <div class="box">
      <div :class="isdisabled && !isaudit ? 'display' : ''">
        <span class="label">{{ ComplaintDetailNumber[`${route.query.number}`] }}</span>
        <span class="text" v-if="!isdisabled">只支持投诉类信息提单,其他咨询等信息暂不支持</span>
        <ElIcon v-if="isdisabled && !isaudit" class="close opacity" @click="jumpRoute">
          <Close />
        </ElIcon>
      </div>
      <el-divider />
      <el-form ref="ruleFormRef" label-width="130px" :model="complaintDTO" :rules="rules">
        <el-row>
          <el-col :lg="8" :xl="6">
            <el-form-item label="客户名称：" :prop="complaintDTOprop('customerId')">
              <el-select
                v-if="!isdisabled"
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.customerId"
                :disabled="isdisabled"
                @change="customerchange"
                :teleported="false"
                :fit-input-width="true"
                placeholder="请选择客户名称"
              >
                <el-option
                  v-for="(item, index) in customerList"
                  :key="index"
                  :label="item.customerName"
                  :value="item.customerId"
                />
              </el-select>
              <el-input
                v-else
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.customerName"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6">
            <el-form-item label="投诉类型：" :prop="complaintDTOprop('complaintTypeCode')">
              <el-select
                clearable
                v-if="!isdisabled"
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.complaintTypeCode"
                :disabled="isdisabled"
                @change="getcomplaintName"
                placeholder="请选择投诉类型"
              >
                <el-option label="产品类" value="1" />
                <el-option label="服务类" value="2" />
              </el-select>
              <el-input
                v-else
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.complaintTypeName"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6" v-if="complaintDTO.complaintTypeCode === '1'">
            <el-form-item label="产品类：" :prop="complaintDTOprop('productTypeCode')">
              <el-select
                v-model="complaintDTO.productTypeCode"
                clearable
                v-if="!isdisabled"
                style="width: 100%; min-width: 100%"
                @change="productchange"
                :disabled="isdisabled"
                placeholder="请选择产品类"
              >
                <el-option
                  v-for="item in cardTypeOptions"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </el-select>
              <el-input
                v-else
                clearable
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.productTypeName"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6">
            <el-form-item label="卡号：">
              <el-input
                clearable
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.cardCode"
                :disabled="isdisabled"
                placeholder="请输入卡号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6">
            <el-form-item label="联系人：" :prop="complaintDTOprop('contactPerson')">
              <el-input
                clearable
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.contactPerson"
                placeholder="请输入联系人"
                :disabled="isdisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6">
            <el-form-item label="联系电话：" :prop="complaintDTOprop('contactPhone')">
              <el-input
                clearable
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.contactPhone"
                placeholder="请输入联系电话"
                :disabled="isdisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6">
            <el-form-item label="投诉日期：" :prop="complaintDTOprop('time')">
              <el-input
                clearable
                style="width: 100%; min-width: 100%"
                v-model="complaintDTO.time"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6" v-if="isdisabled">
            <el-form-item label="投诉状态">
              <el-input
                clearable
                style="width: 100%; min-width: 100%"
                :value="complainStatus[complaintDTO.statusCode]"
                placeholder="请输入投诉状态"
                :disabled="isdisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6" v-if="isdisabled">
            <el-form-item label="责任人：">
              <el-input
                v-model="complaintDTO.lastResponseUserName"
                style="width: 100%; min-width: 100%"
                disabled
            /></el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6" v-if="isdisabled">
            <el-form-item label="责任部门：">
              <el-input
                v-model="complaintDTO.processDepartName"
                style="width: 100%; min-width: 100%"
                disabled
            /></el-form-item>
          </el-col>
          <el-col :lg="8" :xl="6" v-if="isdisabled">
            <el-form-item label="处理日期：">
              <el-input
                v-model="complaintDTO.lastReplyTime"
                disabled
                style="width: 100%; min-width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传附件：">
              <div style="width: 100%">
                <div>
                  <div
                    class="pointer"
                    v-for="(item, index) in complaintDTO.complaintAttachPOS"
                    :key="index"
                    style="
                      padding: 0 5px;
                      display: flex;
                      align-items: center;
                      width: 100%;
                      justify-content: space-between;
                    "
                  >
                    <el-tooltip placement="top" :content="`${item.fileUrl1}`">
                      <div class="link pointers">
                        {{ item.fileUrl1 }}
                      </div>
                    </el-tooltip>
                    <ElIcon @click="closecomplaint(item)" v-if="!isdisabled" class="close"
                      ><Close
                    /></ElIcon>
                  </div>
                </div>
                <div style="width: 100%" v-if="!isdisabled">
                  <el-upload
                    :on-change="filechange"
                    :auto-upload="false"
                    v-model="fileList"
                    action="#"
                    multiple
                    accept=".doc,.docx,.pdf,.ppt,.pptx,.xls,.xlsx,.txt,.zip,.rar"
                  >
                    <el-button type="primary">上传文件</el-button>
                    <template #tip>
                      <div class="el-upload__tip" style="font-size: 14px">
                        最多上传5个文件,单个文件大小50MB内
                      </div>
                    </template>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="orignOrderList?.length > 0 || isdisabled">
            <el-form-item label="投诉订单信息：" :prop="complaintDTOprop('complaintDetailPOS')">
              <el-col :span="24">
                <el-button
                  type="primary"
                  class="mb-8px"
                  :disabled="isResetingOrign"
                  @click="addTableData"
                  v-if="!isdisabled"
                  >增加</el-button
                >
              </el-col>
              <el-col :span="24">
                <el-table
                  v-if="!isdisabled"
                  :data="complaintDTO.complaintDetailPOS"
                  border
                  style="width: 90%"
                  :cell-style="{ textAlign: 'center' }"
                  :header-cell-style="{ textAlign: 'center' }"
                >
                  <el-table-column label="订单编号">
                    <template #default="{ $index }">
                      <el-form-item
                        :prop="complaintDTOprop('complaintDetailPOS.' + $index + '.orderId')"
                        :rules="[{ required: true, message: '请选择订单编号', trigger: 'blur' }]"
                        :style="{ width: '100%' }"
                      >
                        <el-select
                          v-model="complaintDTO.complaintDetailPOS[$index].orderId"
                          @change="(val) => changeComplaintDetailIdOpt(val, $index)"
                          :disabled="isdisabled"
                          :filterable="true"
                          :style="{ width: '100%' }"
                        >
                          <el-option
                            v-for="item in complaintDetailPOSOptionMap[$index].idOpt"
                            :key="item.orderId"
                            :label="item.orderId"
                            :value="item.orderId"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="投诉产品名称" prop="productName">
                    <template #default="{ $index }">
                      <el-form-item
                        :prop="complaintDTOprop('complaintDetailPOS.' + $index + '.productName')"
                        :rules="[
                          { required: true, message: '请选择投诉产品名称', trigger: 'blur' }
                        ]"
                        :style="{ width: '100%' }"
                      >
                        <el-select
                          v-model="complaintDTO.complaintDetailPOS[$index].productName"
                          @change="(val) => changeComplaintDetailProOpt(val, $index)"
                          :disabled="isdisabled"
                          :filterable="true"
                          :style="{ width: '100%' }"
                        >
                          <el-option
                            v-for="(item, index) in complaintDetailPOSOptionMap[$index]
                              .productionOpt"
                            :key="index"
                            :label="item"
                            :value="item"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="投诉产品数量">
                    <template #default="{ $index }">
                      <el-form-item
                        :prop="
                          complaintDTOprop('complaintDetailPOS.' + $index + '.productQuantity')
                        "
                        :rules="[
                          { required: true, message: '请选择投诉产品名称', trigger: 'blur' }
                        ]"
                        :style="{ width: '100%' }"
                      >
                        <el-input
                          readonly
                          :disabled="isdisabled"
                          v-model="complaintDTO.complaintDetailPOS[$index].productQuantity"
                        />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template #default="{ row, $index }">
                      <el-button
                        v-if="!isdisabled"
                        link
                        type="danger"
                        @click="operate(row, $index)"
                        >{{ t('common.delete') }}</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
                <el-table
                  v-else
                  :data="complaintDTO.complaintDetailPOS"
                  border
                  style="width: 90%"
                  :cell-style="{ textAlign: 'center' }"
                  :header-cell-style="{ textAlign: 'center' }"
                >
                  <el-table-column label="订单编号" prop="orderId" />
                  <el-table-column label="投诉产品名称" prop="productName" />
                  <el-table-column label="投诉产品数量" prop="productQuantity" />
                </el-table>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="投诉详细内容：">
              <el-input
                v-model="complaintDTO.complaintContent"
                style="width: 90%"
                type="textarea"
                :autosize="{ minRows: 5, maxRows: 20 }"
                maxlength="1000"
                show-word-limit
                placeholder="请输入投诉内容"
                :disabled="isdisabled"
              />
            </el-form-item>
          </el-col>
          <!-- 审批的内容 -->
          <AuditDetails
            ref="auditDetailsRef"
            v-if="isdisabled"
            v-model:isaudit="isaudit"
            v-model:isdetail="isdetail"
            :processId="complaintDTO.processId"
          />
          <el-col :span="24" v-if="!isdisabled">
            <div style="display: flex; justify-content: center">
              <el-button @click="jumpRoute">{{ t('common.cancel') }}</el-button>
              <el-button type="primary" @click="addsavecomplaint(ruleFormRef, '0')">
                保 存
              </el-button>
              <el-button type="primary" @click="addsavecomplaint(ruleFormRef, '1')">
                提 交
              </el-button>
            </div>
          </el-col>
          <el-col :span="24" v-if="isaudit">
            <div style="display: flex; justify-content: center">
              <el-button @click="jumpRoute">{{ t('common.cancel') }}</el-button>
              <el-button type="primary" @click="sumbit(ruleFormRef)">提 交</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ComplaintDetail'
})

import dayjs from 'dayjs'

const { t } = useI18n()
import type { FormRules, FormInstance, UploadProps } from 'element-plus'
import { useTagsViewStore } from '@/store/modules/tagsView'
import {
  addComplaint,
  uploadComplaintAttach,
  editComplaint,
  findComplaintById,
  complaintSaleAudit,
  getcustomername,
  selectByOtherClient
} from '@/api/CustomerService/ComplaintManagement/index'

/**审核详情组件 */
import AuditDetails from '../Components/AuditDetails.vue'
const auditDetailsRef = ref()

import { ComplaintDetailNumber, cardTypeOptions, complainStatus } from '../utils/options'

const TagsViewStore = useTagsViewStore()
const route = useRoute()
const router = useRouter()

const orignNameOrderList: Ref<string[]> = ref([])

const ruleFormRef = ref<FormInstance>()

let complaintDTO = ref<any>({
  customerName: '',
  customerId: '',
  complaintTypeName: '',
  complaintTypeCode: '',
  productTypeName: '',
  productTypeCode: '',
  cardCode: '',
  contactPerson: '',
  complaintDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  time: dayjs().format('YYYY-MM-DD HH:mm:ss').slice(0, -3),
  complaintDetailPOS: [],
  complaintContent: '',
  complaintAttachPOS: [],
  flag: ''
})

const complaintDetailPOSOptionMap: Ref<any[]> = ref([])

// 切换订单编号的方法
const changeComplaintDetailIdOpt = async (_val, index) => {
  // 获取选中的订单编号的投诉产品列表（只会有一条）
  const res = await getOrderList({
    orderId: _val
  })
  if (res.code != '0') return
  // 由于只有一条数据，不需要考虑去重问题
  let arr: string[] = []
  res.data.forEach((_el) => {
    _el.orderDetailExt.saleName && arr.push(_el.orderDetailExt.saleName)
  })
  complaintDetailPOSOptionMap.value[index].productionOpt = [...arr]
  // 由于只有一条数据，因此唯一一条必定是选中的名称
  if (res.data.length > 0) {
    const objValue = res.data.find((_el) => _el.orderId === _val)
    // ========= end ===========
    complaintDTO.value.complaintDetailPOS[index].orderId = objValue.orderId
    complaintDTO.value.complaintDetailPOS[index].productId =
      objValue.orderDetailExt?.productionInfo?.productId
    complaintDTO.value.complaintDetailPOS[index].productName = objValue.orderDetailExt?.saleName
    complaintDTO.value.complaintDetailPOS[index].productQuantity = objValue.orderExt.diyOrderNumber
  }
}

const changeComplaintDetailProOpt = async (_val, index) => {
  if (!complaintDTO.value.complaintDetailPOS[index].orderId) {
    const res = await getOrderList({
      vname: _val
    })
    if (res.code != '0') return
    complaintDetailPOSOptionMap.value[index].idOpt = res.data
  }
}

const getOrderList = async (_params = {}) => {
  const res = await selectByOtherClient({
    customerId: complaintDTO.value.customerId,
    cardSort: complaintDTO.value.productTypeCode,
    ..._params
  })
  return Promise.resolve(res)
}

// 重置源数据
const isResetingOrign = ref(true)
const orignOrderList = ref([])

const resetOrignOrderList = async (isReset = true) => {
  // 因为每条订单信息添加的选项是互不影响的，每条信息的下拉框选项的初始数据都只考虑客户名称和产品类的筛选，因此需要保存并直接引用源数据

  // 数据量比较大，获取过程比较慢，会导致添加信息获取不到数据，因此添加一个标识要等数据回来后才能添加
  isResetingOrign.value = true
  // 清空已填的数据，如果是编辑的情况下，首次尽量就不要清空
  isReset && (complaintDTO.value.complaintDetailPOS = [])
  complaintDetailPOSOptionMap.value = []
  const res = await getOrderList()
  if (res.code != '0') return
  isResetingOrign.value = false
  orignOrderList.value = res.data
  // let nameMap: any = {}
  // 产品名称需要去重
  orignOrderList.value.forEach((el: any) => {
    // if (!nameMap[el.orderDetailExt.productionInfo.productionNameC]) {
    //   nameMap[el.orderDetailExt.productionInfo.productionNameC] = el
    // }
    if (
      el.orderDetailExt.saleName &&
      orignNameOrderList.value.indexOf(el.orderDetailExt.saleName) === -1
    ) {
      orignNameOrderList.value.push(el.orderDetailExt.saleName)
    }
  })
  if (!isReset) {
    // 编辑的情况下，首次进来所有的数据都能访问
    nextTick(() => {
      complaintDTO.value.complaintDetailPOS.forEach((el) => {
        complaintDetailPOSOptionMap.value.push({
          idOpt: orignOrderList.value,
          // productionOpt: orignNameOrderList.value
          productionOpt: [el.productName]
        })
      })
    })
    console.log('complaintDetailPOSOptionMap.value', complaintDetailPOSOptionMap.value)
  }
}

// ===================== 检测如果客户名称和产品类有变化时，投诉订单信息的选项需要清空并且更新 ============================

// watch(
//   () => complaintDTO.value.customerId,
//   (_val) => {
//     // 获取信息时不需要更新
//     !isdisabled && resetOrignOrderList()
//   },
//   {
//     deep: true
//   }
// )

watch(
  () => complaintDTO.value.complaintTypeCode,
  (_val) => {
    // 投诉类型如果时产品类才能选择产品类，因此如果不是产品类需要清空相关查询条件
    if (_val !== '1') {
      complaintDTO.value.productTypeCode = ''
      complaintDTO.value.productTypeName = ''
    }
  }
)

// watch(
//   () => complaintDTO.value.productTypeCode,
//   (_val) => {
//     !isdisabled && resetOrignOrderList()
//   },
//   {
//     deep: true
//   }
// )

// ===================== end ============================

// 电话规则
const validatepass = (_rule: any, value: any, callback: any) => {
  const phone = /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/
  if (!value) {
    callback(new Error('请填写电话号码'))
  } else {
    const phoneArray = value.split('/')
    const phoneSet = new Set(phoneArray)
    if (phoneArray.length !== phoneSet.size) {
      callback(new Error('电话号码不能重复'))
    } else {
      phoneArray.forEach((e: string) => {
        if (!phone.test(e)) {
          callback(new Error('请填写正确的电话号码'))
        }
      })
    }
    callback()
  }
}
/**校验规则 */
const validate = (_rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请填写最终处理结果'))
  } else {
    callback()
  }
}
/**校验规则 */
const flagvalidate = (_rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请选择审核结果'))
  } else {
    callback()
  }
}
// 校验规则
const rules = reactive<FormRules>({
  customerId: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
  complaintTypeCode: [{ required: true, message: '请选择投诉类型', trigger: 'change' }],
  productTypeCode: [{ required: true, message: '请选择产品类', trigger: 'change' }],
  contactPerson: [{ required: true, message: '请填写联系人', trigger: 'blur' }],
  contactPhone: [
    {
      required: true,
      validator: validatepass,
      trigger: 'blur'
    }
  ],
  time: [{ required: true, message: '请填写投诉日期', trigger: 'blur' }],
  complaintDetailPOS: [{ required: true, message: '请选择投诉订单信息', trigger: 'change' }],
  lastReplyContent: [
    {
      required: true,
      validator: validate,
      trigger: 'blur'
    }
  ],
  flag: [
    {
      required: true,
      validator: flagvalidate,
      trigger: 'change'
    }
  ]
})

/**审核处理 */
const sumbit = (ruleFormRef: FormInstance | undefined) => {
  try {
    if (!ruleFormRef) return
    ruleFormRef.validate(async (valid) => {
      if (!valid) return ElMessage.error('请按提示信息完善输入输入项！')

      //判断审核是否为销售还是品控
      complaintReviews(auditDetailsRef.value)
    })
  } catch {}
}
// /**品控部门审核 */
// const complaintQualityAudits = async ({ complaintDTO, fileList }) => {
//   const complaintId = route.query.complaintId
//   const complain = { complaintId, ...complaintDTO }
//   let complaintAttachPOS = []
//   if (fileList && fileList.length > 0) {
//     const fileLists = fileList
//     const datas = await uploadComplaintAttachs(fileLists, 4)
//     if (datas?.code != '0') return
//     complaintAttachPOS = datas.data
//   }
//   Object.assign(complain, complaintAttachPOS)
//   const { code } = await complaintQualityAudit(complain)
//   if (code != '0') return
//   ElMessage.success('审批成功')
//   jumpRoute()
// }
/**销售审核处理 */
const complaintReviews = async ({ complaintDTO, fileList }) => {
  const complaintId = route.query.complaintId
  const complain = { complaintId, ...complaintDTO }
  let complaintAttachPOS = []
  if (fileList && fileList.length > 0) {
    const fileLists = fileList
    const datas = await uploadComplaintAttachs(fileLists, 3)
    if (datas?.code != '0') return
    complaintAttachPOS = datas.data
  }
  Object.assign(complain, complaintAttachPOS)
  const { code } = await complaintSaleAudit(complain)
  if (code != '0') return
  ElMessage.success('审批成功')
  jumpRoute()
}

// 删除
const operate = (_row, $index) => {
  complaintDTO.value.complaintDetailPOS.splice($index, 1)
  // 删除订单数据的同时，要将对应的数据清空，否则会出现删除后别的投诉订单对上被删投诉订单数据的情况
  complaintDetailPOSOptionMap.value.splice($index, 1)
}

onMounted(() => {
  // 0是新增 2是编辑 1是详情
  if (route.query.number === '1' || route.query.number === '3' || route.query.number === '2') {
    findComplaintByIds()
  }
  if (route.query.number === '2' || route.query.number === '0') {
    getCustomerList()
  }
  watchEffect(() => {
    if (!auditDetailsRef.value) return
    complaintDTO.value.lastReplyContent = auditDetailsRef.value.complaintDTO.lastReplyContent
    complaintDTO.value.flag = auditDetailsRef.value.complaintDTO.flag
  })
})
// 添加
const addTableData = () => {
  let obj = {
    complaintId: '',
    detailId: '',
    orderId: '',
    productId: '',
    productName: '',
    productQuantity: ''
  }
  complaintDTO.value.complaintDetailPOS.push(obj)
  // 添加订单数据的同时，需要记录每个订单数据对应的下拉选项数据
  complaintDetailPOSOptionMap.value.push({
    idOpt: orignOrderList.value,
    productionOpt: orignNameOrderList.value
  })
}
// 获取客户名称
let customerList = ref({})
const getCustomerList = async () => {
  try {
    const { code, data } = await getcustomername('')
    if (code != 0) return
    customerList.value = data
  } catch (error) {}
}

// 获取对应的客户名称
const customerchange = (val) => {
  if (!val) return
  complaintDTO.value.customerName = customerList.value.find(
    (item) => item.customerId === val
  ).customerName
  resetOrignOrderList()
}

// 获取投诉类型名称
const getcomplaintName = (val) => {
  if (!val) return (complaintDTO.value.complaintTypeName = '')
  if (val === '1') {
    complaintDTO.value.complaintTypeName = '产品类'
  } else {
    complaintDTO.value.complaintTypeName = '服务类'
  }
}

//是否不可编辑
let isdisabled = computed(() => route.query.number == '1' || route.query.number == '3')

// 判断是否详情

let isdetail = computed(() => route.query.number === '1')

// 判断是否审核
let isaudit = computed(() => route.query.number === '3')

// 取消跳转路由
const jumpRoute = () => {
  router.push({
    name: 'ComplaintManagement'
  })
  const index = TagsViewStore.visitedViews.findIndex((item) => {
    return item.name === route.name
  })
  TagsViewStore.visitedViews.splice(index, 1)
}
// 保存 / 提交
const addsavecomplaint = async (formEl: FormInstance | undefined, flag: string) => {
  console.log('formEl', formEl)
  console.log('complaintDTO', complaintDTO.value)
  if (!formEl) return
  await formEl.validate(async (valid) => {
    if (!valid) return

    try {
      const file = await uploadComplaintAttachs(fileList.value, 1)
      // 如果上传错误了就抛出
      if (file?.code != 0) return
      fileList.value = []
      complaintDTO.value.complaintAttachPOS = complaintDTO.value.complaintAttachPOS.concat(
        file?.data
      )
      complaintDTO.value.flag = flag
      if (route.query.number === '0') {
        const adddata = await add()
        if (adddata?.code != '0') return
        TagsViewStores()
        if (complaintDTO.value.flag == '1') {
          ElMessage.success('保存成功')
        } else {
          ElMessage.success(adddata?.msg)
        }
      } else {
        edit(flag)
      }
    } catch (error) {
      console.log(error)
    } finally {
    }
  })
}
// 新增：保存/提交
const add = async () => {
  try {
    const { code, data, msg } = await addComplaint(complaintDTO.value)
    return { code, data, msg }
  } catch (error) {}
}
// 编辑：保存/提交
const edit = async (flag) => {
  try {
    // if (complaintDTO.value.complaintAttachPOS.length > 0) {
    //   complaintDTO.value.complaintAttachPOS.forEach((item) => {
    //     delete item.attachId
    //   })
    // }
    const { code, data } = await editComplaint(complaintDTO.value)
    if (code != '0') return
    TagsViewStores()
    if (complaintDTO.value.flag == '1') return ElMessage.success('保存成功')
    return ElMessage.success(data)
  } catch (error) {
  } finally {
  }
}
// 关闭标签页
const TagsViewStores = () => {
  router.push({
    name: 'ComplaintManagement',
    query: {
      name: '1'
    }
  })
  const index = TagsViewStore.visitedViews.findIndex((item) => {
    return item.name === route.name
  })
  TagsViewStore.visitedViews.splice(index, 1)
}
/**销售部门端新建上传为数字1 */
/**销售部门端审核上传为数字3 */
/**品控部门端审核上传为数字4 */
// 上传附件
const uploadComplaintAttachs = async (fileList, falgs) => {
  try {
    if (fileList.length > 0) {
      const formData = new FormData()
      fileList.forEach((item: any) => {
        formData.append('file', item.raw)
      })
      let flag: any = falgs
      formData.append('flag', flag)
      const { code, data } = await uploadComplaintAttach(formData)
      return { code, data }
    } else {
      let code = 0
      let data = []
      return { code, data }
    }
  } catch (error) {}
}
// 校验附件
let fileList = ref([])
const filechange: UploadProps['onChange'] = async (uploadFile: any, iploadFiles: any) => {
  if (!uploadFile) return
  if (uploadFile.size / 1024 / 1024 > 50) {
    const currIdx = iploadFiles.indexOf(uploadFile)
    iploadFiles.splice(currIdx, 1)
    ElMessage.error('单个上传文件大小不能超过50MB')
  }
  if (iploadFiles.length + complaintDTO.value.complaintAttachPOS.length > 5) {
    const currIdx = iploadFiles.indexOf(uploadFile)
    iploadFiles.splice(currIdx, 1)
    ElMessage.error('文件最多上传5个')
  }
  fileList.value = iploadFiles
}

// 获取对应的产品类的名称
const productchange = (val) => {
  if (!val) return
  complaintDTO.value.productTypeName = cardTypeOptions.find((item) => item.value === val)?.label
  resetOrignOrderList()
}

const unGetOrigin = ref(false)

// 查看详情
const findComplaintByIds = async () => {
  try {
    unGetOrigin.value = true
    const complaintId = route.query.complaintId
    const { data, code } = await findComplaintById(complaintId)
    if (code != '0') return
    console.log('data666999', data, 'data')
    complaintDTO.value = data
    complaintDTO.value.time = complaintDTO.value.complaintDate.slice(0, -3)
    resetOrignOrderList(false)
    /**有审核ID后再改 */
    // complaintDTO.value.processId = '723eef44-68f4-11ee-b9e5-5ae457bdf19f'
  } catch (error) {
  } finally {
    unGetOrigin.value = false
  }
}
// 检验规则
const complaintDTOprop = (propName) => {
  if (isdisabled.value) return ''
  return propName
}

// 删除上传的文件
const closecomplaint = ({ attachId }) => {
  if (!complaintDTO.value.complaintAttachPOS && complaintDTO.value.complaintAttachPOS.length < 0)
    return
  let Index = complaintDTO.value.complaintAttachPOS.findIndex((item) => item.attachId === attachId)
  complaintDTO.value.complaintAttachPOS.splice(Index, 1)
}
</script>
<style scoped lang="less">
.box {
  .myDisplay {
    display: flex;
    justify-content: space-between;
  }
  .label {
    font-weight: 700;
    font-style: normal;
    font-size: 20px;
    color: #000000;
    font-family: 'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  }
  .text {
    color: red;
    font-weight: 400;
    font-style: normal;
    font-size: 14px;
    margin-left: 10px;
  }
}
.pointer:hover {
  background-color: #f2f2f2;
}
.pointer:hover .close {
  opacity: 1;
  color: black;
}
.close {
  cursor: pointer;
  opacity: 0;
}
.opacity {
  font-size: 14px;
  opacity: 1;
}
.link {
  width: 234px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.pointers {
  cursor: pointer;
}
:deep(.el-textarea__inner) {
  resize: none;
}
</style>
