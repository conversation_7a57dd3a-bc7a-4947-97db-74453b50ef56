/** *指派 */
<template>
  <el-form :rules="rules" :model="list" ref="listRef" label-position="left" label-width="150px">
    <el-form-item label="请选择指派部门：" prop="lastResponseDepartId">
      <el-tree-select
        style="width: 234px"
        placeholder="请选择指派部门"
        clearable
        v-model="list.lastResponseDepartId"
        filterable
        :data="treedata"
        :props="prop"
        @change="treechange"
      />
    </el-form-item>
    <el-form-item label="指派人员：">
      <el-select
        clearable
        filterable
        style="width: 234px"
        placeholder="请选择指派人员"
        v-model="list.lastResponseUserId"
        @change="processchange"
      >
        <el-option
          v-for="(item, index) in treedatachildren"
          :key="index"
          :value="item.id"
          :label="item.name"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Assign'
})

import { listallsimple, deptUserPage } from '@/api/CustomerService/ComplaintManagement/index'
import { nodechildren } from '../utils/loadNode'
import type { FormRules, FormInstance } from 'element-plus'
const prop = {
  label: 'name',
  value: 'id'
}
const listRef = ref<FormInstance>()
const data = reactive<any>({
  list: {
    lastResponseDepartName: '',
    lastResponseDepartId: '',
    lastResponseUserId: '',
    lastResponseUserName: ''
  },
  treedata: [],
  treedatachildren: []
})
let { list, treedata, treedatachildren } = toRefs(data)
// 校验规则
const rules = reactive<FormRules>({
  lastResponseDepartId: [{ required: true, message: '请选择部门', trigger: 'change' }]
  // lastResponseUserId: [{ required: true, message: '请选择人员', trigger: 'change' }]
})
// 获取部门
const listallsimples = async () => {
  try {
    const { data, code } = await listallsimple()
    if (code == 0) {
      // let tree: any = []
      // tree = loadNode(nodechildren(data))
      treedata.value = data
    }
  } catch (error) {}
}
onMounted(() => {
  listallsimples()
})
// 获取人员
const treechange = async (val) => {
  console.log(val, 'valdata')
  if (!val) {
    list.value.lastResponseUserName = ''
    list.value.lastResponseUserId = ''
    list.value.lastResponseDepartName = ''
    treedatachildren.value = []
    return
  }
  list.value.lastResponseUserId = ''
  list.value.lastResponseDepartName = treedata.value.find((item) => item.id === val).name
  try {
    const { code, data } = await deptUserPage(val)
    if (code != '0') return
    treedatachildren.value = nodechildren(data)
  } catch (error) {}
}
// 校验
const validate = () => {
  return new Promise((resolve) => {
    if (!listRef.value) return
    listRef.value.validate((valid) => {
      if (valid) {
        resolve(true)
      } else {
        resolve(false)
      }
    })
  })
}
// 获取人员名称
const processchange = (val) => {
  if (!val) {
    list.value.lastResponseUserName = ''
    return
  }
  list.value.lastResponseUserName = treedatachildren.value.find((item) => val == item.id).name
}
defineExpose({
  validate,
  list
})
</script>

<style scoped lang="less"></style>
