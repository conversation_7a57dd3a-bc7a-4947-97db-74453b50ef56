<template>
  <div class="informtion">
    <div class="text">
      <span class="text">处理信息</span>
    </div>
    <el-table
      :data="ProcessInformation"
      border
      style="width: 1200px"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column label="处理人" prop="assigneeUser.nickname" />
      <el-table-column label="处理部门" prop="assigneeUser.deptName" />
      <el-table-column label="处理时间" prop="endTime" />
      <el-table-column label="处理备注" prop="reason" />
    </el-table>
  </div>
  <div class="informtion">
    <div class="text">
      <span>审核信息</span>
      <div v-if="isaudit" class="audit">
        <el-button type="primary" link @click="openapproval">查看审核流程</el-button>
      </div>
    </div>
    <el-table
      :data="AuditInformation"
      border
      style="width: 1500px"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column label="审核结果">
        <template #default="{ row }">
          <div>{{ row.status === 2 ? '' : row.status === 0 ? '不通过' : '通过' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="审核人员" prop="assigneeUser.nickname" />
      <el-table-column label="审核部门" prop="assigneeUser.deptName" />
      <el-table-column label="审核时间" prop="endTime" />
      <el-table-column label="审核备注" prop="reason" />
    </el-table>
  </div>
  <el-col :span="24" v-if="isaudit">
    <el-form-item label="审核结果：" prop="flag">
      <el-radio-group v-model="complaintDTO.flag">
        <el-radio label="1">通过</el-radio>
        <el-radio label="0">驳回</el-radio>
      </el-radio-group>
    </el-form-item>
  </el-col>
  <el-col :span="24" v-if="isaudit">
    <el-form-item label="上传附件：">
      <div style="width: 100%" v-show="!isdetail">
        <el-upload
          :auto-upload="false"
          v-model="fileList"
          :on-change="filechange"
          action="#"
          multiple
          :limit="6"
          accept=".doc,.docx,.pdf,.ppt,.pptx,.xls,.xlsx,.txt,.zip,.rar"
        >
          <el-button type="primary">上传文件</el-button>
          <template #tip>
            <div class="el-upload__tip" style="font-size: 14px">
              最多上传5个文件,单个文件大小50MB内
            </div>
          </template>
        </el-upload>
      </div>
    </el-form-item>
  </el-col>
  <el-form-item label="最终处理结果" v-if="isaudit" prop="lastReplyContent">
    <el-radio-group>
      <el-input
        style="min-width: 1000px"
        type="textarea"
        :autosize="{ minRows: 5, maxRows: 20 }"
        maxlength="1000"
        show-word-limit
        resize="none"
        v-model="complaintDTO.lastReplyContent"
        placeholder="请输入最终处理结果"
      />
    </el-radio-group>
  </el-form-item>
  <el-dialog v-model="dialogapproval" title="查看审批流程" width="800px">
    <MyProcessViewer
      key="designer"
      prefix="flowable"
      :activityData="processInstanceActivityList"
      :processInstanceData="processInstance"
      :value="processDefinitionBpmnXml"
      :taskData="processInstanceTaskList"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogapproval = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { MyProcessViewer } from '@/components/BpmnProcessDesigner/package'
import { getProcessInstance } from '@/api/bpm/processInstance/index'
import type { UploadProps } from 'element-plus'
let props = defineProps({
  processId: {
    type: String,
    default: ''
  }
})
let isaudit = defineModel('isaudit', { type: Boolean })
let isdetail = defineModel('isdetail', { type: Boolean })

const data = reactive<any>({
  complaintDTO: {
    lastReplyContent: '',
    flag: ''
  },
  processDefinitionBpmnXml: '',
  fileList: [],
  AuditInformation: [],
  ProcessInformation: [],
  dialogapproval: false,
  processInstanceActivityList: [],
  processInstance: {},
  processInstanceTaskList: []
})
let {
  AuditInformation,
  processDefinitionBpmnXml,
  processInstanceActivityList,
  processInstance,
  ProcessInformation,
  dialogapproval,
  complaintDTO,
  fileList,
  processInstanceTaskList
} = toRefs(data)
/**审核信息/处理信息过滤 */
const AuditFilte = (data, process, Audit) => {
  let arr: any = []
  arr = data.filter((item) => item.definitionKey === process)
  if (Audit) {
    arr = arr.concat(data.filter((item) => item.definitionKey === Audit))
  }
  // 按照时间排序
  arr.sort((a: any, b: any) => {
    const dateA = new Date(a.endTime)
    const dateB = new Date(b.endTime)
    if (dateA < dateB) return -1
    if (dateA > dateB) return 1
    return
  })
  return arr
}
/**审核信息/处理信息 */
const getProcessInstanceDetails = async (processId) => {
  try {
    let process = 'deptLeaderApprovalStatus'
    const { code, data } = await getProcessInstance(processId)
    if (code != '0') return
    processDefinitionBpmnXml.value = data.processDefinitionBpmnXml
    processInstanceActivityList.value = data.processInstanceActivityList
    processInstance.value = data.processInstance
    processInstanceTaskList.value = data.processInstanceTaskList
    /**审核信息 */
    AuditInformation.value = AuditFilte(
      data.processInstanceTaskList,
      'qualityManageApproval',
      'deptLeaderApproval'
    )
    ProcessInformation.value = AuditFilte(data.processInstanceTaskList, 'deptEmployeeHandle', '')
    findcorees(process, data)
  } catch {}
}
const statusfind = (data, processTaskId) => {
  let process = ''
  let arr = Object.keys(data.processInstance.variables).includes(processTaskId)
  if (arr) {
    process = data.processInstance.variables[processTaskId]
    return process
  }
  return 2
}
const findcorees = (process, data) => {
  AuditInformation.value.forEach((el) => {
    el.processTaskId = process + '_' + el.taskId
    el.status = statusfind(data, el.processTaskId)
  })
}
watchEffect(() => {
  let processId = props.processId
  if (!processId) return
  getProcessInstanceDetails(processId)
})
const openapproval = () => {
  dialogapproval.value = true
}
// 校验附件
const filechange: UploadProps['onChange'] = async (uploadFile: any, iploadFiles: any) => {
  if (!uploadFile) return
  if (uploadFile.size / 1024 / 1024 > 50) {
    const currIdx = iploadFiles.indexOf(uploadFile)
    iploadFiles.splice(currIdx, 1)
    ElMessage.error('单个上传文件大小不能超过50MB')
  }
  if (iploadFiles.length > 5) {
    const currIdx = iploadFiles.indexOf(uploadFile)
    iploadFiles.splice(currIdx, 1)
    ElMessage.error('文件最多上传5个')
  }
  fileList.value = iploadFiles
}
defineExpose({
  complaintDTO,
  fileList,
  processInstanceTaskList,
  processInstance
})
</script>

<style scoped lang="less">
.informtion {
  width: 100%;
  margin: 20px 0 80px;
  .text {
    font-weight: 700;
    font-style: normal;
    font-size: 20px;
    color: #000000;
    font-family: 'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
    margin-bottom: 10px;
  }
  .audit {
    font-size: 12px;
  }
}
</style>
