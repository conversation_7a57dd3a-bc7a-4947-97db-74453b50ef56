<template>
  <!-- 投诉管理销售部-->
  <ContentWrap>
    <el-form :model="complaintPageDTO" ref="formRef" label-width="110px" label-position="right">
      <el-row :gutter="20">
        <el-col :lg="8" :xl="6">
          <el-form-item label="客户名称：" prop="customerId">
            <el-select
              style="width: 100%; min-width: 100%"
              placeholder="请选择客户名称"
              v-model="complaintPageDTO.customerId"
              :teleported="false"
              :fit-input-width="true"
              filterable
              clearable
              placement="bottom"
            >
              <el-option
                v-for="(item, index) in customerList"
                :key="index"
                :value="item.customerId"
                :label="item.customerName"
                :title="item.customerName"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :xl="6">
          <el-form-item label="投诉类型：" prop="complaintTypeCode">
            <el-select
              style="width: 100%; min-width: 100%"
              placeholder="请选择投诉类型"
              clearable
              v-model="complaintPageDTO.complaintTypeCode"
            >
              <el-option label="产品类" value="1" />
              <el-option label="服务类" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="8" :xl="6">
          <el-form-item label="订单编号：" prop="orderId">
            <el-input
              v-model="complaintPageDTO.orderId"
              clearable
              placeholder="请输入订单编号"
              style="width: 100%; min-width: 100%"
            />
            <!-- <el-autocomplete
          v-model="complaintPageDTO.orderId"
          :fetch-suggestions="querySearch"
          style="min-width: 234px"
          clearable
          class="inline-input w-50"
          placeholder="请输入订单编号"
          :teleported="false"
          :fit-input-width="true"
          value-key="orderId"
        >
          <template #default="{ item }">
            <span :title="item.orderId">{{ item.orderId }}</span>
          </template>
        </el-autocomplete> -->
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="卡号：" prop="cardCode">
            <el-input
              v-model="complaintPageDTO.cardCode"
              clearable
              placeholder="请输入卡号"
              style="width: 100%; min-width: 100%"
            />
            <!-- <el-autocomplete
          v-model="complaintPageDTO.cardCode"
          :fetch-suggestions="querycardCode"
          style="min-width: 234px"
          clearable
          class="inline-input w-50"
          placeholder="请输入卡号"
          :teleported="false"
          :fit-input-width="true"
          value-key="cardCode"
        >
          <template #default="{ item }">
            <span :title="item.cardCode">{{ item.cardCode }}</span>
          </template>
        </el-autocomplete> -->
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="投诉产品名称： " prop="productName">
            <el-input
              v-model="complaintPageDTO.productName"
              clearable
              placeholder="请输入投诉产品名称"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="投诉状态：" prop="statusCode">
            <el-select
              v-model="complaintPageDTO.statusCode"
              style="width: 100%; min-width: 100%"
              placeholder="请选择投诉状态"
              clearable
            >
              <el-option
                v-for="(item, index) in complaintOption"
                :key="index"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="联系人：" prop="contactPerson">
            <el-input
              v-model="complaintPageDTO.contactPerson"
              clearable
              placeholder="请输入联系人"
              style="width: 100%; min-width: 100%"
            />
            <!-- <el-autocomplete
          v-model="complaintPageDTO.contactPerson"
          :fetch-suggestions="queryPerson"
          style="min-width: 234px"
          clearable
          class="inline-input w-50"
          placeholder="请输入联系人"
          :teleported="false"
          :fit-input-width="true"
          value-key="nickname"
        >
          <template #default="{ item }">
            <span :title="item.nickname">{{ item.nickname }}</span>
          </template>
        </el-autocomplete> -->
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="处理人：" prop="processUserName">
            <el-input
              v-model="complaintPageDTO.processUserName"
              clearable
              placeholder="请输入处理人"
              style="width: 100%; min-width: 100%"
            />
            <!-- <el-autocomplete
          v-model="complaintPageDTO.processUserName"
          :fetch-suggestions="queryPerson"
          style="min-width: 234px"
          clearable
          class="inline-input w-50"
          placeholder="请输入处理人"
          :teleported="false"
          :fit-input-width="true"
          value-key="nickname"
        >
          <template #default="{ item }">
            <span :title="item.nickname">{{ item.nickname }}</span>
          </template>
        </el-autocomplete> -->
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="责任部门：" prop="lastResponseDepartId">
            <el-tree-select
              @change="changeResponsibleDeptList"
              style="width: 100%; min-width: 100%"
              clearable
              placeholder="请选择责任部门"
              v-model="complaintPageDTO.lastResponseDepartId"
              filterable
              :data="responsibleDeptList"
              :props="deptProp"
              :fit-input-width="true"
            />
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="责任人：" prop="lastResponseUserId">
            <el-select
              style="width: 100%; min-width: 100%"
              clearable
              filterable
              placeholder="请选择责任人"
              v-model="complaintPageDTO.lastResponseUserId"
              @focus="getAllPersonList"
              :fit-input-width="true"
            >
              <el-option
                v-for="(item, index) in responsiblePersonList"
                :key="index"
                :value="item.id"
                :label="item.nickname"
                :title="item.nickname"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="投诉日期：">
            <el-date-picker
              v-model="complaintTime"
              clearable
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col v-show="showMore" :lg="8" :xl="6">
          <el-form-item label="处理日期：">
            <el-date-picker
              v-model="replyTime"
              clearable
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :xl="6">
          <el-button type="primary" @click="findComplaintByPages()">{{
            t('common.query')
          }}</el-button>
          <el-button type="warning" @click="resettingcomplaint(formRef)">{{
            t('common.reset')
          }}</el-button>
          <ElLink
            type="primary"
            :underline="false"
            style="margin-left: 20px"
            @click="showMore = !showMore"
          >
            {{ !showMore ? '展开' : '收起' }}
            <ElIcon>
              <ArrowUpBold v-show="showMore" />
              <ArrowDownBold v-show="!showMore" />
            </ElIcon>
          </ElLink>
        </el-col>
      </el-row>
    </el-form>
    <div style="display: flex; justify-content: flex-end; margin: 20px 0">
      <el-button type="primary" @click="goComplaintDetail('0', { complaintId: '' })"
        >新建投诉</el-button
      >
    </div>
    <el-table
      v-loading="tabelLoading"
      v-horizontal-scroll
      :data="tableData"
      :header-cell-style="{
        textAlign: 'center'
      }"
      :cell-style="{ textAlign: 'center' }"
      style="height: 60vh"
    >
      <el-table-column label="客户名称" min-width="150" prop="complaintPO.customerName" />
      <el-table-column label="投诉类型" min-width="120" prop="complaintPO.complaintTypeName" />
      <el-table-column label="订单编号" min-width="120" prop="orderId" />
      <el-table-column label="卡号" min-width="120" prop="complaintPO.cardCode" />
      <el-table-column label="投诉产品名称" min-width="120" prop="productName" />
      <el-table-column label="投诉状态" min-width="120" prop="complaintPO.statusName" />
      <el-table-column label="投诉日期" min-width="180" prop="complaintPO.complaintDate" />
      <el-table-column label="处理日期" min-width="120" prop="complaintPO.lastReplyTime" />
      <el-table-column label="评价结果" min-width="145">
        <template #default="{ row }">
          <el-rate disabled v-model="row.complaintPO.evaluateScore" />
        </template>
      </el-table-column>
      <el-table-column label="联系人" min-width="120" prop="complaintPO.contactPerson" />
      <el-table-column label="联系电话" min-width="120" prop="complaintPO.contactPhone" />
      <el-table-column label="责任部门" min-width="120" prop="complaintPO.lastResponseDepartName" />
      <el-table-column label="责任人" min-width="120" prop="complaintPO.lastResponseUserName" />
      <el-table-column label="操作" fixed="right" min-width="180">
        <template #default="{ row }">
          <!-- /** * 1.草稿 2.待处理 3.已处理 4.待评价 5.已评价 */ -->
          <el-button link size="small" type="primary" @click="goComplaintDetail('1', row)">
            详情
          </el-button>
          <el-button
            v-if="row.complaintPO.statusCode === '1'"
            link
            size="small"
            type="primary"
            @click="goComplaintDetail('2', row)"
          >
            {{ t('common.edit') }}
          </el-button>
          <el-button
            v-if="row.complaintPO.statusCode === '3'"
            link
            size="small"
            type="primary"
            @click="goComplaintDetail('3', row)"
          >
            审核
          </el-button>
          <el-button
            v-if="row.complaintPO.statusCode === '6'"
            link
            size="small"
            type="primary"
            @click="openAssignDialog(row)"
          >
            指派
          </el-button>
          <el-button
            v-if="row.complaintPO.statusCode === '1'"
            link
            size="small"
            type="danger"
            @click="openDeleteDialog(row)"
          >
            {{ t('common.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:page="pageNo"
      v-model:limit="pageSize"
      :total="total"
      @pagination="findComplaintByPages"
    />
    <!-- 指派 -->
    <el-dialog v-model="ifOpenAssignDialog" title="指派信息" width="500px">
      <Assign v-if="ifOpenAssignDialog" ref="listRef" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ifOpenAssignDialog = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="assingDepartment">{{ t('common.ok') }}</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 删除 -->
    <el-dialog v-model="ifOpenDeleteDialog" title="投诉信息" width="450px">
      <span>是否删除此投诉信息？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="ifOpenDeleteDialog = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="deleteList">{{ t('common.ok') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ComplaintManagement'
})

const { t } = useI18n()
import { ArrowUpBold, ArrowDownBold } from '@element-plus/icons-vue'
import Assign from '@/views/CustomerService/ComplaintManagement/Components/Assign.vue'
import {
  listallsimple,
  findComplaintManageByPage,
  findComplaintDetailByList,
  findComplaintByList,
  deptUserPage,
  complaintAssign,
  deleteComplaintById,
  userList,
  getcustomername
} from '@/api/CustomerService/ComplaintManagement/index'
import type { FormInstance } from 'element-plus'

import { complaintOption } from './utils/options'

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()

route.meta.title = '投诉管理列表'

//分页
let tableData = ref([])
let pageNo = ref(1)
let pageSize = ref(10)
let total = ref(0)

let complaintPageDTO = ref({
  customerId: '',
  complaintTypeCode: '',
  orderId: '',
  cardCode: '',
  productName: '',
  statusCode: '',
  complaintStartDate: '',
  complaintEndDate: '',
  replyEndDate: '',
  replyStartDate: '',
  lastResponseDepartId: '',
  lastResponseUserId: '',
  contactPerson: '',
  processUserName: ''
})

let complaintTime = ref<any>([]) //投诉时间
let replyTime = ref<any>([]) //处理时间

const tabelLoading = ref(false)
const showMore = ref(true)

const findComplaintByPages = async () => {
  try {
    tabelLoading.value = true

    const { code, data } = await findComplaintManageByPage({
      ...complaintPageDTO.value,
      complaintStartDate:
        complaintTime.value.length > 0 ? `${complaintTime.value[0]} 00:00:00` : '',
      complaintEndDate: complaintTime.value.length > 0 ? `${complaintTime.value[1]} 23:59:59` : '',
      replyStartDate: replyTime.value.length > 0 ? `${replyTime.value[0]} 00:00:00` : '',
      replyEndDate: replyTime.value.length > 0 ? `${replyTime.value[1]} 23:59:59` : '',
      pageNo: pageNo.value,
      pageSize: pageSize.value
    })
    if (code != 0) return
    tableData.value = data.list
    total.value = data.total
    return { code }
  } catch (error) {
    console.log(error, 'error')
  } finally {
    tabelLoading.value = false
  }
}
onMounted(() => {
  findComplaintByPages()
})

/**
 * 投诉审核/详情/新建
 */
const goComplaintDetail = (number: string, { complaintId }) => {
  router.push({
    name: 'ComplaintDetail',
    query: {
      number,
      complaintId
    }
  })
}

/**
 * 指派dialog
 */
const listRef = ref()

let complaintIds = ref()
let ifOpenAssignDialog = ref(false)
const openAssignDialog = ({ complaintId }) => {
  complaintIds.value = complaintId
  ifOpenAssignDialog.value = true
}

// 指派部门
const assingDepartment = async () => {
  try {
    if (!listRef.value) return
    let valid = await listRef.value?.validate()
    let lists = listRef.value?.list
    if (valid) {
      const { code } = await complaintAssign({
        complaintId: complaintIds.value,
        ...lists
      })
      if (code != 0) return
      ElMessage.success('指派成功')
      ifOpenAssignDialog.value = false
      findComplaintByPages()
    } else {
      ElMessage.error('请选择必选项')
    }
  } catch (error) {}
}

/**删除dialog */
let deleteId = ref('')
let ifOpenDeleteDialog = ref(false)
const openDeleteDialog = ({ complaintId }) => {
  deleteId.value = complaintId
  ifOpenDeleteDialog.value = true
}
// 删除
const deleteList = async () => {
  const { code } = await deleteComplaintById(deleteId.value)
  if (code != '0') return ElMessage.error('删除失败')
  ifOpenDeleteDialog.value = false
  ElMessage.success('删除成功')
  findComplaintByPages()
}

//客户名称
let customerList = ref<any>([])

const getcustomer = async () => {
  try {
    const { code, data } = await getcustomername('')
    if (code != 0) return
    customerList.value = data
  } catch (error) {}
}
onMounted(() => {
  getcustomer()
})

// 重置
const resettingcomplaint = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  complaintTime.value = []
  replyTime.value = []
  complaintPageDTO.value.complaintStartDate = ''
  complaintPageDTO.value.complaintEndDate = ''
  complaintPageDTO.value.replyEndDate = ''
  complaintPageDTO.value.replyStartDate = ''
  findComplaintByPages()
}
// 订单/投诉产品名称模糊搜索
const remoteMethod = async (query: string) => {
  try {
    const date = {
      orderId: query
    }
    const { code, data } = await findComplaintDetailByList(date)
    if (code != '0') return
    return data
  } catch {}
}
const querySearch = async (queryString: string, cb: any): Promise<any> => {
  const data = await remoteMethod(queryString)
  cb(data)
}
// 投诉产品模糊搜索
const productIdMethod = async (query: string) => {
  try {
    const date = {
      productName: query
    }
    const { code, data } = await findComplaintDetailByList(date)
    if (code != '0') return
    return data
  } catch {}
}

// 卡号模糊搜索
const remotecardCode = async (query: string) => {
  try {
    const date = {
      cardCode: query
    }
    const { code, data } = await findComplaintByList(date)
    if (code != '0') return
    return data
  } catch {}
}
const querycardCode = async (queryString: string, cb: any): Promise<any> => {
  try {
    const data = await remotecardCode(queryString)
    cb(data)
  } catch (error) {
    console.log(error, 'error')
  }
}
// 责任部门
const deptProp = {
  value: 'id',
  label: 'name',
  children: 'children'
}

let responsibleDeptList = ref<any>([]) // 责任部门列表
const getResponsibleDeptList = async () => {
  try {
    const { data, code, msg } = await listallsimple()
    if (code == 0) {
      responsibleDeptList.value = data
    }
  } catch (error) {
    console.log(error, 'error')
  }
}

onMounted(() => {
  getResponsibleDeptList()
})

// 监听责任部门
let responsiblePersonList = ref<any>([]) // 部主任列表
const changeResponsibleDeptList = async (val) => {
  try {
    if (!val) {
      complaintPageDTO.value.lastResponseUserId = ''
      responsiblePersonList.value = []
      return
    }

    const { code, data } = await deptUserPage(val)
    if (code != '0') return
    responsiblePersonList.value = data
    complaintPageDTO.value.lastResponseUserId = ''
  } catch (error) {}
}

/**
 * 查询所有人员(当责任部门未选时)
 */
const getAllPersonList = async () => {
  try {
    if (!complaintPageDTO.value.lastResponseDepartId) {
      const { code, data } = await userList('')
      if (code != '0') return
      responsiblePersonList.value = data
    }
  } catch {}
}

const lastResponse = async (queryString) => {
  try {
    const { code, data } = await userList(queryString)
    if (code != '0') return
    return data
  } catch {}
}
/**联系人 */
const queryPerson = async (queryString: string, cb: any): Promise<any> => {
  const data = await lastResponse(queryString)
  cb(data)
}
</script>

<style scoped lang="less"></style>
