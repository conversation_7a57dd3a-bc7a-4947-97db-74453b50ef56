// import { deptUserPage } from '@/api/AgencyManagement/index'
// export const loadNode = (treedata) => {
//   const map = {}
//   const result: any = []
//   treedata.forEach((item) => {
//     map[item.id] = { ...item }
//   })
//   treedata.forEach((item) => {
//     if (item.parentId === 0) {
//       result.push(map[item.id])
//     } else {
//       if (map[item.parentId]) {
//         if (!map[item.parentId].children) {
//           map[item.parentId].children = []
//         }
//         map[item.parentId].children.push(map[item.id])
//       }
//     }
//   })
//   return result
// }
/**
 * 转换部门的结构
 */
export const loadNode = (treedata) => {
  const map: object = {}
  const result: object[] = []
  for (const item of treedata) {
    map[item.id] = { ...item, children: [] }
  }
  for (const item of treedata) {
    const mappedItem: object = map[item.id]
    if (map[item.parentId]) {
      map[item.parentId].children.push(mappedItem)
    } else {
      result.push(mappedItem)
    }
  }
  return result
}
/**
 * 转换数据格式
 */
export const nodechildren = (data) => {
  return data.map((item) => {
    if (item.hasOwnProperty('parentId')) {
      return {
        id: item.id,
        parentId: item.parentId,
        label: item.name,
        value: item.id,
        name: item.name,
        leaderUserId: item.leaderUserId,
        children: [],
        disabled: false,
        childValue: `parent${item.id}` // 这个是为了避免部门Id和用户Id相同的时候引起冲突，配合ElTreeSelect组件的value-key属性使用
      }
    }

    return {
      ...item,
      name: item.nickname,
      id: item.id,
      value: item.id,
      userName: item.nickname,
      disabled: false,
      childValue: item.id
    }
  })
}
/**
 * 获取部门人员
 */
// export const deptUserPages = async (treedata) => {
//   try {
//     const promises = treedata.map(async (item) => {
//       if (!item.hasOwnProperty('disabled') || item.hasOwnProperty('nickname')) return
//       if (item.children && item.children.length < 1) {
//         const { data } = await deptUserPage(item.id)
//         item.children = nodechildren(data)
//       } else {
//         const { data } = await deptUserPage(item.id)
//         if (data && data.length > 0) {
//           item.children = item.children.concat(nodechildren(data))
//         }

//         await deptUserPages(item.children)
//       }
//     })
//     await Promise.all(promises)
//   } catch {}
// }
/**
 * 查找符合节点的数据
 */
export const findNodeParent = (data, id) => {
  for (const item of data) {
    if (item.id === id) {
      return item
    }
    if (item.children) {
      const foundItem = item.children.find((item) => item.id === id)
      if (foundItem) {
        return foundItem
      }
      const itemFrom = findNodeParent(item.children, id)
      if (itemFrom) {
        return itemFrom
      }
    }
  }
  return null
}
