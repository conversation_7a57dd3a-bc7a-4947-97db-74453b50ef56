<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-21 11:25:53
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-06-28 10:35:32
 * @Description:  
-->
<template>
  <div class="bg-light-50 text-dark-800 w-full h-full flex flex-col justify-center items-center">
    <div
      class="max-w-[900px] max-h-[160px]"
      :class="{
        'sso-title-zh': !ifEn,
        'sso-title-en <pc13_3:(!max-w-1000px) <pc13_3:(!max-h-120px) ': ifEn
      }"
      v-if="routerName !== 'SsoError'"
    >
    </div>
    <div
      class="sso-callback !max-w-180 !max-h-90 <pc13_3:(!max-w-120) <pc13_3:(!max-h-65)"
      v-if="routerName == 'SsoError'"
    >
    </div>
    <!-- 插槽 -->
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SsoSlot'
})

const { ifEn } = useI18n()
const router = useRouter()
const routerName = router.currentRoute.value.name
</script>
<style lang="less" scope>
.sso-callback {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/svgs/sso.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.sso-title-en {
  width: 100%;
  height: 100%;
  background-image: url('./img/title_en_us.webp');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.sso-title-zh {
  width: 100%;
  height: 100%;
  background-image: url('./img/title_zh_cn.webp');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
</style>
