<!-- 消息中心 -->
<template>
  <ContentWrap>
    <el-form
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      :labelWidth="ifEn ? '120px' : '90px'"
    >
      <el-form-item :label="t('messageCenter.components.messageTypeText')" prop="messageType">
        <el-select
          v-model="queryParams.messageType"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in messageTypeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('messageCenter.list.status')" prop="status">
        <el-select
          v-model="queryParams.status"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('messageCenter.components.publishTime')" prop="timeRange">
        <el-date-picker
          v-model="queryParams.timeRange"
          type="daterange"
          range-separator="-"
          :start-placeholder="t('messageCenter.list.startDate')"
          :end-placeholder="t('messageCenter.list.endDate')"
          value-format="YYYY-MM-DD"
          :shortcuts="shortcuts"
          style="width: 234px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">{{ t('common.query') }}</el-button>
        <el-button type="warning" @click="resetQuery">{{ t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb-20px">
      <el-col :span="1.5">
        <el-button type="primary" :disabled="multiple" @click="handleRead">{{
          t('messageCenter.list.markRead')
        }}</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="primary" @click="handleOpen">查看</el-button>
      </el-col> -->
    </el-row>

    <el-table
      v-loading="loading"
      v-horizontal-scroll
      :data="list"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="t('messageCenter.list.sortNum')"
        align="center"
        :width="ifEn ? '120px' : '55px'"
      >
        <template #default="scope">
          {{ setIndex(scope.$index) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="t('messageCenter.list.title')"
        align="center"
        prop="title"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span :class="{ 'font-bold-text': scope.row.status.code == '0' }">{{
            scope.row.title
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('messageCenter.list.status')"
        align="center"
        prop="publichStatusText"
        width="100"
      >
        <template #default="scope">
          <span :class="{ 'font-bold-text': scope.row.status.code == '0' }">{{
            scope.row.publichStatusText
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('messageCenter.components.messageTypeText')"
        align="center"
        prop="messageTypeText"
        :width="ifEn ? '140px' : '100px'"
      >
        <template #default="scope">
          <span :class="{ 'font-bold-text': scope.row.status.code == '0' }">{{
            scope.row.messageTypeText
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('messageCenter.list.recevieTime')"
        align="center"
        prop="publishTime"
        width="200"
      >
        <template #default="scope">
          <span :class="{ 'font-bold-text': scope.row.status.code == '0' }">{{
            scope.row.publishTime
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('common.operate')" fixed="right" align="center" width="100">
        <template #default="scope">
          <el-button type="primary" link @click="handleOpen(scope.row)">{{
            t('common.see')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      :total="total"
    />
  </ContentWrap>

  <MessageDetailDialog v-model="dialogVisible" :detail="messageDetail" />
</template>

<script setup lang="ts">
defineOptions({
  name: 'MessageCenter'
})

const { t, ifEn } = useI18n()
import { ContentWrap } from '@/components/ContentWrap'
import * as messageCenterApi from '@/api/messageManagement/messageCenter/index'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { useMessageStore } from '@/store/modules/message'
import MessageDetailDialog from '../components/MessageDetailDialog.vue' // 消息详情弹窗

const messageStore = useMessageStore()
const router = useRouter()
const message = useMessage()

//时间段
const shortcuts = [
  {
    text: t('messageCenter.list.lastWeek'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: t('messageCenter.list.lastMonth'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: t('messageCenter.list.lastThreeMonth'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

const queryRef = ref()
const queryParams = ref({
  messageType: '',
  status: '',
  timeRange: [],
  pageNo: 1,
  pageSize: 10
})

const loading = ref(false)
const list = ref([])
const total = ref(0)
const messageTypeOptions: Ref = ref([]) // 消息类型选项
messageTypeOptions.value = getStrDictOptions(DICT_TYPE.MESSAGE_MESSAGE_TYPE)
const statusOptions: Ref = ref([
  {
    label: t('messageCenter.list.hasRead'),
    value: '0'
  },
  {
    label: t('messageCenter.list.noRead'),
    value: '1'
  }
])

/** 查询按钮操作 */
function handleQuery() {
  queryParams.value.pageNo = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryRef.value.resetFields()
  handleQuery()
}

/** 查询列表 */
async function getList() {
  try {
    loading.value = true
    let params = {
      pageNo: queryParams.value.pageNo,
      pageSize: queryParams.value.pageSize
    }

    if (queryParams.value.messageType) {
      params = Object.assign(params, {
        messageType: {
          code: queryParams.value.messageType
        }
      })
    }

    if (queryParams.value.status) {
      params = Object.assign(params, {
        status: {
          code: queryParams.value.status
        }
      })
    }

    if (queryParams.value.timeRange && queryParams.value.timeRange.length > 0) {
      params = Object.assign(params, {
        publishTimeBegin: `${queryParams.value.timeRange[0]} 00:00:00`,
        publishTimeEnd: `${queryParams.value.timeRange[1]} 23:59:59`
      })
    }

    let res: any = await messageCenterApi.findMessageToByPage(params)

    list.value = res.list?.map((item) => {
      let messageItem = item.message
      return {
        ...item,
        publichStatusText: item.status?.name,
        title: messageItem?.title,
        messageTypeText: messageItem.messageType?.name,
        publishTimeText: messageItem.publishTime ? item.publishTime : '-'
      }
    })
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 生成序列号 */
function setIndex(index) {
  let num = index + 1 + (queryParams.value.pageNo - 1) * queryParams.value.pageSize
  return num > 9 ? num : `0${num}`
}

/** 切换排序 */
function handleSortChange(e) {}

const ids = ref([])
const multiple = ref(true)
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection?.map((item) => item.toId)
  multiple.value = !selection.length
}

/** 标记已读操作 */
function handleRead() {
  message
    .delConfirm(t('messageCenter.list.markReadTips'), t('messageCenter.list.markRead'))
    .then(async () => {
      try {
        const id = unref(ids)
        let batchToIds: any[] = []
        if (typeof id == 'number' || typeof id == 'string') {
          batchToIds = [id]
        } else {
          batchToIds = [...id]
        }
        await messageCenterApi.readMessageTo({ batchToIds })
        messageStore.getUnreadMessage()
        message.success(t('messageCenter.list.operateSuccess'))
        getList()
      } catch (e) {}
    })
}

/** 查看按钮操作 */
const dialogVisible = ref(false)
const messageDetail = ref<any>(null)
async function handleOpen(row) {
  let messageItem = row.message
  if (messageItem.messageType.code == 'NOTICE') {
    messageDetail.value = row
    dialogVisible.value = true
  } else if (messageItem.pageUrl) {
    let { path, query } = getQueryObject(messageItem.pageUrl)
    router.push({ path, query })
  }
  try {
    if (row.status.code === '0') {
      await messageCenterApi.readMessageTo({ batchToIds: [row.toId] })
      messageStore.getUnreadMessage()
      getList()
    }
  } catch (error) {}
}

function getQueryObject(pageUrl) {
  let url: string = decodeURIComponent(pageUrl)
  let query: any = new Object()
  let path = url
  if (url.indexOf('?') != -1) {
    path = url.substr(0, Number(url.indexOf('?')))
    let str = url.substr(Number(url.indexOf('?')) + 1)
    let strs = str.split('&')
    for (let i = 0; i < strs.length; i++) {
      query[strs[i].split('=')[0]] = strs[i].split('=')[1]
    }
  }
  return { path, query }
}

getList()
onActivated(() => {
  if (!loading.value) {
    getList()
  }
})
</script>

<style lang="less">
.font-bold-text {
  font-weight: bold;
}
</style>
