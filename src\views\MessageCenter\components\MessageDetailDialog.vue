<template>
  <!-- 消息详情弹窗 -->
  <el-dialog
    v-model="showDialog"
    class="detail-msg__dialog"
    :show-close="false"
    fullscreen
    destroy-on-close
    append-to-body
    lock-scroll
  >
    <template #header>
      <div class="detail-msg__header">
        <div class="header-title">{{ detailMsgData.title }}</div>
        <div class="header-info-list">
          <div class="info-item">
            <div class="info-item__title">{{ t('messageCenter.components.publishTime') }}</div>
            <div class="info-title__content">{{ detailMsgData.publishTime }}</div>
          </div>
          <div class="info-item">
            <div class="info-item__title">{{ t('messageCenter.components.publishUser') }}</div>
            <div class="info-title__content">{{ detailMsgData.publishUser }}</div>
          </div>
          <div class="info-item">
            <div class="info-item__title">{{ t('messageCenter.components.messageTypeText') }}</div>
            <div class="info-title__content">{{ detailMsgData.messageTypeText }}</div>
          </div>
        </div>
      </div>
    </template>
    <div class="w-e-text-container">
      <div class="detail__rich" v-html="detailMsgData.content" data-slate-editor></div>
    </div>
    <template #footer>
      <div class="dialog__footer">
        <el-button type="primary" @click="cancel">{{ t('common.close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'
import Prism from 'prismjs'
import * as messageCenterApi from '@/api/messageManagement/messageCenter/index'

const { t } = useI18n()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  detail: {
    type: Object,
    default: () => null
  }
})

const showDialog = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  }
})

watch(showDialog, (val) => {
  if (!val) {
    reset()
  } else {
    init()
  }
})
const emit = defineEmits(['update:modelValue'])

const detailMsgData = ref({
  title: '',
  publishTime: '',
  publishUser: '',
  messageTypeText: '',
  content: ''
})

/** 表单重置 */
function reset() {
  detailMsgData.value = {
    title: '',
    publishTime: '',
    publishUser: '',
    messageTypeText: '',
    content: ''
  }
}

function cancel() {
  showDialog.value = false
}

async function init() {
  if (props.detail === null) {
    return
  }
  let messageItem = props.detail.message
  if (messageItem.messageType.code == 'NOTICE') {
    detailMsgData.value.publishTime = props.detail.publishTime
    detailMsgData.value.publishUser = ''
    if (messageItem.createUser && messageItem.createUser.nickname) {
      detailMsgData.value.publishUser = messageItem.createUser.nickname
    }
    if (messageItem.updateUser && messageItem.updateUser.nickname) {
      detailMsgData.value.publishUser = messageItem.updateUser.nickname
    }
    detailMsgData.value.title = messageItem.title
    detailMsgData.value.messageTypeText = messageItem.messageType?.name
    detailMsgData.value.content = ''
    getMessageById(messageItem.messageId)
  }
}

/** 根据消息ID查询推送详情 */
async function getMessageById(messageId) {
  try {
    let res = await messageCenterApi.findMessageById(messageId)
    detailMsgData.value.content = res.content
    nextTick(() => {
      Prism.highlightAll()
    })
  } catch (error) {
  } finally {
  }
}
</script>

<style lang="less" scoped>
.dialog__footer {
  display: flex;
  justify-content: center;
}

.detail-msg__header {
  .header-title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    color: #333333;
  }

  .header-info-list {
    display: flex;
    margin: 20px 0;
    justify-content: center;
  }

  .info-item {
    display: flex;
    padding: 0 40px;
    justify-content: center;
    font-size: 14px;
    color: #333333;
    box-sizing: border-box;

    .info-item__title {
      margin-right: 10px;
    }
  }
}
</style>

<style lang="less">
.detail__rich {
  padding: 0 !important;

  img {
    display: inline;
    margin: 0 3px;
  }
}

.detail-msg__dialog {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0px 50px 0 50px;

  .el-dialog__header {
    flex-shrink: 0;
    height: auto !important;
  }

  .el-dialog__body {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .el-dialog__footer {
    flex-shrink: 0;
  }
}

.w-e-text-container [data-slate-editor] p {
  margin: 0 !important;
}
</style>

<style>
pre[class*='language-'] {
  padding: 0 !important;
  margin: 0 !important;
  overflow: auto;
  background: transparent !important;
}
</style>
