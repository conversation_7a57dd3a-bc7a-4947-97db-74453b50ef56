export function getActiveIndex(status) {
  const { t } = useI18n()
  if (!status || !status.hasOwnProperty('statusName')) {
    return 0
  }
  const step1 = [
    t('productsShow.batchCardProduct.toBeSubmitted'),
    t('productsShow.batchCardProduct.delist')
  ]
  const step2 = [t('productsShow.batchCardProduct.underReview')]
  if (step1.includes(status.statusName)) {
    return 0
  }
  if (step2.includes(status.statusName)) {
    return 1
  }
  return 2 + 1
}

/**
 * 价格输入框中禁止输入e、+、-
 */
const InhibitCharList = ['e', 'E', '+', '-']
export const priceInputInhibit = (e) => {
  const key = e.key
  if (InhibitCharList.includes(key)) {
    e.returnValue = false
  } else {
    e.returnValue = true
  }
}

/**
 * 价格输入框中禁止输入e、+、-， 并限制小数位数
 */
export const priceInputInhibitDecimalLength = async (e, priceValue, length) => {
  const key = e.key
  const priceValueString =
    e?.target?.value?.toString() ?? e?.srcElement?.value?.toString() ?? priceValue?.toString()
  if (priceValueString) {
    // const price = priceValue.toString().split('.')
    const price = priceValueString.split('.')
    // 判断key是否非数字
    const keyFlag = Number.isNaN(Number.parseFloat(key))
    // 如果key是数字并且priceValue是指定长度的小数，就禁止再录入
    const flag = !keyFlag && price.length > 1 && price[1].length === length
    if (flag) {
      e.returnValue = false
      return
    }
  }

  if (InhibitCharList.includes(key)) {
    e.returnValue = false
  } else {
    e.returnValue = true
  }
}

/**
 * 价格输入框中禁止输入e、+、-、 .
 */
export const priceInputInhibitNotDecimal = (e) => {
  const key = e.key
  if (InhibitCharList.includes(key) || '.' === e.key) {
    e.returnValue = false
  } else {
    e.returnValue = true
  }
}

/**
 * 价格输入框粘贴时判断剪贴板内容
 */
export const priceInputPaste = (e) => {
  const cData = e.clipboardData.getData('text')
  const charSet = new Set(InhibitCharList)
  const mixedList = Array.of(...cData).filter((item) => charSet.has(item))
  if (Number.isNaN(Number.parseFloat(cData)) || mixedList.length > 0) {
    e.preventDefault()
  }
}
