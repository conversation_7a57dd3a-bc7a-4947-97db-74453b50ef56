<!-- 产品展示/批卡产品 -->
<template>
  <ContentWrap ifTable>
    <template #search>
      <el-form ref="productQueryFormRef" :model="productQueryForm" :inline="true">
        <el-row :gutter="20">
          <el-col :md="10" :lg="6" :xl="4">
            <el-form-item :label="t('productsShow.batchCardProduct.TheCustomer')" prop="customerId">
              <el-tree-select
                v-model="productQueryForm.customerId"
                :data="data.customerList"
                :props="{ label: 'customerName', children: 'children' }"
                node-key="customerId"
                :loading="data.customerSelectLoading"
                clearable
                check-strictly
                remote
                filterable
                automatic-dropdown
                remote-show-suffix
                :remote-method="getCustomerList"
                :placeholder="t('productsShow.batchCardProduct.TheCustomerPlaceholder')"
                style="min-width: 200px; max-width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :md="10" :lg="6" :xl="4">
            <el-form-item
              :label="t('productsShow.batchCardProduct.productType')"
              prop="productTypeCodeList"
            >
              <el-select
                v-model="productQueryForm.productTypeCodeList"
                :placeholder="t('productsShow.batchCardProduct.productTypePlaceholder')"
                multiple
                filterable
                collapseTags
                collapseTagsTooltip
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in data.productTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="10" :lg="6" :xl="4">
            <el-form-item :label="t('productsShow.batchCardProduct.productName')" prop="saleName">
              <el-input
                v-model="productQueryForm.saleName"
                :placeholder="t('productsShow.batchCardProduct.productNamePlaceholder')"
                clearable
                style="width: 200px"
              />
            </el-form-item>
          </el-col>
          <el-col :md="10" :lg="6" :xl="4">
            <el-form-item :label="t('productsShow.batchCardProduct.cardCode')" prop="cardCode">
              <el-input
                v-model="productQueryForm.cardCode"
                :placeholder="t('productsShow.batchCardProduct.cardCodePlaceholder')"
                clearable
                style="width: 200px"
              />
            </el-form-item>
          </el-col>
          <el-col :md="10" :lg="6" :xl="4">
            <el-form-item
              :label="t('productsShow.batchCardProduct.productStatus')"
              prop="productStatusCodeList"
            >
              <el-select
                v-model="productQueryForm.productStatusCodeList"
                :placeholder="t('productsShow.batchCardProduct.productStatusPlaceholder')"
                multiple
                filterable
                collapseTags
                collapseTagsTooltip
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in data.productStatusList"
                  :key="item.statusCode"
                  :label="item.statusName"
                  :value="item.statusCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="10" :lg="6" :xl="4">
            <el-form-item style="margin-right: 0 !important">
              <el-button type="primary" v-track:click.btn @click="queryProduct">{{
                t('common.query')
              }}</el-button>
              <el-button type="warning" @click="resetQueryForm">{{ t('common.reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>

    <!-- <el-button type="success" @click="addProductDialog" style="margin-top: 10px">添加</el-button> -->
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      style="width: 100%"
      max-height="600"
      border
    >
      <el-table-column
        type="index"
        :index="(data.pageNo - 1) * data.pageSize + 1"
        :label="t('productsShow.batchCardProduct.indexNumber')"
        :width="ifEn ? '120px' : '80px'"
        align="center"
        headerAlign="center"
      />
      <el-table-column
        prop="customer.customerName"
        :label="t('productsShow.batchCardProduct.customerName')"
        minWidth="150"
        align="left"
        headerAlign="center"
        showOverflowTooltip
      />
      <el-table-column
        prop="clientProductUniqueCode"
        :label="t('productsShow.batchCardProduct.clientProductUniqueCode')"
        :minWidth="ifEn ? '200' : '80'"
        align="left"
        headerAlign="center"
        showOverflowTooltip
      />
      <el-table-column
        prop="cardCode"
        :label="t('productsShow.diyCardProduct.cardDraftCode')"
        width="150"
        align="center"
        headerAlign="center"
        showOverflowTooltip
      />
      <el-table-column
        prop="saleName"
        :label="t('productsShow.batchCardProduct.productName')"
        minWidth="150"
        align="left"
        headerAlign="center"
        showOverflowTooltip
      />
      <el-table-column
        :label="t('productsShow.batchCardProduct.productFrontImg')"
        :minWidth="ifEn ? '200' : '120'"
      >
        <template #default="scope">
          <div class="info-item flex-between" v-if="showImg(scope.row.imgList, true)">
            <div class="card-warp flex-index">
              <el-popover placement="right-start" width="355">
                <el-image :src="showImg(scope.row.imgList, true)" alt="" class="popover-img"
                  ><template #error> </template
                ></el-image>
                <template #reference>
                  <el-image :src="showImg(scope.row.imgList, true)" alt="" class="card-img"
                    ><template #error> </template
                  ></el-image>
                </template>
              </el-popover>
            </div>
          </div>
          <p v-else>{{ t('productsShow.batchCardProduct.noData') }}</p>
        </template>
      </el-table-column>
      <el-table-column
        :label="t('productsShow.batchCardProduct.productBackImg')"
        :minWidth="ifEn ? '200' : '120'"
      >
        <template #default="scope">
          <div class="info-item flex-between" v-if="showImg(scope.row.imgList, false)">
            <div class="card-warp flex-index">
              <el-popover placement="right-start" width="355">
                <el-image :src="showImg(scope.row.imgList, false)" alt="" class="popover-img"
                  ><template #error> </template
                ></el-image>
                <template #reference>
                  <el-image :src="showImg(scope.row.imgList, false)" alt="" class="card-img"
                    ><template #error> </template
                  ></el-image>
                </template>
              </el-popover>
            </div>
          </div>
          <p v-else>{{ t('productsShow.batchCardProduct.noData') }}</p>
        </template>
      </el-table-column>
      <el-table-column
        prop="productType.productTypeName"
        :label="t('productsShow.batchCardProduct.productType')"
        :width="ifEn ? '120' : '100'"
        align="center"
        headerAlign="center"
        showOverflowTooltip
      >
        <template #default="{ row }">
          <span>{{ getDictLabel('product_type', row.productType?.productTypeCode) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="status.statusName"
        :label="t('productsShow.batchCardProduct.status')"
        :width="ifEn ? '120' : '100'"
        align="center"
        headerAlign="center"
        showOverflowTooltips
      >
        <template #default="scope">
          <!-- <el-tag v-if="['listed'].includes(scope.row.status.statusCode)" type="success">
            {{ filterStatusName(scope.row.status.statusCode) }}
          </el-tag>
          <el-tag v-else-if="['unlisted'].includes(scope.row.status.statusCode)" type="info">
            {{ filterStatusName(scope.row.status.statusCode) }}
          </el-tag>
          <el-tag v-else>{{ filterStatusName('under_review') }}</el-tag> -->

          <el-switch
            v-if="['listed', 'unlisted'].includes(scope.row.status.statusCode)"
            :inactive-value="['listed'].includes(scope.row.status.statusCode)"
            :active-value="['unlisted'].includes(scope.row.status.statusCode)"
            @click="
              enableProduct(scope.row, ['listed'].includes(scope.row.status.statusCode) ? 0 : 1)
            "
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        :label="t('productsShow.batchCardProduct.createTime')"
        width="180"
        align="center"
        headerAlign="center"
        showOverFlowTooltip
      />
      <el-table-column
        prop="updateDate"
        :label="t('productsShow.batchCardProduct.updateTime')"
        width="180"
        align="center"
        headerAlign="center"
        showOverFlowTooltip
      />
      <el-table-column
        fixed="right"
        :label="t('common.oper')"
        width="150"
        align="left"
        headerAlign="center"
      >
        <template #default="scope">
          <!-- <el-button
            v-if="['unlisted'].includes(scope.row.status.statusCode)"
            text
            bg
            type="primary"
            size="small"
            @click="listedProduct(scope.row)"
          >
            启用
          </el-button>
          <el-button
            v-if="['listed'].includes(scope.row.status.statusCode)"
            text
            bg
            type="info"
            size="small"
            @click="unlistedProduct(scope.row)"
          >
            关闭
          </el-button> -->
          <el-button
            link
            type="primary"
            size="small"
            v-track:click.btn
            @click="editPrice(scope.row, true)"
            v-hasPermi="['batchproduct:price:query']"
          >
            {{ t('productsShow.batchCardProduct.viewPrice') }}
          </el-button>
          <el-button
            link
            type="primary"
            size="small"
            v-track:click.btn
            @click="editPrice(scope.row, false)"
            v-hasPermi="['batchproduct:price:edit']"
          >
            {{ t('productsShow.batchCardProduct.editPrice') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #pagination>
      <Pagination
        v-model:page="data.pageNo"
        v-model:limit="data.pageSize"
        :total="data.productListCount"
        @pagination="getProductList"
      />
    </template>
  </ContentWrap>
  <!-- todo:抽离成子组件？ -->
  <el-dialog
    v-model="data.showProductDialog"
    :title="data.productDialogTitle"
    class="product-data-dialog"
    :before-close="closeProductDialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
    draggable
    center
  >
    <el-scrollbar wrap-class="product-wrap" :height="data.scrollHeight">
      <el-form
        :model="data.productForm"
        ref="productFormRef"
        label-position="right"
        style="text-align: left"
        label-width="100px"
      >
        <el-card shadow="hover" style="margin-top: 10px">
          <template #header>
            <span>{{ t('productsShow.batchCardProduct.basicInfo') }}</span>
          </template>
          <el-form-item
            :label="t('productsShow.batchCardProduct.customerName')"
            prop="customer"
            :rules="[
              {
                required: true,
                message: t('productsShow.batchCardProduct.customerNamePlaceholder')
              }
            ]"
          >
            <el-tree-select
              v-model="data.productForm.customer"
              ref="selectCustomerTree"
              :data="data.customerList"
              :reserve-keyword="false"
              :props="{ label: 'customerName', children: 'children' }"
              node-key="customerId"
              value-key="customerId"
              :loading="data.customerSelectLoading"
              :render-after-expand="false"
              highlight-current
              clearable
              remote
              automatic-dropdown
              remote-show-suffix
              check-strictly
              @change="customerChange"
              :remote-method="getCustomerList"
              :placeholder="t('productsShow.batchCardProduct.TheCustomerPlaceholder')"
              style="width: 100%"
            >
              <template #default="scope">
                <el-option :label="scope.data.customerName" :value="scope.data" />
              </template>
            </el-tree-select>
          </el-form-item>
          <el-form-item
            :label="t('productsShow.batchCardProduct.productType')"
            prop="productType.productTypeCode"
            :rules="[
              {
                required: true,
                message: t('productsShow.batchCardProduct.productTypePlaceholder')
              }
            ]"
          >
            <el-select
              v-model="data.productForm.productType"
              value-key="productTypeCode"
              :placeholder="t('productsShow.batchCardProduct.productTypePlaceholder')"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in data.productTypeList"
                :key="item.value"
                :label="item.label"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px; margin-bottom: 35px">
          <template #header>
            <span>{{ t('productsShow.batchCardProduct.productInfo') }}</span>
          </template>
          <el-form-item
            :label="t('productsShow.batchCardProduct.productName')"
            prop="saleName"
            :rules="[
              {
                required: true,
                message: t('productsShow.batchCardProduct.productNamePlaceholder')
              }
            ]"
          >
            <el-input
              v-model="data.productForm.saleName"
              :placeholder="t('productsShow.batchCardProduct.productNamePlaceholder')"
              type="text"
              maxlength="50"
              show-word-limit
              clearable
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item :label="t('productsShow.batchCardProduct.productImg')" prop="imgList">
            <UploadFileComp
              ref="frontImageUploadRef"
              :limit="1"
              :fileSize="2"
              :accept="'.jpg,.png'"
              :multiple="false"
              :mergeUpload="true"
              :uploadText="t('productsShow.batchCardProduct.frontImg')"
              :fileSuffixType="data.fileSuffixType"
              :url="data.fileUploadUrl"
              :uploadData="data.uploadData"
              :showCancelUpload="data.showReUploadComp"
              @e-success="fileUploadSuccess"
              @e-delete-file="fileDelete"
              @e-cancel-upload-comp="data.showReUploadComp = false"
            />
            <UploadFileComp
              style="padding-left: 10px; display: flex"
              ref="reverseImageUploadRef"
              :limit="1"
              :fileSize="2"
              :accept="'.jpg,.png'"
              :multiple="false"
              :mergeUpload="true"
              :uploadText="t('productsShow.batchCardProduct.backImg')"
              :fileSuffixType="data.fileSuffixType"
              :url="data.fileUploadUrl"
              :uploadData="data.uploadData"
              :showCancelUpload="data.showReUploadComp"
              @e-success="fileUploadSuccess"
              @e-delete-file="fileDelete"
              @e-cancel-upload-comp="data.showReUploadComp = false"
            />
            <div style="padding: 15px; color: darkgray">
              <span>{{ t('productsShow.batchCardProduct.sizeOneTip') }}</span
              ><br />
              <span>{{ t('productsShow.batchCardProduct.formatOneTip') }}</span>
            </div>
          </el-form-item>
        </el-card>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <el-button @click="closeProductDialog">{{ t('common.cancel') }}</el-button>
      <el-button type="success" :loading="data.isSaving" @click="saveProductData(false)">{{
        t('productsShow.batchCardProduct.submit')
      }}</el-button>
    </template>
  </el-dialog>
  <ProductDetailDialog
    v-if="data.showProductDetailDialogFlag"
    :productDetailId="data.productDetailId"
    :closeDialog="closeProductDetailDialog"
  />

  <!-- 修改价格弹窗 -->
  <EditPrice
    v-if="isEditPriceShow"
    v-model="isEditPriceShow"
    :disabled="isEditPriceDisabled"
    :priceStage="priceStage"
    :productId="priceStageProductId"
    @get-list="getProductList"
  />
</template>

<script setup lang="ts">
defineOptions({
  name: 'BatchCardProduct'
})

import { getDictLabel } from '@/utils/dict'
import { ContentWrap } from '@/components/ContentWrap'
import ProductDetailDialog from '@/views/ProductsShow/BatchCardProduct/Detail/ProductDetailDialog.vue'
import UploadFileComp from '@/views/ProductsShow/BatchCardProduct/components/UploadFileComp.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { unref, watch } from 'vue'
import * as ProductApi from '@/api/product/productInfo'
import EditPrice from './components/EditPrice.vue'
import { cloneDeep } from 'lodash-es'
import { useDictStoreWithOut } from '@/store/modules/dict'

const { t, ifEn } = useI18n()
const dictStore = useDictStoreWithOut()

const productQueryFormRef = ref()
const productFormRef = ref()
const frontImageUploadRef = ref()
const reverseImageUploadRef = ref()
const selectCustomerTree = ref()

const data = reactive({
  productForm: {
    productId: '',
    customer: {},
    productType: {},
    imgList: [],
    saleName: '',
    status: {},
    audit: false
  },
  productQueryForm: {
    customerId: '',
    cardCode: '',
    saleName: '',
    productTypeCodeList: [],
    productStatusCodeList: []
  },

  fileUploadUrl: '',
  fileSuffixType: 'jpg、png',
  uploadData: {},
  showReUploadComp: false,

  gIndex: 0,
  pageNo: 1,
  pageSize: 10,
  productListCount: 0,

  customerList: [],
  customerSelectLoading: false,

  projectList: [],
  projectSelectLoading: false,

  productTypeList: [],
  productStatusList: [],

  merchantCardList: [],
  merchantCardSelectLoading: false,

  tableData: [],
  tableLoading: false,

  // 查看产品信息弹窗组件
  showProductDetailDialogFlag: false,
  productDetailId: '',

  // 弹窗 margin:30, 弹窗header:54, 状态进度：58， 弹窗foot：62， 最后的卡片组件margin-bottom: 35, 驳回原因：125
  scrollHeight: 0,
  showOverruleReason: false,

  showProductDialog: false,
  productDialogTitle: '',
  isSaving: false
})

const { productQueryForm } = toRefs(data)

/** 查询产品列表 */
function queryProduct() {
  data.pageNo = 1
  // data.pageSize = 25
  getProductList()
}

/** 重置查询条件 */
function resetQueryForm() {
  unref(productQueryFormRef)?.resetFields()
  queryProduct()
}

/** 查询产品列表 */
const getProductList = async () => {
  productQueryForm.value.pageNo = data.pageNo
  productQueryForm.value.pageSize = data.pageSize
  try {
    data.tableLoading = true
    const { list, total } = await ProductApi.getProductListApi(productQueryForm.value)
    data.tableData = list
    data.productListCount = total
  } catch (e) {
    data.tableData = []
    data.productListCount = 0
    console.error('查询产品列表异常：', e)
  } finally {
    data.tableLoading = false
  }
}

/** 查看产品详情 **/
const showProductDetailDialog = async (row) => {
  // data.productForm = JSON.parse(JSON.stringify(row))
  data.productDetailId = row.productId
  data.showProductDetailDialogFlag = true
}

/** 关闭查看产品详情 **/
function closeProductDetailDialog() {
  data.showProductDetailDialogFlag = false
}

/** 添加产品弹窗 **/
const addProductDialog = async () => {
  await clearProductForm()

  data.showProductDialog = true
  data.productDialogTitle = t('productsShow.batchCardProduct.addProduct')
}

/** 关闭产品弹窗 **/
const closeProductDialog = async () => {
  data.showProductDialog = false
  data.isSaving = false
}

/** 保存产品数据 **/
function saveProductData(audit) {
  unref(productFormRef)?.validate((isValid) => {
    if (isValid) {
      data.isSaving = true
      data.productForm.audit = audit
      if (data.productForm.productId === '') {
        data.pageNo = 1
        addOrUpdateProduct('/product/v1/addClientProduct')
      } else {
        addOrUpdateProduct('/product/out/v1/updateClientProduct')
      }
    }
  })
}

/** 创建产品、更新产品 **/
const addOrUpdateProduct = async (url) => {
  const frontFileList = await frontImageUploadRef.value.getFile()

  const reverseFileList = await reverseImageUploadRef.value.getFile()

  const formData = new FormData()
  const productData = {
    customer: {
      customerId: data.productForm.customer.customerId,
      customerCode: data.productForm.customer.customerCode,
      customerName: data.productForm.customer.customerName
    },
    productType: data.productForm.productType,
    saleName: data.productForm.saleName
  }
  formData.append('data', JSON.stringify(productData))
  frontFileList.forEach((file) => {
    formData.append('frontImages', file.raw)
  })
  reverseFileList.forEach((file) => {
    formData.append('reverseImages', file.raw)
  })

  try {
    const res = await ProductApi.addOrUpdateProductApi(url, formData)
    if (res) {
      ElMessage.success(t('productsShow.batchCardProduct.saveProductSuccess'))
      await closeProductDialog()
      await getProductList()
    }
  } catch (e) {
    console.error('添加产品或更新产品失败：', e)
  } finally {
    data.isSaving = false
  }
}

/** 创建产品、更新产品的回调 **/
function fileUploadSuccess(response, fileList?) {}

/** 删除文件记录 **/
function fileDelete(file) {
  if (file.hasOwnProperty('imageId')) {
    let matchFile = data.productForm.imgList.filter((f) => f.imageId === file.imageId)[0]
    data.productForm.imgList.splice(data.productForm.imgList.indexOf(matchFile), 1)
  }
}

/** 重置表单初始值 **/
const clearProductForm = async () => {
  for (let fld in data.productForm) {
    let ot = Object.prototype.toString.call(data.productForm[fld]).slice(8, -1)
    if (fld === 'customer') {
      data.productForm[fld] = null
    } else {
      data.productForm[fld] =
        ot === 'String'
          ? ''
          : ot === 'Object'
          ? {}
          : ot === 'Array'
          ? []
          : ot === 'Boolean'
          ? true
          : ot === 'Number'
          ? 0
          : ''
    }
  }
}

/** 产品下架 **/
function unlistedProduct(row) {
  ElMessageBox.confirm(
    t('productsShow.batchCardProduct.closeConfirmTip', {
      name: row.saleName
    }),
    t('productsShow.batchCardProduct.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const params = {
          productId: row.productId,
          statusCode: 0
        }
        const res = await ProductApi.productListedApi(params)
        if (res) {
          ElMessage.success(t('productsShow.batchCardProduct.closeProductSuccess'))
          await getProductList()
        }
      } catch (e) {
        console.error('产品关闭失败：', e)
      } finally {
      }
    })
    .catch(() => {})
}

/** 产品上架 **/
function listedProduct(row) {
  ElMessageBox.confirm(
    t('productsShow.batchCardProduct.openConfirmTip', {
      name: row.saleName
    }),
    t('productsShow.batchCardProduct.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const params = {
          productId: row.productId,
          statusCode: 1
        }
        const res = await ProductApi.productListedApi(params)
        if (res) {
          ElMessage.success(t('productsShow.batchCardProduct.openProductSuccess'))
          await getProductList()
        }
      } catch (e) {
        console.error('产品启用失败：', e)
      } finally {
      }
    })
    .catch(() => {})
}

/** 删除产品 **/
function deleteProduct(row) {
  ElMessageBox.confirm(
    t('productsShow.batchCardProduct.delConfirmTip', {
      name: row.saleName
    }),
    t('productsShow.batchCardProduct.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const res = await ProductApi.deleteProductApi(row)
        if (res) {
          ElMessage.success(t('productsShow.batchCardProduct.delProductSuccess'))
          await getProductList()
        }
      } catch (e) {
        console.error('产品删除失败：', e)
      }
    })
    .catch(() => {})
}

function handleSizeChange(pageSize) {
  data.pageSize = pageSize
  data.gIndex = data.pageNo > 1 ? (data.pageNo - 1) * data.pageSize : 0
  getProductList()
}

function handleCurrentChange(pageNo) {
  data.pageNo = pageNo
  data.gIndex = data.pageNo > 1 ? (data.pageNo - 1) * data.pageSize : 0
  getProductList()
}

// 获取产品类型列表
const getProductTypeList = async () => {
  try {
    // data.productTypeList = await ProductApi.getProductTypeListApi()
    data.productTypeList = dictStore.getDictByType('product_type')
  } catch (e) {
    data.productTypeList = []
    console.error('获取产品类型列表异常：', e)
  }
}
// 获取产品状态列表
const statusList = [
  {
    statusCode: 'listed',
    statusName: t('productsShow.batchCardProduct.enabled')
  },
  {
    statusCode: 'unlisted',
    statusName: t('productsShow.batchCardProduct.closed')
  },
  {
    statusCode: 'under_review',
    statusName: t('productsShow.batchCardProduct.process')
  }
]
const getProductStatusList = async () => {
  data.productStatusList = statusList
}
const filterStatusName = (code) => {
  return statusList.find((x) => x.statusCode === code).statusName
}
// 获取客户接口
const getCustomerList = async (value?) => {
  let params = {
    customerName: value || ''
  }
  try {
    data.customerSelectLoading = true
    data.customerList = (await ProductApi.getCustomerListApi(params)) || []
  } catch (e) {
    data.customerList = []
    console.error('获取客户异常：', e)
  } finally {
    data.customerSelectLoading = false
  }
}

// 选中客户重置卡款选中
function customerChange(value) {
  data.productForm.relateProductId = ''
  data.productForm.merchantCard = {}
  data.productForm.productCode = ''
  data.merchantCardList = []
}

watch(
  () => data.productForm,
  (newVal, oldVal) => {
    data.showOverruleReason = newVal.status && newVal.status.statusName === '已驳回'
    data.scrollHeight =
      document.documentElement.clientHeight -
      30 -
      54 -
      58 -
      62 -
      35 -
      (data.showOverruleReason ? 125 : 0)
  },
  {
    immediate: true,
    deep: true
  }
)

/** 产品图片上传校验 **/
const productFormImgListValidator = async (rule: any, value: any, callback: any) => {
  const frontFileList = await frontImageUploadRef.value.getFile()
  if (frontFileList.length === 0) {
    callback(new Error(t('productsShow.batchCardProduct.productFrontImgTip')))
  }
  const reverseFileList = await reverseImageUploadRef.value.getFile()
  if (reverseFileList.length === 0) {
    callback(new Error(t('productsShow.batchCardProduct.productBackImgTip')))
  }
  callback()
}

const showImg = (arr, isFront) => {
  let res = ''
  if (!arr || arr?.length === 0) return ''
  let imgType = isFront ? 'front' : 'reverse'
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].imageType === imgType) {
      res = arr[i].imageUrl
      break
    }
  }
  return res
}

// 修改价格
const isEditPriceShow = ref(false)
const isEditPriceDisabled = ref(false)
const priceStage = ref()
const priceStageProductId = ref('')
const editPrice = (data, disabled) => {
  isEditPriceShow.value = true
  isEditPriceDisabled.value = disabled
  priceStageProductId.value = data.productId
  priceStage.value = cloneDeep(data.priceStage)
}

//直接声明messageHooks
const message = useMessage()

//更新产品状态
const enableProduct = async (productInfo, isEnable) => {
  message
    .confirm(
      `${
        isEnable
          ? t('productsShow.batchCardProduct.openConfirmTip', { name: productInfo.saleName })
          : t('productsShow.batchCardProduct.closeConfirmTip', { name: productInfo.saleName })
      }`,
      t('common.tip')
    )
    .then(() => {
      // tabelRef.value!.clearFilter()
      const formData = new FormData()
      const dataParams = {
        productId: productInfo.productId,
        statusCode: isEnable
      }
      formData.append('data', JSON.stringify(dataParams))
      try {
        data.tableLoading = true
        ProductApi.updateProductStateApi(formData).then(
          (res) => {
            if (res == true) {
              productInfo.status = {
                statusCode: isEnable ? 'listed' : 'unlisted'
              }
              message.success(t('common.handleSuccess'))
            }
            data.tableLoading = false
          },
          () => {
            data.tableLoading = false
          }
        )
        // getList()
      } finally {
      }
    })
}

onMounted(() => {
  getProductList()
  getProductTypeList()
  getProductStatusList()
  getCustomerList()

  window.onresize = () => {
    data.scrollHeight =
      document.documentElement.clientHeight -
      30 -
      54 -
      125 -
      58 -
      62 -
      35 -
      (data.showOverruleReason ? 125 : 0)
  }
})
</script>

<style lang="less">
.product-desc-label {
  width: 110px !important;
}

.product-data-dialog {
  height: 100%;
  width: 70%;
  position: relative;
  margin: 15px auto !important;
  max-height: ~'calc(100% - 30px)';
  max-width: ~'calc(100% - 30px)';
  display: flex;
  flex-direction: column;

  .el-dialog__body {
    overflow: hidden;
    height: 100%;
  }

  .el-input__inner {
    text-align: left;
  }

  .overrule-reason-desc {
    background-color: #efefef;
    border-radius: 5px;
    padding: 5px 10px;
    margin-bottom: 15px;

    .el-descriptions__header {
      margin-bottom: 5px;
    }

    .el-descriptions__body {
      border-radius: 5px;
      padding: 5px;
    }

    .el-descriptions__table {
      border-radius: 5px;

      .el-descriptions__label {
        color: #f44336;
        margin-right: 0;
      }
    }
  }

  .input-number-product-price {
    .el-input {
      position: relative;

      &::before {
        content: '元';
        position: absolute;
        top: 0;
        right: 10px;
        height: 32px;
        line-height: 32px;
        color: #999;
        font-size: 12px;
        z-index: 15;
      }
    }

    .el-input__inner {
      padding-right: 64px;
    }
  }

  .stage-price-card {
    width: 100%;

    .el-card__header {
      padding: 10px 20px !important;
    }

    .el-card__body {
      padding: 10px !important;
    }

    .el-form-item {
      margin: 9px 0 !important;

      .el-form-item__error {
        z-index: 99999 !important;
      }
    }

    .stage-price-splitter {
      align-items: center;
      justify-content: center;
      display: flex;
    }
  }
}
.card-warp {
  margin-top: 10px;
  display: flex;
  align-items: center;
  &:first-of-type {
    margin-top: 0;
  }
}
.info-item {
  margin-bottom: 15px;
  &:last-of-type {
    margin-bottom: 0;
  }
}
.popover-img {
  width: 330px;
  height: auto;
}
</style>
