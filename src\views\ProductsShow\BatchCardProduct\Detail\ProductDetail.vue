<template>
  <ContentWrap>
    <el-card shadow="hover" style="margin-top: 10px">
      <template #header>
        <span>{{ t('productsShow.batchCardProduct.basicInfo') }}</span>
      </template>
      <el-descriptions border :column="1">
        <el-descriptions-item
          :label="t('productsShow.batchCardProduct.customerName')"
          label-class-name="product-desc-label"
          label-align="right"
          align="left"
        >
          {{ (productForm.customer && productForm.customer.customerName) || '' }}
        </el-descriptions-item>
        <el-descriptions-item
          :label="t('productsShow.batchCardProduct.productType')"
          label-class-name="product-desc-label"
          label-align="right"
          align="left"
        >
          {{ (productForm.productType && productForm.productType.productTypeName) || '' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card shadow="hover" style="margin-top: 10px; margin-bottom: 35px">
      <template #header>
        <span>{{ t('productsShow.batchCardProduct.operateInfo') }}</span>
      </template>
      <el-descriptions border :column="1">
        <el-descriptions-item
          :label="t('productsShow.batchCardProduct.productName')"
          label-class-name="product-desc-label"
          label-align="right"
          align="left"
          >{{ productForm.saleName || '' }}</el-descriptions-item
        >
        <el-descriptions-item
          :label="t('productsShow.batchCardProduct.productImg')"
          label-class-name="product-desc-label"
          label-align="right"
          align="left"
        >
          <UploadFileComp
            ref="frontFileCompRef"
            style="float: left"
            :limit="1"
            :multiple="true"
            :showFileFlag="true"
            :uploadText="t('productsShow.batchCardProduct.frontImg')"
            :url="''"
          />
          <UploadFileComp
            ref="reverseFileCompRef"
            style="padding-left: 10px; display: flex"
            :limit="1"
            :multiple="false"
            :showFileFlag="true"
            :uploadText="t('productsShow.batchCardProduct.backImg')"
            :url="''"
          />
        </el-descriptions-item>
        <el-descriptions-item
          :label="t('productsShow.batchCardProduct.updateTime')"
          label-class-name="product-desc-label"
          label-align="right"
          align="left"
          >{{ productForm.updateDate || '' }}</el-descriptions-item
        >
      </el-descriptions>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import UploadFileComp from '@/views/ProductsShow/BatchCardProduct/components/UploadFileComp.vue'
import * as ProductApi from '@/api/product/merchantCard'
import { inject, watch } from 'vue'
const { t } = useI18n()

const productDetailId = ref()
const reverseFileCompRef = ref()
const frontFileCompRef = ref()

let productForm = ref({
  status: {},
  imgList: [],
  priceStage: {}
})

// 注入流程变量
const processVariables = inject('processVariables')
watch(
  processVariables,
  () => {
    if (processVariables?.value?.productId) {
      productDetailId.value = processVariables.value.productId
    }
  },
  { immediate: true, deep: true }
)

// 监听产品详情ID值变动
watch(
  productDetailId,
  (value) => {
    if (value) {
      getProductDetailList(value)
    }
  },
  { immediate: true, deep: true }
)

/** 获取产品详情 **/
async function getProductDetailList(productId) {
  try {
    productForm.value = await ProductApi.getProductDetailApi(productId)

    if (productForm.value.imgList.length > 0) {
      await frontFileCompRef.value.setShowFileList(
        productForm.value.imgList.filter((img) => img.imageType === 'front')
      )
      await reverseFileCompRef.value.setShowFileList(
        productForm.value.imgList.filter((img) => img.imageType === 'reverse')
      )
    }
  } catch (e) {
    console.error('查询产品详情失败：', e)
  } finally {
  }
}

defineExpose({
  productDetailId
})

onMounted(async () => {})
</script>

<style lang="less" scoped>
:deep(.product-desc-label) {
  width: 110px !important;
}
</style>
