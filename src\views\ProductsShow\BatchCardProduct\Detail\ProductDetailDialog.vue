<template>
  <el-dialog
    v-model="data.showProductDialog"
    :title="t('productsShow.batchCardProduct.viewProduct')"
    class="product-data-dialog"
    :before-close="props.closeDialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
    draggable
    center
  >
    <el-scrollbar wrap-class="product-wrap">
      <ProductDetail ref="productDetailRef" />
    </el-scrollbar>
  </el-dialog>
</template>

<script setup lang="ts">
defineOptions({
  name: 'BatchCardProductDetailDialog'
})

import ProductDetail from '@/views/ProductsShow/BatchCardProduct/Detail/ProductDetail.vue'
const { t } = useI18n()

const productDetailRef = ref()

const data = reactive({
  showProductDialog: true,
  scrollHeight: 0
})

const props = defineProps({
  productDetailId: {
    type: String,
    required: true
  },
  closeDialog: {
    type: Function,
    required: true
  }
})

onMounted(async () => {
  if (props.productDetailId) {
    nextTick(() => {
      productDetailRef.value.productDetailId = props.productDetailId
    })
  }
})
</script>
