<template>
  <el-dialog
    :title="
      props.disabled
        ? t('productsShow.batchCardProduct.viewPrice')
        : t('productsShow.batchCardProduct.editPrice')
    "
    v-model="isShow"
    :before-close="handleClose"
    :close-on-click-modal="false"
    append-to-body
    style="width: 1000px"
  >
    <el-form ref="editRef" :model="editQuery" class="mt-20px" :rules="rules">
      <el-form-item :label="t('productsShow.batchCardProduct.priceType')" prop="stage">
        <el-radio-group v-model="editQuery.stage" :disabled="props.disabled">
          <el-radio :value="true">{{ t('productsShow.batchCardProduct.ladderPrice') }}</el-radio>
          <el-radio :value="false">{{ t('productsShow.batchCardProduct.fixedOffer') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        :label="t('productsShow.batchCardProduct.fixedOffer')"
        prop="price"
        v-if="!editQuery.stage"
      >
        <el-input-number
          v-model="editQuery.price"
          :min="1"
          :max="9999999"
          :precision="2"
          @paste="priceInputPaste"
          @keydown="priceInputInhibit"
          :disabled="props.disabled"
        />
      </el-form-item>
      <el-form-item :label="t('productsShow.batchCardProduct.ladderPrice')" prop="priceList" v-else>
        <el-table :data="editQuery.priceList" max-height="400" border>
          <el-table-column
            prop="lowerLimit"
            :label="t('productsShow.batchCardProduct.intervalStart')"
            minWidth="150"
            headerAlign="center"
          >
            <template #default="scope">
              <el-input-number
                v-model="scope.row.lowerLimit"
                :min="0"
                :max="9999999"
                :precision="0"
                @paste="priceInputPaste"
                @keydown="priceInputInhibit"
                :disabled="props.disabled"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="upperLimit"
            :label="t('productsShow.batchCardProduct.intervalEnd')"
            minWidth="150"
            headerAlign="center"
          >
            <template #default="scope">
              <el-input-number
                v-model="scope.row.upperLimit"
                :min="1"
                :max="9999999"
                :precision="0"
                @paste="priceInputPaste"
                @keydown="priceInputInhibit"
                :disabled="props.disabled"
              />
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="currency"
            :label="t('productsShow.batchCardProduct.currency')"
            minWidth="100"
            headerAlign="center"
          >
            <template #default="scope">
              <el-select
                v-model="scope.row.currency"
                :placeholder="t('productsShow.batchCardProduct.currencyPlaceholder')"
                filterable
                clearable
                :disabled="props.disabled"
              >
                <el-option
                  v-for="item in currencyList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column> -->
          <el-table-column
            prop="price"
            :label="t('productsShow.batchCardProduct.intervalPrice')"
            minWidth="150"
            headerAlign="center"
          >
            <template #default="scope">
              <el-input-number
                v-model="scope.row.price"
                :min="0"
                :max="9999999"
                :precision="2"
                @paste="priceInputPaste"
                @keydown="priceInputInhibit"
                :disabled="props.disabled"
              />
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="condition"
            :label="t('productsShow.batchCardProduct.intervalSymbol')"
            minWidth="80"
            headerAlign="center"
          >
            <template #default="scope">
              <el-select
                v-model="scope.row.condition"
                :placeholder="t('productsShow.batchCardProduct.intervalSymbolPlaceholder')"
                filterable
                clearable
                :disabled="props.disabled"
              >
                <el-option
                  v-for="item in conditionList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column> -->
          <el-table-column
            fixed="right"
            :label="t('makeCard.table.operate')"
            minWidth="60"
            v-if="!props.disabled"
          >
            <template #default="{ $index }">
              <el-button type="primary" link @click="delRow($index)">
                {{ t('common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button class="add" @click="addRow" v-if="!props.disabled">{{
          t('productsShow.batchCardProduct.addRow')
        }}</el-button>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">{{ t('common.cancel') }}</el-button>
      <el-button type="primary" :loading="isLoading" @click="handleSubmit" v-if="!props.disabled">{{
        t('common.save')
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { updatePriceStageApi } from '@/api/product/productInfo'
import { sortBy, cloneDeep } from 'lodash-es'
//import { useDictStoreWithOut } from '@/store/modules/dict'
//const dictStore = useDictStoreWithOut()

const { t } = useI18n()
const props = defineProps<{
  modelValue: boolean
  disabled: boolean
  priceStage: any
  productId: string
}>()

const isShow = computed(() => {
  return props.modelValue
})

const isLoading = ref(false)
interface IEditQuery {
  price: undefined | number
  priceList: IPriceList[]
  stage: boolean
}
interface IPriceList {
  condition?: string
  currency?: string
  lowerLimit: any
  price?: undefined | number
  upperLimit: any
}
const editQuery = ref<IEditQuery>({
  price: 0,
  priceList: [],
  stage: false
})

// 监听表单数据
watch(
  () => props.priceStage,
  () => {
    if (!props.priceStage) return
    editQuery.value = cloneDeep(props.priceStage)
    editQuery.value.priceList = sortBy(editQuery.value.priceList, (x) => x.upperLimit)
  },
  { immediate: true, deep: true }
)

// 区间符号
const conditionList = ref([
  {
    label: t('cardProductBusiness.proxyCustomerToOrder.lessThan'),
    value: 'lt'
  },
  {
    label: t('productsShow.batchCardProduct.equal'),
    value: 'eq'
  },
  {
    label: t('cardProductBusiness.proxyCustomerToOrder.greaterThan'),
    value: 'gt'
  }
])

// 币种
//const currencyList = ref(dictStore.getDictMap['coin_type'])

const editRef = ref()
// 校验规则
/** 表单校验方法start */
const validateAddr = (_rule: any, _value: any, callback: any) => {
  try {
    editQuery.value.priceList.forEach((item, index, arr) => {
      if (
        (!item.lowerLimit && index !== 0) ||
        (!item.upperLimit && index !== editQuery.value.priceList?.length - 1)
      ) {
        throw new Error(t('productsShow.batchCardProduct.intervalTip'))
      }
      //从第二项开始判断开始是否小于结束
      if (index > 0) {
        if (item.lowerLimit <= arr[index - 1].upperLimit) {
          throw new Error(t('productsShow.batchCardProduct.intervalBackTip'))
        }
      }
      if (item.lowerLimit && item.upperLimit) {
        if (item.lowerLimit >= item.upperLimit) {
          throw new Error(t('productsShow.batchCardProduct.intervalNextTip'))
        }
      }
      // if (!item.currency) {
      //   throw new Error(t('productsShow.batchCardProduct.currencyPlaceholder'))
      // }
      if (!item.price) {
        throw new Error(t('productsShow.batchCardProduct.intervalPricePlaceholder'))
      }
      // if (!item.condition) {
      //   throw new Error(t('productsShow.batchCardProduct.intervalSymbolPlaceholder'))
      // }
    })
    callback()
  } catch (error) {
    callback(error)
  }
}

const rules = reactive({
  stage: [
    {
      required: true,
      message: t('productsShow.batchCardProduct.priceTypePlaceholder'),
      trigger: 'change'
    }
  ],
  priceList: [{ required: false, validator: validateAddr, trigger: 'change' }]
})

const emit = defineEmits(['update:modelValue', 'getList'])
const handleClose = () => {
  emit('update:modelValue', false)
}
const handleSubmit = async () => {
  await editRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isLoading.value = true
        const queryFrom = {
          productId: props.productId,
          priceStage: editQuery.value
        }
        await updatePriceStageApi(queryFrom)
        ElMessage.success(t('productsShow.batchCardProduct.editSuccess'))
        emit('getList')
        handleClose()
      } catch (err) {
      } finally {
        isLoading.value = false
      }
    }
  })
}

// 添加阶梯价
const addRow = () => {
  const length = editQuery.value?.priceList?.length
  let lowerLimit = 0
  if (length > 0 && editQuery.value.priceList[length - 1].upperLimit) {
    const upperLimitTemp = editQuery.value.priceList[length - 1].upperLimit || 0
    lowerLimit = upperLimitTemp + 1
  }
  let currency: string | undefined = ''
  if (length > 0 && editQuery.value.priceList[length - 1].currency) {
    currency = editQuery.value.priceList[length - 1].currency
  }
  editQuery.value.priceList.push({
    condition: 'eq', // 默认值 不显示
    price: 0,
    currency,
    lowerLimit,
    upperLimit: null
  })
}
// 删除一行阶梯价
const delRow = (index) => {
  editQuery.value.priceList.splice(index, 1)
  editRef.value?.validateField('priceList') // 有时候删除之后触发验证失败，再次删除不合法数据后，需要手动触发验证
}

/**
 * 价格输入框中禁止输入e、+、-
 */
const InhibitCharList = ['e', 'E', '+', '-']
const priceInputInhibit = (e) => {
  const key = e.key
  if (InhibitCharList.includes(key)) {
    e.returnValue = false
  } else {
    e.returnValue = true
  }
}

/**
 * 价格输入框粘贴时判断剪贴板内容
 */
const priceInputPaste = (e) => {
  const cData = e.clipboardData.getData('text')
  const charSet = new Set(InhibitCharList)
  const mixedList = Array.of(...cData).filter((item) => charSet.has(item))
  if (Number.isNaN(Number.parseFloat(cData)) || mixedList.length > 0) {
    e.preventDefault()
  }
}
</script>

<style scoped lang="scss">
.add {
  width: 100%;
  height: 50px;
  margin-top: 20px;
}
</style>
