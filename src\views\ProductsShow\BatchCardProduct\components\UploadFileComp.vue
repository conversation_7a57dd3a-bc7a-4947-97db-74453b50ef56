<template>
  <div>
    <el-upload
      :disabled="props.showFileFlag"
      ref="uploadModuleRef"
      :class="data.displayUpload ? 'custom-upload-comp-disabled' : 'custom-upload-comp'"
      :action="props.url"
      :headers="{ Authorization: data.token === null ? '' : data.token }"
      :limit="props.limit"
      :multiple="props.multiple"
      v-model:file-list="data.fileList"
      list-type="picture-card"
      :on-preview="onPreview"
      :data="props.uploadData"
      :on-success="onFileUploadSuccess"
      :before-upload="beforeUpload"
      :on-change="fileChange"
      :auto-upload="props.autoUpload"
      :on-remove="onRemove"
      :on-exceed="onExceedHandle"
      :accept="props.accept"
    >
      <div class="el-upload__text" style="text-align: center">
        <el-icon><Plus /></el-icon>
        <p style="color: darkgray">{{
          t('productsShow.batchCardProduct.pleaseUpload') + props.uploadText
        }}</p>
      </div>
    </el-upload>
    <!--    <div class="el-upload__tip" v-if="!props.showFileFlag">-->
    <!--      &lt;!&ndash;      <span class='text' v-if='props.limit > 0'> 数量限制：{{ props.limit }}</span><br />&ndash;&gt;-->
    <!--      <span class="text">大小限制：2M</span><br />-->
    <!--      <span class="text">支持扩展名：{{ props.fileSuffixType }}</span>-->
    <!--    </div>-->
    <div class="reupload-button" v-if="props.showCancelUpload">
      <el-tooltip
        v-if="props.showCancelUpload"
        :content="t('productsShow.batchCardProduct.cancelReUploadFile')"
        effect="dark"
        placement="top"
      >
        <el-button type="danger" size="mini" :icon="Close" @click="cancelShowUploadComp">{{
          t('productsShow.batchCardProduct.cancelReUpload')
        }}</el-button>
      </el-tooltip>
    </div>

    <el-image-viewer
      v-if="data.showImageDialog"
      @close="data.showImageDialog = false"
      teleported
      :zoom-rate="1.2"
      :url-list="[data.dialogImageUrl]"
    />

    <!--    <el-dialog
      v-model="data.showImageDialog"
      :title="props.uploadText"
      append-to-body
      draggable
      center
    >
      <img w-full :src="data.dialogImageUrl" alt="预览文件" />
    </el-dialog>-->
  </div>
</template>

<script lang="ts">
const { t } = useI18n()
</script>

<script setup lang="ts">
import { Plus, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
// import { withDefaults, defineProps } from "vue"
import { unref, watch, computed } from 'vue'
import { RouteLocationNormalizedLoaded } from 'vue-router'
import * as ProductApi from '@/api/product/merchantCard'

const uploadModuleRef = ref()

const data = reactive({
  token: '',
  showImageDialog: false,
  dialogImageUrl: '',

  displayUpload: false,
  fileList: []
})

const props = defineProps({
  limit: {
    type: Number
  },

  multiple: {
    type: Boolean,
    default: false
  },

  accept: {
    type: String,
    default: t('productsShow.batchCardProduct.file')
  },

  uploadText: {
    type: String,
    default: t('productsShow.batchCardProduct.file')
  },

  fileSize: {
    type: Number,
    default: 100
  },

  fileSuffixType: {
    type: String,
    default: 'rar、zip、doc、docx、xls、xlsx、pdf、jpg、jpeg、png、bmp、gif'
  },

  //是否自动上传
  autoUpload: {
    type: Boolean,
    default: false
  },

  showCancelUpload: {
    type: Boolean,
    default: false
  },

  url: {
    type: String,
    required: true
  },

  uploadData: {
    type: Object,
    required: false
  },

  mergeUpload: {
    type: Boolean,
    default: false
  },

  showFileFlag: {
    type: Boolean,
    default: false
  }
})

toRefs(data)
const { uploadData } = toRefs(props)
const emit = defineEmits(['e-cancel-upload-comp', 'e-success', 'e-delete-file'])

function onPreview(file) {
  console.info(file)
  data.showImageDialog = true
  data.dialogImageUrl = file.url
}

// 获取组件文件列表
const getFile = async () => {
  return data.fileList || []
}

const submitFile = async (obj?) => {
  if (data.fileList && data.fileList.length > 0) {
    if (props.mergeUpload) {
      // 合并文件上传
      let formData = new FormData()
      formData.append('data', JSON.stringify(uploadData.value))
      data.fileList.forEach((file) => {
        formData.append('files', file.raw)
      })
      try {
        const res = await ProductApi.addOrUpdateProductApi(props.url, formData)
        if (res) {
          emit('e-success', res, data.fileList)
        } else {
          obj.data.isSaving = false
        }
      } catch (e) {
        console.info('合并文件上传异常：', e)
      }
    } else {
      // 单个文件上传
      unref(uploadModuleRef)?.submit()
    }
  } else {
    ElMessage.error(obj.errorMessage)
    obj.data.isSaving = false
  }
}

function clear() {
  unref(uploadModuleRef)?.clearFiles()
  data.fileList = []
}

// 移除文件时同步文件列表
function onRemove(file, fileList) {
  ElMessage.warning(
    t('productsShow.batchCardProduct.removeTip', {
      fileName: file.name
    })
  )
  emit('e-delete-file', file)
  data.fileList = fileList
}

// 文件上传具体业务
function beforeUpload(file) {
  return verifyFile(file)
}

function fileChange(file, fileList) {
  data.fileList = fileList
  if (!props.autoUpload) {
    verifyFile(file)
  }
}

function onFileUploadSuccess(response, file, fileList) {
  console.info('file', file)
  console.info('fileList', fileList)
  if (response.success) {
    if (props.autoUpload) {
      //自动上传
      submitFile()
    }
  } else {
    // 上传失败时重置文件状态
    file.status = 'ready'
    file.percentage = 0
  }
  emit('e-success', response, file, fileList)
}

function verifyFile(file) {
  if (file.size > props.fileSize * 1048576) {
    ElMessage.error(
      t('productsShow.batchCardProduct.sizeTip', {
        size: props.fileSize
      })
    )
    data.fileList.splice(data.fileList.indexOf(file), 1)
    return false
  }
  let fileSuffix = /\.(?:jpg|png)$/.test(file.name)
  if (!fileSuffix) {
    ElMessage.error(
      t('productsShow.batchCardProduct.formatTip', {
        format: props.fileSuffixType
      })
    )
    data.fileList.splice(data.fileList.indexOf(file), 1)
  }
  return fileSuffix
}

function onExceedHandle(files, fileList) {
  if (props.limit === 1) {
    nextTick(() => {
      clear()
      unref(uploadModuleRef)?.handleStart(files[files.length - 1])
      ElMessage.warning(t('productsShow.batchCardProduct.onlyOneTip'))
    })
  } else {
    ElMessage.error(t('productsShow.batchCardProduct.numberOutOfLimit'))
  }
}

function cancelShowUploadComp() {
  emit('e-cancel-upload-comp')
}

watch(
  () => data.fileList,
  (newVal, oldVal) => {
    data.displayUpload = newVal.length >= props.limit
  },
  { immediate: true, deep: true }
)

/** 设置图片回显 **/
const setShowFileList = async (fileList: Array<any>) => {
  setTimeout(() => {
    if (fileList && fileList.length > 0) {
      let showFileList = []
      fileList.forEach((file) => {
        showFileList.push({ name: file.imageName, url: file.imageUrl, imageId: file.imageId })
      })
      data.fileList = showFileList
    }
  }, 50)
}

onMounted(async () => {})

defineExpose({
  data,
  getFile,
  submitFile,
  setShowFileList
})
</script>
<style lang="less">
.custom-upload-comp-disabled {
  .el-upload.el-upload--picture-card {
    display: none;
  }
}
</style>

<style lang="less" scoped>
.el-upload__tip {
  line-height: 16px;
  //position: absolute;
  //bottom: 5px;
  //left: 15px;
  .text {
    /*display: flex;*/
    color: darkgray;
  }

  .limit-tip {
    position: absolute;
    top: 0;
    margin: 0 10px;
    color: darkgray;
  }

  .size-tip {
    position: absolute;
    top: 20px;
    margin: 0 10px;
    color: darkgray;
  }
}
</style>
