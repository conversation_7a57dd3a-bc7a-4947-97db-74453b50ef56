<template>
  <ContentWrap>
    <el-alert
      type="info"
      style="margin-bottom: 10px; height: 80px"
      :closable="false"
      effect="light"
      class="show-project-customer"
    >
      <template #default>
        <!-- <span class="required-field-tag">
          <span style="margin-right: 20px">项目名称</span>{{ productForm.relateProjectName }}
        </span> -->
        <span>
          <span style="margin-right: 20px">{{ t('productsShow.diyCardProduct.serviceName') }}</span
          >{{ productForm.applyServiceName }}
        </span>
      </template>
    </el-alert>
    <!--    <el-scrollbar wrap-class="product-wrap" :max-height="data.scrollHeight">-->
    <el-alert
      v-if="productForm.statusName === '已驳回'"
      type="error"
      :title="t('productsShow.diyCardProduct.causeOfRejection') + ':'"
      :description="productForm.overruleReason"
      style="margin-bottom: 10px; height: 100px"
      :closable="false"
      effect="light"
    />
    <el-form
      :model="productForm"
      ref="productFormRef"
      label-position="right"
      style="text-align: left"
      :labelWidth="ifEn ? '240px' : '135px'"
    >
      <el-collapse v-model="data.expandCollapseList" class="product-collapse">
        <el-collapse-item :title="t('productsShow.diyCardProduct.basicInfo')" name="1">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.cardDraftCode')">
                {{ productForm.cardCode }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.cardStyleName')">
                {{ productForm.cardName }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.cardOtherOfCName')">
                {{ productForm.cardAliasC }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.cardType')">
                {{ getDictLabel('diy_product_card_type', productForm.cardTypeCode) }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.cardIdent')" prop="cardAliasOut">
                {{ productForm.cardAliasOut }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.subcategory')" prop="catalog">
                {{ productForm.catalogName }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item
                :label="t('productsShow.diyCardProduct.customerProductName')"
                prop="cardProductName"
              >
                {{ productForm.cardProductName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="t('productsShow.diyCardProduct.customerProductCode')"
                prop="cardProductCode"
              >
                {{ productForm.cardProductCode }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item
                :label="t('productsShow.diyCardProduct.financeProductCode')"
                prop="financeProductCode"
              >
                {{ productForm.financeProductCode }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="t('productsShow.diyCardProduct.ProductCode')"
                prop="productCode"
              >
                {{ productForm.productCode }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item
                :label="t('productsShow.diyCardProduct.visibilityRange')"
                prop="rangeList"
              >
                <div v-if="productForm.allRangeFlag">{{
                  t('productsShow.diyCardProduct.allCity')
                }}</div>
                <div v-else>
                  <el-button
                    type="primary"
                    text
                    bg
                    v-for="(range, index) in productForm.rangeList"
                    :key="index"
                    style="margin: 5px 0 0 5px"
                  >
                    {{ range.provinceName + ' / ' + range.cityName }}
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="productForm.applyServiceCardType === '3'">
              <el-form-item :label="t('productsShow.diyCardProduct.supplier')" prop="supplierName">
                {{ productForm.supplierName }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item
                :label="t('productsShow.diyCardProduct.productIntro')"
                prop="productDesc"
              >
                {{ productForm.productDesc }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.designFile')" prop="designFile">
                <el-row :gutter="10" style="width: 100%">
                  <el-col :span="20" v-if="productForm.designFile">
                    <el-tooltip :content="productForm.designFile">
                      <el-text type="primary" truncated>
                        {{ productForm.designFile }}
                      </el-text>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="4" style="display: flex; justify-content: flex-start">
                    <el-button
                      v-if="productForm.designFile"
                      text
                      type="primary"
                      @click="
                        downloadProductPlatformFile(
                          productForm.designFileUrl,
                          productForm.designFile
                        )
                      "
                    >
                      {{ t('productsShow.diyCardProduct.download') }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <!-- 稿样文件 -->
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.draftFile')" prop="exampleFile">
                <el-row :gutter="10" style="width: 100%">
                  <el-col :span="16" v-if="productForm.exampleFile">
                    <el-tooltip :content="productForm.exampleFile">
                      <el-text type="primary" truncated>
                        {{ productForm.exampleFile }}
                      </el-text>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="8" style="display: flex; justify-content: flex-start">
                    <el-button
                      v-if="productForm.exampleFile"
                      text
                      type="primary"
                      @click="
                        downloadProductPlatformFile(
                          productForm.exampleFileUrl,
                          productForm.exampleFile
                        )
                      "
                    >
                      {{ t('productsShow.diyCardProduct.download') }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item :label="t('productsShow.diyCardProduct.file3D')" prop="threeDimFile">
                <el-row :gutter="10" style="width: 100%">
                  <el-col :span="20" v-if="productForm.threeDimFile">
                    <el-tooltip :content="fileNameFormatter(productForm.threeDimFile)">
                      <el-text type="primary" truncated>
                        {{ fileNameFormatter(productForm.threeDimFile) }}
                      </el-text>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="4" style="display: flex; justify-content: flex-start">
                    <el-button
                      v-if="productForm.threeDimFile"
                      text
                      type="primary"
                      @click="downloadFile(productForm.threeDimFileUrl)"
                    >
                      {{ t('productsShow.diyCardProduct.download') }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10">
            <el-form-item
              :label="t('productsShow.diyCardProduct.customAttribute')"
              prop="customAttribute"
              class="attribute-form-item"
            >
              <DiyAttribute
                :disabled="true"
                ref="diyAttributeRef"
                :currentProjectService="currentProjectService"
              />
            </el-form-item>
          </el-row>
        </el-collapse-item>

        <el-collapse-item :title="t('productsShow.diyCardProduct.imgInfo')" name="2">
          <el-form label-position="top">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item :label="t('productsShow.diyCardProduct.maskImg')" label-pos>
                  <div v-if="data.imageInfoForm.maskImage">
                    <div class="select-image-div-finish">
                      <el-image
                        :src="data.imageInfoForm.maskImage.fileUrl"
                        fit="scale-down"
                        preview-teleported
                        :initial-index="0"
                        :preview-src-list="[data.imageInfoForm.maskImage.fileUrl]"
                      />
                    </div>
                  </div>

                  <div v-else class="select-image-div">
                    <span>{{ t('productsShow.diyCardProduct.noMaskImg') }}</span>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item :label="t('productsShow.diyCardProduct.frontImg')">
                  <div v-if="data.imageInfoForm.frontImage">
                    <div class="select-image-div-finish">
                      <el-image
                        :src="data.imageInfoForm.frontImage.fileUrl"
                        fit="scale-down"
                        preview-teleported
                        :initial-index="0"
                        :preview-src-list="[data.imageInfoForm.frontImage.fileUrl]"
                      />
                    </div>
                    <div
                      v-if="
                        data.imageInfoForm.frontImage.locationInfo &&
                        data.imageInfoForm.frontImage.locationInfo.pixelInfo
                      "
                    >
                      <el-button
                        text
                        bg
                        type="success"
                        style="margin-top: 5px"
                        @click="showImageLocationDialog"
                      >
                        {{ t('productsShow.diyCardProduct.viewSetRegion') }}
                      </el-button>
                      <el-form
                        :model="data.imageInfoForm.frontImage.locationInfo.pixelInfo"
                        label-width="0px"
                        label-position="right"
                        class="front-image-location-form"
                      >
                        <el-row :gutter="10">
                          <el-col :span="6">
                            <el-form-item prop="top" label="">
                              <el-button text bg type="primary">
                                {{ t('productsShow.diyCardProduct.top') }}：
                                {{ data.imageInfoForm.frontImage.locationInfo.pixelInfo.top }} px
                              </el-button>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item prop="bottom" label="">
                              <el-button text bg type="primary">
                                {{ t('productsShow.diyCardProduct.bottom') }}：
                                {{ data.imageInfoForm.frontImage.locationInfo.pixelInfo.bottom }} px
                              </el-button>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item prop="left" label="">
                              <el-button text bg type="primary">
                                {{ t('productsShow.diyCardProduct.left') }}
                                {{ data.imageInfoForm.frontImage.locationInfo.pixelInfo.left }} px
                              </el-button>
                            </el-form-item>
                          </el-col>
                          <el-col :span="6">
                            <el-form-item prop="right" label="">
                              <el-button text bg type="primary">
                                {{ t('productsShow.diyCardProduct.right') }}：
                                {{ data.imageInfoForm.frontImage.locationInfo.pixelInfo.right }} px
                              </el-button>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </div>
                  </div>

                  <div v-else class="select-image-div">
                    <span>{{ t('productsShow.diyCardProduct.noFrontImg') }}</span>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item :label="t('productsShow.diyCardProduct.cardBackImg')">
                  <div v-if="data.imageInfoForm.backImage">
                    <div class="select-image-div-finish">
                      <el-image
                        :src="data.imageInfoForm.backImage.fileUrl"
                        fit="scale-down"
                        preview-teleported
                        :initial-index="0"
                        :preview-src-list="[data.imageInfoForm.backImage.fileUrl]"
                      />
                    </div>
                  </div>

                  <div v-else class="select-image-div">
                    <span>{{ t('productsShow.diyCardProduct.noBackImg') }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row style="margin-top: 20px">
              <el-col
                :span="
                  data.imageInfoForm.stickImageGroup &&
                  data.imageInfoForm.stickImageGroup.length > 0
                    ? 24
                    : 8
                "
              >
                <el-form-item
                  prop="stickImageGroup"
                  :label="t('productsShow.diyCardProduct.cardImg')"
                >
                  <div
                    style="width: 100%"
                    v-if="
                      data.imageInfoForm.stickImageGroup &&
                      data.imageInfoForm.stickImageGroup.length > 0
                    "
                  >
                    <el-button
                      text
                      bg
                      type="success"
                      class="check-image-button"
                      v-for="(stickImage, index) in data.imageInfoForm.stickImageGroup"
                      :key="index"
                    >
                      {{ stickImage.stickGroupName }}
                    </el-button>
                  </div>
                  <div v-else class="select-image-group-div">
                    <span>{{ t('productsShow.diyCardProduct.noGroupImg') }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row style="margin-top: 20px" v-if="productForm?.cardTypeCode === '4'">
              <el-col :span="data?.imageInfoForm?.elementImageGroup?.length > 0 ? 24 : 8">
                <el-form-item
                  prop="elementImageGroup"
                  :label="t('productsShow.diyCardProduct.elementSplicing')"
                >
                  <div
                    style="width: 100%"
                    v-if="data?.imageInfoForm?.elementImageGroup?.length > 0"
                  >
                    <el-row
                      :gutter="10"
                      style="margin-bottom: 5px"
                      v-for="(elementImage, index) in data.imageInfoForm.elementImageGroup"
                      :key="index"
                    >
                      <el-col :span="4">
                        <el-button
                          text
                          bg
                          type="success"
                          style="width: 100% !important"
                          class="check-image-button"
                        >
                          {{ elementImage.elementGroupName }}
                        </el-button>
                      </el-col>
                      <el-col :span="20">
                        <el-button
                          text
                          bg
                          type="success"
                          v-for="(childrenElementImage, eIndex) in elementImage.checkChildren"
                          :key="eIndex"
                          class="check-image-button"
                        >
                          {{ childrenElementImage.elementGroupName }}
                        </el-button>
                      </el-col>
                    </el-row>
                  </div>
                  <div v-else class="select-image-group-div">
                    <span>{{ t('productsShow.diyCardProduct.noElementSplicing') }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item :title="t('productsShow.diyCardProduct.setPrice')" name="3">
          <el-row>
            <PriceSetting :productDetailFlag="true" ref="priceSettingRef" />
          </el-row>
          <el-row>
            <el-text>{{ t('productsShow.diyCardProduct.defaultTip') }}</el-text>
          </el-row>
        </el-collapse-item>

        <el-collapse-item :title="t('productsShow.diyCardProduct.setImgAudit')" name="4">
          <VerifySetting :productDetailFlag="true" ref="verifySettingRef" />
        </el-collapse-item>
      </el-collapse>
    </el-form>
    <!--    </el-scrollbar>-->
  </ContentWrap>

  <!-- 正面图定位点设定 -->
  <ImageLocation
    ref="imageLocationRef"
    v-if="data.showImageLocationDialog"
    :dialogTitle="t('productsShow.diyCardProduct.viewHalfImgSetRegion')"
    :closeDialog="closeImageLocationDialog"
  />
</template>

<script setup lang="ts">
import { getDictLabel } from '@/utils/dict'

import * as ProductApi from '@/api/product/diyCard'
import { convertImageList } from '@/views/ProductsShow/DiyCardProduct/diyCardProduct'
import DiyAttribute from '@/views/ProductsShow/DiyCardProduct/components/DiyAttribute.vue'
import PriceSetting from '@/views/ProductsShow/DiyCardProduct/components/PriceSetting.vue'
import VerifySetting from '@/views/ProductsShow/DiyCardProduct/components/VerifySetting.vue'
import ImageLocation from '@/views/ProductsShow/DiyCardProduct/components/ImageLocation.vue'
import useMessage from '@/utils/useMessage'
const message = useMessage()
import { useRoute } from 'vue-router'
let route = useRoute()
const { t, ifEn } = useI18n()
// const emit = defineEmits()

const diyAttributeRef = ref()
const imageInfoRef = ref()
const priceSettingRef = ref()
const verifySettingRef = ref()
const productFormRef = ref()
const imageLocationRef = ref()

const props = defineProps<{ productDetailId: string }>()
const productDetailId = computed(() => props.productDetailId)
const currentProjectService = ref({})
let productForm = ref({
  cardId: '',
  cardCodeObj: {},
  cardNameObj: {},
  catalog: {}
})

const data = reactive({
  imageInfoForm: {},
  expandCollapseList: ['1', '2', '3', '4'],
  scrollHeight: 0,

  showImageLocationDialog: false
})

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.indexOf('_') + 1, fileName.length)
}

const downloadFile = async (fileUrl) => {
  let fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1, fileUrl.length)
  try {
    const res = await ProductApi.downloadFileApi(fileUrl)
    if (res === null || res?.data === null || res?.data?.size === 0) {
      message.error(t('productsShow.diyCardProduct.fileDownloadErr'))
      console.error('文件下载失败', res)
      return
    }
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = await fileNameFormatter(fileName)
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.info(e)
  }
}

// 下载设计文件、稿样文件
const downloadProductPlatformFile = async (fileUrl, fileName?) => {
  // 文件名 + 后缀
  let downFileName = fileName + fileUrl.substring(fileUrl.lastIndexOf('.'), fileUrl.length)
  try {
    const params = {
      fileUrl: fileUrl,
      fileName: downFileName
    }
    const res = await ProductApi.downloadProductPlatformFileApi(params)
    if (res === null || res?.data === null || res?.data?.size === 0) {
      message.error(t('productsShow.diyCardProduct.fileDownloadErr'))
      console.error('文件下载失败', res)
      return
    }
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = downFileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  }
}

const showImageLocationDialog = async () => {
  data.showImageLocationDialog = true
  nextTick(async () => {
    await imageLocationRef?.value?.setImageLocation(
      data.imageInfoForm.frontImage.locationInfo.pixelInfo,
      data.imageInfoForm.frontImage.fileUrl,
      true
    )
  })
}

const closeImageLocationDialog = async () => {
  data.showImageLocationDialog = false
}

// 注入流程变量
const processVariables = inject('processVariables')
watch(
  processVariables,
  () => {
    if (processVariables?.value?.diyCardId) {
      productDetailId.value = processVariables.value.diyCardId
    }
    // productDetailId.value = '1682301403468115969'
  },
  { immediate: true, deep: true }
)

// 监听产品详情ID值变动
watch(
  productDetailId,
  (value) => {
    if (value) {
      getProductDetailList(value)
    }
  },
  { immediate: true, deep: true }
)

/** 获取产品详情 **/
async function getProductDetailList(cardId) {
  try {
    productForm.value = await ProductApi.getProductDetailApi(cardId)

    // 设置定制属性信息
    currentProjectService.value = {
      applyServiceId: productForm.value.applyServiceId
    }
    await diyAttributeRef.value.setAttributeList(productForm.value.attributeList)
    // 设置图片信息
    await convertImageList(data.imageInfoForm, productForm.value.imageList)
    // 设置价格信息
    await priceSettingRef.value.setPriceList(
      productForm.value.price,
      productForm.value.salePrice,
      productForm.value.priceList
    )
    // 设置图审信息
    await verifySettingRef.value.setVerifyServiceList(
      productForm.value.diyVerifyFlag,
      productForm.value.verifyList
    )
  } catch (e) {
    console.error('查询产品详情失败：', e)
  } finally {
  }
}

defineExpose({
  productDetailId
})

onMounted(async () => {
  // 标签页打开方式
  if (route.query.cardId) {
    await getProductDetailList(route.query.cardId)
  }

  // 弹窗 margin:30, 弹窗header:54, 项目服务展示：90 驳回原因：110
  // let reasonHeight = ['已驳回'].includes(productForm.value.statusName) ? 110 : 0
  // data.scrollHeight = document.documentElement.clientHeight - 30 - 54 - 90 - 35 - 75
  // window.onresize = () => {
  //   data.scrollHeight = document.documentElement.clientHeight - 30 - 54 - 90 - 35 - 75
  // }
})
</script>

<style scoped lang="less">
.attribute-form-item {
  width: 99%;
  .el-descriptions__label.el-descriptions__cell.is-bordered-label {
    width: 135px;
  }
}

:deep(.el-checkbox__input.is-disabled + span.el-checkbox__label) {
  color: #263238;
  cursor: not-allowed;
}
:deep(.el-radio__input.is-disabled + span.el-radio__label) {
  color: #263238;
  cursor: not-allowed;
}

:deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
  background-color: white;
  border-color: #607d8b;
  color: white;
}
:deep(.el-radio__input.is-disabled .el-radio__inner) {
  background-color: white;
  border-color: #607d8b;
  color: white;
}

:deep(.el-checkbox__input.is-disabled .el-checkbox__inner)::after {
  border-color: #263238;
  color: white;
}
:deep(.el-radio__input.is-disabled .el-radio__inner)::after {
  border-color: #263238;
  background-color: #263238;
}

.show-project-customer {
  :deep(.el-alert__content) {
    width: 100%;
    .el-alert__description {
      color: #616161 !important;
      height: 32px;
      line-height: 32px;
      font-size: 16px;
      display: flex;
      justify-content: space-between;
      width: 80%;
    }
  }
}

.select-image-div {
  background-color: #e0e0e0;
  width: 375px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.select-image-div-finish {
  width: 375px;
  height: 150px;
  display: flex;
  justify-content: left;
  cursor: pointer;
}

.select-image-group-div {
  background-color: #e0e0e0;
  width: 375px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.product-collapse {
  :deep(.el-collapse-item__header) {
    font-size: 14px;
    font-weight: bold;
    &::before {
      content: '';
      position: relative;
      top: 0;
      left: 0;
      z-index: 999;
      width: 10px;
      height: 100%;
      background-color: #4caf50;
      margin-right: 10px;
    }
  }
}
</style>
