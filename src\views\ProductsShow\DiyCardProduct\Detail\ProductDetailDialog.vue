<template>
  <el-dialog
    v-model="model"
    :title="t('productsShow.diyCardProduct.viewProduct')"
    class="product-data-dialog"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
    draggable
    center
  >
    <el-scrollbar wrap-class="product-wrap">
      <ProductDetail ref="productDetailRef" :productDetailId="props.productDetailId" />
    </el-scrollbar>
  </el-dialog>
</template>

<script setup lang="ts">
defineOptions({
  name: 'diyCardProductDetailDialog'
})

import ProductDetail from '@/views/ProductsShow/DiyCardProduct/Detail/ProductDetail.vue'
const { t } = useI18n()

const productDetailRef = ref({})

const props = defineProps({
  productDetailId: {
    type: String,
    required: true
  }
})
const model = defineModel<boolean>()
// const emit = defineEmits(['closeDialog'])
// function closeDialog() {
//   data.showProductDialog = false
//   nextTick(() => {
//     emit('closeDialog')
//   })
// }
// onMounted(async () => {
//   if (props.productDetailId) {
//     nextTick(() => {
//       productDetailRef.value.productDetailId = props.productDetailId
//     })
//   }
// })
</script>

<style></style>
