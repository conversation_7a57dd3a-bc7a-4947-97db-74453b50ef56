<!-- 产品展示/DIY产品 -->
<template>
  <content-wrap ifTable>
    <template #search>
      <el-form
        ref="productQueryFormRef"
        :labelWidth="ifEn ? '160px' : '80px'"
        :model="productQueryForm"
        :inline="true"
        :rules="productQueryFormRules"
      >
        <el-form-item
          :label="t('productsShow.diyCardProduct.customerSelect')"
          prop="relateCustomer.customerId"
        >
          <el-select
            v-model="productQueryForm.relateCustomer"
            value-key="customerId"
            :placeholder="t('productsShow.diyCardProduct.customerSelectPlaceholder')"
            filterable
            :loading="data.customerSelectLoading"
            @change="relateCustomerChange"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in data.customerList"
              :key="item.customerId"
              :label="item.customerName"
              :value="deepClone(item)"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="关联项目" prop="relateProject.projectId">
        <el-select
          v-model="productQueryForm.relateProject"
          value-key="projectId"
          placeholder="请选择关联项目"
          filterable
          @focus="getProjectList"
          :loading="data.projectSelectLoading"
          @change="relateProjectChange"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in data.projectList"
            :key="item.projectId"
            :label="item.projectName"
            :value="item"
          />
        </el-select>
      </el-form-item> -->
        <el-form-item
          :label="t('productsShow.diyCardProduct.serviceName')"
          prop="service.diyConfigInfoId"
        >
          <el-select
            v-model="productQueryForm.service"
            value-key="diyConfigInfoId"
            :placeholder="t('productsShow.diyCardProduct.serviceNamePlaceholder')"
            filterable
            @focus="getServiceList"
            :loading="data.serviceSelectLoading"
            @change="relateServiceChange"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in data.serviceList"
              :key="item.diyConfigInfoId"
              :label="item.diyConfigInfoName"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('productsShow.diyCardProduct.cardAliasC')" prop="cardAliasC">
          <el-input
            v-model="productQueryForm.cardAliasC"
            :placeholder="t('productsShow.diyCardProduct.cardAliasCPlaceholder')"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item :label="t('productsShow.diyCardProduct.cardDraftCode')" prop="cardCode">
          <el-input
            v-model="productQueryForm.cardCode"
            :placeholder="t('productsShow.diyCardProduct.cardDraftCodeTextPlaceholder')"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item :label="t('productsShow.diyCardProduct.cardType')" prop="cardTypeCode">
          <el-select
            v-model="productQueryForm.cardTypeCode"
            :placeholder="t('productsShow.batchCardProduct.productTypePlaceholder')"
            filterable
            clearable
            style="width: 200px"
          >
            <el-option :label="t('productsShow.diyCardProduct.all')" value="" />
            <el-option
              v-for="item in getStrDictOptions('diy_product_card_type')"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('productsShow.diyCardProduct.productStatus')" prop="statusCode">
          <el-select
            v-model="productQueryForm.statusCode"
            :placeholder="t('productsShow.diyCardProduct.productStatusPlaceholder')"
            filterable
            clearable
            style="width: 200px"
          >
            <el-option :label="t('productsShow.diyCardProduct.all')" value="" />
            <el-option
              v-for="item in getStrDictOptions('diy_card_production_state')"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="margin-right: 0 !important">
          <el-button type="primary" v-track:click.btn @click="queryProduct">{{
            t('common.query')
          }}</el-button>
          <el-button type="warning" @click="resetQueryForm">{{ t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </template>
    <!-- 暂时屏蔽 -->
    <!-- <el-button type="success" @click="addProductDialog" :disabled="addDialogDisabled">
      添加产品
    </el-button> -->
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      style="width: 100%; margin: 10px 0"
      max-height="600"
      border
    >
      <el-table-column
        type="index"
        :index="(data.pageNo - 1) * data.pageSize + 1"
        :label="t('tableDemo.index')"
        width="100"
        align="center"
        headerAlign="center"
      />
      <el-table-column
        prop="customerName"
        :label="t('productsShow.batchCardProduct.customerName')"
        minWidth="150"
        align="left"
        headerAlign="center"
        showOverflowTooltip
      />
      <el-table-column
        prop="cardAliasC"
        :label="t('productsShow.diyCardProduct.cardAliasC')"
        minWidth="150"
        align="left"
        headerAlign="center"
        showOverflowTooltip
      />
      <el-table-column
        prop="cardCode"
        :label="t('productsShow.diyCardProduct.cardDraftCode')"
        width="150"
        align="center"
        headerAlign="center"
        showOverflowTooltip
      />
      <el-table-column
        prop="cardTypeName"
        :label="t('productsShow.diyCardProduct.cardType')"
        width="150"
        align="center"
        headerAlign="center"
        showOverflowTooltip
      >
        <template #default="scope">
          {{ getDictLabel('diy_product_card_type', scope.row.cardTypeCode) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="priceStage"
        :label="t('productsShow.diyCardProduct.image')"
        width="350"
        align="left"
        headerAlign="center"
        showOverflowTooltip
      >
        <template #default="scope">
          <div
            class="diy-card-img-div"
            v-for="(image, index) in scope.row.imageList.filter((image) =>
              ['MASK_IMAGE', 'FRONT_IMAGE', 'BACK_IMAGE'].includes(image.diyImageTypeCode)
            )"
            :key="index"
          >
            <el-image
              :title="
                t('productsShow.diyCardProduct.preview') +
                '：' +
                image.diyImageTypeName +
                '-' +
                image.diyImageFileJson.fileName
              "
              class="diy-image"
              fit="scale-down"
              lazy
              :zoom-rate="1.2"
              :src="image.diyImageFileJson.fileUrl"
              preview-teleported
              :initial-index="index"
              :preview-src-list="scope.row.showImageList"
            >
              <template #error>
                <el-icon><Picture /></el-icon>
              </template>
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="status.statusName"
        :label="t('productsShow.batchCardProduct.status')"
        width="100"
        align="center"
        headerAlign="center"
        showOverflowTooltips
      >
        <template #default="scope">
          <el-tag v-if="['待提交'].includes(scope.row.statusName)">
            {{ getDictLabel('diy_card_production_state', scope.row.statusCode) }}
          </el-tag>
          <el-tag v-else-if="['审核中'].includes(scope.row.statusName)" type="warning">
            {{ getDictLabel('diy_card_production_state', scope.row.statusCode) }}
          </el-tag>
          <el-tag v-else-if="['已上架'].includes(scope.row.statusName)" type="success">
            {{ getDictLabel('diy_card_production_state', scope.row.statusCode) }}
          </el-tag>
          <el-tag v-else-if="['已驳回'].includes(scope.row.statusName)" type="danger">
            {{ getDictLabel('diy_card_production_state', scope.row.statusCode) }}
          </el-tag>
          <el-tag v-else-if="['已下架'].includes(scope.row.statusName)" type="info">
            {{ getDictLabel('diy_card_production_state', scope.row.statusCode) }}
          </el-tag>
          <el-tag v-else>
            {{ getDictLabel('diy_card_production_state', scope.row.statusCode) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        :label="t('productsShow.diyCardProduct.operate')"
        width="280"
        align="center"
        headerAlign="center"
      >
        <template #default="scope">
          <el-button
            v-if="['待提交', '已驳回', '已下架'].includes(scope.row.statusName)"
            text
            bg
            type="success"
            size="small"
            v-track:click.btn
            @click="listedProduct(scope.row)"
          >
            {{
              scope.row.statusName === '已驳回'
                ? t('productsShow.diyCardProduct.resubmit')
                : t('productsShow.diyCardProduct.submit')
            }}
          </el-button>
          <el-button
            v-if="['待提交', '已驳回', '已下架'].includes(scope.row.statusName)"
            text
            bg
            type="primary"
            size="small"
            v-track:click.btn
            @click="editProductDialog(scope.row)"
          >
            {{ t('common.edit') }}
          </el-button>
          <!-- <el-button
            v-if="['已上架'].includes(scope.row.statusName)"
            text
            bg
            type="info"
            size="small"
            @click="unlistedProduct(scope.row)"
          >
            下架
          </el-button> -->
          <el-button
            text
            bg
            type="warning"
            size="small"
            v-track:click.btn
            @click="showProductDetailDialog(scope.row)"
          >
            {{ t('productsShow.diyCardProduct.view') }}
          </el-button>
          <el-button
            v-if="['待提交', '已驳回', '已下架'].includes(scope.row.statusName)"
            text
            bg
            type="danger"
            size="small"
            v-track:click.btn
            @click="deleteProduct(scope.row)"
          >
            {{ t('common.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #pagination>
      <Pagination
        v-model:page="data.pageNo"
        v-model:limit="data.pageSize"
        :total="data.productListCount"
        @pagination="getProductList"
      />
    </template>
  </content-wrap>

  <DiyCardEdit
    :dialogTitle="data.editDialogTitle"
    :cardTypeList="getStrDictOptions('diy_product_card_type')"
    :updateProductForm="productForm"
    :currentProjectService="data.currentProjectService"
    :updateFlag="updateFlag"
    :closeDialog="closeProductEditDialog"
    v-if="data.showProductEditDialogFlag"
    @close-refresh-list="closeAndRefreshList"
  />

  <ProductDetailDialog
    v-model="data.showProductDetailDialogFlag"
    :productDetailId="data.productDetailId"
  />
</template>

<script setup lang="ts">
defineOptions({
  name: 'DiyCardProduct'
})

import { getDictLabel, getStrDictOptions } from '@/utils/dict'

const { t, ifEn } = useI18n()
import Pagination from '@/components/Pagination/index.vue'
import DiyCardEdit from '@/views/ProductsShow/DiyCardProduct/Edit/Index.vue'
import ProductDetailDialog from '@/views/ProductsShow/DiyCardProduct/Detail/ProductDetailDialog.vue'
import { Picture } from '@element-plus/icons-vue'
import * as ProductApi from '@/api/product/diyCard'
import useMessage from '@/utils/useMessage'
import { deepClone } from '@/utils/deep'
import { getTenantId } from '@/utils/auth'
const message = useMessage()
import { ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { c } from 'vite/dist/node/types.d-aGj9QkWt'
const { push, currentRoute } = useRouter()

const productQueryFormRef = ref()
const addDialogDisabled = ref(true)

const productForm = ref()
const updateFlag = ref(false)

const data = reactive({
  productQueryForm: {},

  gIndex: 0,
  pageNo: 1,
  pageSize: 10,
  productListCount: 0,

  customerList: [],
  customerSelectLoading: false,

  projectList: [],
  projectSelectLoading: false,

  serviceList: [],
  serviceSelectLoading: false,

  merchantCardList: [],
  merchantCardSelectLoading: false,

  tableData: [],
  tableLoading: false,

  // 查看产品信息弹窗组件
  showProductDetailDialogFlag: false,
  productDetailId: '',

  // 添加、修改产品信息弹窗组件
  showProductEditDialogFlag: false,
  editDialogTitle: ''
})

const productQueryFormRules = reactive({
  'relateCustomer.customerId': [
    { required: false, message: t('productsShow.diyCardProduct.customerSelectPlaceholder') }
  ],
  'relateProject.projectId': [
    { required: false, message: t('productsShow.diyCardProduct.selectProjectPlaceholder') }
  ],
  'service.diyConfigInfoId': [
    { required: false, message: t('productsShow.diyCardProduct.serviceNamePlaceholder') }
  ]
  // 'service.diyConfigInfoId': [
  //   {
  //     required: false,
  //     validator: (rule, value, callback) => {
  //       if (
  //         !data.productQueryForm.hasOwnProperty('relateCustomer') ||
  //         !data.productQueryForm.relateCustomer
  //       ) {
  //         return callback(new Error('请先选择客户'))
  //       }
  //       let customerId = data.productQueryForm.relateCustomer.customerId
  //       if (customerId === null || customerId === undefined) {
  //         return callback(new Error('请先选择客户'))
  //       }
  //       // if (
  //       //   !data.productQueryForm.hasOwnProperty('relateProject') ||
  //       //   !data.productQueryForm.relateProject
  //       // ) {
  //       //   return callback(new Error('请先选择关联项目'))
  //       // }
  //       // let projectId = data.productQueryForm.relateProject.projectId
  //       // if (projectId === null || projectId === undefined) {
  //       //   return callback(new Error('请先选择关联项目'))
  //       // }
  //       if (value === null || value === undefined) {
  //         callback(new Error('请选择服务'))
  //       } else {
  //         callback()
  //       }
  //     }
  //   }
  // ]
})

const { productQueryForm } = toRefs(data)

const relateCustomerChange = (e) => {
  delete data.productQueryForm.relateProject
  delete data.productQueryForm.service
  data.projectList = []
  data.serviceList = []
  unref(productQueryFormRef)?.validate((isValid) => {})
}

const relateProjectChange = async (value) => {
  delete data.productQueryForm.service
  data.serviceList = []
  unref(productQueryFormRef)?.validate((isValid) => {})
}

const relateServiceChange = async (value) => {
  unref(productQueryFormRef)?.validate((isValid) => {})
}

watch(
  () => data.productQueryForm,
  (newVal, oldVal) => {
    unref(productQueryFormRef)?.validate((isValid) => {
      addDialogDisabled.value = !isValid
    })
  },
  { immediate: true, deep: true }
)

/** 查询产品列表 */
function queryProduct() {
  unref(productQueryFormRef)?.validate((isValid) => {
    if (isValid) {
      data.pageNo = 1
      getProductList()
    }
  })
}

/** 重置查询条件 */
const resetQueryForm = async () => {
  data.productQueryForm.relateCustomer = undefined
  await unref(productQueryFormRef)?.resetFields()
  data.productQueryForm.relateCustomer = null
  data.productQueryForm.relateProject = null
  data.productQueryForm.service = null
  data.projectList = []
  data.serviceList = []
  await unref(productQueryFormRef)?.validate((isValid) => {
    addDialogDisabled.value = !isValid
  })
}

/** 查询产品列表 */
const getProductList = async () => {
  await unref(productQueryFormRef)?.validate((isValid) => {
    addDialogDisabled.value = !isValid
  })
  if (addDialogDisabled.value) {
    return
  }

  let queryParams = Object.assign({}, productQueryForm.value)
  delete queryParams.service
  delete queryParams.relateProject
  delete queryParams.relateCustomer

  queryParams.pageNo = data.pageNo
  queryParams.pageSize = data.pageSize

  queryParams.applyServiceId = productQueryForm.value?.service?.diyConfigInfoId || ''
  queryParams.applyServiceCode = productQueryForm.value?.service?.diyConfigInfoCode || ''
  queryParams.customerId = productQueryForm.value?.relateCustomer?.customerId || ''
  queryParams.customerName = productQueryForm.value?.relateCustomer?.customerName || ''

  // queryParams.applyServiceId = productQueryForm.value.service.diyConfigInfoId
  // queryParams.applyServiceCode = productQueryForm.value.service.diyConfigInfoCode
  // queryParams.applyServiceName = productQueryForm.value.service.diyConfigInfoName
  // queryParams.relateProjectId = productQueryForm.value.relateProject.projectId
  // queryParams.relateProjectName = productQueryForm.value.relateProject.projectName
  // queryParams.customerId = productQueryForm.value.relateCustomer.customerId
  // queryParams.customerName = productQueryForm.value.relateCustomer.customerName
  queryParams.tenantId = getTenantId()

  try {
    data.tableLoading = true
    const { list, total } = await ProductApi.getProductListApi(queryParams)
    data.tableData = list
    data.productListCount = total
  } catch (e) {
    data.tableData = []
    data.productListCount = 0
    console.error('查询产品列表异常：', e)
  } finally {
    data.tableLoading = false
  }
}

const remoteGetCustom = async (query = '') => {
  data.customerSelectLoading = true
  try {
    const res = await ProductApi.getCustomerName(query)
    data.customerList = res.data
  } finally {
    data.customerSelectLoading = false
  }
}

// 获取项目接口
const getProjectList = async () => {
  if (!productQueryForm.value.hasOwnProperty('relateCustomer')) {
    data.projectList = []
    unref(productQueryFormRef)?.validate((isValid) => {})
    return
  }
  const relateCustomerId = productQueryForm.value.relateCustomer.customerId
  if (relateCustomerId === null || relateCustomerId === undefined) {
    data.projectList = []
    unref(productQueryFormRef)?.validate((isValid) => {})
    return
  }
  let params = {
    customerId: relateCustomerId
  }
  try {
    data.projectSelectLoading = true
    const res = await ProductApi.getProjectListApi(params)
    data.projectList = res || []
  } catch (e) {
    data.projectList = []
    console.error('获取项目异常：', e)
  } finally {
    data.projectSelectLoading = false
  }
}

// 第一次select获取焦点时不会触发远程，需要调用focus方法进行触发
function getServiceListByFocus() {
  getServiceList()
}

// 获取服务接口
const getServiceList = async (value?) => {
  // 暂时客户直接选择服务
  // if (!productQueryForm.value.hasOwnProperty('relateProject')) {
  //   data.serviceList = []
  //   unref(productQueryFormRef)?.validate((isValid) => {})
  //   return
  // }
  // const relateProjectId = productQueryForm.value.relateProject.projectId
  // if (relateProjectId === null || relateProjectId === undefined) {
  //   data.serviceList = []
  //   unref(productQueryFormRef)?.validate((isValid) => {})
  //   return
  // }

  try {
    data.serviceSelectLoading = true
    // const relateCustomerId = productQueryForm.value.relateCustomer.customerId
    const relateCustomerId = productQueryForm.value?.relateCustomer?.customerId || ''
    let formData = new FormData()
    formData.append('diyConfigInfoCustomid', relateCustomerId)
    const res = await ProductApi.getServiceListApi(formData)
    data.serviceList = res || []
  } catch (e) {
    data.serviceList = []
    console.error('获取服务异常：', e)
  } finally {
    data.serviceSelectLoading = false
  }
}

const addProductDialog = (): void => {
  updateFlag.value = false
  data.showProductEditDialogFlag = true
  data.editDialogTitle = t('productsShow.diyCardProduct.addProduct')

  data.currentProjectService = {
    // relateProjectId: productQueryForm.value.relateProject.projectId,
    // relateProjectName: productQueryForm.value.relateProject.projectName,
    relateProjectCodeJoint: productQueryForm.value.projectCodeJoint,
    applyServiceId: productQueryForm.value.service.diyConfigInfoId,
    applyServiceCode: productQueryForm.value.service.diyConfigInfoCode,
    applyServiceName: productQueryForm.value.service.diyConfigInfoName,
    applyServiceCardType: productQueryForm.value.service.diyConfigInfoCardtype,
    customerId: productQueryForm.value.relateCustomer.customerId,
    customerName: productQueryForm.value.relateCustomer.customerName,
    tenantId: getTenantId()
  }
}

function editProductDialog(row) {
  updateFlag.value = true
  productForm.value = row
  data.showProductEditDialogFlag = true
  data.editDialogTitle = t('productsShow.diyCardProduct.editProduct')

  data.currentProjectService = {
    relateProjectId: row.relateProjectId,
    relateProjectName: row.relateProjectName,
    applyServiceId: row.applyServiceId,
    applyServiceCode: row.applyServiceCode,
    applyServiceName: row.applyServiceName,
    applyServiceCardType: row.applyServiceCardType,
    customerId: productQueryForm.value.relateCustomer.customerId,
    customerName: productQueryForm.value.relateCustomer.customerName,
    tenantId: getTenantId()
  }
}

const showProductDetailDialog = async (row) => {
  // 方式1 tag展示
  /*let pageParams = {
    updateProductForm: productForm.value,
    cardTypeList: data.cardTypeList,
    updateFlag: true,
    currentProjectService: data.currentProjectService
  }
  push({
    name: 'productDetail',
    query: {
      cardId: row.cardId
    }
  })*/

  // 方式2 dialog展示
  data.productDetailId = row.cardId
  data.showProductDetailDialogFlag = true
}

/** 关闭查看产品详情 **/
function closeProductDetailDialog() {
  console.log('123123')
  data.showProductDetailDialogFlag = false
}

/** 关闭新增编辑产品详情 **/
function closeProductEditDialog() {
  data.showProductEditDialogFlag = false
}

/** 保存、提交产品后并刷新列表 **/
const closeAndRefreshList = async () => {
  data.showProductEditDialogFlag = false
  await getProductList()
}

/** 产品上架 **/
function listedProduct(row) {
  ElMessageBox.confirm(
    t('productsShow.diyCardProduct.submitConfirmTip', {
      name: row.cardAliasC
    }),
    t('productsShow.batchCardProduct.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const res = await ProductApi.productListedApi(row.cardId)
        if (res) {
          message.success(t('productsShow.diyCardProduct.submitSuccess'))
          await getProductList()
        }
      } catch (e) {
        console.error('产品提交失败：', e)
      } finally {
      }
    })
    .catch(() => {})
}

/** 产品下架 **/
function unlistedProduct(row) {
  ElMessageBox.confirm(
    t('productsShow.diyCardProduct.delistConfirmTip', {
      name: row.cardAliasC
    }),
    t('productsShow.batchCardProduct.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const res = await ProductApi.productUnListedApi(row.cardId)
        if (res) {
          message.success(t('productsShow.diyCardProduct.delistSuccess'))
          await getProductList()
        }
      } catch (e) {
        console.error('产品下架失败：', e)
      } finally {
      }
    })
    .catch(() => {})
}

/** 删除产品 **/
function deleteProduct(row) {
  ElMessageBox.confirm(
    t('productsShow.batchCardProduct.delConfirmTip', {
      name: row.cardAliasC
    }),
    t('productsShow.batchCardProduct.tip'),
    {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const res = await ProductApi.deleteProductApi(row.cardId)
        if (res) {
          message.success(t('productsShow.batchCardProduct.delProductSuccess'))
          await getProductList()
        }
      } catch (e) {
        console.error('产品删除失败：', e)
      }
    })
    .catch(() => {})
}

onMounted(async () => {
  getProductList()
  remoteGetCustom()
})

onActivated(async () => {
  await getProductList()
})
</script>

<style lang="less" scoped>
.diy-card-img-div {
  .diy-image {
    width: 80px;
    height: 60px;
    float: left;
    margin: 5px;
  }
  .el-image__wrapper {
    display: contents;
    font-size: 45px;
    .el-icon {
      width: 80px;
      height: 60px;
      background-color: #a9a9a933;
    }
  }
}
</style>
