<template>
  <el-dialog
    v-model="data.showProductDialog"
    :title="props.dialogTitle"
    class="product-data-dialog"
    :before-close="props.closeDialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
    draggable
    center
  >
    <el-alert
      type="info"
      style="margin-bottom: 10px; height: 80px"
      :closable="false"
      effect="light"
      class="show-project-customer"
    >
      <template #default>
        <!-- <span>
          <span style="margin-right: 20px">项目名称</span>{{ productForm.relateProjectName }}
        </span> -->
        <span>
          <span style="margin-right: 20px">{{ t('productsShow.diyCardProduct.serviceName') }}</span
          >{{ productForm.applyServiceName }}
        </span>
      </template>
    </el-alert>
    <el-scrollbar wrap-class="product-wrap" :max-height="data.scrollHeight">
      <el-alert
        v-if="productForm.statusName === '已驳回'"
        type="error"
        :title="t('productsShow.diyCardProduct.causeOfRejection') + ': '"
        :description="productForm.overruleReason"
        style="margin-bottom: 10px; height: 100px"
        :closable="false"
        effect="light"
      />
      <el-form
        :model="productForm"
        ref="productFormRef"
        label-position="right"
        style="text-align: left"
        label-width="135px"
        :rules="productFormRules"
      >
        <el-collapse v-model="data.expandCollapseList" class="product-collapse">
          <el-collapse-item :title="t('productsShow.diyCardProduct.basicInfo')" name="1">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.cardDraftCode')"
                  prop="cardCodeObj.cardId"
                >
                  <el-select
                    v-model="productForm.cardCodeObj"
                    :placeholder="t('productsShow.diyCardProduct.fuzzySearchSelection')"
                    value-key="cardId"
                    filterable
                    remote
                    automatic-dropdown
                    remote-show-suffix
                    placement="bottom"
                    :reserve-keyword="false"
                    @change="merchantCardCodeChange"
                    :remote-method="getMerchantCardCodeList"
                    :loading="data.merchantCardCodeSelectLoading"
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in data.merchantCardCodeList"
                      :key="item.cardId"
                      :label="item.cardCode"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.cardStyleName')"
                  prop="cardNameObj.cardId"
                >
                  <el-select
                    v-model="productForm.cardNameObj"
                    :placeholder="t('productsShow.diyCardProduct.fuzzySearchSelection')"
                    filterable
                    value-key="cardId"
                    remote
                    automatic-dropdown
                    remote-show-suffix
                    placement="bottom"
                    :reserve-keyword="false"
                    @change="merchantCardNameChange"
                    :remote-method="getMerchantCardNameList"
                    :loading="data.merchantCardNameSelectLoading"
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in data.merchantCardNameList"
                      :key="item.cardId"
                      :label="item.cardName"
                      :value="item"
                    >
                      <span style="float: left">{{ item.cardName }}</span>
                      <span style="float: right; color: #afb7af; font-size: 8px; margin-left: 30px">
                        {{ item.cardCode }}
                      </span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="t('productsShow.diyCardProduct.quickAdd')">
                  <el-select
                    v-model="data.similarProduct"
                    :placeholder="t('productsShow.diyCardProduct.fuzzySearchSelection')"
                    value-key="cardId"
                    filterable
                    remote
                    automatic-dropdown
                    remote-show-suffix
                    :reserve-keyword="false"
                    @change="similarProductChange"
                    :remote-method="getSimilarProductList"
                    :loading="data.similarProductSelectLoading"
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in data.similarProductList"
                      :key="item.cardId"
                      :label="item.cardAliasC"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                style="display: flex; align-items: center; margin-bottom: 18px; color: #9e9e9e"
              >
                {{ t('productsShow.diyCardProduct.chooseProductTip') }}
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.cardOtherOfCName')"
                  prop="cardAliasC"
                >
                  <el-input
                    v-model="productForm.cardAliasC"
                    :placeholder="t('productsShow.diyCardProduct.cardOtherOfCNamePlaceholder')"
                    type="text"
                    maxlength="20"
                    show-word-limit
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.cardType')"
                  prop="cardTypeCode"
                >
                  <el-row :gutter="10" style="width: 100%">
                    <el-col
                      :span="productForm.cardTypeCode === CustomDiyCardType.CZ.value ? 12 : 24"
                    >
                      <el-select
                        v-model="productForm.cardTypeCode"
                        :placeholder="t('productsShow.diyCardProduct.cardTypePlaceholder')"
                        filterable
                        clearable
                        style="width: 100%"
                        @change="cardTypeChange"
                      >
                        <el-option
                          :label="CustomDiyCardType.CZ.label"
                          :value="CustomDiyCardType.CZ.value"
                        />
                        <el-option
                          v-for="item in props.cardTypeList.filter(
                            (ct) => !['1', '2'].includes(ct.value)
                          )"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-col>
                    <el-col
                      :span="12"
                      v-if="productForm.cardTypeCode === CustomDiyCardType.CZ.value"
                    >
                      <el-form-item label-width="0px" prop="cardTypeCodeCZ">
                        <el-radio-group v-model="productForm.cardTypeCodeCZ">
                          <el-radio label="1">{{
                            t('productsShow.diyCardProduct.allImg')
                          }}</el-radio>
                          <el-radio label="2">{{
                            t('productsShow.diyCardProduct.halfImg')
                          }}</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.cardIdent')"
                  prop="cardAliasOut"
                >
                  <el-input
                    v-model="productForm.cardAliasOut"
                    :placeholder="t('productsShow.diyCardProduct.cardIdentPlaceholder')"
                    type="text"
                    maxlength="20"
                    show-word-limit
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('productsShow.diyCardProduct.subcategory')" prop="catalog">
                  <el-tree-select
                    v-model="productForm.catalog"
                    ref="selectCatalogTree"
                    :data="data.catalogList"
                    :reserve-keyword="false"
                    :props="{ label: 'diyGroupInfoName', children: 'childGroupInfo' }"
                    node-key="diyGroupInfoId"
                    value-key="diyGroupInfoId"
                    :loading="data.catalogSelectLoading"
                    :render-after-expand="false"
                    highlight-current
                    clearable
                    check-strictly
                    @change="catalogChange"
                    :placeholder="t('productsShow.diyCardProduct.defaultSortPlaceholder')"
                    style="width: 100%"
                  >
                    <template #default="scope">
                      <el-option :label="scope.data.diyGroupInfoName" :value="scope.data" />
                    </template>
                  </el-tree-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.customerProductName')"
                  prop="cardProductName"
                >
                  <el-input
                    v-model="productForm.cardProductName"
                    :placeholder="t('productsShow.diyCardProduct.customerProductNamePlaceholder')"
                    type="text"
                    maxlength="20"
                    show-word-limit
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.customerProductCode')"
                  prop="cardProductCode"
                >
                  <el-input
                    v-model="productForm.cardProductCode"
                    :placeholder="t('productsShow.diyCardProduct.customerProductCodePlaceholder')"
                    type="text"
                    maxlength="20"
                    show-word-limit
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.financeProductCode')"
                  prop="financeProductCode"
                >
                  <el-input
                    v-model="productForm.financeProductCode"
                    :placeholder="t('productsShow.diyCardProduct.financeProductCodePlaceholder')"
                    type="text"
                    maxlength="20"
                    show-word-limit
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.ProductCode')"
                  prop="productCode"
                >
                  <el-input
                    v-model="productForm.productCode"
                    :placeholder="t('productsShow.diyCardProduct.ProductCode')"
                    type="text"
                    disabled
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.visibilityRange')"
                  prop="selectRangeList"
                >
                  <div style="display: flex; width: 100%" v-loading="data.cityLoading">
                    <el-checkbox v-model="productForm.allRangeFlag">{{
                      t('productsShow.diyCardProduct.allCity')
                    }}</el-checkbox>
                    <el-cascader
                      ref="cityCascaderRef"
                      style="width: 100%; margin-left: 10px"
                      v-model="productForm.selectRangeList"
                      :placeholder="t('productsShow.diyCardProduct.visibilityRangePlaceholder')"
                      :options="data.cityList"
                      :props="cityProps"
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      clearable
                    />
                  </div>
                </el-form-item>
              </el-col>
              <!-- DIY授权服务分类为交通卡时才显示 -->
              <el-col :span="12" v-if="props.currentProjectService.applyServiceCardType === '3'">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.supplier')"
                  prop="supplierCode"
                >
                  <el-select
                    v-model="productForm.supplierCode"
                    :placeholder="t('productsShow.diyCardProduct.supplierPlaceholder')"
                    filterable
                    clearable
                    style="width: 100%"
                    @change="supplierChange"
                  >
                    <el-option
                      v-for="item in data.supplierList"
                      :key="item.diySupplierInfoCode"
                      :label="item.diySupplierInfoName"
                      :value="item.diySupplierInfoCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="24">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.productIntro')"
                  prop="productDesc"
                >
                  <el-input
                    v-model="productForm.productDesc"
                    type="textarea"
                    :placeholder="t('productsShow.diyCardProduct.productIntroPlaceholder')"
                    maxlength="2000"
                    show-word-limit
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.designFile')"
                  prop="designFile"
                >
                  <el-row :gutter="10" style="width: 100%">
                    <el-col :span="13" v-if="productForm.designFile">
                      <el-tooltip :content="productForm.designFile">
                        <el-text type="primary" truncated>
                          {{ productForm.designFile }}
                        </el-text>
                      </el-tooltip>
                    </el-col>
                    <el-col :span="11" style="display: flex; justify-content: flex-start">
                      <el-button
                        v-if="productForm.designFile"
                        text
                        type="primary"
                        @click="
                          downloadProductPlatformFile(
                            productForm.designFileUrl,
                            productForm.designFile
                          )
                        "
                      >
                        {{ t('productsShow.diyCardProduct.download') }}
                      </el-button>
                      <!--                      <el-button
                        v-if="productForm.designFile"
                        text
                        type="danger"
                        style="margin-left: 5px"
                        @click="deleteFile(ProductApi.DiyFileType.DESIGN_FILE)"
                      >
                        删除
                      </el-button>
                      <el-upload
                        ref="designFileUploadRef"
                        :http-request="fileUpload"
                        :name="ProductApi.DiyFileType.DESIGN_FILE"
                        :auto-upload="true"
                        :show-file-list="false"
                        :limit="1"
                        :on-exceed="designFileExceedHandle"
                      >
                        <el-button text bg style="margin-left: 5px">
                          {{ productForm.designFile ? '重新上传' : '上传' }}
                        </el-button>
                      </el-upload>-->
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <!-- 稿样文件 -->
              <el-col :span="12">
                <el-form-item
                  :label="t('productsShow.diyCardProduct.draftFile')"
                  prop="exampleFile"
                >
                  <el-row :gutter="10" style="width: 100%">
                    <el-col :span="13" v-if="productForm.exampleFile">
                      <el-tooltip :content="productForm.exampleFile">
                        <el-text type="primary" truncated>
                          {{ productForm.exampleFile }}
                        </el-text>
                      </el-tooltip>
                    </el-col>
                    <el-col :span="11" style="display: flex; justify-content: flex-start">
                      <el-button
                        v-if="productForm.exampleFile"
                        text
                        type="primary"
                        @click="
                          downloadProductPlatformFile(
                            productForm.exampleFileUrl,
                            productForm.exampleFile
                          )
                        "
                      >
                        {{ t('productsShow.diyCardProduct.download') }}
                      </el-button>
                      <!--                      <el-button
                        v-if="productForm.exampleFile"
                        text
                        type="danger"
                        style="margin-left: 5px"
                        @click="deleteFile(ProductApi.DiyFileType.EXAMPLE_FILE)"
                      >
                        删除
                      </el-button>
                      <el-upload
                        ref="exampleFileUploadRef"
                        :http-request="fileUpload"
                        :name="ProductApi.DiyFileType.EXAMPLE_FILE"
                        :auto-upload="true"
                        :show-file-list="false"
                        :limit="1"
                        :on-exceed="exampleFileExceedHandle"
                      >
                        <el-button text bg style="margin-left: 5px">
                          {{ productForm.exampleFile ? '重新上传' : '上传' }}
                        </el-button>
                      </el-upload>-->
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="t('productsShow.diyCardProduct.file3D')" prop="threeDimFile">
                  <el-row :gutter="10" style="width: 100%">
                    <el-col :span="13" v-if="productForm.threeDimFile">
                      <el-tooltip :content="fileNameFormatter(productForm.threeDimFile)">
                        <el-text type="primary" truncated>
                          {{ fileNameFormatter(productForm.threeDimFile) }}
                        </el-text>
                      </el-tooltip>
                    </el-col>
                    <el-col :span="11" style="display: flex; justify-content: flex-start">
                      <el-button
                        v-if="productForm.threeDimFile"
                        text
                        type="primary"
                        @click="downloadFile(productForm.threeDimFileUrl)"
                      >
                        {{ t('productsShow.diyCardProduct.download') }}
                      </el-button>
                      <el-button
                        v-if="productForm.threeDimFile"
                        text
                        type="danger"
                        style="margin-left: 5px"
                        @click="deleteFile(ProductApi.DiyFileType.THREE_DIM_FILE)"
                      >
                        {{ t('common.delete') }}
                      </el-button>
                      <el-upload
                        ref="threeDimFileUploadRef"
                        :http-request="fileUpload"
                        :name="ProductApi.DiyFileType.THREE_DIM_FILE"
                        :auto-upload="true"
                        :show-file-list="false"
                        :limit="1"
                        :on-exceed="threeDimFileExceedHandle"
                      >
                        <el-button text bg style="margin-left: 5px">
                          {{
                            productForm.threeDimFile
                              ? t('productsShow.diyCardProduct.reupload')
                              : t('productsShow.diyCardProduct.upload')
                          }}
                        </el-button>
                      </el-upload>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-form-item
                :label="t('productsShow.diyCardProduct.customAttribute')"
                prop="customAttribute"
                class="attribute-form-item"
              >
                <DiyAttribute
                  ref="diyAttributeRef"
                  :currentProjectService="props.currentProjectService"
                />
              </el-form-item>
            </el-row>
          </el-collapse-item>

          <el-collapse-item :title="t('productsShow.diyCardProduct.imgInfo')" name="2">
            <ImageInfo ref="imageInfoRef" :currentProjectService="props.currentProjectService" />
          </el-collapse-item>

          <el-collapse-item :title="t('productsShow.diyCardProduct.setPrice')" name="3">
            <PriceSetting ref="priceSettingRef" />
            <el-text>{{ t('productsShow.diyCardProduct.defaultTip') }}</el-text>
          </el-collapse-item>

          <el-collapse-item :title="t('productsShow.diyCardProduct.setImgAudit')" name="4">
            <VerifySetting
              ref="verifySettingRef"
              :currentProjectService="props.currentProjectService"
            />
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div style="justify-content: center; display: flex; width: 100%">
        <el-button type="success" :loading="data.isSaving" @click="saveProductData(true)">
          {{ t('makeCard.common.submit') }}
        </el-button>
        <el-button type="success" :loading="data.isSaving" @click="saveProductData(false)">
          {{ t('common.save') }}
        </el-button>
        <el-button @click="props.closeDialog">{{ t('common.back') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
const { t } = useI18n()
import * as ProductApi from '@/api/product/diyCard'
import ImageInfo from '@/views/ProductsShow/DiyCardProduct/components/ImageInfo.vue'
import DiyAttribute from '@/views/ProductsShow/DiyCardProduct/components/DiyAttribute.vue'
import PriceSetting from '@/views/ProductsShow/DiyCardProduct/components/PriceSetting.vue'
import VerifySetting from '@/views/ProductsShow/DiyCardProduct/components/VerifySetting.vue'
import download from '@/utils/download'
import { mergeMaskImageAndFrontImage } from '@/views/ProductsShow/DiyCardProduct/components/imageMerge'
import { CustomDiyCardType } from '@/views/ProductsShow/DiyCardProduct/diyCardProduct'
import { unref } from 'vue'
import useMessage from '@/utils/useMessage'
import { CascaderProps, FormRules } from 'element-plus'
const message = useMessage()

const emit = defineEmits(['close-refresh-list'])

const cityCascaderRef = ref()

const diyAttributeRef = ref()
const imageInfoRef = ref()
const priceSettingRef = ref()
const verifySettingRef = ref()

const productFormRef = ref()
const designFileUploadRef = ref()
const exampleFileUploadRef = ref()
const threeDimFileUploadRef = ref()

let productForm = ref({
  cardId: '',
  cardCodeObj: {},
  cardNameObj: {},
  catalog: {},
  allRangeFlag: true,
  selectRangeList: []
})

const data = reactive({
  showProductDialog: true,
  scrollHeight: 0,
  // 卡款代码列表
  merchantCardCodeList: [],
  merchantCardCodeSelectLoading: false,
  // 卡款名称列表
  merchantCardNameList: [],
  merchantCardNameSelectLoading: false,
  // 相似产品
  similarProductList: [],
  similarProductSelectLoading: false,
  // 版型列表
  cardStyleList: [],
  // 定制属性列表
  customAttributeList: [],

  catalogSelectLoading: false,
  catalogList: [],
  cityList: [],
  cityLoading: false,

  supplierList: [],

  isSaving: false,
  expandCollapseList: ['1', '2', '3', '4']
})

const cityProps: CascaderProps = {
  checkStrictly: false,
  label: 'label',
  value: 'value',
  children: 'children',
  multiple: true,
  emitPath: true,
  lazy: true,
  lazyLoad(node, resolve) {
    const { value, level, isLeaf } = node
    if (level === 1) {
      ProductApi.getCityListApi({ areaId: value }).then((result) => {
        const cityList = result?.data?.data || []
        let childrenCityList = convertCityCascader(cityList, true)
        resolve(childrenCityList)
      })
    }
    resolve([])
  }
}

const props = defineProps({
  dialogTitle: {
    type: String,
    required: true
  },
  updateFlag: {
    type: Boolean,
    required: true
  },
  updateProductForm: {
    type: Object,
    default: () => {}
  },
  currentProjectService: {
    type: Object,
    required: true
  },
  cardTypeList: {
    type: Array,
    default: () => []
  },
  closeDialog: {
    type: Function,
    required: true
  }
})

toRefs(data)
// const { updateProductForm } = toRefs(props)

const productFormRules = reactive<FormRules>({
  'cardCodeObj.cardId': [
    {
      required: true,
      message: t('productsShow.diyCardProduct.cardDraftCodePlaceholder'),
      trigger: 'change'
    }
  ],
  'cardNameObj.cardId': [
    {
      required: true,
      message: t('productsShow.diyCardProduct.cardStyleNamePlaceholder'),
      trigger: 'change'
    }
  ],
  cardAliasC: [
    {
      required: true,
      message: t('productsShow.diyCardProduct.cardOtherOfCNamePlaceholder'),
      trigger: 'change'
    }
  ],
  cardTypeCode: [
    {
      required: true,
      message: t('productsShow.diyCardProduct.cardTypePlaceholder'),
      trigger: 'change'
    }
  ],
  cardTypeCodeCZ: [
    {
      required: true,
      message: t('productsShow.diyCardProduct.colorImgTypePlaceholder'),
      trigger: 'change'
    }
  ],
  cardAliasOut: [
    {
      required: true,
      message: t('productsShow.diyCardProduct.cardIdentPlaceholder'),
      trigger: 'change'
    }
  ],
  supplierCode: [
    {
      required: true,
      message: t('productsShow.diyCardProduct.supplierPlaceholder'),
      trigger: 'change'
    }
  ],
  catalog: [
    {
      required: true,
      message: t('productsShow.diyCardProduct.defaultSortPlaceholder'),
      trigger: 'change'
    }
  ],
  selectRangeList: [
    {
      required: true,
      asyncValidator: async (rule: any, value: any, callback: any) => {
        if (value.length === 0 && !productForm.value.allRangeFlag) {
          callback(new Error(t('productsShow.diyCardProduct.visibilityRangePlaceholder')))
        }
        callback()
      },
      trigger: 'change'
    }
  ],
  price: [
    {
      required: true,
      asyncValidator: async (rule: any, value: any, callback: any) => {
        if (value === null || value === undefined || isNaN(value)) {
          callback(new Error(t('productsShow.diyCardProduct.priceAndUnitPlaceholder')))
        }
        if (Number(value) <= 0) {
          callback(new Error(t('productsShow.diyCardProduct.priceErr')))
        }
        callback()
      },
      trigger: 'blur'
    }
  ],
  salePrice: [
    {
      required: false,
      asyncValidator: async (rule: any, value: any, callback: any) => {
        if (value === null || value === undefined || isNaN(value)) {
          callback()
        }
        if (Number(value) <= 0) {
          callback(new Error(t('productsShow.diyCardProduct.priceErr')))
        }
        callback()
      },
      trigger: 'blur'
    }
  ]
})

watch(
  () => productForm.value.allRangeFlag,
  (value) => {
    if (value) {
      productForm.value.selectRangeList = []
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => productForm.value.selectRangeList,
  (value) => {
    if (value && value.length > 0) {
      productForm.value.allRangeFlag = false
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => productForm.value.cardTypeCode,
  (value) => {
    if (value && value === '4') {
      imageInfoRef.value.setShowElementImageGroupFlag(true)
    } else {
      imageInfoRef.value.setShowElementImageGroupFlag(false)
    }
  }
)

watch(
  () => productForm.value.cardTypeCodeCZ,
  (value) => {
    if (value && value === '2') {
      imageInfoRef.value.setShowImageLocationFlag(true, props.updateFlag)
    } else {
      imageInfoRef.value.setShowImageLocationFlag(false, props.updateFlag)
    }
  }
)

/** 获取卡款代码列表 **/
const getMerchantCardCodeList = async (value?) => {
  // if (value) {
  let params = {
    cardCode: value || ''
    // cardName: productForm.value?.cardNameObj?.cardName || ''
  }
  try {
    data.merchantCardCodeSelectLoading = true
    data.merchantCardCodeList = (await ProductApi.getMerchantCardListApi(params)) || []
  } catch (e) {
    data.merchantCardCodeList = []
    console.error('获取卡款列表异常：', e)
  } finally {
    data.merchantCardCodeSelectLoading = false
  }
  // }
}

/** 选中卡款代码更改卡款名称 **/
const merchantCardCodeChange = async (value) => {
  if (value) {
    data.merchantCardNameList = [value]
    productForm.value.cardNameObj = value
    productForm.value.productCode = value.productCode

    // 设计文件
    productForm.value.designFile = value.designFileName
    productForm.value.designFileUrl = value.designFileUrl
    // 稿样文件
    productForm.value.exampleFile = value.draftFileName
    productForm.value.exampleFileUrl = value.draftFileUrl
  } else {
    data.merchantCardCodeList = []
    data.merchantCardNameList = []
    productForm.value.cardCodeObj = {}
    productForm.value.cardNameObj = {}
    productForm.value.productCode = ''

    // 设计文件
    productForm.value.designFile = ''
    productForm.value.designFileUrl = ''
    // 稿样文件
    productForm.value.exampleFile = ''
    productForm.value.exampleFileUrl = ''
  }
}

/** 获取卡款名称列表 **/
const getMerchantCardNameList = async (value?) => {
  // if (value) {
  let params = {
    // cardCode: productForm.value?.cardCodeObj?.cardCode || '',
    cardName: value || ''
  }
  try {
    data.merchantCardNameSelectLoading = true
    data.merchantCardNameList = (await ProductApi.getMerchantCardListApi(params)) || []
  } catch (e) {
    data.merchantCardNameList = []
    console.error('获取卡款列表异常：', e)
  } finally {
    data.merchantCardNameSelectLoading = false
  }
  // }
}

// 选中卡款更改卡款基础属性
const merchantCardNameChange = async (value) => {
  if (value) {
    data.merchantCardCodeList = [value]
    productForm.value.cardCodeObj = value
    productForm.value.productCode = value.productCode

    // 设计文件
    productForm.value.designFile = value.designFileName
    productForm.value.designFileUrl = value.designFileUrl
    // 稿样文件
    productForm.value.exampleFile = value.draftFileName
    productForm.value.exampleFileUrl = value.draftFileUrl
  } else {
    data.merchantCardCodeList = []
    data.merchantCardNameList = []
    productForm.value.cardCodeObj = {}
    productForm.value.cardNameObj = {}
    productForm.value.productCode = ''

    // 设计文件
    productForm.value.designFile = ''
    productForm.value.designFileUrl = ''
    // 稿样文件
    productForm.value.exampleFile = ''
    productForm.value.exampleFileUrl = ''
  }
}

/** 第一次select获取焦点时不会触发远程，需要调用focus方法进行触发 **/
function getSimilarProductListByFocus() {
  getSimilarProductList()
}

/** 获取相似产品列表 **/
const getSimilarProductList = async (value?) => {
  let params = {
    cardAliasC: value || ''
  }
  try {
    data.similarProductSelectLoading = true
    data.similarProductList = (await ProductApi.getSimilarProductListApi(params)) || []
  } catch (e) {
    data.similarProductList = []
    console.error('获取相似产品列表异常：', e)
  } finally {
    data.similarProductSelectLoading = false
  }
}

const cardTypeChange = async (value) => {
  if (value) {
    if (value === CustomDiyCardType.CZ.value) {
      productForm.value.cardTypeName = CustomDiyCardType.CZ.label
    } else {
      productForm.value.cardTypeName = props.cardTypeList.filter(
        (ct) => ct.value === value
      )[0].label
      productForm.value.cardTypeCodeCZ = ''
    }
  }
}

const supplierChange = async (value) => {
  if (value) {
    productForm.value.supplierName = data.supplierList.filter(
      (ct) => ct.diySupplierInfoCode === value
    )[0].diySupplierInfoName
  }
}

/** 选择相似产品后填充相关字段 **/
function similarProductChange(value) {
  if (value) {
    // 卡款别名
    productForm.value.cardAliasC = value.cardAliasC
    // 卡款标识（对外）
    productForm.value.cardAliasOut = value.cardAliasOut
    // 客户产品编号
    productForm.value.cardProductCode = value.cardProductCode
    // 客户产品名称
    productForm.value.cardProductName = value.cardProductName
    // 财务产品编码
    productForm.value.financeProductCode = value.financeProductCode
    // 产品简介
    productForm.value.productDesc = value.productDesc
    // 所属分类
    productForm.value.catalogCode = value.catalogCode
    // 卡款类型
    if (CustomDiyCardType.CZ.mapValue.includes(value.cardTypeCode)) {
      productForm.value.cardTypeCode = CustomDiyCardType.CZ.value
      productForm.value.cardTypeNam = CustomDiyCardType.CZ.label
    } else {
      productForm.value.cardTypeCode = value.cardTypeCode
      productForm.value.cardTypeName = props.cardTypeList.filter(
        (ct) => ct.value === value.cardTypeCode
      )[0].label
    }
  }
}

/** 所属分类 **/
const getCatalogList = async () => {
  try {
    data.catalogSelectLoading = true
    // let serviceId = '1676787768374538241'
    let serviceId = props.currentProjectService.applyServiceId
    let formData = new FormData()
    formData.append('diyInfoConfigid', serviceId)
    formData.append('diyGroupInfoReusetype', '3')
    const res = await ProductApi.getCataLogListApi(formData)
    data.catalogList = res.data || []
    // 默认分类
    data.catalogList.unshift({
      diyGroupInfoId: '0',
      diyGroupInfoName: t('productsShow.diyCardProduct.defaultSort')
    })
  } catch (e) {
    data.catalogList = []
    console.error('查询所属分类失败：', e)
  } finally {
    data.catalogSelectLoading = false
  }
}

const catalogChange = async (value) => {}

/** 保存产品数据 **/
const saveProductData = async (audit) => {
  data.isSaving = true
  data.expandCollapseList = []
  unref(productFormRef)?.validate(async (isValid, ValidateFieldsError) => {
    const checkFlag = await checkOtherInfo()
    if (!checkFlag) {
      data.isSaving = false
    }
    if (!isValid) {
      data.isSaving = false
      if (!data.expandCollapseList.includes('1')) {
        data.expandCollapseList.push('1')
      }
      Object.keys(ValidateFieldsError).forEach((key, i) => {
        const propName = ValidateFieldsError[key][0].field
        if (i === 0) {
          unref(productFormRef).scrollToField(propName)
        }
      })
    }
    if (isValid && checkFlag) {
      productForm.value.audit = audit

      if (productForm.value.cardId === '') {
        await addOrUpdateProduct('/diyCard/v1/add')
      } else {
        await addOrUpdateProduct('/diyCard/v1/update')
      }
    }
  })
  //   // 只保存，只校验卡款代码
  //   unref(productFormRef)?.validateField('cardCodeObj.cardId', async (isValid) => {
  //     if (isValid) {
  //     }
  //   })
}

const checkOtherInfo = async (audit?) => {
  // 定制属性信息
  let attributeValidate = false
  const diyAttributeFormRef = diyAttributeRef.value.diyAttributeFormRef
  await unref(diyAttributeFormRef)?.validate(async (valid, ValidateFieldsError) => {
    if (valid) {
      attributeValidate = true
    } else {
      if (!data.expandCollapseList.includes('1')) {
        data.expandCollapseList.push('1')
      }
      Object.keys(ValidateFieldsError).forEach((key, i) => {
        const propName = ValidateFieldsError[key][0].field
        if (i === 0) {
          unref(diyAttributeFormRef).scrollToField(propName)
        }
      })
    }
  })

  // 图片配置信息
  let imageValidate = false
  const imageInfoFormRef = imageInfoRef.value.imageInfoFormRef
  await unref(imageInfoFormRef)?.validate(async (valid, ValidateFieldsError) => {
    if (valid) {
      imageValidate = true
    } else {
      if (!data.expandCollapseList.includes('2')) {
        data.expandCollapseList.push('2')
      }
      if (attributeValidate) {
        Object.keys(ValidateFieldsError).forEach((key, i) => {
          const propName = ValidateFieldsError[key][0].field
          if (i === 0) {
            unref(imageInfoFormRef).scrollToField(propName)
          }
        })
      }
    }
  })

  // 价格配置信息
  let priceValidate = false
  const priceSettingFormRef = priceSettingRef.value.priceSettingFormRef
  await unref(priceSettingFormRef)?.validate(async (valid, ValidateFieldsError) => {
    if (valid) {
      priceValidate = true
    } else {
      if (!data.expandCollapseList.includes('3')) {
        data.expandCollapseList.push('3')
      }
      if (attributeValidate && imageValidate) {
        Object.keys(ValidateFieldsError).forEach((key, i) => {
          const propName = ValidateFieldsError[key][0].field
          if (i === 0) {
            unref(priceSettingFormRef).scrollToField(propName)
          }
        })
      }
    }
  })

  // 图审配置信息
  let verifyValidate = false
  const verifySettingFormRef = verifySettingRef.value.verifySettingFormRef
  await unref(verifySettingFormRef)?.validate(async (valid, ValidateFieldsError) => {
    if (valid) {
      verifyValidate = true
    } else {
      if (!data.expandCollapseList.includes('4')) {
        data.expandCollapseList.push('4')
      }
      if (attributeValidate && imageValidate && priceValidate) {
        Object.keys(ValidateFieldsError).forEach((key, i) => {
          const propName = ValidateFieldsError[key][0].field
          if (i === 0) {
            unref(verifySettingFormRef).scrollToField(propName)
          }
        })
      }
    }
  })

  if (!priceValidate || !imageValidate || !attributeValidate || !verifyValidate) {
    return false
  }

  const priceSettingData = await priceSettingRef.value.getPriceSettingData()
  productForm.value.price = priceSettingData.price
  productForm.value.salePrice = priceSettingData.salePrice
  productForm.value.priceList = priceSettingData.tableData

  const imageInfoData = await imageInfoRef.value.getImageInfoData()
  productForm.value.imageList = imageInfoData

  try {
    const mergeImage = await mergeMaskImageAndFrontImage(imageInfoData)
    productForm.value.imageList.push(mergeImage)
  } catch (e) {
    console.error('生成合成图发送异常: ' + e)
    message.error(t('productsShow.diyCardProduct.createImgSendErr') + ':' + e)
  }

  const diyAttributeData = await diyAttributeRef.value.getAttributeData()
  productForm.value.attributeList = diyAttributeData

  const diyVerifyData = await verifySettingRef.value.getVerifySettingData()
  productForm.value.diyVerifyFlag = diyVerifyData.diyVerifyFlag
  if (diyVerifyData.diyVerifyFlag) {
    productForm.value.verifyList = diyVerifyData.verifyList
  } else {
    delete productForm.value.verifyList
  }
  return true
}

/** 创建产品、更新产品 **/
const addOrUpdateProduct = async (url) => {
  // 提取卡款属性
  productForm.value.cardCode = productForm.value.cardCodeObj.cardCode
  productForm.value.cardName = productForm.value.cardCodeObj.cardName
  productForm.value.productCode = productForm.value.cardCodeObj.productCode
  productForm.value.productName = productForm.value.cardCodeObj.productName
  // 提取所属分类属性
  productForm.value.catalogId = productForm.value.catalog.diyGroupInfoId
  productForm.value.catalogName = productForm.value.catalog.diyGroupInfoName
  // 提取所见范围
  if (productForm.value.selectRangeList.length > 0) {
    productForm.value.rangeList = convertRangeList(productForm.value.selectRangeList)
  }

  // 彩照卡处理
  if (productForm.value.cardTypeCode === CustomDiyCardType.CZ.value) {
    productForm.value.cardTypeCode = productForm.value.cardTypeCodeCZ
    productForm.value.cardTypeName = props.cardTypeList.filter(
      (ct) => ct.value === productForm.value.cardTypeCodeCZ
    )[0].label
  }

  try {
    const res = await ProductApi.addOrUpdateProductApi(url, {
      ...productForm.value,
      ...props.currentProjectService
    })
    if (res) {
      message.success(t('productsShow.batchCardProduct.saveProductSuccess'))
      await emit('close-refresh-list')
    }
  } catch (e) {
    console.error('添加产品或更新产品失败：', e)
  } finally {
    data.isSaving = false
  }
}

function convertRangeList(selectRangeList: Array<any>) {
  let rangeList = []
  if (selectRangeList.length > 0) {
    const checkedCityNode = cityCascaderRef.value.getCheckedNodes()
    selectRangeList.forEach((item) => {
      const checkCity = checkedCityNode.filter((node) => node.value === item[1])[0]
      rangeList.push({
        provinceCode: checkCity.pathValues[0],
        provinceName: checkCity.pathLabels[0],
        cityCode: checkCity.pathValues[1],
        cityName: checkCity.pathLabels[1]
      })
    })
  }
  return rangeList
}

const convertSelectRangeList = async (rangeList: Array<any>) => {
  let selectRangeList = []
  let getChildrenList = []
  let promiseList = []
  if (rangeList.length > 0) {
    for (let i = 0; i < rangeList.length; i++) {
      selectRangeList.push([rangeList[i].provinceCode, rangeList[i].cityCode])
      if (!getChildrenList.includes(rangeList[i].provinceCode)) {
        promiseList.push(getCityChildrenList(rangeList[i].provinceCode))
        getChildrenList.push(rangeList[i].provinceCode)
      }
    }
    // 并发子城市请求
    await Promise.all(promiseList).then((result) => {
      return selectRangeList
    })
  }
  return selectRangeList
}

/** 获取可见范围-城市列表 **/
const getCityList = async (areaId = null) => {
  let params = { areaId: areaId }
  const res = await ProductApi.getCityListApi(params)
  let cityList = res.data.data || []
  data.cityList = convertCityCascader(cityList)
}

/** 获取可见范围-城市列表 **/
const getCityChildrenList = async (areaId = null) => {
  const index = data.cityList.findIndex((dc) => dc.value === areaId)
  if (index >= 0 && areaId) {
    let params = { areaId: areaId }
    const res = await ProductApi.getCityListApi(params)
    const childrenList = res.data.data || []
    data.cityList[index].children = await convertCityCascader(childrenList, true)
  }
}

/** 将api接口的城市数据转换成城市级联面板的数据格式 **/
function convertCityCascader(cityDataList: Array<any>, leaf = false) {
  let cityList = []
  if (cityDataList.length > 0) {
    cityDataList.forEach((item) => {
      cityList.push({
        value: item.id.toString(),
        label: item.name,
        leaf: leaf
      })
    })
  }
  return cityList
}

/** 查询供应商列表 **/
const getSupplierList = async () => {
  try {
    const res = await ProductApi.getSupplierListApi()
    data.supplierList = res || []
  } catch (e) {
    data.supplierList = []
    console.error('查询供应商列表失败：', e)
  } finally {
  }
}

function fileNameFormatter(fileName) {
  return fileName.substring(fileName.indexOf('_') + 1, fileName.length)
}

const fileUpload = async (params) => {
  let { file, filename } = params
  const formData: any = new FormData()
  formData.append(filename, file)
  const { data } = await ProductApi.uploadFileApi(filename, formData)
  if (data) {
    productForm.value[filename] = data.substring(data.lastIndexOf('/') + 1, data.length)
    productForm.value[filename + 'Url'] = data
  }
}

const designFileExceedHandle = async (files) => {
  designFileUploadRef.value!.clearFiles()
  designFileUploadRef.value!.handleStart(files[files.length - 1])
  designFileUploadRef.value!.submit()
}

const exampleFileExceedHandle = async (files) => {
  exampleFileUploadRef.value!.clearFiles()
  exampleFileUploadRef.value!.handleStart(files[files.length - 1])
  exampleFileUploadRef.value!.submit()
}

const threeDimFileExceedHandle = async (files) => {
  threeDimFileUploadRef.value!.clearFiles()
  threeDimFileUploadRef.value!.handleStart(files[files.length - 1])
  threeDimFileUploadRef.value!.submit()
}

const deleteFile = async (fileType: ProductApi.DiyFileType) => {
  switch (fileType) {
    case ProductApi.DiyFileType.DESIGN_FILE:
      await designFileUploadRef.value!.clearFiles()
      productForm.value.designFile = ''
      break
    case ProductApi.DiyFileType.EXAMPLE_FILE:
      await exampleFileUploadRef.value!.clearFiles()
      productForm.value.exampleFile = ''
      break
    case ProductApi.DiyFileType.THREE_DIM_FILE:
      await threeDimFileUploadRef.value!.clearFiles()
      productForm.value.threeDimFile = ''
      break
  }
}

const downloadFile = async (fileUrl) => {
  let downFileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1, fileUrl.length)
  try {
    const res = await ProductApi.downloadFileApi(fileUrl)
    if (res === null || res?.data === null || res?.data?.size === 0) {
      message.error(t('productsShow.diyCardProduct.fileDownloadErr'))
      console.error('文件下载失败', res)
      return
    }
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = await fileNameFormatter(downFileName)
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  }
}

// 下载设计文件、稿样文件
const downloadProductPlatformFile = async (fileUrl, fileName?) => {
  // 文件名 + 后缀
  let downFileName = fileName + fileUrl.substring(fileUrl.lastIndexOf('.'), fileUrl.length)
  try {
    const params = {
      fileUrl: fileUrl,
      fileName: downFileName
    }
    const res = await ProductApi.downloadProductPlatformFileApi(params)
    if (res === null || res?.data === null || res?.data?.size === 0) {
      message.error(t('productsShow.diyCardProduct.fileDownloadErr'))
      console.error('文件下载失败', res)
      return
    }
    const blob = new Blob([res.data], { type: res.data.type })
    window.URL = window.URL || window.webkitURL
    const href = URL.createObjectURL(blob)
    const downA = document.createElement('a')
    downA.href = href
    downA.download = downFileName
    downA.click()
    window.URL.revokeObjectURL(href)
  } catch (e) {
    console.error('文件下载异常：' + e)
  }
}

onMounted(async () => {
  await getCatalogList()
  await getCityList()
  await getSupplierList()

  productForm.value.relateProjectName = props.currentProjectService.relateProjectName
  productForm.value.applyServiceName = props.currentProjectService.applyServiceName

  if (props.updateFlag) {
    productForm.value = { selectRangeList: [], ...props.updateProductForm }

    // 设置定制属性信息
    await diyAttributeRef.value.setAttributeList(productForm.value.attributeList)
    // 设置图片信息
    await imageInfoRef.value.setImageList(productForm.value.imageList)
    // 设置价格信息
    await priceSettingRef.value.setPriceList(
      productForm.value.price,
      productForm.value.salePrice,
      productForm.value.priceList
    )
    // 设置图审信息
    await verifySettingRef.value.setVerifyServiceList(
      productForm.value.diyVerifyFlag,
      productForm.value.verifyList
    )

    let cardObj = {
      cardId: 1,
      cardCode: productForm.value.cardCode,
      cardName: productForm.value.cardName
    }
    productForm.value.cardCodeObj = cardObj
    productForm.value.cardNameObj = cardObj
    data.merchantCardCodeList.push(cardObj)
    data.merchantCardNameList.push(cardObj)

    // 彩照卡处理
    if (CustomDiyCardType.CZ.mapValue.includes(productForm.value.cardTypeCode)) {
      productForm.value.cardTypeCodeCZ = productForm.value.cardTypeCode
      productForm.value.cardTypeCode = CustomDiyCardType.CZ.value
      productForm.value.cardTypeName = CustomDiyCardType.CZ.label
    }

    nextTick(async () => {
      productForm.value.catalog = {}
      productForm.value.catalog.diyGroupInfoId = productForm.value.catalogId
      productForm.value.catalog.diyGroupInfoName = productForm.value.catalogName

      // 回显可见范围
      const rangeList = productForm.value.rangeList
      if (rangeList.length > 0) {
        data.cityLoading = true
        productForm.value.selectRangeList = await convertSelectRangeList(rangeList)
        data.cityLoading = false
      }
    })
  } else {
    nextTick(() => {
      productForm.value.catalog.diyGroupInfoId = '0'
      productForm.value.catalog.diyGroupInfoName = t('productsShow.diyCardProduct.defaultSort')
    })
  }

  // 弹窗 margin:30, 弹窗header:54, 项目服务展示：90 驳回原因：110
  // let reasonHeight = ['已驳回'].includes(productForm.value.statusName) ? 110 : 0
  data.scrollHeight = document.documentElement.clientHeight - 30 - 54 - 90 - 35 - 75
  window.onresize = () => {
    data.scrollHeight = document.documentElement.clientHeight - 30 - 54 - 90 - 35 - 75
  }
})
</script>

<style lang="less">
.product-data-dialog {
  height: 100%;
  width: 70%;
  position: relative;
  margin: 15px auto !important;
  max-height: ~'calc(100% - 30px)';
  max-width: ~'calc(100% - 30px)';
  display: flex;
  flex-direction: column;

  .el-dialog__body {
    overflow: hidden;
    height: 100%;
  }

  .el-input__inner {
    text-align: left;
  }

  .attribute-form-item {
    width: 99%;
    .el-descriptions__label.el-descriptions__cell.is-bordered-label {
      width: 135px;
    }
  }
  .show-project-customer {
    .el-alert__content {
      width: 100%;
      .el-alert__description {
        color: #616161 !important;
        height: 32px;
        line-height: 32px;
        font-size: 16px;
        display: flex;
        justify-content: space-between;
        width: 80%;
      }
    }
  }
  .product-collapse {
    .el-collapse-item__header {
      font-size: 14px;
      font-weight: bold;
      &::before {
        content: '';
        position: relative;
        top: 0;
        left: 0;
        z-index: 999;
        width: 10px;
        height: 100%;
        background-color: #4caf50;
        margin-right: 10px;
      }
    }
  }
}
</style>
