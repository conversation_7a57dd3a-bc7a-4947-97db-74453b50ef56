<template>
  <el-form
    :model="productForm.customAttribute"
    ref="diyAttributeFormRef"
    label-position="left"
    style="text-align: left; width: 100%"
    label-width="135px"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item>
        <template #label>
          <span class="required-field-tag">{{ t('productsShow.diyCardProduct.pattern') }}</span>
        </template>
        <el-radio-group
          v-model="productForm.customAttribute.cardStyle.bindValue"
          style="width: 100%"
          @change="cardStyleChange"
        >
          <el-radio
            v-for="item in data.cardStyleList"
            :key="item.value"
            :label="item.value"
            style="width: 135px"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
        <el-form-item
          label-width="0"
          class="diy-attribute-form-error"
          prop="cardStyle.bindValue"
          :rules="[
            { required: true, message: t('productsShow.diyCardProduct.patternPlaceholder') }
          ]"
        />
      </el-descriptions-item>

      <div v-for="(attr, index) in data.attributeList" :key="index" style="margin-top: 0">
        <el-descriptions-item>
          <template #label>
            <el-checkbox
              v-model="
                productForm.customAttribute[getEnumKeyByValue(attr.value)].diyAttributeChecked
              "
              :label="getDictLabel('diy_authorization_card_custom', attr.value)"
              style="width: 220px"
              @change="attributeChange(attr, $event)"
            />
          </template>
          <div style="width: 100%">
            <el-radio-group
              v-if="['4', '6', '7'].includes(attr.value)"
              v-model="productForm.customAttribute[getEnumKeyByValue(attr.value)].bindValue"
              @change="radioAttributeChange(attr, attr.children, $event)"
              style="width: 100%"
            >
              <el-radio
                v-for="(ac, cIndex) in attr.children"
                :key="cIndex"
                :label="ac.value"
                style="width: 135px"
              >
                {{ n18Attr(ac.value, attr.value) }}
              </el-radio>
            </el-radio-group>

            <el-checkbox-group
              v-if="['1', '2', '3', '5'].includes(attr.value)"
              v-model="productForm.customAttribute[getEnumKeyByValue(attr.value)].bindValue"
              @change="checkboxAttributeChange(attr, attr.children, $event)"
              style="width: 100%"
            >
              <el-checkbox
                v-for="(ac, cIndex) in attr.children"
                :key="cIndex"
                :label="n18Attr(ac.value, attr.value)"
                style="width: 135px"
              >
                {{ n18Attr(ac.value, attr.value) }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <el-form-item
            label-width="0px"
            v-if="attr.children.length > 0"
            class="diy-attribute-form-error"
            :prop="getEnumKeyByValue(attr.value) + '.bindValue'"
            :rules="
              productForm.customAttribute[getEnumKeyByValue(attr.value)].diyAttributeChecked
                ? [
                    {
                      required: true,
                      message: t('productsShow.diyCardProduct.selectPlaceholder') + attr.label
                    }
                  ]
                : [{ required: false }]
            "
          />
        </el-descriptions-item>

        <!-- 图片编辑中的子分类 -->
        <el-descriptions-item
          v-if="
            attr.value === '1' &&
            productForm.customAttribute[getEnumKeyByValue(attr.value)].bindValue.includes('1')
          "
        >
          <el-checkbox-group
            v-model="productForm.customAttribute[getEnumKeyByValue(attr.value)].imageEditValue"
            @change="imageEditChange(attr, attr.children.filter((a) => a.value === '1')[0], $event)"
            style="width: 100%"
          >
            <el-checkbox
              v-for="(ac, cIndex) in attr.children.filter((a) => a.value === '1')[0].children"
              :key="cIndex"
              :label="ac.value"
              style="width: 135px"
            >
              {{ getDictLabel('diy_authorization_card_image_edit', ac.value) }}
            </el-checkbox>
          </el-checkbox-group>
          <el-form-item
            label-width="0px"
            v-if="attr.children.length > 0"
            class="diy-attribute-form-error"
            :prop="getEnumKeyByValue(attr.value) + '.imageEditValue'"
            :rules="
              productForm.customAttribute[getEnumKeyByValue(attr.value)].bindValue.includes('1')
                ? [
                    {
                      required: true,
                      message: t('productsShow.diyCardProduct.selectImgEditPlaceholder', {
                        name: attr.label
                      })
                    }
                  ]
                : [{ required: false }]
            "
          />
        </el-descriptions-item>
      </div>
    </el-descriptions>
  </el-form>
</template>

<script setup lang="ts">
import * as ProductApi from '@/api/product/diyCard'
import { reactive, unref } from 'vue'
import { isArray, isString } from '@/utils/is'
import { getDictLabel } from '@/utils/dict'
const { t } = useI18n()

const diyAttributeFormRef = ref()

let productForm = ref({
  customAttribute: {
    cardStyle: {
      value: '0',
      diyAttributeCode: 'cardStyle',
      diyAttributeName: t('productsShow.diyCardProduct.pattern'),
      diyAttributeChecked: true,
      bindValue: '',
      diyAttributeOptionJson: []
    },
    cardFace: {
      value: '1',
      diyAttributeCode: '1',
      diyAttributeName: t('productsShow.diyCardProduct.cardSurfaceCustomization'),
      diyAttributeChecked: false,
      bindValue: [],
      diyAttributeOptionJson: [],
      // 图片编辑绑定
      imageEditValue: []
    },
    // cardNum: {
    //   value: '2',
    //   diyAttributeCode: '2',
    //   diyAttributeName: '卡号定制',
    //   diyAttributeChecked: false,
    //   bindValue: [],
    //   diyAttributeOptionJson: []
    // },
    // // 权益定制
    // cardEquity: {
    //   value: '3',
    //   diyAttributeCode: '3',
    //   diyAttributeName: '权益定制',
    //   diyAttributeChecked: false,
    //   bindValue: [],
    //   diyAttributeOptionJson: []
    // },
    // 卡组织定制
    cardOrg: {
      value: '4',
      diyAttributeCode: '4',
      diyAttributeName: t('productsShow.diyCardProduct.cardOrganCustomization'),
      diyAttributeChecked: false,
      bindValue: '',
      diyAttributeOptionJson: []
    },
    // 卡基颜色定制
    cardBaseColor: {
      value: '5',
      diyAttributeCode: '5',
      diyAttributeName: t('productsShow.diyCardProduct.cardColorCustomization'),
      diyAttributeChecked: false,
      bindValue: [],
      diyAttributeOptionJson: []
    },
    // 卡背面颜色定制
    cardBackColor: {
      value: '6',
      diyAttributeCode: '6',
      diyAttributeName: t('productsShow.diyCardProduct.cardBackColorCustomization'),
      diyAttributeChecked: false,
      bindValue: '',
      diyAttributeOptionJson: []
    },
    // Logo标识定制
    cardLogo: {
      value: '7',
      diyAttributeCode: '7',
      diyAttributeName: t('productsShow.diyCardProduct.cardLogoCustomization'),
      diyAttributeChecked: false,
      bindValue: '',
      diyAttributeOptionJson: []
    }
  }
})

const data = reactive({
  cardStyleList: [],
  attributeList: []
})

const props = defineProps({
  editAttributeData: {
    type: Array,
    default: () => []
  },
  currentProjectService: {
    type: Object,
    default: () => {}
  }
})

watch(
  () => productForm.value.customAttribute,
  async (val, ac) => {
    for (let key: Object in productForm.value.customAttribute) {
      let attribute = productForm.value.customAttribute[key]
      if (isArray(attribute.bindValue)) {
        if (attribute.diyAttributeOptionJson.length > 0) {
          attribute.diyAttributeChecked = true
        }
      }
      if (isString(attribute.bindValue)) {
        if (attribute.diyAttributeOptionJson.length > 0) {
          attribute.diyAttributeChecked = true
        }
      }
    }
  },
  { immediate: true, deep: true }
)

enum AttrCodeType {
  // 1 卡面定制
  cardFace = '1',
  // 卡号定制
  // cardNum = '2',
  // 权益定制
  // cardEquity = '3',
  // 卡组织定制
  cardOrg = '4',
  // 卡基颜色定制
  cardBaseColor = '5',
  // 卡背面颜色定制
  cardBackColor = '6',
  // Logo标识定制
  cardLogo = '7'
}

function getEnumKeyByValue(enumKey) {
  return Object.keys(AttrCodeType)[Object.values(AttrCodeType as any).indexOf(enumKey)]
}

const cardStyleChange = async (value) => {
  productForm.value.customAttribute.cardStyle.diyAttributeOptionJson = []
  productForm.value.customAttribute.cardStyle.diyAttributeOptionJson.push(
    data.cardStyleList.filter((cs) => cs.value === value)[0]
  )
}

const radioAttributeChange = async (attr, attrChildren, value) => {
  let cusAttribute = productForm.value.customAttribute[getEnumKeyByValue(attr.value)]
  cusAttribute.diyAttributeOptionJson = []
  cusAttribute.diyAttributeOptionJson.push(attrChildren.filter((ac) => ac.value === value)[0])
  formValidate()
}

const checkboxAttributeChange = async (attr, attrChildren, value) => {
  let cusAttribute = productForm.value.customAttribute[getEnumKeyByValue(attr.value)]
  if (value.length > 0) {
    cusAttribute.diyAttributeOptionJson = attrChildren
      .filter((ac) => value.includes(ac.value))
      .map((ac) => {
        if (ac?.color) {
          return { value: ac.value, label: ac.label, color: ac.color }
        }
        return { value: ac.value, label: ac.label }
      })
  } else {
    cusAttribute.diyAttributeOptionJson = []
  }
  formValidate()
}

const imageEditChange = async (attr, attrChildren, value) => {
  let cusAttribute = productForm.value.customAttribute[getEnumKeyByValue(attr.value)]
  if (value.length > 0) {
    const imageEditChildren = attrChildren.children
    cusAttribute.diyAttributeOptionJson.map((diyAttr) => {
      if (diyAttr.value === attrChildren.value) {
        diyAttr.children = imageEditChildren
          .filter((ac) => value.includes(ac.value))
          .map((ac) => {
            return { value: ac.value, label: ac.label }
          })
      }
    })
  } else {
    cusAttribute.diyAttributeOptionJson.map((diyAttr) => {
      if (diyAttr.value === attrChildren.value) {
        if (diyAttr.hasOwnProperty('children')) {
          delete diyAttr.children
        }
      }
    })
  }
  formValidate()
}

const attributeChange = async (attr, event) => {
  if (!event) {
    let cusAttribute = productForm.value.customAttribute[getEnumKeyByValue(attr.value)]
    if (cusAttribute.hasOwnProperty('diyAttributeOptionJson')) {
      if (isArray(cusAttribute.bindValue)) {
        cusAttribute.bindValue = []
        cusAttribute.diyAttributeOptionJson = []
      }
      if (isString(cusAttribute.bindValue)) {
        cusAttribute.bindValue = ''
        cusAttribute.diyAttributeOptionJson = []
      }
    }
    if (cusAttribute.hasOwnProperty('imageEditValue')) {
      cusAttribute.imageEditValue = []
    }
    cusAttribute.diyAttributeChecked = false
  }
  formValidate()
}

watch(
  () => props.currentProjectService,
  async (value) => {
    if (value && value.applyServiceId) {
      await getAttributeList(value.applyServiceId)
    }
  },
  { immediate: true, deep: true }
)

async function getAttributeList(serviceId) {
  try {
    let params = {
      diyConfigInfoId: serviceId
    }
    const res = await ProductApi.getAttributeListApi(params)
    if (res.length > 0) {
      // 过滤掉卡号定制，权益定制的属性
      data.attributeList = res.filter((r) => !['2', '3'].includes(r.value))
    } else {
      data.attributeList = []
    }
  } catch (e) {
    data.attributeList = []
    console.error('查询定制属性失败：', e)
  } finally {
  }
}

/** 获取版型列表 **/
const getCardStyleList = async () => {
  try {
    // data.cardStyleList = (await ProductApi.getCardStyleListApi()) || []
    data.cardStyleList = [
      { value: 'HORIZONTAL', label: t('productsShow.diyCardProduct.horizontal') },
      { value: 'VERTICAL', label: t('productsShow.diyCardProduct.vertical') },
      { value: 'ANOMALY', label: t('productsShow.diyCardProduct.anomaly') }
    ] as any
  } catch (e) {
    data.cardStyleList = []
    console.error('获取版型列表异常：', e)
  } finally {
  }
}

const formValidate = () => {
  unref(diyAttributeFormRef)?.validate((valid) => {})
}

const getAttributeData = async () => {
  let attributeList = []
  for (let key in productForm.value.customAttribute) {
    let attribute = productForm.value.customAttribute[key]
    attributeList.push(attribute)
  }
  return attributeList
}

// 回显价格配置
const setAttributeList = async (editAttributeList: Array<any>) => {
  setTimeout(() => {
    if (editAttributeList) {
      editAttributeList.forEach((item) => {
        let attribute = null
        for (let key in productForm.value.customAttribute) {
          if (productForm.value.customAttribute[key].diyAttributeCode === item.diyAttributeCode) {
            attribute = productForm.value.customAttribute[key]
          }
        }

        attribute.diyAttributeChecked = item.diyAttributeChecked
        if (isString(attribute.bindValue)) {
          if (isArray(item.diyAttributeOptionJson) && item.diyAttributeOptionJson.length > 0) {
            attribute.bindValue = item.diyAttributeOptionJson[0].value
          }
        }
        if (isArray(attribute.bindValue)) {
          let bindValue = []

          item.diyAttributeOptionJson.map((db) => {
            bindValue.push(db.value)
            // 卡面定制(value:1)-图片编辑(value:1)回显处理
            if (attribute.value === '1' && db.value === '1' && db.children) {
              let imageEditValue = []
              db.children.map((dbc) => {
                imageEditValue.push(dbc.value)
              })
              attribute.imageEditValue = imageEditValue
            }
          })
          attribute.bindValue = bindValue
        }
        attribute.diyAttributeOptionJson = item.diyAttributeOptionJson
      })
    }
  }, 50)
}

onMounted(async () => {
  await getCardStyleList()
  // await getAttributeList()
})

defineExpose({
  diyAttributeFormRef,
  setAttributeList,
  getAttributeData
})
/**n18翻译 */
function n18Attr(value: string, key: '1' | '2' | '3' | '4' | '5' | '6' | '7') {
  switch (key) {
    case '1':
      return getDictLabel('diy_authorization_card_custom_face', value)
    // case '2':
    //   return getDictLabel('1', value)
    // case '3':
    //   return getDictLabel('1', value)
    case '4':
      return getDictLabel('diy_authorization_card_custom_organization', value)
    case '5':
      return getDictLabel('diy_authorization_card_custom_color', value) || value
    case '6':
      return getDictLabel('diy_authorization_card_custom_back_color', value) || value
    case '7':
      return getDictLabel('diy_authorization_card_custom_logo_color', value) || value
    default:
      return value
  }
}
</script>

<style lang="less">
.diy-attribute-form-error {
  .el-form-item__error {
    display: contents;
  }
}
.required-field-tag::before {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}
</style>
