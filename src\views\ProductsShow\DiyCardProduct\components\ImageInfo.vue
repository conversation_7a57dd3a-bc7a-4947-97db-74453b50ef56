<template>
  <el-form
    :model="data.imageInfoForm"
    label-width="110px"
    label-position="top"
    ref="imageInfoFormRef"
    class="price-setting-form"
  >
    <el-row :gutter="10">
      <el-col :span="8">
        <el-form-item
          prop="maskImage.fileUrl"
          :label="t('productsShow.diyCardProduct.maskImg')"
          :rules="[
            {
              required: true,
              message: t('productsShow.diyCardProduct.maskImgPlaceholder'),
              trigger: 'change'
            }
          ]"
        >
          <div v-if="data.imageInfoForm.maskImage">
            <div class="select-image-div-finish" @click="selectImage(ImageType.MASK_IMAGE)">
              <el-image :src="data.imageInfoForm.maskImage.fileUrl" fit="scale-down" />
            </div>
            <el-button
              bg
              text
              type="danger"
              :icon="Delete"
              style="margin-top: 5px"
              @click="data.imageInfoForm.maskImage = null"
            >
              {{ t('common.delete') }}
            </el-button>
          </div>

          <div v-else class="select-image-div" @click="selectImage(ImageType.MASK_IMAGE)">
            <span>+{{ t('productsShow.diyCardProduct.maskImgUploadPlaceholder') }}</span>
          </div>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item
          prop="frontImage.fileUrl"
          :label="t('productsShow.diyCardProduct.frontImg')"
          :rules="[{ validator: checkImageLocation, trigger: 'change' }]"
        >
          <div v-if="data.imageInfoForm.frontImage">
            <div class="select-image-div-finish" @click="selectImage(ImageType.FRONT_IMAGE)">
              <el-image :src="data.imageInfoForm.frontImage.fileUrl" fit="scale-down" />
            </div>
            <el-button
              text
              bg
              type="danger"
              :icon="Delete"
              style="margin: 5px"
              @click="data.imageInfoForm.frontImage = null"
            >
              {{ t('common.delete') }}
            </el-button>
            <el-button
              v-if="showImageLocationFlag"
              text
              bg
              type="success"
              style="margin-top: 5px"
              @click="showImageLocationDialog"
            >
              {{ t('productsShow.diyCardProduct.settingRegion') }}
            </el-button>
            <el-form
              v-if="showImageLocationFlag"
              :model="data.imageInfoForm.frontImage.locationInfo.pixelInfo"
              label-width="0px"
              label-position="right"
              class="front-image-location-form"
            >
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item prop="top" label="">
                    <el-button text bg type="primary">
                      {{ t('productsShow.diyCardProduct.top') }}：{{
                        data.imageInfoForm.frontImage.locationInfo.pixelInfo.top
                      }}
                      px
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item prop="bottom" label="">
                    <el-button text bg type="primary">
                      {{ t('productsShow.diyCardProduct.bottom') }}：{{
                        data.imageInfoForm.frontImage.locationInfo.pixelInfo.bottom
                      }}
                      px
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item prop="left" label="">
                    <el-button text bg type="primary">
                      {{ t('productsShow.diyCardProduct.left') }}：{{
                        data.imageInfoForm.frontImage.locationInfo.pixelInfo.left
                      }}
                      px
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item prop="right" label="">
                    <el-button text bg type="primary">
                      {{ t('productsShow.diyCardProduct.right') }}：{{
                        data.imageInfoForm.frontImage.locationInfo.pixelInfo.right
                      }}
                      px
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <div v-else class="select-image-div" @click="selectImage(ImageType.FRONT_IMAGE)">
            <span>+{{ t('productsShow.diyCardProduct.uploadFrontImg') }}</span>
          </div>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item
          prop="backImage.fileUrl"
          :label="t('productsShow.diyCardProduct.cardBackImg')"
          :rules="[
            {
              required: true,
              message: t('productsShow.diyCardProduct.uploadBackImg'),
              trigger: 'change'
            }
          ]"
        >
          <div v-if="data.imageInfoForm.backImage">
            <div class="select-image-div-finish" @click="selectImage(ImageType.BACK_IMAGE)">
              <el-image :src="data.imageInfoForm.backImage.fileUrl" fit="scale-down" />
            </div>
            <el-button
              text
              bg
              type="danger"
              :icon="Delete"
              style="margin-top: 5px"
              @click="data.imageInfoForm.backImage = unll"
            >
              {{ t('common.delete') }}
            </el-button>
          </div>

          <div v-else class="select-image-div" @click="selectImage(ImageType.BACK_IMAGE)">
            <span>+{{ t('productsShow.diyCardProduct.uploadBackImgPlaceholder') }}</span>
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-col
        :span="
          data.imageInfoForm.stickImageGroup && data.imageInfoForm.stickImageGroup.length > 0
            ? 24
            : 8
        "
      >
        <el-form-item prop="stickImageGroup" :label="t('productsShow.diyCardProduct.cardImg')">
          <div
            style="width: 100%"
            v-if="
              data.imageInfoForm.stickImageGroup && data.imageInfoForm.stickImageGroup.length > 0
            "
          >
            <el-button
              text
              bg
              type="success"
              class="check-image-button"
              v-for="(stickImage, index) in data.imageInfoForm.stickImageGroup"
              :key="index"
              @click="selectStickImageGroup(ImageGroupType.STICK_IMAGE_GROUP)"
            >
              {{ stickImage.stickGroupName }}
            </el-button>
          </div>
          <div
            v-else
            class="select-image-group-div"
            @click="selectStickImageGroup(ImageGroupType.STICK_IMAGE_GROUP)"
          >
            <span>{{ t('productsShow.diyCardProduct.uploadGroupImg') }}</span>
          </div>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px" v-if="showElementImageGroupFlag">
      <el-col
        :span="
          data.imageInfoForm.elementImageGroup && data.imageInfoForm.elementImageGroup.length > 0
            ? 24
            : 8
        "
      >
        <el-form-item
          prop="elementImageGroup"
          :label="t('productsShow.diyCardProduct.elementSplicing')"
        >
          <div
            style="width: 100%"
            v-if="
              data.imageInfoForm.elementImageGroup &&
              data.imageInfoForm.elementImageGroup.length > 0
            "
          >
            <el-row
              :gutter="10"
              class="element-image-row"
              v-for="(elementImage, index) in data.imageInfoForm.elementImageGroup"
              :key="index"
            >
              <el-col :span="4">
                <el-button
                  text
                  bg
                  type="success"
                  style="width: 100% !important"
                  class="check-image-button"
                  @click="selectElementImageGroup(ImageGroupType.ELEMENT_IMAGE_GROUP)"
                >
                  {{ elementImage.elementGroupName }}
                </el-button>
              </el-col>
              <el-col :span="20">
                <el-button
                  text
                  bg
                  type="success"
                  v-for="(childrenElementImage, eIndex) in elementImage.checkChildren"
                  :key="eIndex"
                  class="check-image-button"
                  @click="selectElementImageGroup(ImageGroupType.ELEMENT_IMAGE_GROUP)"
                >
                  {{ childrenElementImage.elementGroupName }}
                </el-button>
              </el-col>
            </el-row>
          </div>
          <div
            v-else
            class="select-image-group-div"
            @click="selectElementImageGroup(ImageGroupType.ELEMENT_IMAGE_GROUP)"
          >
            <span>{{ t('productsShow.diyCardProduct.uploadGroupElement') }}</span>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <!-- 蒙层、正面、反面图片选择 -->
  <SelectImage
    ref="selectImageRef"
    v-if="data.showSelectImageDialog"
    :dialogTitle="data.selectImageDialogTitle"
    :currentProjectService="props.currentProjectService"
    :closeDialog="closeSelectImageDialog"
    :getSelectImageList="getSelectImageList"
  />
  <!-- 贴图组选择 -->
  <SelectStickImageGroup
    ref="selectStickImageGroupRef"
    v-if="data.showSelectStickImageGroupDialog"
    :dialogTitle="data.selectStickImageGroupDialogTitle"
    :currentProjectService="props.currentProjectService"
    :closeDialog="closeSelectStickImageGroupDialog"
    :currentSelectGroupList="data.currentSelectStickGroupList"
    :getSelectImageGroupList="getSelectStickImageGroupList"
  />
  <!--元素拼接图片组选择 -->
  <SelectElementImageGroup
    ref="selectElementImageGroupRef"
    v-if="data.showSelectElementImageGroupDialog"
    :dialogTitle="data.selectElementImageGroupDialogTitle"
    :currentProjectService="props.currentProjectService"
    :currentSelectGroupList="data.currentSelectElementGroupList"
    :closeDialog="closeSelectElementImageGroupDialog"
    :getSelectImageGroupList="getSelectElementImageGroupList"
  />
  <!-- 正面图定位点设定 -->
  <ImageLocation
    ref="imageLocationRef"
    v-if="data.showImageLocationDialog"
    :dialogTitle="t('productsShow.diyCardProduct.halfImgCustomerSetRegion')"
    :imageUrl="data.imageInfoForm.frontImage.fileUrl"
    :closeDialog="closeImageLocationDialog"
    :setImageLocationInfo="setImageLocationInfo"
  />
</template>

<script setup lang="ts">
const { t } = useI18n()
import ImageLocation from '@/views/ProductsShow/DiyCardProduct/components/ImageLocation.vue'
import SelectImage from '@/views/ProductsShow/DiyCardProduct/components/SelectImage.vue'
import {
  ImageType,
  ImageGroupType,
  convertImageList
} from '@/views/ProductsShow/DiyCardProduct/diyCardProduct'
import SelectStickImageGroup from '@/views/ProductsShow/DiyCardProduct/components/SelectStickImageGroup.vue'
import SelectElementImageGroup from '@/views/ProductsShow/DiyCardProduct/components/SelectElementImageGroup.vue'
import { reactive, unref, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const showImageLocationFlag = ref(false)
const imageLocationRef = ref()

const imageInfoFormRef = ref()

const selectImageRef = ref()
const currentSelectImageType = ref()

const selectStickImageGroupRef = ref()

const showElementImageGroupFlag = ref(false)
const selectElementImageGroupRef = ref()

const currentSelectImageGroupType = ref()

const data = reactive({
  tableLoading: false,
  imageInfoForm: {},

  showImageLocationDialog: false,

  selectImageDialogTitle: '',
  showSelectImageDialog: false,

  selectStickImageGroupDialogTitle: '',
  showSelectStickImageGroupDialog: false,

  selectElementImageGroupDialogTitle: '',
  showSelectElementImageGroupDialog: false,

  currentSelectElementGroupList: [],
  currentSelectStickGroupList: []
})

const props = defineProps({
  currentProjectService: {
    type: Object,
    required: true
  },
  editImageData: {
    type: Array,
    default: () => []
  }
})

/** 显示图片选择弹窗 **/
const selectImage = async (imageType) => {
  currentSelectImageType.value = imageType
  data.selectImageDialogTitle =
    t('productsShow.diyCardProduct.upload') + currentSelectImageType.value.name
  data.showSelectImageDialog = true
}

/** 关闭图片选择弹窗 **/
const closeSelectImageDialog = async () => {
  data.showSelectImageDialog = false
}

/** 选择图片 **/
const getSelectImageList = async () => {
  selectImageRef.value.data.isSaving = true
  data.imageInfoForm[currentSelectImageType.value.field] =
    selectImageRef.value.data.selectImageList[0]
  // 在新增时选定图片后需要增加locationInfo.pixelInfo字段
  if (showImageLocationFlag.value && currentSelectImageType.value.field === 'frontImage') {
    data.imageInfoForm[currentSelectImageType.value.field].locationInfo = {
      pixelInfo: {}
    }
  }
  selectImageRef.value.data.isSaving = false
  await closeSelectImageDialog()
}

/** 显示贴图组选择弹窗 **/
const selectStickImageGroup = async (imageType) => {
  currentSelectImageGroupType.value = imageType
  data.currentSelectStickGroupList = data.imageInfoForm.stickImageGroup
  data.selectStickImageGroupDialogTitle =
    t('productsShow.diyCardProduct.upload') + currentSelectImageGroupType.value.name
  data.showSelectStickImageGroupDialog = true
}

/** 关闭贴图组选择弹窗 **/
const closeSelectStickImageGroupDialog = async () => {
  data.showSelectStickImageGroupDialog = false
}

/** 选择贴图组 **/
const getSelectStickImageGroupList = async () => {
  selectStickImageGroupRef.value.data.isSaving = true
  data.imageInfoForm[currentSelectImageGroupType.value.field] =
    selectStickImageGroupRef.value.data.selectImageGroupList
  selectStickImageGroupRef.value.data.isSaving = false
  await closeSelectStickImageGroupDialog()
}

/** 显示元素拼接选择弹窗 **/
const selectElementImageGroup = async (imageType) => {
  currentSelectImageGroupType.value = imageType
  data.currentSelectElementGroupList = data.imageInfoForm.elementImageGroup
  data.selectElementImageGroupDialogTitle =
    t('productsShow.diyCardProduct.upload') + currentSelectImageGroupType.value.name
  data.showSelectElementImageGroupDialog = true
}

/** 关闭元素拼接选择弹窗 **/
const closeSelectElementImageGroupDialog = async () => {
  data.showSelectElementImageGroupDialog = false
}

/** 选择元素拼接 **/
const getSelectElementImageGroupList = async () => {
  selectElementImageGroupRef.value.data.isSaving = true
  data.imageInfoForm[currentSelectImageGroupType.value.field] =
    selectElementImageGroupRef.value.data.selectImageGroupList
  selectElementImageGroupRef.value.data.isSaving = false
  await closeSelectElementImageGroupDialog()
}

const getImageInfoData = async () => {
  let imageList = []

  if (data.imageInfoForm.hasOwnProperty(ImageType.MASK_IMAGE.field)) {
    let maskImage = data.imageInfoForm[ImageType.MASK_IMAGE.field]
    imageList.push({
      diyImageTypeCode: ImageType.MASK_IMAGE.code,
      diyImageTypeName: ImageType.MASK_IMAGE.name,
      diyImageFileJson: maskImage
    })
  }

  if (data.imageInfoForm.hasOwnProperty(ImageType.FRONT_IMAGE.field)) {
    let frontImage = data.imageInfoForm[ImageType.FRONT_IMAGE.field]
    if (frontImage) {
      imageList.push({
        diyImageTypeCode: ImageType.FRONT_IMAGE.code,
        diyImageTypeName: ImageType.FRONT_IMAGE.name,
        diyImageFileJson: frontImage
      })
    }
  }

  if (data.imageInfoForm.hasOwnProperty(ImageType.BACK_IMAGE.field)) {
    let backImage = data.imageInfoForm[ImageType.BACK_IMAGE.field]
    imageList.push({
      diyImageTypeCode: ImageType.BACK_IMAGE.code,
      diyImageTypeName: ImageType.BACK_IMAGE.name,
      diyImageFileJson: backImage
    })
  }

  if (data.imageInfoForm.hasOwnProperty(ImageGroupType.STICK_IMAGE_GROUP.field)) {
    let stickImageGroup = data.imageInfoForm[ImageGroupType.STICK_IMAGE_GROUP.field]
    if (stickImageGroup?.length > 0) {
      imageList.push({
        diyImageTypeCode: ImageGroupType.STICK_IMAGE_GROUP.code,
        diyImageTypeName: ImageGroupType.STICK_IMAGE_GROUP.name,
        diyImageFileJson: stickImageGroup
      })
    }
  }

  if (data.imageInfoForm.hasOwnProperty(ImageGroupType.ELEMENT_IMAGE_GROUP.field)) {
    let elementImageGroup = data.imageInfoForm[ImageGroupType.ELEMENT_IMAGE_GROUP.field]
    if (elementImageGroup?.length > 0) {
      imageList.push({
        diyImageTypeCode: ImageGroupType.ELEMENT_IMAGE_GROUP.code,
        diyImageTypeName: ImageGroupType.ELEMENT_IMAGE_GROUP.name,
        diyImageFileJson: elementImageGroup
      })
    }
  }

  return imageList
}

const setImageList = async (editImageList: Array<any>) => {
  setTimeout(() => {
    convertImageList(data.imageInfoForm, JSON.parse(JSON.stringify(editImageList)))
  }, 50)
}

const showImageLocationDialog = async () => {
  data.showImageLocationDialog = true
  if (data.imageInfoForm?.frontImage?.locationInfo?.pixelInfo?.width) {
    nextTick(async () => {
      await imageLocationRef?.value?.setImageLocation(
        data.imageInfoForm.frontImage.locationInfo.pixelInfo
      )
    })
  }
}

const closeImageLocationDialog = async () => {
  data.showImageLocationDialog = false
}

const setImageLocationInfo = async () => {
  data.imageInfoForm.frontImage.locationInfo = await imageLocationRef.value.getImageLocation()
  await closeImageLocationDialog()
}

const setShowElementImageGroupFlag = async (flag: Boolean) => {
  setTimeout(() => {
    showElementImageGroupFlag.value = flag
    if (!flag) {
      delete data.imageInfoForm[ImageGroupType.ELEMENT_IMAGE_GROUP.field]
    }
  }, 50)
}

const setShowImageLocationFlag = async (flag: Boolean, update: Boolean) => {
  setTimeout(() => {
    showImageLocationFlag.value = flag
    if (flag) {
      if (
        data.imageInfoForm?.frontImage &&
        !data.imageInfoForm.frontImage.hasOwnProperty('locationInfo')
      ) {
        data.imageInfoForm.frontImage.locationInfo = {
          pixelInfo: {}
        }
      }
    } else {
      if (data.imageInfoForm?.frontImage?.locationInfo) {
        delete data.imageInfoForm.frontImage.locationInfo
      }
    }
  }, 50)
}

/** 框选区域校验 **/
const checkImageLocation = async (rule: any, value: any, callback: any) => {
  if (
    showImageLocationFlag.value &&
    data.imageInfoForm?.frontImage &&
    !data.imageInfoForm?.frontImage?.locationInfo?.pixelInfo?.hasOwnProperty('width')
  ) {
    callback(new Error(t('productsShow.diyCardProduct.halfImgSetRegion')))
  }
  callback()
}

onMounted(async () => {})

defineExpose({
  imageInfoFormRef,
  setImageList,
  getImageInfoData,
  setShowImageLocationFlag,
  setShowElementImageGroupFlag
})
</script>

<style lang="less">
.select-image-div {
  background-color: #e0e0e0;
  width: 375px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.select-image-div-finish {
  width: 375px;
  height: 150px;
  display: flex;
  justify-content: left;
  cursor: pointer;
}

.select-image-group-div {
  background-color: #e0e0e0;
  width: 375px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.check-image-button {
  width: 150px;
  height: auto;
  margin: 5px !important;
  padding: 8px 15px !important;
  span {
    white-space: break-spaces;
    text-wrap: balance;
  }
}

.element-image-row {
  margin-bottom: 5px;
}

.front-image-location-form {
  margin-top: 5px;
  .el-button {
    //min-width: 100px !important;
    min-width: 100% !important;
  }
}
</style>
