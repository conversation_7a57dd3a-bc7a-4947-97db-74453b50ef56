<template>
  <Dialog
    v-model="data.showDialog"
    :title="props.dialogTitle"
    :before-close="props.closeDialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :width="'80%'"
  >
    <div
      v-if="data.showDialog"
      :style="{
        position: 'relative',
        height: data.imageHeight + 'px'
      }"
    >
      <el-image v-if="imageUrl !== ''" @load="imageLoad" :src="imageUrl" />
      <Drager
        v-bind="locationInfo"
        :gridX="1"
        :gridY="1"
        :snapToGrid="true"
        :resizable="true"
        :rotatable="locationInfo.rotatable"
        :selected="locationInfo.selected"
        boundary
        @change="onChange"
        :disabled="showDetail"
        :style="{ border: '1px dashed #FF9800', opacity: '1' }"
      >
        <template #rotate>
          <div></div>
        </template>
      </Drager>
    </div>

    <el-form
      :disabled="showDetail"
      :model="locationInfo"
      label-width="120px"
      label-position="right"
    >
      <el-card
        shadow="hover"
        style="margin-top: 10px"
        :header="t('productsShow.diyCardProduct.setRegion')"
      >
        <el-row :gutter="10" style="width: 100%">
          <el-col :span="6">
            <el-form-item prop="width" :label="t('productsShow.diyCardProduct.setWidth')">
              <el-input-number
                v-model="locationInfo.width"
                :precision="0"
                :min="0"
                :controls="false"
                :max="data.imageWidth - (locationInfo.left < 0 ? 0 : locationInfo.left)"
                value-on-clear="min"
                clearable
              />
              <el-text style="margin-left: 5px">{{ 'px' }}</el-text>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="height" :label="t('productsShow.diyCardProduct.setHight')">
              <el-input-number
                v-model="locationInfo.height"
                :precision="0"
                :min="0"
                :controls="false"
                :max="data.imageHeight - (locationInfo.top < 0 ? 0 : locationInfo.top)"
                value-on-clear="min"
                clearable
              />
              <el-text style="margin-left: 5px">{{ 'px' }}</el-text>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="hover"
        style="margin-top: 10px"
        :header="t('productsShow.diyCardProduct.setLocation')"
      >
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item prop="top" :label="t('productsShow.diyCardProduct.distanceTop')">
              <el-input-number
                v-model="locationInfo.top"
                :precision="0"
                :min="0"
                :controls="false"
                :max="data.imageHeight - locationInfo.height"
                value-on-clear="min"
                clearable
              />
              <el-text style="margin-left: 5px">{{ 'px' }}</el-text>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="bottom" :label="t('productsShow.diyCardProduct.distanceBottom')">
              <el-input-number
                v-model="locationInfo.bottom"
                :precision="0"
                :min="0"
                :controls="false"
                :max="data.imageHeight - locationInfo.height"
                value-on-clear="min"
                clearable
              />
              <el-text style="margin-left: 5px">{{ 'px' }}</el-text>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="left" :label="t('productsShow.diyCardProduct.distanceLeft')">
              <el-input-number
                v-model="locationInfo.left"
                :precision="0"
                :min="0"
                :controls="false"
                :max="data.imageWidth - locationInfo.width"
                value-on-clear="min"
                clearable
              />
              <el-text style="margin-left: 5px">{{ 'px' }}</el-text>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="right" :label="t('productsShow.diyCardProduct.distanceRight')">
              <el-input-number
                v-model="locationInfo.right"
                :precision="0"
                :min="0"
                :controls="false"
                :max="data.imageWidth - locationInfo.width"
                value-on-clear="min"
                clearable
              />
              <el-text style="margin-left: 5px">{{ 'px' }}</el-text>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <template #footer>
      <el-button @click="props.closeDialog">{{ t('common.cancel') }}</el-button>
      <el-button
        v-if="!showDetail"
        type="success"
        :loading="data.isSaving"
        @click="setImageLocationInfo"
      >
        {{ t('common.ok') }}
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
const { t } = useI18n()
import * as ProductApi from '@/api/product/diyCard'
import { Dialog } from '@/components/Dialog'
import Pagination from '@/components/Pagination/index.vue'
import { reactive, unref } from 'vue'
import 'es-drager/lib/style.css'
import Drager, { DragData } from 'es-drager'

const showDetail = ref(false)
const initDragData = ref(true)
const locationInfo = ref<DragData>({
  width: 100,
  height: 100,
  top: 0,
  left: 0,
  angle: 0,
  rotatable: false,
  selected: true,
  // 自定义底部，右边
  bottom: 0,
  right: 0
})

const data = reactive({
  showDialog: true,
  isSaving: false,

  imageWidth: 0,
  imageHeight: 0,

  updateImageLocation: {}
})

const props = defineProps({
  dialogTitle: {
    type: String,
    required: true
  },
  closeDialog: {
    type: Function,
    required: true
  },
  imageUrl: {
    type: String,
    required: false
  },
  setImageLocationInfo: {
    type: Function,
    required: false
  }
})

const imageUrl = toRef(props.imageUrl)

const onChange = (dragData: DragData) => {
  // locationInfo.value = dragData
  locationInfo.value.width = dragData.width
  locationInfo.value.height = dragData.height
  locationInfo.value.top = dragData.top
  locationInfo.value.left = dragData.left
  locationInfo.value.angle = dragData.angle
  locationInfo.value.selected = dragData.selected
  const bottom = data.imageHeight - dragData.height - dragData.top
  const right = data.imageWidth - dragData.width - dragData.left
  locationInfo.value.bottom = bottom < 0 ? 0 : bottom
  locationInfo.value.right = right < 0 ? 0 : right
}

watch(
  () => locationInfo.value,
  () => {
    const bottom = data.imageHeight - locationInfo.value.height - locationInfo.value.top
    locationInfo.value.bottom = bottom < 0 ? 0 : bottom
    const right = data.imageWidth - locationInfo.value.width - locationInfo.value.left
    locationInfo.value.right = right < 0 ? 0 : right
  },
  { immediate: true, deep: true }
)

watch(
  () => locationInfo.value.bottom,
  (bottom) => {
    const top = data.imageHeight - locationInfo.value.height - bottom
    locationInfo.value.top = top < 0 ? 0 : top
  },
  { immediate: true, deep: true }
)

watch(
  () => locationInfo.value.right,
  (right) => {
    const left = data.imageWidth - locationInfo.value.width - right
    locationInfo.value.left = left < 0 ? 0 : left
  },
  { immediate: true, deep: true }
)

const imageLoad = async (e: Event) => {
  data.imageWidth = e.target.width
  data.imageHeight = e.target.height
  // 需要初始化框定区域
  if (initDragData.value) {
    locationInfo.value.width = e.target.width
    locationInfo.value.height = e.target.height
  } else {
    locationInfo.value.width = data.updateImageLocation.width
    locationInfo.value.height = data.updateImageLocation.height
    locationInfo.value.top = data.updateImageLocation.top
    locationInfo.value.left = data.updateImageLocation.left
    locationInfo.value.bottom = data.updateImageLocation.bottom
    locationInfo.value.right = data.updateImageLocation.right
    locationInfo.value.angle = 0
    locationInfo.value.selected = false
  }
}

const setImageLocation = async (imageLocationPixelInfo, url?, isDetail?) => {
  data.updateImageLocation = imageLocationPixelInfo
  // 不需要初始化框定区域
  initDragData.value = false

  if (url) {
    imageUrl.value = url
  }
  if (isDetail) {
    showDetail.value = true
  }
}

const getImageLocation = async () => {
  const width = Number(locationInfo.value.width.toFixed(0))
  const height = Number(locationInfo.value.height.toFixed(0))
  const top = Number(locationInfo.value.top.toFixed(0))
  const bottom = Number(locationInfo.value.bottom.toFixed(0))
  const left = Number(locationInfo.value.left.toFixed(0))
  const right = Number(locationInfo.value.right.toFixed(0))
  return {
    imageWidth: data.imageWidth,
    imageHeight: data.imageHeight,
    // 位置像素信息
    pixelInfo: {
      width: width,
      height: height,
      top: top,
      bottom: bottom,
      left: left,
      right: right
    },
    // 位置百分比信息
    percentageInfo: {
      width: Number(((width / data.imageWidth) * 100).toFixed(3)),
      height: Number(((height / data.imageHeight) * 100).toFixed(3)),
      top: Number(((top / data.imageHeight) * 100).toFixed(3)),
      bottom: Number(((bottom / data.imageHeight) * 100).toFixed(3)),
      left: Number(((left / data.imageWidth) * 100).toFixed(3)),
      right: Number(((right / data.imageWidth) * 100).toFixed(3))
    }
  }
}

onMounted(async () => {
  if (props.imageUrl) {
    imageUrl.value = props.imageUrl
  }
})

defineExpose({ setImageLocation, getImageLocation })
</script>

<style scoped lang="less"></style>
