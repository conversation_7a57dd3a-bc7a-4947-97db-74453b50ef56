<template>
  <el-form :model="data.priceSettingForm" ref="priceSettingFormRef" class="price-setting-form">
    <el-row :gutter="10" style="margin: 10px 0">
      <el-col :span="12">
        <el-form-item
          :label="t('productsShow.diyCardProduct.priceAndUnit')"
          prop="price"
          :rules="[{ required: true, asyncValidator: checkPrice, trigger: 'blur' }]"
        >
          <div v-if="props.productDetailFlag"> {{ data.priceSettingForm.price }} </div>
          <el-input-number
            v-else
            v-model="data.priceSettingForm.price"
            :placeholder="t('productsShow.diyCardProduct.priceAndUnitPlaceholder')"
            :precision="2"
            :min="0.0"
            :max="99999999.99"
            :controls="false"
            :value-on-clear="null"
            clearable
            style="width: 100%"
            @paste="priceInputPaste"
            @keydown="priceInputInhibit"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          :label="t('productsShow.diyCardProduct.originalPriceAndUnit')"
          prop="salePrice"
          label-width="120px"
          :rules="[{ required: false, asyncValidator: checkSalePrice, trigger: 'blur' }]"
        >
          <div v-if="props.productDetailFlag"> {{ data.priceSettingForm.salePrice }} </div>
          <el-input-number
            v-else
            v-model="data.priceSettingForm.salePrice"
            :placeholder="t('productsShow.diyCardProduct.originalPriceAndUnitPlaceholder')"
            :precision="2"
            :min="0.0"
            :max="99999999.99"
            :controls="false"
            :value-on-clear="null"
            clearable
            style="width: 100%"
            @paste="priceInputPaste"
            @keydown="priceInputInhibit"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-text>{{ t('productsShow.diyCardProduct.otherPriceAllocation') }}:</el-text>
    </el-row>
    <el-row class="product-price-row">
      <el-table
        class="product-price-table"
        :scrollbar-always-on="true"
        :data="data.priceSettingForm.tableData"
        v-loading="data.tableLoading"
        :span-method="rowSpan"
        :cell-class-name="cellClass"
        @header-dragend="headerDragend"
        border
      >
        <el-table-column
          prop="paramName"
          :resizable="true"
          :min-width="180"
          align="left"
          headerAlign="center"
          showOverflowTooltip
        >
          <template #header>
            <span class="required-field-tag">{{
              t('productsShow.diyCardProduct.argumentName')
            }}</span>
          </template>
          <template #default="scope">
            <el-form-item
              label-width="0px"
              :prop="'tableData.' + scope.$index + '.paramName'"
              :rules="[
                {
                  required: true,
                  message: t('productsShow.diyCardProduct.argumentNamePlaceholder'),
                  trigger: 'blur'
                }
              ]"
            >
              <div v-if="props.productDetailFlag"> {{ scope.row[scope.column.property] }} </div>
              <el-input
                v-else
                v-model.trim="scope.row[scope.column.property]"
                type="textarea"
                :rows="3"
                :placeholder="t('productsShow.diyCardProduct.argumentName')"
                maxlength="20"
                show-word-limit
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="paramValue"
          :resizable="true"
          :min-width="180"
          align="left"
          headerAlign="center"
          showOverflowTooltip
        >
          <template #header>
            <span class="required-field-tag">{{
              t('productsShow.diyCardProduct.argumentValue')
            }}</span>
          </template>
          <template #default="scope">
            <el-form-item
              label-width="0px"
              :prop="'tableData.' + scope.$index + '.paramValue'"
              :rules="[{ required: true, asyncValidator: checkParamValue, trigger: 'blur' }]"
            >
              <div v-if="props.productDetailFlag"> {{ scope.row[scope.column.property] }} </div>
              <el-input
                v-else
                v-model.trim="scope.row[scope.column.property]"
                type="textarea"
                :rows="3"
                :placeholder="t('productsShow.diyCardProduct.argumentValue')"
                maxlength="20"
                show-word-limit
                clearable
                style="width: 100%"
                @blur="formValidate"
              />
            </el-form-item>
          </template>
        </el-table-column>

        <!-- 动态表格区域 -->
        <el-table-column
          align="left"
          headerAlign="center"
          :resizable="true"
          :width="tableColWidth.startDateWidth"
          style="border: none !important"
        >
          <template #header>
            <span class="required-field-tag">{{
              t('productsShow.diyCardProduct.effectBeginTime')
            }}</span>
          </template>
          <template #default="priceAttrScope">
            <el-table
              class="product-price-table-attr"
              :data="priceAttrScope.row.priceJson"
              border
              :show-header="false"
              row-class-name="price-attr-table-row"
            >
              <el-table-column
                prop="startDate"
                :label="t('productsShow.diyCardProduct.effectBeginTime')"
                :width="tableColWidth.startDateWidth"
                align="left"
                headerAlign="center"
                showOverflowTooltip
              >
                <template #default="scope">
                  <el-form-item
                    label-width="0px"
                    :prop="
                      'tableData.' +
                      priceAttrScope.$index +
                      '.priceJson.' +
                      scope.$index +
                      '.startDate'
                    "
                    :rules="[
                      {
                        required: true,
                        asyncValidator: checkStartDate,
                        trigger: ['change', 'blur']
                      }
                    ]"
                  >
                    <div v-if="props.productDetailFlag">
                      {{ scope.row[scope.column.property] }}
                    </div>
                    <el-date-picker
                      v-else
                      v-model="scope.row[scope.column.property]"
                      type="datetime"
                      :placeholder="t('productsShow.diyCardProduct.beginTime')"
                      format="YYYY.MM.DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      style="width: 100%"
                      @change="formValidate"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                prop="endDate"
                :label="t('productsShow.diyCardProduct.effectFinishDay')"
                align="left"
                :width="tableColWidth.endDateWidth"
                headerAlign="center"
                showOverflowTooltip
              >
                <template #default="scope">
                  <el-form-item
                    label-width="0px"
                    :prop="
                      'tableData.' +
                      priceAttrScope.$index +
                      '.priceJson.' +
                      scope.$index +
                      '.endDate'
                    "
                    :rules="[
                      { required: false, asyncValidator: checkEndDate, trigger: ['change', 'blur'] }
                    ]"
                  >
                    <div v-if="props.productDetailFlag">
                      {{ scope.row[scope.column.property] }}
                    </div>
                    <el-date-picker
                      v-else
                      v-model="scope.row[scope.column.property]"
                      type="datetime"
                      :placeholder="t('productsShow.diyCardProduct.finishTime')"
                      format="YYYY.MM.DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      style="width: 100%"
                      @change="formValidate"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                prop="price"
                :width="tableColWidth.priceWidth"
                :label="t('productsShow.diyCardProduct.priceAndUnit')"
                align="left"
                headerAlign="center"
                showOverflowTooltip
              >
                <template #default="scope">
                  <el-form-item
                    label-width="0px"
                    :prop="
                      'tableData.' + priceAttrScope.$index + '.priceJson.' + scope.$index + '.price'
                    "
                    :rules="[{ required: true, asyncValidator: checkPrice, trigger: 'blur' }]"
                  >
                    <div v-if="props.productDetailFlag">
                      {{ scope.row[scope.column.property] }}
                    </div>
                    <el-input-number
                      v-else
                      v-model="scope.row[scope.column.property]"
                      :placeholder="t('productsShow.diyCardProduct.priceAndUnitPlaceholder')"
                      :precision="2"
                      :min="0.0"
                      :max="99999999.99"
                      :controls="false"
                      :value-on-clear="null"
                      clearable
                      style="width: 100%"
                      @blur="formValidate"
                      @paste="priceInputPaste"
                      @keydown="priceInputInhibit"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                prop="salePrice"
                :width="tableColWidth.salePriceWidth"
                :label="t('productsShow.diyCardProduct.originalPriceAndUnit')"
                align="left"
                headerAlign="center"
                showOverflowTooltip
              >
                <template #default="scope">
                  <el-form-item
                    label-width="0px"
                    :prop="
                      'tableData.' +
                      priceAttrScope.$index +
                      '.priceJson.' +
                      scope.$index +
                      '.salePrice'
                    "
                    :rules="[{ required: true, asyncValidator: checkSalePrice, trigger: 'blur' }]"
                  >
                    <div v-if="props.productDetailFlag">
                      {{ scope.row[scope.column.property] }}
                    </div>
                    <el-input-number
                      v-else
                      v-model="scope.row[scope.column.property]"
                      :placeholder="
                        t('productsShow.diyCardProduct.originalPriceAndUnitPlaceholder')
                      "
                      :precision="2"
                      :min="0.0"
                      :max="99999999.99"
                      :controls="false"
                      :value-on-clear="null"
                      clearable
                      style="width: 100%"
                      @blur="formValidate"
                      @paste="priceInputPaste"
                      @keydown="priceInputInhibit"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                v-if="!props.productDetailFlag"
                align="center"
                :width="tableColWidth.operateWidth"
                headerAlign="center"
                :label="t('productsShow.diyCardProduct.operate')"
              >
                <template #default="scope">
                  <el-button
                    plain
                    type="danger"
                    :icon="Minus"
                    @click="
                      deletePriceAttrRow(priceAttrScope.row, priceAttrScope.$index, scope.$index)
                    "
                  />
                </template>
              </el-table-column>
            </el-table>
            <div class="price-setting-add-attr" v-if="!props.productDetailFlag">
              <el-button
                plain
                type="primary"
                :icon="Plus"
                @click="addPriceAttrRow(priceAttrScope.row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('productsShow.diyCardProduct.effectFinishTime')"
          align="left"
          headerAlign="center"
          :width="tableColWidth.endDateWidth"
          :resizable="true"
          showOverflowTooltip
        />
        <el-table-column
          :width="tableColWidth.priceWidth"
          align="left"
          headerAlign="center"
          :resizable="true"
          showOverflowTooltip
        >
          <template #header>
            <span class="required-field-tag">{{
              t('productsShow.diyCardProduct.priceAndUnit')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :width="tableColWidth.salePriceWidth"
          :label="t('productsShow.diyCardProduct.originalPriceAndUnit')"
          align="left"
          headerAlign="center"
          :resizable="true"
          showOverflowTooltip
        />
        <el-table-column
          v-if="!props.productDetailFlag"
          align="center"
          :width="tableColWidth.operateWidth"
          headerAlign="center"
          :resizable="true"
          :label="t('productsShow.diyCardProduct.operate')"
        />
      </el-table>
      <div class="price-setting-add-row" v-if="!props.productDetailFlag">
        <el-button plain type="primary" :icon="Plus" @click="addPriceRow" />
      </div>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import {
  priceInputInhibit,
  priceInputPaste
} from '@/views/ProductsShow/DiyCardProduct/diyCardProduct'
import { reactive, unref } from 'vue'
import { isNullOrUnDef } from '@/utils/is'
import { Plus, Minus } from '@element-plus/icons-vue'
const { t } = useI18n()

const priceSettingFormRef = ref()

const tableColWidth = ref({
  startDateWidth: 220,
  endDateWidth: 220,
  priceWidth: 180,
  salePriceWidth: 180,
  operateWidth: 80
})

const data = reactive({
  tableLoading: false,
  priceSettingForm: {
    tableData: []
  }
})

const props = defineProps({
  editPriceData: {
    type: Array,
    default: () => []
  },
  productDetailFlag: {
    type: Boolean,
    default: false
  }
})

/** 添加价格配置（生效时间部分） **/
const addPriceAttrRow = async (row) => {
  row.priceJson.push({ startDate: '', endDate: '', price: null, salePrice: null })
}

/** 删除价格配置（生效时间部分） **/
const deletePriceAttrRow = async (attrRow, attrIndex, dIndex) => {
  // 如果只有一个，需要同步删除这一整行的数据
  if (attrRow.priceJson.length === 1) {
    data.priceSettingForm.tableData.splice(attrIndex, 1)
  } else {
    attrRow.priceJson.splice(dIndex, 1)
  }
}

/** 添加价格配置整行 **/
function addPriceRow() {
  data.priceSettingForm.tableData.push({
    paramName: '',
    paramValue: '',
    priceJson: [{ startDate: '', endDate: '', price: null, salePrice: null }]
  })
}

const rowSpan = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 2) {
    return [1, 5]
  } else {
    return [1, 1]
  }
}

const cellClass = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 2) {
    return 'start-date-cell-span'
  }
}

const headerDragend = (newWidth, oldWidth, column, event) => {
  if (column.no === 2) {
    tableColWidth.value.startDateWidth = newWidth
  }
  if (column.no === 3) {
    tableColWidth.value.endDateWidth = newWidth
  }
  if (column.no === 4) {
    tableColWidth.value.priceWidth = newWidth
  }
  if (column.no === 5) {
    tableColWidth.value.salePriceWidth = newWidth
  }
  if (column.no === 6) {
    tableColWidth.value.operateWidth = newWidth
  }
}

/** 参数值校验 **/
const checkParamValue = async (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error(t('productsShow.diyCardProduct.argumentValuePlaceholder')))
  }
  let repetitionParamValueList = data.priceSettingForm.tableData.filter(
    (data) => data.paramValue === value
  )
  if (repetitionParamValueList.length > 1) {
    callback(new Error(t('productsShow.diyCardProduct.argumentValueNoRepeat')))
  }
}

const checkStartDate = async (rule: any, value: any, callback: any) => {
  let field = rule.field.split('.')
  let rowIndex = Number(field[1])
  let rowAttrIndex = Number(field[3])
  if (!value) {
    callback(new Error(t('productsShow.diyCardProduct.effectBeginTimePlaceholder')))
  }

  let attrPriceList = data.priceSettingForm.tableData[rowIndex].priceJson
  // 当前行属性
  let rowAttr = attrPriceList[rowAttrIndex]
  // 非当前行属性
  let nonCurrentRow = attrPriceList.filter((item, index) => index !== rowAttrIndex)
  if (rowAttr.startDate && rowAttr.endDate && nonCurrentRow.length > 0) {
    let currStartTime = new Date(rowAttr.startDate).getTime()
    let currEndTime = new Date(rowAttr.endDate).getTime()
    // 当前行属性与非当前行属性进行范围匹配
    for (let i = 0; i < nonCurrentRow.length; i++) {
      let nonCurrAttr = nonCurrentRow[i]
      let startTime = new Date(nonCurrAttr.startDate).getTime()
      let endTime = new Date(nonCurrAttr.endDate).getTime()
      if (
        (currStartTime >= startTime && currStartTime <= endTime) ||
        (currEndTime >= startTime && currEndTime <= endTime)
      ) {
        callback(new Error(t('productsShow.diyCardProduct.effectBeginTimeNoRepeat')))
      }
    }
  }

  /*let repetitionParamValueList = attrPriceList.filter((data) => data.startDate === value)
  if (repetitionParamValueList.length > 1) {
    callback(new Error('同参数值生效时间配置不能重合'))
  }

  if (rowAttrIndex !== 0) {
    let startDate = attrPriceList[rowAttrIndex].startDate
    let endDate = attrPriceList[rowAttrIndex - 1].endDate
    if (new Date(startDate).getTime() <= new Date(endDate).getTime()) {
      callback(new Error('同参数值生效时间配置不能重合'))
    }
  }*/

  if (rowAttr.endDate) {
    let startDate = new Date(value)
    if (startDate.getTime() >= new Date(rowAttr.endDate).getTime()) {
      callback(new Error(t('productsShow.diyCardProduct.effectBeginTimeSmall')))
    }
  }
}

const checkEndDate = async (rule: any, value: any, callback: any) => {
  let field = rule.field.split('.')
  let rowIndex = Number(field[1])
  let rowAttrIndex = Number(field[3])
  let attrPriceList = data.priceSettingForm.tableData[rowIndex].priceJson

  const nullEntDateList = attrPriceList.filter(
    (item) => isNullOrUnDef(item.endDate) || item.endDate === ''
  )
  if (nullEntDateList.length > 1) {
    callback(new Error(t('productsShow.diyCardProduct.effectBeginTimeOnlyOneNull')))
  }

  // 永久时间段的开始时间节点
  const prStartDate = nullEntDateList[0]?.startDate || ''
  if (prStartDate) {
    let endDate = attrPriceList[rowAttrIndex].endDate
    if (!isNullOrUnDef(endDate) && endDate !== '') {
      if (new Date(endDate).getTime() >= new Date(prStartDate).getTime()) {
        callback(new Error(t('productsShow.diyCardProduct.effectFinishDayForeverSmall')))
      }
    }
  }
}

/** 价格校验 **/
const checkPrice = async (rule: any, value: any, callback: any) => {
  if (value === null || value === undefined || isNaN(value)) {
    // 强制刷新123e这种情况
    await priceInputChange(rule.field)
    callback(new Error(t('productsShow.diyCardProduct.pricePlaceholder')))
  }
  //   if (Number(value) <= 0) {
  //     callback(new Error('价格不能小于或等于0'))
  //   }
  callback()
}

/** 原价-字段校验 **/
const checkSalePrice = async (rule: any, value: any, callback: any) => {
  if (value === null || value === undefined || isNaN(value)) {
    await priceInputChange(rule.field)
    callback()
  }
  //   if (Number(value) <= 0) {
  //     callback(new Error('价格不能小于或等于0'))
  //   }
}

/** 处理价格输入框输入特殊字符后无法自动更新的问题 **/
const priceInputChange = async (field) => {
  const fieldArray = field.split('.')
  if (fieldArray.length === 1) {
    const readField = fieldArray[0]
    nextTick(() => {
      data.priceSettingForm[readField] = 1
      nextTick(() => {
        data.priceSettingForm[readField] = NaN
      })
    })
  }
  if (fieldArray.length > 1) {
    // tableData.0.priceJson.0.salePrice
    const tabIndex = fieldArray[1]
    const jsonIndex = fieldArray[3]
    const readField = fieldArray[4]
    let updateField = data.priceSettingForm.tableData[tabIndex].priceJson[jsonIndex][readField]
    nextTick(() => {
      data.priceSettingForm.tableData[tabIndex].priceJson[jsonIndex][readField] = 1
      nextTick(() => {
        data.priceSettingForm.tableData[tabIndex].priceJson[jsonIndex][readField] = NaN
      })
    })
  }
}

const formValidate = () => {
  unref(priceSettingFormRef)?.validate((valid) => {})
}

const getPriceSettingData = async () => {
  return {
    price: data.priceSettingForm.price,
    salePrice:
      typeof data.priceSettingForm.salePrice == 'number' ? data.priceSettingForm.salePrice : null,
    tableData: data.priceSettingForm.tableData
  }
}

// 回显价格配置
const setPriceList = async (
  price: number | null,
  salePrice: number | null,
  editPriceList: Array<any>
) => {
  setTimeout(() => {
    data.priceSettingForm.price = price
    data.priceSettingForm.salePrice = salePrice
    data.priceSettingForm.tableData = editPriceList
  }, 50)
}

onMounted(async () => {})

defineExpose({
  priceSettingFormRef,
  setPriceList,
  getPriceSettingData
})
</script>

<style lang="less">
.product-price-row {
  overflow-x: auto;
  width: 100%;
  display: inline-block;
  border: solid 1px #ebeef5;
}
.product-price-table {
  .product-price-table-attr {
    //margin-top: 10px;
  }
  width: fit-content !important;
  max-width: fit-content !important;
  min-width: 100% !important;
  //margin-top: -1px;
  //margin-left: -1px;
  .el-table__inner-wrapper {
    width: fit-content;
  }
  .el-scrollbar {
    .el-scrollbar__bar.is-horizontal {
      display: none !important;
    }
  }
}
.el-table td.start-date-cell-span {
  padding: 0 !important;
  border: none !important;
  > :first-child {
    min-height: 100px;
    padding: 0 !important;
    margin-top: -1px !important;
    .el-table--border::before {
      width: 0px !important;
    }
    .el-table__border-left-patch {
      display: none !important;
    }
  }
}
.price-setting-form {
  width: 100%;
  .el-form-item {
    margin-bottom: 0 !important;
    .el-form-item__error {
      position: static;
    }
  }
}
.required-field-tag::before {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}
.price-setting-add-row {
  text-align: center;
  width: 100%;
  padding: 5px 0;
  //border: solid 1px #ebeef5;
  border-top: none;
}
.price-setting-add-attr {
  border-right: solid 1px #ebeef5;
  text-align: center;
  width: 100%;
  padding: 5px 0;
}

.price-attr-table-row > tr:first-child {
  td {
    border-top: none !important;
  }
}
.price-attr-table-row {
  tr:nth-child(1) {
    border-top: none !important;
  }
  td:last-child(1) {
    border-right: none !important;
  }
}
</style>
