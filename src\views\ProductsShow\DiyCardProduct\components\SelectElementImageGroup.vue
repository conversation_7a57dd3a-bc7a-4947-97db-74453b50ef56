<template>
  <Dialog
    v-model="data.showDialog"
    :title="props.dialogTitle"
    :before-close="props.closeDialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :width="'80%'"
  >
    <el-checkbox-group v-model="data.selectImageGroupIdList">
      <el-row :gutter="10" v-for="(imageGroup, index) in data.imageGroupList" :key="index">
        <!-- 第二级分类 -->
        <el-col :span="24">
          <el-row
            :gutter="10"
            v-for="(attrChildren, cIndex) in imageGroup.childMaterialAttr"
            :key="cIndex"
          >
            <!-- 第一级分类 在二级分类渲染时再渲染 -->
            <el-col :span="4" class="image-group-name-col">
              <el-checkbox-button
                v-if="cIndex === 0"
                :label="imageGroup.diyMaterialInfoGroupid"
                class="group-name-button"
                @change="
                  addElementGroup(
                    {
                      elementGroupId: imageGroup.diyMaterialInfoGroupid,
                      elementGroupName: imageGroup.diyMaterialInfoGroupname
                    },
                    imageGroup.childMaterialAttr,
                    $event
                  )
                "
              >
                {{ imageGroup.diyMaterialInfoGroupname }}
              </el-checkbox-button>
            </el-col>

            <el-col :span="4" class="image-group-name-col">
              <el-checkbox-group v-model="data.selectImageGroupChildrenIdList">
                <el-checkbox-button
                  :label="attrChildren.diyMaterialInfoGroupid"
                  class="group-name-button"
                  @change="
                    addElementGroupChildren(
                      {
                        elementGroupId: imageGroup.diyMaterialInfoGroupid,
                        elementGroupName: imageGroup.diyMaterialInfoGroupname
                      },
                      {
                        elementGroupParentId: imageGroup.diyMaterialInfoGroupid,
                        elementGroupId: attrChildren.diyMaterialInfoGroupid,
                        elementGroupName: attrChildren.diyMaterialInfoGroupname
                      },
                      $event
                    )
                  "
                >
                  {{ attrChildren.diyMaterialInfoGroupname }}
                </el-checkbox-button>
              </el-checkbox-group>
            </el-col>
            <el-col
              :span="16"
              v-if="attrChildren.childMaterialInfo && attrChildren.childMaterialInfo.length > 0"
            >
              <el-image
                v-for="(infoChildren, dIndex) in attrChildren.childMaterialInfo"
                :key="dIndex"
                :title="
                  t('productsShow.diyCardProduct.preview') + ': ' + infoChildren.diyMaterialInfoName
                "
                class="product-image-detail-list"
                fit="scale-down"
                lazy
                :zoom-rate="1.2"
                :src="infoChildren.diyMaterialInfoUrl"
                preview-teleported
                :initial-index="0"
                :preview-src-list="[infoChildren.diyMaterialInfoUrl]"
                ><template #error>
                  <el-icon><Picture /></el-icon>
                </template>
              </el-image>
            </el-col>
            <el-col :span="16" v-else>
              <el-button
                text
                bg
                type="primary"
                style="width: 100%; height: 80px; margin: 5px; justify-content: flex-start"
              >
                {{ t('productsShow.diyCardProduct.noImgOfGroup') }}...
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-checkbox-group>
    <template #footer>
      <el-button @click="props.closeDialog">{{ t('common.cancel') }}</el-button>
      <el-button type="success" :loading="data.isSaving" @click="getSelectImageGroupList">
        {{ t('common.ok') }}
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
const { t } = useI18n()
import * as ProductApi from '@/api/product/diyCard'
import { Dialog } from '@/components/Dialog'
import Pagination from '@/components/Pagination/index.vue'
import { reactive, unref } from 'vue'

const data = reactive({
  showDialog: true,
  isSaving: false,
  imageInfoForm: {},

  selectImageGroupList: [],
  selectImageGroupIdList: [],
  selectImageGroupChildrenIdList: [],

  imageGroupList: [],

  pageNo: 0,
  pageSize: 10,
  imageListCount: 0
})

const props = defineProps({
  dialogTitle: {
    type: String,
    required: true
  },
  currentProjectService: {
    type: Object,
    required: true
  },
  currentSelectGroupList: {
    type: Array,
    default: () => []
  },
  closeDialog: {
    type: Function,
    required: true
  },
  getSelectImageGroupList: {
    type: Function,
    required: true
  }
})

/** 获取图片组接口 **/
const getDiyImageGroupList = async () => {
  // let serviceId = '1676787768374538241'
  let serviceId = props.currentProjectService.applyServiceId
  let params = {
    diyInfoConfigid: serviceId,
    diyMaterialInfoType: '2'
  }
  try {
    const { childMaterialAttr } = (await ProductApi.getDiyElementImageGroupListApi(params)) || []
    data.imageGroupList = childMaterialAttr
  } catch (e) {
    data.imageGroupList = []
    console.error('获取元素拼接图片组异常：', e)
  } finally {
  }
}

/** 点击一级分类 **/
const addElementGroup = async (group, sourceChildren, event) => {
  if (event) {
    let index = data.selectImageGroupList.findIndex(
      (g) => g.elementGroupId === group.elementGroupId
    )

    let children = []
    sourceChildren.forEach((item, iIndex) => {
      children.push({
        elementGroupParentId: group.elementGroupId,
        elementGroupId: item.diyMaterialInfoGroupid,
        elementGroupName: item.diyMaterialInfoGroupname
      })
      if (!data.selectImageGroupChildrenIdList.includes(item.diyMaterialInfoGroupid)) {
        data.selectImageGroupChildrenIdList.push(item.diyMaterialInfoGroupid)
      }
    })

    if (index < 0) {
      data.selectImageGroupList.push(group)
      data.selectImageGroupList[data.selectImageGroupList.length - 1].checkChildren = children
    } else {
      data.selectImageGroupList[index].checkChildren = children
    }
  } else {
    data.selectImageGroupList.splice(
      data.selectImageGroupList.indexOf(group.diyMaterialInfoGroupid),
      1
    )
    sourceChildren.forEach((item, iIndex) => {
      let cIndex = data.selectImageGroupChildrenIdList.indexOf(item.diyMaterialInfoGroupid)
      data.selectImageGroupChildrenIdList.splice(cIndex, 1)
    })
  }
}

/** 点击子分类 **/
const addElementGroupChildren = async (group, children, enevt) => {
  let index = data.selectImageGroupList.findIndex((g) => g.elementGroupId === group.elementGroupId)
  if (index >= 0) {
    if (enevt) {
      // 增加子分类
      let childrenIndex = data.selectImageGroupList[index].checkChildren.findIndex(
        (c) => c.elementGroupId === children.elementGroupId
      )
      if (childrenIndex < 0) {
        data.selectImageGroupList[index].checkChildren.push(children)
      }
    } else {
      // 删除子分类
      let cIndex = data.selectImageGroupList[index].checkChildren.findIndex(
        (g) => g.elementGroupId === children.elementGroupId
      )
      data.selectImageGroupList[index].checkChildren.splice(cIndex, 1)
      // 如果已经没有子分类，则去掉一级分类
      if (data.selectImageGroupList[index].checkChildren.length === 0) {
        data.selectImageGroupList.splice(index, 1)
        data.selectImageGroupIdList.splice(
          data.selectImageGroupIdList.indexOf(group.elementGroupId),
          1
        )
      }
    }
  } else {
    // 如果还没有一级分类
    data.selectImageGroupIdList.push(group.elementGroupId)
    data.selectImageGroupList.push(group)
    let pIndex = data.selectImageGroupList.findIndex(
      (g) => g.elementGroupId === group.elementGroupId
    )
    data.selectImageGroupList[pIndex].checkChildren = [children]
  }
}

onMounted(async () => {
  await getDiyImageGroupList()

  // 回显所选拼接元素
  if (props.currentSelectGroupList.length > 0) {
    props.currentSelectGroupList.forEach((item) => {
      let index = data.imageGroupList.findIndex(
        (g) => g.diyMaterialInfoGroupid === item.elementGroupId
      )
      if (index >= 0) {
        // 填充父级分类
        let parentGroup = data.imageGroupList[index]
        data.selectImageGroupIdList.push(item.elementGroupId)
        let parentNode = {
          elementGroupId: parentGroup.diyMaterialInfoGroupid,
          elementGroupName: parentGroup.diyMaterialInfoGroupname,
          checkChildren: []
        }

        // 填充子分类
        let parentChildrenFlag =
          parentGroup.childMaterialAttr && parentGroup.childMaterialAttr.length > 0
        let itemChildrenFlag = item.checkChildren && item.checkChildren.length > 0
        if (itemChildrenFlag && parentChildrenFlag) {
          item.checkChildren.forEach((children) => {
            let cIndex = parentGroup.childMaterialAttr.findIndex(
              (g) => g.diyMaterialInfoGroupid === children.elementGroupId
            )
            data.selectImageGroupChildrenIdList.push(children.elementGroupId)
            let childrenGroup = parentGroup.childMaterialAttr[cIndex]
            parentNode.checkChildren.push({
              elementGroupParentId: parentGroup.diyMaterialInfoGroupid,
              elementGroupId: childrenGroup.diyMaterialInfoGroupid,
              elementGroupName: childrenGroup.diyMaterialInfoGroupname
            })
          })
        }

        // 填充具体值对象
        data.selectImageGroupList.push(parentNode)
      }
    })
  }
})

defineExpose({ data })
</script>

<style scoped lang="less"></style>
