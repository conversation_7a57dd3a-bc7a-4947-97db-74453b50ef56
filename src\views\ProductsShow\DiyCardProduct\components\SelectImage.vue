<template>
  <Dialog
    v-model="data.showDialog"
    :title="props.dialogTitle"
    :before-close="props.closeDialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :width="'75%'"
  >
    <el-radio-group v-model="data.imageType" @change="imageTypeChange">
      <el-radio-button label="">{{ t('productsShow.diyCardProduct.all') }}</el-radio-button>
      <el-radio-button
        v-for="(imageTypeObj, index) in data.imageTypeList"
        :label="imageTypeObj.diyGroupInfoId"
        :key="index"
      >
        {{ imageTypeObj.diyGroupInfoName }}
      </el-radio-button>
    </el-radio-group>
    <el-checkbox-group v-model="data.selectImageIdList" :max="1" class="content">
      <div
        class="item"
        v-for="(imageObj, index) in data.imageList"
        :key="index"
        @click="selectImage(imageObj, $event)"
      >
        <el-checkbox
          class="item-checkbox"
          @click.stop.prevent="selectImage(imageObj, $event)"
          :label="imageObj.diyMaterialInfoId"
          size="large"
        >
          {{ '' }}
        </el-checkbox>
        <el-image :src="imageObj.diyMaterialInfoUrl" fit="scale-down" />
        <el-text truncated class="image-name-text">{{ imageObj.diyMaterialInfoName }}</el-text>
      </div>
    </el-checkbox-group>
    <Pagination
      style="margin-top: 10px"
      v-model:page="data.pageNo"
      v-model:limit="data.pageSize"
      :total="data.imageListCount"
      @pagination="getDiyImageList"
    />
    <template #footer>
      <el-button @click="props.closeDialog">{{ t('common.cancel') }}</el-button>
      <el-button type="success" :loading="data.isSaving" @click="getSelectImageList">
        {{ t('common.ok') }}
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
const { t } = useI18n()
import * as ProductApi from '@/api/product/diyCard'
import { Dialog } from '@/components/Dialog'
import Pagination from '@/components/Pagination/index.vue'
import { reactive, unref } from 'vue'

const data = reactive({
  showDialog: true,
  isSaving: false,
  imageInfoForm: {},

  selectImageList: [],
  selectImageIdList: [],

  imageType: '',
  imageTypeList: [],
  imageList: [],

  pageNo: 0,
  pageSize: 10,
  imageListCount: 0
})

const props = defineProps({
  dialogTitle: {
    type: String,
    required: true
  },
  currentProjectService: {
    type: Object,
    required: true
  },
  closeDialog: {
    type: Function,
    required: true
  },
  getSelectImageList: {
    type: Function,
    required: true
  }
})

const imageTypeChange = async (value) => {
  await getDiyImageList()
}

/** 获取图片素材类型接口 **/
const getDiyImageTypeList = async () => {
  // let serviceId = '1676787768374538241'
  let serviceId = props.currentProjectService.applyServiceId
  let formData = new FormData()
  formData.append('diyInfoConfigid', serviceId)
  formData.append('diyGroupInfoReusetype', '4')
  try {
    const res = await ProductApi.getDiyImageTypeListApi(formData)
    data.imageTypeList = res.data || []
  } catch (e) {
    data.imageTypeList = []
    console.error('获取图片素材分组异常：', e)
  } finally {
  }
}

/** 获取图片素材分页接口 **/
const getDiyImageList = async () => {
  // let serviceId = '1676787768374538241'
  let serviceId = props.currentProjectService.applyServiceId

  // 图片分组ID
  let groupId = data.imageType
  let formData = new FormData()
  if (groupId) {
    formData.append('diyMaterialInfoGroupid', groupId)
  }
  formData.append('diyInfoConfigid', serviceId)
  formData.append('diyMaterialInfoType', '0')
  formData.append('pageSize', data.pageSize)
  formData.append('pageNum', data.pageNo)
  try {
    const res = await ProductApi.getDiyImageListApi(formData)
    data.imageList = res.data.list || []
    data.imageListCount = res.data.total
  } catch (e) {
    data.imageList = []
    data.imageListCount = 0
    console.error('获取图片素材异常：', e)
  } finally {
  }
}

const selectImage = async (imageObj, e) => {
  // 设置选中样式
  await setItemDivElementClass(e.target.parentElement)

  let imageId = imageObj.diyMaterialInfoId
  let index = data.selectImageIdList.indexOf(imageObj.diyMaterialInfoId)
  if (index >= 0) {
    let objIndex = data.selectImageList.findIndex((i) => i.fileId === imageId)
    data.selectImageList.splice(objIndex, 1)

    data.selectImageIdList.splice(data.selectImageIdList.indexOf(imageId), 1)
  } else {
    data.selectImageIdList[0] = imageId
    data.selectImageList[0] = {
      fileId: imageId,
      fileName: imageObj.diyMaterialInfoName,
      fileUrl: imageObj.diyMaterialInfoUrl
    }
  }
}

// 上一个选中图片dom临时变量
const imageSelectElement = ref('') as HTMLElement
/** 设置样式 **/
const setItemDivElementClass = async (htmlElement: HTMLElement) => {
  const findClass = 'item'
  const itemCheckClass = 'item-checked'
  const isChecked = 'is-checked'
  const element: HTMLElement | null = htmlElement.parentElement
  if (element !== null) {
    // 找到 div item
    if (element.classList.contains(findClass)) {
      // 判断是否选中
      if (element.children[0].classList.contains(isChecked)) {
        element.classList.remove(itemCheckClass)
      } else {
        // 先清除上一个选中的样式
        imageSelectElement?.value?.classList?.remove(itemCheckClass)
        imageSelectElement.value = element
        element.classList.add(itemCheckClass)
      }
    } else {
      await setItemDivElementClass(element)
    }
  }
}

onMounted(async () => {
  await getDiyImageTypeList()
  await getDiyImageList()
})

defineExpose({ data })
</script>

<style scoped lang="less">
.el-checkbox-group.content {
  margin-top: 10px;
  display: grid;
  grid-template-rows: auto auto 1fr;
  //grid-template-columns: 20% 20% 20% 20% 20%;
  //grid-template-columns: 243px 243px 243px 243px 243px;
  grid-template-columns: repeat(auto-fill, minmax(243px, 1fr));
  grid-gap: 10px;
  width: 100%;
  //width: calc(100% - 40px);
}
.item {
  width: 243px;
  //width: 100%;
  //height: 243px;
  display: grid;
  position: relative;
  :hover {
    cursor: pointer;
  }
  &.item-checked {
    .el-image {
      border: 2px solid var(--el-color-primary);
    }
  }
  /*  &:has(.el-checkbox.is-checked) {
    .el-image {
      border: 2px solid var(--el-color-primary);
    }
  }*/
  .el-image {
    //height: 150px;
    height: 243px;
    background-color: var(--el-color-info-light-9);
    border-radius: 5px;
    border: 1px solid var(--el-color-info-light-8);
  }
  .el-checkbox {
    &.is-checked {
      display: block;
      //display: none;
    }
    &:hover {
    }
    position: absolute;
    right: 5px;
    top: 10px;
    display: none;
  }
  .image-name-text {
    line-height: 18px;
  }
  &:hover {
    .el-image {
      border: 2px solid var(--el-color-primary);
    }
    .el-checkbox {
      position: absolute;
      right: 5px;
      top: 10px;
      display: block;
      //display: none;
      :deep(.el-checkbox__inner) {
        border-color: var(--el-color-primary);
      }
    }
  }
}
</style>
