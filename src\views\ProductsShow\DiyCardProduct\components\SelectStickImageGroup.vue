<template>
  <Dialog
    v-model="data.showDialog"
    :title="props.dialogTitle"
    :before-close="props.closeDialog"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :width="'80%'"
  >
    <el-checkbox-group v-model="data.selectImageGroupIdList">
      <el-row :gutter="10" v-for="(imageGroup, index) in data.imageGroupList" :key="index">
        <el-col :span="4" class="image-group-name-col">
          <el-checkbox-button
            :label="imageGroup.diyMaterialInfoGroupid"
            class="group-name-button"
            @change="
              addGroup(
                {
                  stickGroupId: imageGroup.diyMaterialInfoGroupid,
                  stickGroupName: imageGroup.diyMaterialInfoGroupname
                },
                $event
              )
            "
          >
            {{ imageGroup.diyMaterialInfoGroupname }}
          </el-checkbox-button>
        </el-col>
        <el-col :span="20">
          <el-image
            v-for="(imageGroupChildren, cIndex) in imageGroup.childMaterialInfo"
            :key="cIndex"
            :title="
              t('productsShow.diyCardProduct.preview') +
              ': ' +
              imageGroupChildren.diyMaterialInfoName
            "
            class="product-image-detail-list"
            fit="scale-down"
            lazy
            :zoom-rate="1.2"
            :src="imageGroupChildren.diyMaterialInfoUrl"
            preview-teleported
            :initial-index="0"
            :preview-src-list="[imageGroupChildren.diyMaterialInfoUrl]"
            ><template #error>
              <el-icon><Picture /></el-icon>
            </template>
          </el-image>
        </el-col>
      </el-row>
    </el-checkbox-group>
    <template #footer>
      <el-button @click="props.closeDialog">{{ t('common.cancel') }}</el-button>
      <el-button type="success" :loading="data.isSaving" @click="getSelectImageGroupList">
        {{ t('common.ok') }}
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
const { t } = useI18n()
import * as ProductApi from '@/api/product/diyCard'
import { Dialog } from '@/components/Dialog'
import Pagination from '@/components/Pagination/index.vue'
import { reactive, unref } from 'vue'

const data = reactive({
  showDialog: true,
  isSaving: false,
  imageInfoForm: {},

  selectImageGroupIdList: [],
  selectImageGroupList: [],
  imageGroupList: [],

  pageNo: 0,
  pageSize: 10,
  imageListCount: 0
})

const props = defineProps({
  dialogTitle: {
    type: String,
    required: true
  },
  currentProjectService: {
    type: Object,
    required: true
  },
  currentSelectGroupList: {
    type: Array,
    default: () => []
  },
  closeDialog: {
    type: Function,
    required: true
  },
  getSelectImageGroupList: {
    type: Function,
    required: true
  }
})

/** 获取图片组接口 **/
const getDiyImageGroupList = async () => {
  // let serviceId = '1676787768374538241'
  let serviceId = props.currentProjectService.applyServiceId
  let params = {
    diyInfoConfigid: serviceId,
    diyMaterialInfoType: '1'
  }
  try {
    const { childMaterialAttr } = (await ProductApi.getDiyStickImageGroupListApi(params)) || []
    data.imageGroupList = childMaterialAttr
  } catch (e) {
    data.imageGroupList = []
    console.error('获取贴图组异常：', e)
  } finally {
  }
}

const addGroup = async (group, event) => {
  let index = data.selectImageGroupList.findIndex((g) => g.stickGroupId === group.stickGroupId)
  if (event) {
    if (index < 0) {
      data.selectImageGroupList.push(group)
    }
  } else {
    data.selectImageGroupList.splice(index, 1)
  }
}

onMounted(async () => {
  await getDiyImageGroupList()
  // 回显选择的贴图组
  if (props.currentSelectGroupList && props.currentSelectGroupList.length > 0) {
    props.currentSelectGroupList.forEach((item) => {
      let index = data.imageGroupList.findIndex(
        (g) => g.diyMaterialInfoGroupid === item.stickGroupId
      )
      if (index >= 0) {
        let imageGroup = data.imageGroupList[index]
        data.selectImageGroupIdList.push(imageGroup.diyMaterialInfoGroupid)
        data.selectImageGroupList.push({
          stickGroupId: imageGroup.diyMaterialInfoGroupid,
          stickGroupName: imageGroup.diyMaterialInfoGroupname
        })
      }
    })
  }
})

defineExpose({ data })
</script>

<style lang="less">
.el-col.image-group-name-col {
  display: grid;
  align-content: center;
  text-align: end;
  .group-name-button {
    width: 100% !important;
    .el-checkbox-button__inner {
      width: 100% !important;
      white-space: break-spaces;
      text-wrap: balance;
    }
  }
}
.el-image.product-image-detail-list {
  width: 120px;
  height: 100px;
  float: left;
  margin: 5px;
  border-radius: 5px;
  background-color: var(--el-color-info-light-9);
  border: 1px solid var(--el-color-info-light-8);
}
</style>
