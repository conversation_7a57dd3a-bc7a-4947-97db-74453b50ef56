<template>
  <el-form :model="data.verifySettingForm" ref="verifySettingFormRef" class="verify-setting-form">
    <el-row :gutter="10">
      <el-col :span="24">
        <el-radio-group
          v-model="data.verifySettingForm.diyVerifyFlag"
          :disabled="props.productDetailFlag"
        >
          <el-radio :label="true">{{ t('productsShow.diyCardProduct.needImgAudit') }}</el-radio>
          <el-radio :label="false">{{ t('productsShow.diyCardProduct.noImgAudit') }}</el-radio>
        </el-radio-group>
      </el-col>
    </el-row>

    <el-table
      v-if="data.verifySettingForm.diyVerifyFlag"
      :data="data.verifySettingForm.tableData"
      v-loading="data.tableLoading"
      style="width: 100%; margin-top: 10px"
      border
    >
      <el-table-column prop="verifyCode" align="left" headerAlign="center" showOverflowTooltip>
        <template #header>
          <span class="required-field-tag">{{
            t('productsShow.diyCardProduct.imgAuditIdent')
          }}</span>
        </template>
        <template #default="scope">
          <el-form-item label-width="0px" :prop="'tableData.' + scope.$index + '.verifyCode'">
            <div v-if="props.productDetailFlag">
              {{ scope.row['verifyName'] }}
            </div>
            <el-select
              v-else
              v-model="scope.row[scope.column.property]"
              @change="verifyCodeChange($event, scope.$index)"
              :placeholder="t('productsShow.diyCardProduct.imgAuditIdentPlaceholder')"
              style="width: 100%"
            >
              <el-option :label="t('productsShow.diyCardProduct.empty')" value="" />
              <el-option
                v-for="(verifyCode, index) in data.verifyCodeList"
                :label="verifyCode.name"
                :value="verifyCode.auditCode"
                :key="index"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column
        prop="verifyServiceCode"
        align="left"
        headerAlign="center"
        showOverflowTooltip
      >
        <template #header>
          <span class="required-field-tag">{{
            t('productsShow.diyCardProduct.bindImgAuditService')
          }}</span>
        </template>
        <template #default="scope">
          <el-form-item
            label-width="0px"
            :prop="'tableData.' + scope.$index + '.verifyServiceCode'"
            :rules="[
              {
                required: true,
                message: t('productsShow.diyCardProduct.bindImgAuditServicePlaceholder'),
                trigger: 'change'
              }
            ]"
          >
            <div v-if="props.productDetailFlag"> {{ scope.row['verifyServiceName'] }} </div>
            <el-select
              v-else
              v-model="scope.row[scope.column.property]"
              :placeholder="t('productsShow.diyCardProduct.bindImgAuditServicePlaceholder')"
              @change="verifyServiceChange($event, scope.$index)"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="(verifyService, index) in data.verifyServiceList"
                :label="verifyService.reviewName + '-' + verifyService.reviewNo"
                :value="verifyService.reviewNo"
                :key="index"
                :disabled="
                  data.verifySettingForm.tableData.filter(
                    (vst) => vst.verifyServiceCode === verifyService.reviewNo
                  ).length > 0
                "
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column prop="remark" align="left" headerAlign="center" showOverflowTooltip>
        <template #header>
          <span>{{ t('productsShow.diyCardProduct.remark') }}</span>
        </template>
        <template #default="scope">
          <el-form-item label-width="0px" :prop="'tableData.' + scope.$index + '.remark'">
            <div v-if="props.productDetailFlag"> {{ scope.row[scope.column.property] }} </div>
            <el-input
              v-else
              v-model="scope.row[scope.column.property]"
              :placeholder="t('productsShow.diyCardProduct.remarkPlaceholder')"
              type="text"
              maxlength="20"
              show-word-limit
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column
        v-if="!props.productDetailFlag"
        width="80px"
        align="center"
        headerAlign="center"
        :label="t('productsShow.diyCardProduct.operate')"
      >
        <template #default="scope">
          <el-button
            v-if="scope.$index !== 0"
            plain
            type="danger"
            :icon="Minus"
            @click="deleteVerifyServiceRow(scope.$index)"
          />
        </template>
      </el-table-column>
    </el-table>
    <div
      class="verify-setting-add-row"
      v-if="!props.productDetailFlag && data.verifySettingForm.diyVerifyFlag"
    >
      <el-button plain type="primary" :icon="Plus" @click="addVerifyServiceRow" />
    </div>
  </el-form>
</template>

<script setup lang="ts">
import * as ProductApi from '@/api/product/diyCard'
import { reactive, unref } from 'vue'
import { Plus, Minus } from '@element-plus/icons-vue'
const { t } = useI18n()

const verifySettingFormRef = ref()

const data = reactive({
  tableLoading: false,
  verifySettingForm: {
    diyVerifyFlag: true,
    tableData: [
      {
        verifyCode: '',
        verifyName: t('productsShow.diyCardProduct.empty'),
        verifyServiceCode: '',
        verifyServiceName: '',
        remark: ''
      }
    ]
  },
  verifyServiceList: [],
  verifyCodeList: []
})

const props = defineProps({
  productDetailFlag: {
    type: Boolean,
    default: false
  },
  currentProjectService: {
    type: Object,
    default: () => {}
  }
})

watch(
  () => props.currentProjectService,
  async (value) => {
    if (value && value.relateProjectId) {
      await getVerifyServiceList(value.relateProjectId)
    }
    if (value && value.applyServiceId) {
      await getVerifyCodeList(value.applyServiceId)
    }
  },
  { immediate: true, deep: true }
)

/** 获取图审服务列表 **/
async function getVerifyServiceList(projectId) {
  try {
    const list = await ProductApi.getProjectListApi({ projectId: projectId })
    const project = list[0]
    const projectSecret = await ProductApi.getProjectSecretKeyApi({
      projectTypeCode: project.applyServiceCode,
      customerId: project.customerId
    })
    const res = await ProductApi.getVerifyServiceList(projectSecret, {
      serviceName: 'REVIEW_SCENARIO_REVIEW_SERVER',
      reqData: {
        customerNo: project.customerId,
        // itemNo: projectId,
        itemNo: project.projectCodeJoint,
        reviewStatus: '0'
      },
      timestamp: new Date().getTime()
    })
    data.verifyServiceList = res?.data?.data?.respData || []
  } catch (e) {
    data.verifyServiceList = []
    console.error('查询图审配置列表失败：', e)
  } finally {
  }
}

/** 获取图审标识列表 **/
async function getVerifyCodeList(applyServiceId) {
  try {
    const formDate = new FormData()
    formDate.append('diyConfigInfoId', applyServiceId)
    const res = await ProductApi.getVerifyCodeList(formDate)
    data.verifyCodeList = res || []
  } catch (e) {
    data.verifyCodeList = []
    console.error('查询图审标识列表失败：', e)
  } finally {
  }
}

function verifyServiceChange(value, index) {
  if (value) {
    let verifyService = data.verifyServiceList.filter((vs) => vs.reviewNo === value)[0]
    if (verifyService.hasOwnProperty('reviewName')) {
      data.verifySettingForm.tableData[index]['verifyServiceName'] = verifyService['reviewName']
    }
  }
}

function verifyCodeChange(value, index) {
  if (value) {
    let verifyCode = data.verifyCodeList.filter((vs) => vs.auditCode === value)[0]
    if (verifyCode.hasOwnProperty('name')) {
      data.verifySettingForm.tableData[index]['verifyName'] = verifyCode['name']
    }
  } else {
    data.verifySettingForm.tableData[index]['verifyName'] = t('productsShow.diyCardProduct.empty')
  }
}

/** 添加图审配置行 **/
function addVerifyServiceRow() {
  data.verifySettingForm.tableData.push({
    verifyCode: '',
    verifyName: t('productsShow.diyCardProduct.empty'),
    verifyServiceCode: '',
    verifyServiceName: '',
    remark: ''
  })
}

/** 删除图审配置整行 **/
function deleteVerifyServiceRow(index) {
  data.verifySettingForm.tableData.splice(index, 1)
}

const formValidate = () => {
  unref(verifySettingFormRef)?.validate((valid) => {})
}

const getVerifySettingData = async () => {
  return {
    diyVerifyFlag: data.verifySettingForm.diyVerifyFlag,
    verifyList: data.verifySettingForm.tableData
  }
}

// 回显价格配置
const setVerifyServiceList = async (diyVerifyFlag: Boolean, editVerifyList: Array<any>) => {
  setTimeout(() => {
    data.verifySettingForm.diyVerifyFlag = diyVerifyFlag
    if (diyVerifyFlag) {
      data.verifySettingForm.tableData = editVerifyList
    }
  }, 50)
}

onMounted(async () => {})

defineExpose({
  verifySettingFormRef,
  setVerifyServiceList,
  getVerifySettingData
})
</script>

<style scoped lang="less">
.verify-setting-form {
  width: 100%;
  :deep(.el-form-item) {
    margin-bottom: 0 !important;
    .el-form-item__error {
      position: static;
    }
  }
}
.required-field-tag::before {
  content: '*';
  color: var(--el-color-danger);
  margin-right: 4px;
}
.verify-setting-add-row {
  text-align: center;
  width: 100%;
  padding: 5px 0;
  border: solid 1px #ebeef5;
  border-top: none;
}
</style>
