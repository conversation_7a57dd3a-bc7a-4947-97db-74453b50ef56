/**
 * 图片合成处理
 */
import * as ProductApi from '@/api/product/diyCard'
import { ImageType } from '@/views/ProductsShow/DiyCardProduct/diyCardProduct'

export class ImgMerge {
  canvas: HTMLCanvasElement = document.createElement('canvas')
  ctx: CanvasRenderingContext2D | null = this.canvas.getContext('2d')
  imageList = []
  opts: { compress: number; type: string } = {}
  imageFileList = []

  constructor(imageList = [], options) {
    const { t } = useI18n()
    // 图片数组默认配置项
    const defaultImageItem = {
      url: '',
      x: 0,
      y: 0
    }
    // 导出图片的格式与压缩程度默认配置项
    const defaultOpts: { compress: number; type: string } = {
      type: 'image/jpeg',
      compress: 1
    }

    try {
      imageList.forEach((item, i, arr) => {
        arr[i] = Object.assign({}, defaultImageItem, item)
      })
    } catch (e) {
      throw t('productsShow.diyCardProduct.needTrueArrayTip')
    }

    this.imageList = imageList // 图片数组配置项
    this.opts = Object.assign({}, defaultOpts, options) // 其他配置项
    this.imageFileList = [] // 图片对象数组

    this.createCanvas() // 创建画布
    return this.outputImg() // 导出图片
  }

  // 创建画布
  createCanvas() {
    const { t } = useI18n()
    const w = this.imageList[0].width
    const h = this.imageList[0].height

    if (!w) {
      throw t('productsShow.diyCardProduct.noWidth')
    }
    if (!h) {
      throw t('productsShow.diyCardProduct.noHight')
    }
    this.canvas.width = w
    this.canvas.height = h
  }

  // 绘制图片
  drawImg(i) {
    const img: HTMLImageElement = new Image()
    img.setAttribute('crossOrigin', 'anonymous')
    img.src = this.imageList[i].url
    this.imageFileList.push(img)

    return new Promise((resolve) => {
      img.onload = resolve
    })
  }

  // 导出图片
  outputImg() {
    const imgArr = []
    // 将单张图片的Promise对象存入数组
    this.imageList.forEach((item, i) => {
      imgArr.push(this.drawImg(i))
    })

    // 所有图片加载成功后将图片绘制于Canvas中，后将Canvas导出为图片
    return Promise.all(imgArr).then(() => {
      this.imageList.forEach((item, i) => {
        const drawPara = [this.imageFileList[i], this.imageList[i].x, this.imageList[i].y]
        // 此处判断参数中图片是否设置了宽高，若宽高均设置，则绘制已设置的宽高，否则按照图片默认宽高绘制
        if (this.imageList[i].width && this.imageList[i].height) {
          drawPara.push(this.imageList[i].width, this.imageList[i].height)
        }
        this.ctx.drawImage(...drawPara)
      })
      // 以base64格式导出图片
      return Promise.resolve(this.ctx.canvas.toDataURL(this.opts.type), this.opts.compress)
    })
  }
}

export const mergeMaskImageAndFrontImage = async (imageData) => {
  return new Promise<Object>((resolve, reject) => {
    const maskImg = imageData.filter((i) => i.diyImageTypeCode === ImageType.MASK_IMAGE.code)
    const frontImg = imageData.filter((i) => i.diyImageTypeCode === ImageType.FRONT_IMAGE.code)
    if (frontImg.length === 0 || frontImg?.[0]?.diyImageFileJson === null) {
      // 没有上传卡正面图片，合成图片直接引用蒙层图片url即可
      resolve({
        diyImageTypeCode: ImageType.MERGE_IMAGE.code,
        diyImageTypeName: ImageType.MERGE_IMAGE.noFrontImageName,
        diyImageFileJson: {
          fileUrl: maskImg[0].diyImageFileJson.fileUrl
        }
      })
    } else {
      Promise.all([
        ProductApi.downloadEosFileApi(frontImg[0].diyImageFileJson.fileUrl),
        ProductApi.downloadEosFileApi(maskImg[0].diyImageFileJson.fileUrl)
      ]).then(async (result) => {
        // 转换base64Url
        Promise.all([getBase64UrlByBlob(result[0]), getBase64UrlByBlob(result[1])]).then(
          async (baseUrlResult) => {
            const imageMerge: ImgMerge | Promise<never> = new ImgMerge(
              [
                {
                  url: baseUrlResult[0].base64Url,
                  width: baseUrlResult[0].width,
                  height: baseUrlResult[0].height
                },
                // 蒙层图片也以正面图片的宽高为准
                {
                  url: baseUrlResult[1].base64Url,
                  width: baseUrlResult[0].width,
                  height: baseUrlResult[0].height
                }
              ],
              null
            )

            await imageMerge.then(async (base64Url) => {
              const blob: Blob = await getBlobByBase64Url(base64Url)
              const file = new File([blob], 'mergeImage.jpg', {
                type: 'image/jpeg'
              })
              resolve(await uploadMergeImage(file))
            })
          }
        )
      })
    }
  })
}

export const uploadMergeImage = async (file: File | Blob) => {
  const formData: any = new FormData()
  formData.append(ImageType.MERGE_IMAGE.field, file)
  const { data } = await ProductApi.uploadFileApi(ImageType.MERGE_IMAGE.field, formData)
  if (data) {
    const uidFileName = data.substring(data.lastIndexOf('/') + 1, data.length)
    const fileName = uidFileName.substring(uidFileName.indexOf('_') + 1, data.length)
    return {
      diyImageTypeCode: ImageType.MERGE_IMAGE.code,
      diyImageTypeName: ImageType.MERGE_IMAGE.name,
      diyImageFileJson: {
        fileName: fileName,
        fileUrl: data
      }
    }
  }
}

export const getBlobByBase64Url = async (base64Url: String, type = 'image/jpeg') => {
  base64Url = decodeURIComponent(base64Url)
  const binary = base64Url.split(',')
  const mime = binary[0].match(/:(.*?);/)?.[1]
  const bStr = window.atob(binary[1])
  let length = bStr.length
  const u8arr = new Uint8Array(length)
  while (length--) {
    u8arr[length] = bStr.charCodeAt(length)
  }
  return new Blob([u8arr], { type: mime | type })
}

export const getBase64UrlByBlob = async (blob: Blob) => {
  return new Promise<Object>((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = (e) => {
      // resolve(e.target.result)
      const img = new Image()
      img.src = <string>e.target.result
      img.onload = () => {
        resolve({ width: img.width, height: img.height, base64Url: e.target.result })
      }
    }
  })
}
