const { t } = useI18n()

export enum ImageType {
  MASK_IMAGE = {
    field: 'maskImage',
    code: 'MASK_IMAGE',
    name: t('productsShow.diyCardProduct.maskImg')
  },
  FRONT_IMAGE = {
    field: 'frontImage',
    code: 'FRONT_IMAGE',
    name: t('productsShow.diyCardProduct.frontImage')
  },
  BACK_IMAGE = {
    field: 'backImage',
    code: 'BACK_IMAGE',
    name: t('productsShow.diyCardProduct.backImage')
  },
  MERGE_IMAGE = {
    field: 'mergeImage',
    code: 'MERGE_IMAGE',
    name: t('productsShow.diyCardProduct.maskAndFrontImgCraft'),
    noFrontImageName: t('productsShow.diyCardProduct.maskImgCraft')
  }
}

export enum ImageGroupType {
  STICK_IMAGE_GROUP = {
    field: 'stickImageGroup',
    code: 'STICK_IMAGE_GROUP',
    name: t('productsShow.diyCardProduct.textureMaterial')
  },
  ELEMENT_IMAGE_GROUP = {
    field: 'elementImageGroup',
    code: 'ELEMENT_IMAGE_GROUP',
    name: t('productsShow.diyCardProduct.elementMaterial')
  }
}

export enum CustomDiyCardType {
  CZ = {
    value: 'cz',
    label: t('productsShow.diyCardProduct.colorPhotoCard'),
    mapValue: ['1', '2']
  }
}

export function convertImageList(imageInfoForm, editImageList: Array<any>) {
  if (editImageList && editImageList.length > 0) {
    editImageList.forEach((item) => {
      if (item.diyImageTypeCode === ImageType.MASK_IMAGE.code) {
        imageInfoForm[ImageType.MASK_IMAGE.field] = item.diyImageFileJson
      }
      if (item.diyImageTypeCode === ImageType.FRONT_IMAGE.code) {
        imageInfoForm[ImageType.FRONT_IMAGE.field] = item.diyImageFileJson
      }
      if (item.diyImageTypeCode === ImageType.BACK_IMAGE.code) {
        imageInfoForm[ImageType.BACK_IMAGE.field] = item.diyImageFileJson
      }

      if (item.diyImageTypeCode === ImageGroupType.STICK_IMAGE_GROUP.code) {
        imageInfoForm[ImageGroupType.STICK_IMAGE_GROUP.field] = item.diyImageFileJson
      }
      if (item.diyImageTypeCode === ImageGroupType.ELEMENT_IMAGE_GROUP.code) {
        imageInfoForm[ImageGroupType.ELEMENT_IMAGE_GROUP.field] = item.diyImageFileJson
      }
    })
  }
}

/**
 * 价格输入框中禁止输入e、+、-
 */
const InhibitCharList = ['e', 'E', '+', '-']
export const priceInputInhibit = (e) => {
  const key = e.key
  if (InhibitCharList.includes(key)) {
    e.returnValue = false
  } else {
    e.returnValue = true
  }
}

/**
 * 价格输入框粘贴时判断剪贴板内容
 */
export const priceInputPaste = (e) => {
  const cData = e.clipboardData.getData('text')
  const charSet = new Set(InhibitCharList)
  const mixedList = Array.of(...cData).filter((item) => charSet.has(item))
  if (Number.isNaN(Number.parseFloat(cData)) || mixedList.length > 0) {
    e.preventDefault()
  }
}
