const { t } = useI18n()

// EXCEL模板信息表头---导入样卡
export const sampleCardHeaderInfo = [
  t('productsShow.sampleCardEdit.XlsHeaderInfos.ProductName'),
  t('productsShow.sampleCardEdit.XlsHeaderInfos.CustomerProductCode'),
  t('productsShow.sampleCardEdit.XlsHeaderInfos.GSCCode'),
  t('productsShow.sampleCardEdit.XlsHeaderInfos.ProjectNumber'),
  t('productsShow.sampleCardEdit.XlsHeaderInfos.Quantity'),
  t('productsShow.preparationEdit.XlsHeaderInfos.Remarks')
]

// EXCEL模板信息表头---导入备库
export const prepareHeaderInfo = [
  t('productsShow.preparationEdit.XlsHeaderInfos.ProductName'),
  t('productsShow.preparationEdit.XlsHeaderInfos.CustomerProductCode'),
  t('productsShow.preparationEdit.XlsHeaderInfos.GSCCode'),
  t('productsShow.preparationEdit.XlsHeaderInfos.BackupType'),
  t('productsShow.preparationEdit.XlsHeaderInfos.Quantity'),
  t('productsShow.preparationEdit.XlsHeaderInfos.BranchInfo'),
  t('productsShow.preparationEdit.XlsHeaderInfos.ProductType'),
  t('productsShow.preparationEdit.XlsHeaderInfos.Remarks')
]
