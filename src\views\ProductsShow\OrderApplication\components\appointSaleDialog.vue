<template>
  <!--接单确认弹窗 -->
  <Dialog
    v-model="visible"
    :title="t('productsShow.appointSaleDialog.selectedSaleMan')"
    width="40%"
    :scroll="false"
    :close-on-click-modal="false"
  >
    <div v-loading="loading" class="dialog-content">
      <el-form :model="selectedSaleMan" label-width="auto">
        <el-form-item :label="t('productsShow.appointSaleDialog.saleMen')" prop="saleUserId">
          <el-select
            value-key="id"
            v-model="selectedSaleMan"
            :placeholder="t('productsShow.appointSaleDialog.PleaseSelectedSaleMan')"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option v-for="item in saleMen" :key="item.id" :label="item.name" :value="item" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="onClose">{{ t('productsShow.appointSaleDialog.closed') }}</el-button>
      <ButtonPromise
        type="primary"
        :clickPromise="onSubmit"
        :loadingText="t('productsShow.appointSaleDialog.submitting')"
        >{{ t('productsShow.appointSaleDialog.submit') }}</ButtonPromise
      >
    </template>
  </Dialog>
</template>

<script setup lang="ts">
defineOptions({
  name: 'appointSaleDialog'
})

import { useOrderApplicationCommonService } from '../hooks/useOrderApplicationCommonService'
import ISaleMan from '../types/SaleMan'

const { t } = useI18n()

const commonService = useOrderApplicationCommonService()
const { saleMen } = commonService

const emit = defineEmits<{
  (e: 'saleManSelected', man: ISaleMan): void
}>()
const visible = ref<boolean>(false)
const loading = ref<boolean>(false)
const selectedSaleMan = ref<ISaleMan | null>(null)

function show(): void {
  visible.value = true
  nextTick(() => {
    commonService.getSaleMen()
  })
}

async function onClose() {
  visible.value = false
}

async function onSubmit() {
  if (!selectedSaleMan.value || selectedSaleMan.value == null) {
    ElMessage.error(t('productsShow.appointSaleDialog.PleaseSelectedSaleMan'))
    throw t('productsShow.appointSaleDialog.PleaseSelectedSaleMan')
  }
  const model = toRaw(selectedSaleMan.value as ISaleMan)
  await emit('saleManSelected', model)
  onClose()
}

defineExpose({ show })
</script>
