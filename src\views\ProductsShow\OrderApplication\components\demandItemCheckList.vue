<template>
  <div class="checkListContant">
    <el-checkbox-group v-model="checkedList">
      <el-checkbox
        v-for="item in checkArray"
        :disabled="readonly"
        :key="item.key"
        :value="item"
        @change="(checked:boolean)=>{onItemChanged(checked, item)}"
        >{{ item.value }}
      </el-checkbox>
      <el-input
        v-model="remarkComputed"
        :readonly="readonly"
        size="small"
        class="!w-[200px] ml-2"
        :placeholder="t('productsShow.sampleCardEdit.Remarks')"
      />
    </el-checkbox-group>
  </div>
  <el-divider />
</template>

<script setup lang="ts">
import { IDemandItem } from '@/api/orderApplication/types/orderApplicationDemand.d'
import { cloneDeep, remove } from 'lodash-es'
const { t } = useI18n()
type IProps = {
  checkArray: IDemandItem[]
  checked: IDemandItem[]
  remark: ''
  readonly: boolean
}

const checkedList = computed({
  get(): IDemandItem[] {
    return props.checked
  },
  set(value: IDemandItem[]) {
    emits('update:checked', value)
  }
})

async function onItemChanged(checked: boolean, item: IDemandItem) {
  const list: IDemandItem[] = cloneDeep(checkedList.value)
  //是否选择 '无' 选项
  if (item.key === '1' && checked) {
    remove(list, (x) => x.key !== '1') //移除
    remarkComputed.value = ''
  } else {
    remove(list, (x) => x.key === '1')
  }
  checkedList.value = list
}

const remarkComputed = computed({
  get(): string {
    return props.remark
  },
  set(value: string) {
    emits('update:remark', value)
  }
})

const props = withDefaults(defineProps<IProps>(), {})
const emits = defineEmits(['update:checked', 'update:remark'])
</script>

<style lang="less" scoped>
.checkListContant {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  .el-checkbox-group {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-right: 20px;
  }
}

.el-divider--horizontal {
  margin: 15px 0px !important;
}

// :deep(.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner) {
//   background-color: #409eff;
// }

// :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
//   color: #409eff;
// }
</style>
