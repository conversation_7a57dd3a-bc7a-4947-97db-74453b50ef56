<template>
  <!--接单确认弹窗 -->
  <Dialog
    v-model="visible"
    title="销售领导评审"
    width="40%"
    :scroll="false"
    :close-on-click-modal="false"
  >
    <div v-loading="loading" class="dialog-content">
      <el-form :model="data" label-width="auto">
        <el-form-item label="评审结果" :rules="{ required: true }">
          <el-select v-model="data.result" placeholder="评审结果" style="width: 100%">
            <el-option
              :key="orderApplicationReviewResultEnum.OK"
              label="OK"
              :value="orderApplicationReviewResultEnum.OK"
            />
            <el-option
              :key="orderApplicationReviewResultEnum.NT"
              label="NG"
              :value="orderApplicationReviewResultEnum.NT"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评审意见">
          <el-input
            v-model="data.remark"
            :rows="4"
            type="textarea"
            :maxlength="1000"
            :show-word-limit="true"
            :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入评审意见"
            clearable
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="onClose">关 闭</el-button>
      <ButtonPromise type="primary" :clickPromise="onSubmit" loadingText="提交中..."
        >确 认</ButtonPromise
      >
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { useOrderApplicationCommonService } from '../hooks/useOrderApplicationCommonService'
import { orderApplicationReviewResultEnum } from '@/api/orderApplication/types/enum.d'
const commonService = useOrderApplicationCommonService()
interface IData {
  result?: orderApplicationReviewResultEnum
  remark?: string
}

const emit = defineEmits<{
  (e: 'submit', result: orderApplicationReviewResultEnum, remark: string): void
}>()
const visible = ref<boolean>(false)
const loading = ref<boolean>(false)
const data = ref<IData>({})

function show(): void {
  visible.value = true
  nextTick(() => {
    commonService.getSaleMen()
  })
}

async function onClose() {
  visible.value = false
  data.value = {}
}

async function onSubmit() {
  if (!data.value.result) {
    ElMessage.error('请选择评审结果')
    throw '请选择评审结果'
  }
  if (
    data.value.result == orderApplicationReviewResultEnum.NT &&
    (!data.value.remark || !data.value.remark.replace(/\s+/g, ''))
  ) {
    ElMessage.error('请填写评审意见')
    throw '请填写评审意见'
  }
  const model = toRaw(data.value as IData)
  await emit('submit', model.result as orderApplicationReviewResultEnum, model.remark ?? '')
  onClose()
}

defineExpose({ show })
</script>
