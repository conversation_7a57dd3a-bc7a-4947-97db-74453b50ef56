<template>
  <el-dialog
    :title="t('productsShow.preparationProductDialog.FillBackupProduct')"
    v-model="visible"
    :width="ifEn ? '65%' : '50%'"
    @close="onClose"
  >
    <el-form
      :model="data"
      ref="productFormRef"
      :rules="rules"
      :label-width="ifEn ? 160 : 85"
      :inline="false"
    >
      <el-row :gutter="20">
        <el-col :span="24" :offset="0">
          <el-form-item
            :label="t('productsShow.preparationProductDialog.productName')"
            prop="productName"
          >
            <el-select
              v-model="product"
              value-key="id"
              filterable
              remote
              reserve-keyword
              allow-create
              default-first-option
              :placeholder="t('productsShow.preparationProductDialog.PleaseSelectProduct')"
              :remote-method="queryProductAsync"
              style="width: 100%"
              :loading="loading"
            >
              <el-option v-for="item in products" :key="item.id" :label="item.name" :value="item" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12" :offset="0">
          <el-form-item :label="t('productsShow.preparationProductDialog.cardNumber')">
            <el-input v-model="data.cardCode" /> </el-form-item
        ></el-col>
        <el-col :span="12" :offset="0">
          <el-form-item :label="t('productsShow.preparationProductDialog.customerProductCode')">
            <template #label>
              <span style="font-size: 12px">{{
                t('productsShow.preparationProductDialog.customerProductCode')
              }}</span>
            </template>
            <el-input v-model="data.customerProductCode" /> </el-form-item
        ></el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12" :offset="0">
          <el-form-item
            :label="t('productsShow.preparationProductDialog.BackupQuantity')"
            prop="amount"
          >
            <el-input v-model="data.amount" @input="handleInput" :maxlength="12" />
            <div v-if="data.amount > 0 && !ifEn" style="color: #ff4040">{{ capitalNum }} </div>
          </el-form-item>
        </el-col>
        <el-col :span="12" :offset="0">
          <el-form-item :label="t('productsShow.preparationProductDialog.BackupType')">
            <el-select
              v-model="data.standbyType"
              :placeholder="t('productsShow.preparationProductDialog.PleaseSelectType')"
            >
              <el-option
                v-for="item in standbyTypeArray"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select> </el-form-item
        ></el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12" :offset="0">
          <el-form-item :label="t('productsShow.preparationProductDialog.BranchMessage')">
            <el-input v-model="data.branchInfo" /> </el-form-item
        ></el-col>
        <el-col :span="12" :offset="0">
          <el-form-item
            :label="t('productsShow.preparationProductDialog.ProductType')"
            prop="productType"
          >
            <el-select
              v-model="data.productType"
              :placeholder="t('productsShow.preparationProductDialog.PleaseSelectType')"
            >
              <el-option
                v-for="item in productTypeArray"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select> </el-form-item
        ></el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24" :offset="0">
          <el-form-item :label="t('productsShow.preparationProductDialog.Remarks')">
            <el-input
              v-model="data.remark"
              :rows="4"
              type="textarea"
              :maxlength="1000"
              :show-word-limit="true"
              :autosize="{ minRows: 2, maxRows: 6 }"
              :placeholder="t('productsShow.preparationProductDialog.PleaseEnterRemarks')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="onClose">{{
        t('productsShow.preparationProductDialog.Closed')
      }}</el-button>
      <el-button type="primary" @click="onSave">{{
        t('productsShow.preparationProductDialog.Save')
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import IOrderApplicationProduct from '@/api/orderApplication/types/orderApplicationProduct'
import { useOrderAppliactionService } from '../hooks/useOrderApplicationService'
import { useOrderApplicationCommonService } from '../hooks/useOrderApplicationCommonService'
import ICustoemr from '../types/Customer'
import IProduct from '../types/product.d'
import { standbyTypeArray, productTypeArray } from '../types/data.d'
import { unref } from 'vue'
import { productTypeEnum } from '@/api/orderApplication/types/enum.d'
const { t, ifEn } = useI18n()

//定义表单规则对象
interface RuleForm {
  productName: string
  // cardCode: string
  amount: number
  productType: []
}

const emit = defineEmits<{
  (e: 'saveProduct', data: IOrderApplicationProduct): void
}>()
const customer = inject<ICustoemr>('customer')
const { getDefaultProduct } = useOrderAppliactionService()
const { products, searchProdcuts } = useOrderApplicationCommonService()
const visible = ref<boolean>(false)
const data = ref<IOrderApplicationProduct>(getDefaultProduct())
const loading = ref<boolean>(false)

const productFormRef = ref<FormInstance>()
//数字转中文
const toCapital = (num) => {
  //  四位四位的进行分割
  const parts = num
    .toString()
    .replace(/(?=(\d{4})+$)/g, ',')
    .split(',')
    .filter(Boolean)

  const map = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千']
  // 把连续的零给去掉 合并为1个零  当零在末尾的时候去掉
  function _handleZero(str) {
    return str.replace(/零+/g, '零').replace(/零$/, '')
  }
  function _transform(n) {
    let result = ''
    for (let i = 0; i < n.length; i++) {
      const c = map[n[i]]
      let u = units[n.length - i - 1]
      if (c === '零') {
        u = ''
      }
      result += c + u
    }
    result = _handleZero(result)
    return result
  }
  const bigUnits = ['', '万', '亿']
  let result = ''
  for (let i = 0; i < parts.length; i++) {
    const p = parts[i]
    const c = _transform(p)
    const u = bigUnits[parts.length - i - 1]
    if (c === '') {
      result += '零'
      continue
    }
    result += c + u
  }
  result = _handleZero(result)
  return result
}
let capitalNum = ''
const handleInput = (value) => {
  capitalNum = toCapital(value)
}

const validateAmount = (rule: any, value: any, callback: any) => {
  rule
  if (value === '') {
    callback(new Error(t('productsShow.preparationProductDialog.PleaseEnterApplyQuantity')))
    return
  }
  const numberRegx = /^\d+$/
  if (!numberRegx.test(value)) {
    callback(new Error(t('productsShow.preparationProductDialog.PleaseEnterCorrectQuantity')))
    return
  }
  if (value <= 0) {
    callback(new Error(t('productsShow.preparationProductDialog.ApplyQuantityMorethanZero')))
    return
  }
  callback()
}

//表单校验规格
const rules = reactive<FormRules<RuleForm>>({
  productName: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.selectProductPlaceholder'),
      trigger: ['blur', 'change']
    }
  ],
  // cardCode: [{ required: true, message: '请填写卡基编号', trigger: 'blur' }],
  amount: [
    {
      required: true,
      message: t('productsShow.preparationProductDialog.PleaseEnterApplyQuantity'),
      trigger: 'blur'
    },
    { validator: validateAmount, trigger: 'blur' }
  ],
  productType: [
    {
      required: true,
      message: t('productsShow.preparationProductDialog.PleaseSelectProductType'),
      trigger: ['blur', 'change']
    }
  ]
})

/** 申请单的产品信息
 * @type {*} */
const product = computed({
  get(): IProduct {
    return {
      id: data.value.productId,
      name: data.value.productName,
      cardCode: data.value.cardCode,
      clientProductUniqueCode: data.value.customerProductCode,
      productCode: '',
      productType: ''
    }
  },
  set(value: IProduct | string) {
    console.log(value)
    //const isStrType:boolean  = typeof value === 'string'
    if (typeof value === 'string') {
      data.value.productId = ''
      data.value.productName = value
      data.value.cardCode = ''
      data.value.customerProductCode = ''
      data.value.productType = ''

      products.value.push({
        id: '',
        name: value,
        cardCode: '',
        clientProductUniqueCode: '',
        productCode: '',
        productType: ''
      })
    } else {
      data.value.productId = value?.id
      data.value.productName = value?.name
      data.value.cardCode = value?.cardCode
      data.value.customerProductCode = value?.clientProductUniqueCode
      data.value.productType =
        value.productType === 'merchant_card' ? productTypeEnum.card : productTypeEnum.nonCard // 产品类型（1-卡产品，2-非卡产品）
    }
  }
})

function show(edit: IOrderApplicationProduct): void {
  data.value = edit ?? getDefaultProduct()
  visible.value = true

  nextTick(() => {
    products.value.push(toRaw(product.value as IProduct))
  })
}

async function onClose() {
  productFormRef.value.clearValidate()
  products.value = []
  visible.value = false
}

async function queryProductAsync(queryString: string) {
  if (!customer) {
    ElMessage.warning(t('productsShow.preparationProductDialog.PleaseSelectCustomer'))
    throw t('productsShow.preparationProductDialog.PleaseSelectCustomer')
  }
  const customerId = customer.value.id
  if (queryString) await searchProdcuts(customerId, queryString, '')
}

async function onSave() {
  unref(productFormRef)?.validate(async (isValid) => {
    if (isValid) {
      const model = toRaw(data.value as IOrderApplicationProduct)
      await emit('saveProduct', model)
      onClose()
    }
  })
}

defineExpose({ show })
</script>

<style scoped></style>
