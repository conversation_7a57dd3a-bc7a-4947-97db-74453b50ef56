<template>
  <el-dialog
    :title="t('productsShow.sampleProductDailog.SampleCardApplicationProductFill')"
    v-model="visible"
    :width="ifEn ? '65%' : '50%'"
    @close="onClose"
  >
    <el-form
      :model="data"
      ref="productFormRef"
      :rules="rules"
      :label-width="ifEn ? 160 : 85"
      :inline="false"
    >
      <el-row :gutter="20">
        <el-col :span="24" :offset="0">
          <el-form-item
            :label="t('productsShow.sampleProductDailog.ProductName')"
            prop="productName"
          >
            <el-select
              v-model="product_model.productName"
              value-key="id"
              filterable
              remote
              reserve-keyword
              allow-create
              default-first-option
              :placeholder="t('productsShow.sampleProductDailog.PleaseSelectProduct')"
              :remote-method="
                (val) => {
                  return queryProductAsync(val, 'cardName')
                }
              "
              style="width: 100%"
              :loading="loading"
              @change="
                (val) => {
                  productCodeChange(val, 'cardName')
                }
              "
            >
              <el-option
                v-for="item in products"
                :key="item.id"
                ::label="item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12" :offset="0">
          <el-form-item :label="t('productsShow.sampleProductDailog.cardNumber')">
            <el-select
              v-model="product_model.productCode"
              value-key="id"
              filterable
              remote
              reserve-keyword
              allow-create
              default-first-option
              :placeholder="t('productsShow.sampleProductDailog.PleaseEnterCardNumber')"
              :remote-method="
                (val) => {
                  return queryProductAsync(val, 'cardCode')
                }
              "
              style="width: 100%"
              :loading="loading"
              @change="
                (val) => {
                  return productCodeChange(val, 'cardCode')
                }
              "
            >
              <el-option
                v-for="item in products"
                :key="item.id"
                :label="item.cardCode"
                :value="item"
              />
            </el-select>
            <!--            <el-input v-model="data.cardCode" /> -->
          </el-form-item>
        </el-col>
        <el-col :span="12" :offset="0">
          <el-form-item :label="t('productsShow.sampleProductDailog.CustomerProductCode')">
            <template #label>
              <span style="font-size: 12px">{{
                t('productsShow.sampleProductDailog.CustomerProductCode')
              }}</span>
            </template>
            <el-input v-model="data.customerProductCode" /> </el-form-item
        ></el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12" :offset="0"
          ><el-form-item
            :label="t('productsShow.sampleProductDailog.ApplicationQuantity')"
            prop="amount"
          >
            <el-input v-model="data.amount" /> </el-form-item
        ></el-col>
        <el-col :span="12" :offset="0">
          <el-form-item :label="t('productsShow.sampleProductDailog.ProjectNumber')">
            <el-input v-model="data.plan" /> </el-form-item
        ></el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24" :offset="0">
          <el-form-item :label="t('productsShow.sampleProductDailog.Remarks')">
            <el-input
              v-model="data.remark"
              :rows="4"
              type="textarea"
              :maxlength="1000"
              :show-word-limit="true"
              :autosize="{ minRows: 2, maxRows: 6 }"
              :placeholder="t('productsShow.sampleProductDailog.PleaseEnterComments')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="onClose">{{ t('productsShow.sampleProductDailog.Close') }}</el-button>
      <el-button type="primary" @click="onSave">{{
        t('productsShow.sampleProductDailog.save')
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import type { FormInstance, FormRules } from 'element-plus'
import IOrderApplicationProduct from '@/api/orderApplication/types/orderApplicationProduct'
import { useOrderAppliactionService } from '../hooks/useOrderApplicationService'
import { useOrderApplicationCommonService } from '../hooks/useOrderApplicationCommonService'
import ICustoemr from '../types/Customer'
import IProduct from '../types/product.d'
import { productTypeEnum } from '@/api/orderApplication/types/enum.d'

const { t, ifEn } = useI18n()
//定义表单规则对象
interface RuleForm {
  productName: string
  // cardCode: string
  amount: number
}

const emit = defineEmits<{
  (e: 'saveProduct', data: IOrderApplicationProduct): void
}>()
const customer = inject<ICustoemr>('customer')
const { getDefaultProduct } = useOrderAppliactionService()
const { products, searchProdcuts } = useOrderApplicationCommonService()
const visible = ref<boolean>(false)
const data = ref<IOrderApplicationProduct>(getDefaultProduct())
const loading = ref<boolean>(false)
const productFormRef = ref<FormInstance>()

const validateAmount = (rule: any, value: any, callback: any) => {
  const numberRegx = /^\d+$/
  if (value != '' && !numberRegx.test(value)) {
    callback(new Error(t('productsShow.sampleProductDailog.correctQuantityValue')))
    return
  }
  /*if (value <= 0) {
    callback(new Error('申请数量不能小于零'))
    return
  }*/
  callback()
}

//表单校验规格
const rules = reactive<FormRules<RuleForm>>({
  productName: [
    {
      required: true,
      message: t('cardProductBusiness.proxyCustomerToOrder.selectProductPlaceholder'),
      trigger: ['blur', 'change']
    }
  ],
  // productName: [{ required: true, message: '请选择产品', trigger: ['blur', 'change'] }],
  // cardCode: [{ required: true, message: '请填写卡基编号', trigger: 'blur' }],
  amount: [{ validator: validateAmount, trigger: 'blur' }]
})

const product_model = ref({
  productName: '',
  productCode: ''
})

const productCodeChange = (value, type) => {
  if (type === 'cardName') {
    const productNameArr = []
    products.value.forEach((v, index) => {
      productNameArr.push(v.name)
    })
    if (productNameArr.includes(value.name)) {
      product_model.value.productCode = value?.cardCode
      data.value.productId = value?.id
      data.value.productName = value?.name
      data.value.cardCode = value?.cardCode
      data.value.customerProductCode = value?.clientProductUniqueCode
      data.value.productType =
        value.productType === 'merchant_card' ? productTypeEnum.card : productTypeEnum.nonCard // 产品类型（1-卡产品，2-非卡产品）
    } else {
      data.value.productName = value
      data.value.productId = ''
      data.value.customerProductCode = ''
      data.value.productType = productTypeEnum.card
    }
  }
  if (type === 'cardCode') {
    const productCodeArr = []
    products.value.forEach((v, index) => {
      productCodeArr.push(v.cardCode)
    })
    if (productCodeArr.includes(value.cardCode)) {
      product_model.value.productName = value?.name
      data.value.productId = value?.id
      data.value.productName = value?.name
      data.value.cardCode = value?.cardCode
      data.value.customerProductCode = value?.clientProductUniqueCode
      data.value.productType =
        value.productType === 'merchant_card' ? productTypeEnum.card : productTypeEnum.nonCard // 产品类型（1-卡产品，2-非卡产品）
    } else {
      data.value.cardCode = value
      data.value.productId = ''
      data.value.customerProductCode = ''
      data.value.productType = productTypeEnum.card
    }
  }
}

/** 申请单的产品信息
 * @type {*} */
const product = computed({
  get(): IProduct {
    return {
      id: data.value.productId,
      name: data.value.productName,
      cardCode: data.value.cardCode,
      clientProductUniqueCode: data.value.customerProductCode,
      productCode: '',
      productType: ''
    }
  },
  set(value: IProduct | string) {
    if (typeof value === 'string') {
      data.value.productId = ''
      data.value.productName = value
      data.value.cardCode = ''
      data.value.customerProductCode = ''
      data.value.productType = ''

      products.value.push({
        id: '',
        name: value,
        cardCode: '',
        clientProductUniqueCode: '',
        productCode: '',
        productType: ''
      })
    } else {
      data.value.productId = value?.id
      data.value.productName = value?.name
      data.value.cardCode = value?.cardCode
      data.value.customerProductCode = value?.clientProductUniqueCode
      data.value.productType =
        value.productType === 'merchant_card' ? productTypeEnum.card : productTypeEnum.nonCard // 产品类型（1-卡产品，2-非卡产品）
    }
  }
})

function show(edit: IOrderApplicationProduct): void {
  data.value = edit ?? getDefaultProduct()
  console.log(data.value)
  product_model.value.productName = data.value.productName
  product_model.value.productCode = data.value.cardCode
  visible.value = true
  nextTick(() => {
    products.value.push(toRaw(product.value as IProduct))
  })
}

async function onClose() {
  products.value = []
  visible.value = false
  data.value = getDefaultProduct()
  productFormRef.value?.resetFields()
  product_model.value = {}
}

async function queryProductAsync(queryString: string, querytype: String) {
  if (!customer) {
    ElMessage.warning(t('productsShow.sampleProductDailog.PleaseSelectCustomer'))
    throw t('productsShow.sampleProductDailog.UnableQueryproduct')
  }
  const customerId = customer.value.id
  if (queryString) await searchProdcuts(customerId, queryString, querytype)
}

async function onSave() {
  productFormRef.value.validate(async (valid) => {
    if (valid) {
      const model = cloneDeep(toRaw(data.value as IOrderApplicationProduct))
      await emit('saveProduct', model)
      onClose()
    }
  })
}

defineExpose({ show })
</script>

<style scoped></style>
