import dayjs from 'dayjs'
import { getNamesApi } from '@/api/order'
import {
  getProductListApi as queryBatchProducts //查询批卡产品List
} from '@/api/product/merchantCard/index'

import ISaleMan from '../types/SaleMan'
import ICustoemr from '../types/Customer'
import IProduct from '../types/product.d'

import { sampleCardApplicationTypeArray, standbyTypeArray, productTypeArray } from '../types/data.d'
import {
  orderApplicationTypeEnum,
  standbyTypeEnum,
  productTypeEnum
} from '@/api/orderApplication/types/enum.d'

import { getSaleCustomerListApi } from '@/api/CustomList'
/**
 * @description 订单申请通用操作Service
 * @export
 */
export function useOrderApplicationCommonService() {
  //获取销售人员列表
  async function getSaleMen() {
    try {
      const { data } = await getSaleCustomerListApi()
      //console.log(data)
      saleMen.value = data.map((item) => {
        return { id: item.id, name: item.nickname }
      })
    } catch (ex) {
      console.error(ex)
      ElMessage.error(t('productsShow.sampleCardEdit.NoneSales'))
    }
  }
  //获取客户列表
  async function getCustomers() {
    try {
      const datas = await getNamesApi()
      //console.log(datas)
      customers.value = datas.map((item) => {
        return {
          id: item.customerId,
          name: item.customerName,
          customerCode: item.customerCode,
          code: item.customerCode
        }
      })
    } catch (ex) {
      console.error(ex)
      ElMessage.error(t('productsShow.sampleCardEdit.NoneCustomer'))
    }
  }
  /**
   * @description 查询卡产品数据操作
   * @param {productType} type 产品类型
   * @param {string} customerId 客户Id
   * @param {string} name 产品名称
   * @return {*}  {demandProdcut[]} 可供选择的产品数据
   */
  async function searchProdcuts(
    customerId: string,
    name: string,
    querytype: String
  ): Promise<IProduct[]> {
    let prodcutList: IProduct[] = []
    if (!customerId) {
      ElMessage.warning(t('productsShow.sampleCardEdit.PleaseEnterCustomerName'))
      return []
    }
    //查询批卡产品
    let dataQuery
    if (querytype === 'cardCode') {
      dataQuery = {
        cardCode: name,
        customerId: customerId
      }
    } else {
      dataQuery = {
        saleName: name,
        customerId: customerId
      }
    }
    const result = await queryBatchProducts(dataQuery)

    const { list } = result
    prodcutList = list.map((item) => {
      return {
        id: item.productId,
        name: item.saleName,
        clientProductUniqueCode: item.clientProductUniqueCode,
        productCode: item.productCode,
        cardCode: item.cardCode,
        productType: item.productType.productTypeCode
      }
    })
    products.value = prodcutList
    return prodcutList
  }

  /**
   * @description 订单申请类型映射
   * @param {orderApplicationTypeEnum} value
   * @return {*}  {string}
   */
  function applicationTypeMapper(value: orderApplicationTypeEnum): string {
    // const item = first(filter(sampleCardApplicationTypeArray, (item) => item.value == value))
    const item = sampleCardApplicationTypeArray.filter((x) => x.value == value)[0]
    return item ? item.label : value
  }

  /**
   * @description 备库类型映射
   * @param {standbyTypeEnum} value
   * @return {*}  {string}
   */
  function standbyTypeMapper(value: standbyTypeEnum): string {
    const item = standbyTypeArray.filter((x) => x.value == value)[0]
    return item ? item.label : value
  }

  /**
   * @description 产品类型映射
   * @param {productTypeEnum} value
   * @return {*}  {string}
   */
  function productTypeMapper(value: productTypeEnum): string {
    const item = productTypeArray.filter((x) => x.value == value)[0]
    return item ? item.label : value
  }

  function dateFormat(date?: Date): string {
    if (!date) return '-'
    return dayjs(date).format('YYYY-MM-DD')
  }

  /** 销售人员列表
   *  @type {*} */
  const saleMen = ref<ISaleMan[]>([])

  /** 客户信息列表
   *  @type {*} */
  const customers = ref<ICustoemr[]>([])

  /** 产品信息列表
   *  @type {*} */
  const products = ref<IProduct[]>([])

  return {
    saleMen,
    customers,
    products,

    getSaleMen,
    getCustomers,
    searchProdcuts,

    applicationTypeMapper,
    standbyTypeMapper,
    productTypeMapper,
    dateFormat
  }
}
