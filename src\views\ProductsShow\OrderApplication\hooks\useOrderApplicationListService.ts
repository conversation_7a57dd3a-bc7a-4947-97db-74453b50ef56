import { IOrderApplicationQueryParams } from '@/api/orderApplication/types/request.d' //查询参数
import IOrderApplication from '@/api/orderApplication/types/orderApplication'
import * as orderApplicationApi from '@/api/orderApplication'

import { cloneDeep } from 'lodash-es'
/**
 * @description 订单申请表单相关操作Service
 * @export
 */
export function useOrderAppliactionListService() {
  const getDefaultData = (): IOrderApplicationQueryParams => {
    return {
      applyCode: '',
      type: undefined,
      customerCode: '',
      customerName: '',
      status: undefined
    }
  }

  const pageIndex = ref<number>(1)
  const pageSize = ref<number>(20)
  const params = ref<IOrderApplicationQueryParams>(getDefaultData())

  /** 查询的分页数据
   * @type {*}
   * */
  const datas = ref<IOrderApplication[]>([]) //数据

  /** 数据总数
   *  @type  {*}
   * */
  const total = ref<number>(0)

  /**
   * @description 查询
   */
  async function searchSamples() {
    const searchParams = toRaw(params.value)
    const result = await orderApplicationApi.searchSampleApplications(
      pageIndex.value,
      pageSize.value,
      searchParams
    )
    datas.value = cloneDeep(result.list)
    total.value = result.total
  }

  /**
   * @description 查询
   */
  async function searchPreparations() {
    const searchParams = toRaw(params.value)
    const result = await orderApplicationApi.searchPreparationApplications(
      pageIndex.value,
      pageSize.value,
      searchParams
    )
    datas.value = cloneDeep(result.list)
    total.value = result.total
  }

  /**
   * @description 重置查询参数
   */
  async function resetParmas() {
    pageIndex.value = 1
    pageSize.value = 20
    params.value = getDefaultData()
  }

  return {
    params,
    pageIndex,
    pageSize,

    datas,
    total,

    searchSamples,
    searchPreparations,
    resetParmas
  }
}
