import IOrderApplication from '@/api/orderApplication/types/orderApplication'
import {
  orderApplicationTypeEnum,
  orderApplicationReviewResultEnum,
  orderApplicationStatusEnum,
  productTypeEnum,
  standbyTypeEnum
} from '@/api/orderApplication/types/enum.d'
import IOrderApplicationDemand from '@/api/orderApplication/types/orderApplicationDemand'
import IOrderApplicationProduct from '@/api/orderApplication/types/orderApplicationProduct'

import { findMakeCardApi } from '@/api/makeCardService/index'
import { getDraftListApi } from '@/api/makeCardService/draft/index'

import ISaleMan from '../types/SaleMan'
import ICustoemr from '../types/Customer'

import * as orderApplicationApi from '@/api/orderApplication'
import { cloneDeep, isEmpty } from 'lodash-es'

import { useSampleCardApplicationStore } from '@/store/modules/sampleCardApplication'

/**
 * @description 订单申请表单相关操作Service
 * @export
 */
export function useOrderAppliactionService() {
  const sampleCardApplicationStore = useSampleCardApplicationStore()

  const getDefaultData = (): IOrderApplication => {
    return {
      id: '',
      applyCode: '',
      type: orderApplicationTypeEnum.FreeSampleCard,
      customerId: '',
      customerCode: '',
      customerName: '',
      remark: '',
      createBy: '',
      createName: '',
      saleUserId: '',
      saleUserName: '',
      saleUserResult: orderApplicationReviewResultEnum.OK,
      managerId: '',
      managerName: '',
      managerResult: undefined,
      deliveryType: '',
      urgentSign: false,
      urgentReason: '',
      status: orderApplicationStatusEnum.CREATE,
      demand: undefined,
      productList: [],
      requirementCode: '',
      umvOrderCode: '',
      umvOrderId: '',
      leaderId: '',
      leaderName: '',
      leaderResult: undefined
    }
  }
  const getDefaultDemand = (): IOrderApplicationDemand => {
    return {
      applyId: '',
      id: '',
      purpose: { items: [], remark: '' },
      front: { items: [], remark: '' },
      back: { items: [], remark: '' },
      other: { items: [], remark: '' },
      personal: { items: [], remark: '' },
      pm: { items: [], remark: '' },
      safety: { items: [], remark: '' },
      remark: ''
    }
  }

  const getDefaultProduct = (): IOrderApplicationProduct => {
    return {
      id: '',
      applyId: '',
      productId: '',
      productName: '',
      cardCode: '',
      amount: 0,
      remark: '',
      plan: '',
      branchInfo: '',
      standbyType: standbyTypeEnum.finish,
      productType: productTypeEnum.card,
      customerProductCode: '',
      deliveryAt: undefined,
      index: ''
    }
  }

  /**
   *  订单申请表单数据
   *  @type {*} */
  const application = ref<IOrderApplication>(getDefaultData())

  /** 产品数据
   * @type {*} */
  const products = ref<IOrderApplicationProduct[]>([])

  /** 申请单的要素需求
   * @type {*} */
  const demand = ref<IOrderApplicationDemand | undefined>(getDefaultDemand())

  /** 申请单的销售人员
   * @type {*} */
  const currSaleMan = computed({
    get(): ISaleMan {
      return {
        id: application.value.saleUserId,
        name: application.value.saleUserName
      }
    },
    set(value: ISaleMan) {
      application.value.saleUserId = value?.id
      application.value.saleUserName = value?.name
    }
  })
  /** 申请单的客户信息
   * @type {*} */
  const currCustomer = computed({
    get(): ICustoemr {
      return {
        id: application.value.customerId,
        name: application.value.customerName,
        customerCode: application.value.customerCode
      }
    },
    set(value: ICustoemr) {
      //console.log(value)
      application.value.customerId = value?.id
      application.value.customerName = value?.name
      application.value.customerCode = value?.customerCode
    }
  })

  /** 是否存在销售人员信息
   * @type {*} */
  const hasSaleMan = computed<boolean>(() => {
    return !isEmpty(application.value.saleUserName)
  })

  /** 是否存在销售经理信息
   * @type {*} */
  const hasSaleManager = computed<boolean>(() => {
    return !isEmpty(application.value.managerName)
  })

  /**
   * @description 更新或者保存申请单的产品信息
   * @param {IOrderApplicationProduct} product
   */
  async function onSaveProduct(product: IOrderApplicationProduct) {
    //产品不存在
    if (product.index === '') {
      product.applyId = application.value.id
      products.value.push(product)
      return
    }
    //更新产品
    products.value[product.index] = {
      ...product
    }
  }

  /**
   * @description 删除产品信息
   * @param {number} productIndex
   */
  async function delProduct(productIndex: number) {
    products.value.splice(productIndex, 1)
  }

  /**
   * @description 获取指定的订单申请数据
   * @param {string} applyId
   */
  async function getApplication(applyId: string | undefined) {
    //console.log('applyId"', applyId)
    if (applyId) {
      const data = await orderApplicationApi.getApplication(applyId)
      //console.log(data)
      application.value = cloneDeep(data)
      if (data.demand) demand.value = cloneDeep(data.demand)
      products.value = data.productList
    } else {
      application.value = getDefaultData()
      demand.value = getDefaultDemand()
      products.value = []
    }
  }

  /**
   * @description 提交当前的表单数据
   * @param {ISaleMan} targetSaleMan 提交申请单后的目标销售人，
   * @return {*}  {Promise<boolean>}
   */
  async function submitApplication(targetSaleMan: ISaleMan): Promise<boolean> {
    const data = toRaw(application.value)
    data.productList = toRaw(products.value)
    data.demand = toRaw(demand.value)
    if (
      application.value.type !== orderApplicationTypeEnum.FreeSampleCard &&
      application.value.type !== orderApplicationTypeEnum.InboundSampleCard &&
      application.value.type !== orderApplicationTypeEnum.ChargeSampleCard
    ) {
      data.type = orderApplicationTypeEnum.StandbyOrder
    }

    switch (application.value.status) {
      case orderApplicationStatusEnum.WAIT_SUBMIT: //当待提交时 选择的销售人未销售人员
        data.saleUserId = targetSaleMan.id
        data.saleUserName = targetSaleMan.name
        await orderApplicationApi.submitApplication(data)
        break
      case orderApplicationStatusEnum.CREATE: //当新建时 选择的销售人未销售人员
        data.saleUserId = targetSaleMan.id
        data.saleUserName = targetSaleMan.name
        await orderApplicationApi.submitApplication(data)
        break
      case orderApplicationStatusEnum.SALE_AUDIT: //状态未销售审批时 选择的销售人为销售经理
        data.managerId = targetSaleMan.id
        data.managerName = targetSaleMan.name
        await orderApplicationApi.saleAudit(data)
        break
      default:
        ElMessage.error(`当前提交的申请单状态错误,请联系管理员`)
        throw `application cant submit, curr status error:  ${application.value.status}`
    }
    return true
  }

  /**
   * @description 保存当前的表单数据
   * @return {*}  {Promise<boolean>}
   */
  async function saveApplication(): Promise<boolean> {
    const data = toRaw(application.value)
    data.productList = toRaw(products.value)
    data.demand = toRaw(demand.value)
    if (
      application.value.type !== orderApplicationTypeEnum.FreeSampleCard &&
      application.value.type !== orderApplicationTypeEnum.InboundSampleCard &&
      application.value.type !== orderApplicationTypeEnum.ChargeSampleCard
    ) {
      data.type = orderApplicationTypeEnum.StandbyOrder
    }
    await orderApplicationApi.saveApplication(data)
    return true
  }

  /**
   * @description 大区经理审批提交
   * @return {*}  {Promise<boolean>}
   */
  async function managerAuditSubmit(): Promise<boolean> {
    const applyId: string = application.value.id
    const result: orderApplicationReviewResultEnum = application.value
      .managerResult as orderApplicationReviewResultEnum
    const remark: string = application.value.remark
    const leaderId: string = application.value.leaderId
    await orderApplicationApi.saleManagerAudit(applyId, result, remark, leaderId)
    return true
  }

  /**
   * @description 销售领导审批提交
   * @return {*}  {Promise<boolean>}
   */
  async function leaderAuditSubmit(): Promise<boolean> {
    const applyId: string = application.value.id
    const result: orderApplicationReviewResultEnum = application.value
      .managerResult as orderApplicationReviewResultEnum
    const remark: string = application.value.remark
    await orderApplicationApi.saleLeaderAudit(applyId, result, remark)
    return true
  }

  /**
   * @description 取消申请单
   * @return {*}  {Promise<boolean>}
   */
  async function cancelApplication(): Promise<boolean> {
    const applyId = application.value.id
    return orderApplicationApi.cancelApplication(applyId)
  }

  // 获取制卡详情数据
  async function getProductrequirement(requirementInfoId: string) {
    const { data } = await findMakeCardApi({
      makeCardRequirementInfoId: requirementInfoId
    })
    const {
      makeCardNumber, //需求编号
      makeCardRequirementInfoCId, //客户主键ID
      makeCardRequirementInfoCname, //客户名称
      makeCardRequirementInfoCcode //客户(租户)ID
    } = data as any
    application.value.requirementCode = makeCardNumber
    application.value.customerId = makeCardRequirementInfoCId
    application.value.customerName = makeCardRequirementInfoCname
    application.value.customerCode = makeCardRequirementInfoCcode
    // console.log(data)
  }

  // 获取稿样详情数据
  async function getDraftList(requirementInfoId: string) {
    //根据需求ID获取稿样数列表
    const { data } = await getDraftListApi({ makecardDraftschemeInfoCardnumber: requirementInfoId })
    if (!data) {
      console.error('无法获取稿样方案数据')
      return
    }
    const params = sampleCardApplicationStore.getRequirementById(requirementInfoId) //获取卡产品需求参数
    if (!params.schemeInfoIds) {
      return
    }

    params.schemeInfoIds.forEach((schemeId) => {
      const schemeItem = data.filter((scheme) => scheme.makecardDraftschemeInfoId == schemeId)[0]
      if (schemeItem) {
        const { makecardDraftschemeInfoCardid, makecardDraftschemeInfoCardname } = schemeItem
        const productItem: IOrderApplicationProduct = getDefaultProduct()
        productItem.productName = makecardDraftschemeInfoCardname
        productItem.cardCode = makecardDraftschemeInfoCardid
        products.value.push(productItem)
      }
    })
  }

  /**
   * @description 加载卡产品需求内容数据
   * @param {string} requirementInfoId 需求ID
   */
  async function loadProductRequirement(requirementInfoId: string) {
    try {
      await getProductrequirement(requirementInfoId)
      await getDraftList(requirementInfoId)
    } catch (ex) {
      console.error(`获取需求稿样详情数据失败`, ex)
    }
  }

  return {
    //data
    application,
    products,
    demand,
    currSaleMan,
    currCustomer,
    hasSaleMan,
    hasSaleManager,
    //events
    getApplication,
    submitApplication,
    saveApplication,
    //saleAudiSubmit,
    managerAuditSubmit,
    leaderAuditSubmit,
    onSaveProduct, //更新产品列表
    delProduct,
    cancelApplication,
    getDefaultProduct,

    loadProductRequirement
  }
}
