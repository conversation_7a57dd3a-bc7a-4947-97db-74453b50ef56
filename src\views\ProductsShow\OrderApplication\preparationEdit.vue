<template>
  <ContentWrap v-loading="loading">
    <div class="header__row">{{ t('productsShow.preparationEdit.ApplicationForm') }}</div>
    <div class="form__content">
      <!-- 申请单 -->
      <el-form
        ref="applicationFormRef"
        :rules="rules"
        :model="application"
        label-width="auto"
        inline
        class="application_form form-item-h"
      >
        <el-row :gutter="20">
          <el-form-item
            v-if="application.applyCode"
            :label="t('productsShow.preparationEdit.ApplicationFormNo')"
          >
            {{ application.applyCode }}
          </el-form-item>
          <el-form-item :label="t('productsShow.preparationEdit.CustomerName')" prop="customerId">
            <el-select
              v-model="currCustomer"
              :disabled="readonly"
              value-key="id"
              :placeholder="t('productsShow.preparationEdit.PleaseEnterCustomerName')"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in customers"
                :key="item.id"
                :label="item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>

          <!--          <el-col :md="8" :lg="8" :xl="6">-->
          <!--            <el-form-item :label="交付方式" prop="deliveryType">-->
          <!--              <el-select-->
          <!--                v-model="application.deliveryType"-->
          <!--                :placeholder="请选择交付方式"-->
          <!--                filterable-->
          <!--                clearable-->
          <!--                style="width: 100%; min-width: 100%"-->
          <!--              >-->
          <!--                <el-option-->
          <!--                  v-for="dict in deliveryTypeOptions"-->
          <!--                  :key="dict.value"-->
          <!--                  :label="dict.label"-->
          <!--                  :value="dict.value"-->
          <!--                />-->
          <!--              </el-select>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
        </el-row>
        <el-row :gutter="20">
          <el-form-item :label="t('productsShow.preparationEdit.deliveryDate')" prop="deliveryAt">
            <el-date-picker
              v-model="application.deliveryAt"
              :disabled="readonly"
              type="date"
              :placeholder="t('productsShow.preparationEdit.PleaseSelectDeliveryDate')"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%; min-width: 100%"
            />
          </el-form-item>
          <el-form-item :label="t('productsShow.preparationEdit.UrgentDelivery')" prop="urgentSign">
            <el-checkbox
              v-model="application.urgentSign"
              :disabled="readonly"
              label=""
              size="default"
            />
          </el-form-item>
          <el-form-item
            v-if="application.urgentSign"
            :label="t('productsShow.preparationEdit.urgentReason')"
            prop="urgentReason"
          >
            <el-input
              v-model="application.urgentReason"
              :disabled="readonly"
              :rows="4"
              type="textarea"
              :maxlength="1000"
              :show-word-limit="true"
              :autosize="{ minRows: 1, maxRows: 6 }"
              :placeholder="t('productsShow.preparationEdit.PleaseEnterUrgentReason')"
              clearable
            />
          </el-form-item>
          <el-form-item
            v-if="!application.urgentSign"
            :label="t('productsShow.preparationEdit.ReviewComments')"
            prop="urgentReason"
          >
            <el-input
              v-model="application.urgentReason"
              :disabled="readonly"
              :rows="4"
              type="textarea"
              :maxlength="1000"
              :show-word-limit="true"
              :autosize="{ minRows: 1, maxRows: 6 }"
              :placeholder="t('productsShow.preparationEdit.PleaseEnterReviewComments')"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 产品 -->
      <el-row>
        <el-col :span="6">
          <div class="header__row header__row_2">{{
            t('productsShow.preparationEdit.ProductList')
          }}</div>
        </el-col>
        <el-col :md="18" :lg="18" :xl="12" v-if="!mangerAuditBtnShow && !leaderAuditBtnShow">
          <div class="flex flex-row content-center justify-end mr-5">
            <el-button type="primary" @click="downloadTemplatePrepareFile()">{{
              t('productsShow.preparationEdit.DownloadTemplate')
            }}</el-button>
            <el-button type="primary" @click="onAddProduct">{{
              t('productsShow.preparationEdit.AddProducts')
            }}</el-button>
            <Upload
              class="w-20 ml-2"
              ref="uploadExcelRef"
              @file-change="fileChange"
              :limit="1"
              :limitFormat="['xlsx', 'xls']"
              accept=".xlsx,.xls"
            >
              <template #btn>
                <el-button type="primary">
                  {{ t('productsShow.preparationEdit.BatchImport') }}
                </el-button>
              </template>
            </Upload>
          </div>
        </el-col>
      </el-row>
      <el-table :data="products" border width="100%">
        <el-table-column
          prop="productName"
          :label="t('productsShow.preparationEdit.ProductName')"
          width="250"
          align="center"
        />
        <el-table-column
          prop="customerProductCode"
          :label="t('productsShow.preparationEdit.CustomerProductCode')"
          :min-width="ifEn ? 200 : 150"
          align="center"
        />
        <el-table-column
          prop="cardCode"
          :label="t('productsShow.preparationEdit.CardBaseNumber')"
          :min-width="ifEn ? 180 : 120"
          align="center"
        />
        <el-table-column
          prop="standbyType"
          :label="t('productsShow.preparationEdit.BackupType')"
          :min-width="ifEn ? 200 : 120"
          align="center"
        >
          <template #default="{ row }">
            <span
              class="badge"
              :style="{
                background:
                  row.standbyType === standbyTypeEnum.finish
                    ? 'rgb(252, 179, 34)'
                    : 'rgb(128, 117, 196)'
              }"
              >{{ standbyTypeMapper(row.standbyType) }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="amount"
          :label="t('productsShow.preparationEdit.BackupQuantity')"
          :min-width="ifEn ? 180 : 120"
          align="center"
        />
        <el-table-column
          prop="branchInfo"
          :label="t('productsShow.preparationEdit.BranchMessage')"
          :min-width="ifEn ? 180 : 120"
          align="center"
        />
        <el-table-column
          prop="productType"
          :label="t('productsShow.preparationEdit.ProductType')"
          :min-width="ifEn ? 200 : 120"
          align="center"
        >
          <template #default="{ row }">
            <span
              class="badge"
              :style="{
                background:
                  row.productType === productTypeEnum.card ? 'rgb(255 75 75)' : 'rgb(131 130 120)'
              }"
              >{{ productTypeMapper(row.productType) }}</span
            >
          </template>
        </el-table-column>
        <!-- <el-table-column prop="deliveryAt" :label="期望交期" /> -->
        <el-table-column
          prop="remark"
          :label="t('productsShow.preparationEdit.Remarks')"
          width="150"
          align="center"
        />
        <el-table-column
          fixed="right"
          :label="t('productsShow.preparationEdit.Actions')"
          width="200"
          v-if="!readonly"
          align="center"
        >
          <template #default="{ row, $index }">
            <el-button type="primary" link @click="onEditProduct(row, $index)">{{
              t('productsShow.preparationEdit.edit')
            }}</el-button>
            <el-button type="primary" link @click="onDelProduct($index)">{{
              t('productsShow.preparationEdit.delete')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </ContentWrap>

  <div class="affix-container">
    <el-affix position="bottom" :offset="30">
      <el-button type="primary" @click="onLeaderDialog" v-if="leaderAuditBtnShow">{{
        t('productsShow.preparationEdit.Approva')
      }}</el-button>
      <el-button type="primary" @click="onManagerDialog" v-if="mangerAuditBtnShow">{{
        t('productsShow.preparationEdit.Approva')
      }}</el-button>
      <el-button type="primary" @click="onSave" v-if="saveBtnsShow">{{
        t('productsShow.preparationEdit.Save')
      }}</el-button>
      <el-button type="primary" @click="onShowSaleDailog" v-if="submitBtnShow">{{
        t('productsShow.preparationEdit.Submit')
      }}</el-button>
      <el-button type="danger" @click="onCancel" v-if="cancelBtnShow">{{
        t('productsShow.preparationEdit.Cancel')
      }}</el-button>
      <el-button type="primary" @click="onGobackList">{{
        t('productsShow.preparationEdit.Back')
      }}</el-button>
    </el-affix>
  </div>

  <preparationProductDialog ref="preparationProductDialogRef" @save-product="onSaveProduct" />
  <appointSaleDialog ref="appointSaleDialogRef" @sale-man-selected="onSaleSelected" />
  <managerAuditDialog ref="managerAuditDialogRef" @submit="onManagerSubmit" />
  <leaderAuditDialog ref="leaderAuditDialogRef" @submit="onLeaderSubmit" />
</template>

<script setup lang="ts">
defineOptions({
  name: 'preparationEdit'
})

import type { FormInstance, FormRules } from 'element-plus'
//import { getStrDictOptions, DictDataType } from '@/utils/dict'
import {
  orderApplicationReviewResultEnum,
  orderApplicationStatusEnum,
  orderApplicationTypeEnum,
  standbyTypeEnum,
  productTypeEnum
} from '@/api/orderApplication/types/enum.d'
import { useOrderAppliactionService } from './hooks/useOrderApplicationService'
import { useOrderApplicationCommonService } from './hooks/useOrderApplicationCommonService'
import { useTagsViewStore } from '@/store/modules/tagsView'
import Upload from './components/Upload.vue'
// <!-- 组件 -->
import preparationProductDialog from './components/preparationProductDialog.vue'
import appointSaleDialog from './components/appointSaleDialog.vue'
import managerAuditDialog from './components/managerAuditDialog.vue'
import leaderAuditDialog from './components/leaderAuditDialog.vue'
import IOrderApplicationProduct from '@/api/orderApplication/types/orderApplicationProduct'
import { cloneDeep } from 'lodash-es'
import ISaleMan from './types/SaleMan'
import { prepareHeaderInfo } from './common/lib'
import { useXlsx } from '@/hooks/web/useXlsx'

const { t, ifEn } = useI18n()

//定义表单规则对象
interface RuleForm {
  customerId: string
  deliveryAt: Date
  // deliveryType: string
}

const loading = ref<boolean>(false)

const route = useRoute()
const router = useRouter()
const preparationProductDialogRef = ref()
const appointSaleDialogRef = ref()
const managerAuditDialogRef = ref()
const leaderAuditDialogRef = ref()
// 导入模板 解析数据
const uploadExcelRef = ref()
const { analyzeExcel, exportExcel, getExcelHeader } = useXlsx()

const commonService = useOrderApplicationCommonService()
const { customers, standbyTypeMapper, productTypeMapper } = commonService
const orderApplicationService = useOrderAppliactionService()
const { application, products, currCustomer } = orderApplicationService
const tagsViewStore = useTagsViewStore()
const applicationFormRef = ref<FormInstance>() //表单对象

//表单校验规格
const rules = reactive<FormRules<RuleForm>>({
  customerId: [
    {
      required: true,
      message: t('productsShow.preparationEdit.PleaseEnterCustomerName'),
      trigger: ['blur', 'change']
    }
  ],
  deliveryAt: [
    {
      required: true,
      message: t('productsShow.preparationEdit.PleaseEnterDeliveryDate'),
      trigger: 'blur'
    }
  ]
  // deliveryType: [{ required: true, message: '请选择交付方式', trigger: ['blur', 'change'] }]
})

//const deliveryTypeOptions = ref<DictDataType[]>(getStrDictOptions('mail_mode')) // 交付方式选项
const leaderAuditBtnShow = computed<boolean>(() => {
  return application.value.status == orderApplicationStatusEnum.LEADER_AUDIT
})
const mangerAuditBtnShow = computed<boolean>(() => {
  return application.value.status == orderApplicationStatusEnum.MANAGE_AUDIT
})
const saveBtnsShow = computed<boolean>(() => {
  return (
    application.value.status == orderApplicationStatusEnum.WAIT_SUBMIT ||
    application.value.status == orderApplicationStatusEnum.CREATE
  )
})
const submitBtnShow = computed<boolean>(() => {
  return (
    application.value.status == orderApplicationStatusEnum.SALE_AUDIT ||
    application.value.status == orderApplicationStatusEnum.WAIT_SUBMIT ||
    application.value.status == orderApplicationStatusEnum.CREATE
  )
})

const cancelBtnShow = computed<boolean>(() => {
  return (
    application.value.status == orderApplicationStatusEnum.SALE_AUDIT ||
    application.value.status == orderApplicationStatusEnum.WAIT_SUBMIT
  )
})
//只读
const readonly = computed<boolean>(() => {
  const status: orderApplicationStatusEnum = application.value.status
  return (
    status == orderApplicationStatusEnum.MANAGE_AUDIT || status == orderApplicationStatusEnum.CANCEL
  )
})

async function onSaveProduct(product: IOrderApplicationProduct) {
  orderApplicationService.onSaveProduct(product)
}

async function onAddProduct() {
  preparationProductDialogRef.value?.show()
}
async function onEditProduct(data: IOrderApplicationProduct, index: any) {
  const editData = cloneDeep(toRaw(data))
  editData.index = index
  preparationProductDialogRef.value?.show(editData)
}
async function onDelProduct(index: number) {
  orderApplicationService.delProduct(index)
}

async function onShowSaleDailog() {
  //校验表单规则
  applicationFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      appointSaleDialogRef.value?.show()
    } else {
      console.error('form valid error')
      return
    }
  })
}
async function onSaleSelected(saleMan: ISaleMan) {
  submit(saleMan) //提交任务
}

async function submit(saleMan: ISaleMan) {
  try {
    loading.value = true
    application.value.type = orderApplicationTypeEnum.StandbyOrder //指定备库
    const result = await orderApplicationService.submitApplication(saleMan)
    if (result) {
      ElMessage.success(t('productsShow.preparationEdit.SubmitSuccess'))
      onGobackList()
    }
  } finally {
    loading.value = false
  }
}

async function onSave() {
  try {
    loading.value = true
    application.value.type = orderApplicationTypeEnum.StandbyOrder //指定备库
    const result = await orderApplicationService.saveApplication()
    if (result) {
      ElMessage.success(t('productsShow.preparationEdit.SaveSuccess'))
      onGobackList()
    }
  } finally {
    loading.value = false
  }
}

async function onLeaderDialog() {
  leaderAuditDialogRef.value?.show()
}
async function onManagerDialog() {
  managerAuditDialogRef.value?.show()
}

async function onManagerSubmit(
  reviewResult: orderApplicationReviewResultEnum,
  remark: string,
  leaderId: string
) {
  if (!reviewResult) {
    ElMessage.error(t('productsShow.preparationEdit.PleaseSelectCommentsResult'))
    throw t('productsShow.preparationEdit.PleaseSelectCommentsResult')
  }
  try {
    loading.value = true
    application.value.managerResult = reviewResult
    application.value.remark = remark
    application.value.leaderId = leaderId
    const result = await orderApplicationService.managerAuditSubmit()
    if (result) {
      ElMessage.success(t('productsShow.preparationEdit.SubmitSuccess'))
      onGobackList()
    }
  } finally {
    loading.value = false
  }
}

async function onLeaderSubmit(reviewResult: orderApplicationReviewResultEnum, remark: string) {
  if (!reviewResult) {
    ElMessage.error(t('productsShow.preparationEdit.PleaseSelectCommentsResult'))
    throw t('productsShow.preparationEdit.PleaseSelectCommentsResult')
  }
  try {
    loading.value = true
    application.value.managerResult = reviewResult
    application.value.remark = remark
    const result = await orderApplicationService.leaderAuditSubmit()
    if (result) {
      ElMessage.success(t('productsShow.preparationEdit.SubmitSuccess'))
      onGobackList()
    }
  } finally {
    loading.value = false
  }
}

async function onCancel() {
  ElMessageBox.confirm(
    t('productsShow.preparationEdit.CancelApply'),
    t('productsShow.preparationEdit.Tips'),
    {
      confirmButtonText: t('productsShow.preparationEdit.Submit2'),
      cancelButtonText: t('productsShow.preparationEdit.Cancel2'),
      type: 'warning'
    }
  )
    .then(async () => {
      await orderApplicationService.cancelApplication()
      ElMessage.success(t('productsShow.preparationEdit.CancelSuccess'))
      onGobackList()
    })
    .catch(() => {})
}

async function onGobackList() {
  router.push({
    name: `PreparationList`
  })
  onClose()
}

async function onClose() {
  tagsViewStore.delCurView()
}
provide('customer', currCustomer) //传递给子组件的客户对象

onMounted(() => {
  const applyId: string | undefined = route.query.id as string
  orderApplicationService.getApplication(applyId)

  nextTick(() => {
    commonService.getSaleMen()
    commonService.getCustomers()
  })
})

const downloadTemplatePrepareFile = () => {
  //exportExcel(prepareHeaderInfo, [], t('productsShow.preparationEdit.ImportBackupListTemplate'))
  const url = `static/preparation/${t(
    'productsShow.preparationEdit.ImportBackupListTemplate'
  )}.xlsx` //模板選擇
  console.log(url)

  const downA = document.createElement('a')
  downA.href = url
  downA.target = '_blank'
  downA.download = `${t('productsShow.preparationEdit.ImportBackupListTemplate')}.xlsx`
  downA.click()
}

const fileChange = async () => {
  const res = await uploadExcelRef.value.submitFileBase64()
  resolveExcel(res)
}

// 解析excel数据
const resolveExcel = async (res) => {
  try {
    let headers = await getExcelHeader(res[0].raw)
    console.log(headers)
    prepareHeaderInfo.forEach((item, index) => {
      if (item !== headers[index]) {
        throw Error(t('productsShow.preparationEdit.ImportFileError'))
      }
    })
    console.log(res[0])
    let result = await analyzeExcel(res[0].raw)
    batchImportProduct(result)
  } catch (error) {
    ElMessage.error(t('productsShow.preparationEdit.ImportFileError'))
  } finally {
    nextTick(() => {
      uploadExcelRef.value.clearFile()
    })
  }
}

// 产品最大数量
const PRODUCT_NUM = 9999900

// 批量导出创建产品
const batchImportProduct = (result) => {
  try {
    result.forEach((item) => {
      const regexNum = /^\d+$/
      if (
        item[prepareHeaderInfo[3]] != t('productsShow.typeData.finishedProductBackup') &&
        item[prepareHeaderInfo[3]] != t('productsShow.typeData.semiProductBackup')
      ) {
        throw new Error(t('productsShow.preparationEdit.BackupTypeError'))
      }
      if (
        item[prepareHeaderInfo[6]] != t('productsShow.typeData.CardProduct') &&
        item[prepareHeaderInfo[6]] != t('productsShow.typeData.nonCardProduct')
      ) {
        throw new Error(t('productsShow.preparationEdit.ProductTypeError'))
      }
      if (!regexNum.test(item[prepareHeaderInfo[4]])) {
        throw new Error(t('productsShow.preparationEdit.QuantityError'))
      }
      if (item[prepareHeaderInfo[4]] < 1 || item[prepareHeaderInfo[4]] > PRODUCT_NUM) {
        throw new Error(t('productsShow.preparationEdit.QuantityRangeError'))
      }
    })
    let newProductList: any = []
    result.forEach((item) => {
      const newElement = {
        productName: item[prepareHeaderInfo[0]] || '',
        customerProductCode: item[prepareHeaderInfo[1]] || '',
        cardCode: item[prepareHeaderInfo[2]] || '',
        standbyType:
          item[prepareHeaderInfo[3]] == t('productsShow.typeData.finishedProductBackup')
            ? standbyTypeEnum.finish
            : standbyTypeEnum.semiFinish,
        amount: item[prepareHeaderInfo[4]] || '',
        branchInfo: item[prepareHeaderInfo[5]] || '',
        productType:
          item[prepareHeaderInfo[6]] == t('productsShow.typeData.CardProduct')
            ? productTypeEnum.card
            : productTypeEnum.nonCard,
        remark: item[prepareHeaderInfo[7]] || ''
      }
      newProductList.push(newElement)
    })
    products.value.push(...newProductList)
  } catch (error: any) {
    ElMessage.error(error.message)
  } finally {
  }
}
</script>

<style lang="less" scoped>
.header__row {
  font-size: 20px;
  border-bottom: 1px dotted rgba(0, 0, 0, 0.2);
  padding: 15px 0px;
  text-transform: uppercase;
  color: #535351;
  font-weight: bold;
}

.header__row_2 {
  font-size: 17px !important;
  border-bottom: none !important;
  padding: 10px 0px !important;
}

.form__content {
  padding: 15px 0;
  .application_form {
    .el-form-item {
      min-width: 400px;
    }
  }
}

.form-item-h {
  :deep(.el-form-item__content) {
    align-items: flex-start;
  }
}

.affix-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  text-align: right;
  padding-right: 30px;

  .el-affix {
    min-width: 450px !important;
  }
}

.tool-bar {
  float: right;
  margin-bottom: 10px;
}

.demandForm {
  padding: 20px 20px 0px 20px;
  border: 1px dotted rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999;
  border-radius: 10px;
}
</style>
