<template>
  <ContentWrap ifTable>
    <!-- 查询 -->
    <template #search>
      <el-form ref="makeCardList" :model="params" :inline="false">
        <el-row :gutter="10">
          <el-col :md="8" :lg="8" :xl="6">
            <el-form-item :label="t('productsShow.preparationList.customerName')">
              <el-input
                v-model="params.customerName"
                :placeholder="t('productsShow.preparationList.customerNamePlaceholder')"
              />
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="6">
            <el-form-item :label="t('productsShow.preparationList.ApplicationStatus')">
              <el-select
                v-model="params.status"
                :placeholder="t('productsShow.preparationList.ApplicationStatusSelect')"
                clearable
              >
                <el-option
                  v-for="item in orderApplicationStatusArray"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :md="8" :lg="8" :xl="6">
            <!--查询 重置-->
            <el-form-item label-width="0">
              <el-button type="primary" v-track:click.btn @click="onSearch">
                {{ t('productsShow.preparationList.search') }}
              </el-button>
              <el-button type="warning" @click="onReset">
                {{ t('productsShow.preparationList.reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>

    <!-- Tool -->
    <div class="tool-bar">
      <el-button type="primary" v-track:click.btn @click="onAdd">{{
        t('productsShow.preparationList.new')
      }}</el-button>
    </div>
    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="datas"
      height="100%"
      :header-cell-style="{ background: '#F7F7F9', color: '#606266' }"
      style="width: 100%"
    >
      <el-table-column
        prop="applyCode"
        :label="t('productsShow.preparationList.applyCode')"
        :min-width="ifEn ? 200 : 150"
      >
        <template #default="{ row }">
          <el-link type="primary" :underline="false" v-track:click.btn @click="onView(row)">
            {{ row.applyCode }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="customerName"
        :label="t('productsShow.preparationList.customerName')"
        :min-width="ifEn ? 200 : 150"
      />
      <el-table-column
        prop="createName"
        :label="t('productsShow.preparationList.createName')"
        :min-width="ifEn ? 100 : 80"
      />
      <el-table-column
        prop="saleUserName"
        :label="t('productsShow.preparationList.saleUserName')"
        :min-width="ifEn ? 140 : 100"
      />
      <el-table-column
        prop="managerName"
        :label="t('productsShow.preparationList.managerName')"
        :min-width="ifEn ? 140 : 100"
      >
        <template #default="{ row }">
          {{ row.managerName }}
          <span class="badge" style="background: rgb(245, 108, 108)" v-if="isNG(row.managerResult)">
            NG
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="leaderName"
        :label="t('productsShow.preparationList.LeaderApproval')"
        :min-width="ifEn ? 140 : 100"
      >
        <template #default="{ row }">
          {{ row.leaderName }}
          <span class="badge" style="background: rgb(245, 108, 108)" v-if="isNG(row.leaderResult)"
            >NG</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        :label="t('productsShow.preparationList.ApplicationStatus')"
        width="180"
      >
        <template #default="{ row }">
          <span class="badge" :style="{ background: statusColorMapper(row.status) }">
            {{ dispalyStatus(row.status) }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        :label="t('productsShow.preparationList.createDate')"
        :min-width="ifEn ? 200 : 200"
      />
      <el-table-column fixed="right" :label="t('productsShow.preparationList.actions')" width="200">
        <template #default="{ row }">
          <el-button type="primary" link v-track:click.btn @click="onView(row)">{{
            t('productsShow.preparationList.views')
          }}</el-button>
          <el-button
            type="primary"
            link
            v-track:click.btn
            @click="onEdit(row)"
            v-if="isEditable(row.status)"
            >{{ t('productsShow.preparationList.edit') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <template #pagination>
      <Pagination
        :total="total"
        v-model:current-page="pageIndex"
        v-model:page-size="pageSize"
        @pagination="searchPreparations"
      />
    </template>
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({
  name: 'preparationList'
})

import IOrderApplication from '@/api/orderApplication/types/orderApplication'
import { orderApplicationStatusArray } from './types/data.d'
import { useOrderAppliactionListService } from './hooks/useOrderApplicationListService'
import {
  orderApplicationTypeEnum,
  orderApplicationStatusEnum,
  orderApplicationReviewResultEnum
} from '@/api/orderApplication/types/enum.d'
import { first, filter } from 'lodash-es'

const { t, ifEn } = useI18n()
const router = useRouter()
const orderApplicationService = useOrderAppliactionListService()
const { params, searchPreparations, resetParmas, datas, total, pageIndex, pageSize } =
  orderApplicationService //查询条件
const loading = ref()

//显示申请类型名称
function dispalyStatus(value: orderApplicationStatusEnum): string {
  const item = first(filter(orderApplicationStatusArray, (item) => item.value == value))
  if (item) {
    return item.label
  }
  return ''
}
//显示申请状态对应的颜色
function statusColorMapper(value: orderApplicationStatusEnum): string {
  const result = orderApplicationStatusArray.filter((item) => item.value == value)[0]
  if (result) {
    return result.color
  }
  return ''
}

//判断是否可以编辑
function isEditable(status: orderApplicationStatusEnum): boolean {
  return status != orderApplicationStatusEnum.FINISH && status != orderApplicationStatusEnum.CANCEL
}

function onSearch() {
  params.value.type = orderApplicationTypeEnum.StandbyOrder //查询备库申请
  pageIndex.value = 1
  searchPreparations()
}
function onReset() {
  resetParmas()
}

function onAdd() {
  router.push({
    name: `PreparationEdit`
  })
}

function onView(data: IOrderApplication) {
  const id: string = data.id
  router.push({
    name: `PreparationView`,
    query: {
      id
    }
  })
}

function onEdit(data: IOrderApplication) {
  const id: string = data.id
  router.push({
    name: `PreparationEdit`,
    query: {
      id
    }
  })
}

//评审是否NT
function isNG(value: orderApplicationReviewResultEnum) {
  return value == orderApplicationReviewResultEnum.NT
}
onMounted(() => {
  nextTick(() => {
    onSearch()
  })
})
onActivated(() => searchPreparations())
</script>

<style scoped lang="less">
.tool-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.bottom-view {
  margin-top: 10px;
  height: 50px;
  text-align: center;
  float: right;
}

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999;
  border-radius: 10px;
}
</style>
