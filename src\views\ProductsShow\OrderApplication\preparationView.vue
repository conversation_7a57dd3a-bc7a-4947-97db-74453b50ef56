<template>
  <ContentWrap v-loading="loading">
    <div class="header__row">{{ t('productsShow.preparationEdit.ApplicationForm') }}</div>
    <div class="form__content">
      <!-- 申请单 -->
      <el-form
        ref="formRef"
        :model="application"
        label-width="auto"
        inline
        class="application_form form-item-h"
      >
        <el-row :gutter="20">
          <el-form-item
            :label="t('productsShow.preparationEdit.ApplicationFormNo')"
            v-if="application.applyCode"
          >
            {{ application.applyCode }}
          </el-form-item>

          <el-form-item :label="t('productsShow.preparationEdit.CustomerName')" prop="currCustomer">
            {{ application.customerName }}
          </el-form-item>

          <!-- <el-form-item :label="交付方式" prop="deliveryType">
              {{ deliveryTypeMapper }}
            </el-form-item> -->
        </el-row>
        <el-row :gutter="20">
          <el-form-item :label="t('productsShow.preparationEdit.deliveryDate')" prop="deliveryAt">
            {{ dateFormat(application.deliveryAt) }}
          </el-form-item>
          <el-form-item :label="t('productsShow.preparationEdit.UrgentDelivery')" prop="urgentSign">
            <el-tag type="danger" size="default" v-if="application.urgentSign">{{
              t('makeCard.common.yes')
            }}</el-tag>
            <el-tag type="success" size="default" v-else>{{ t('makeCard.common.no') }}</el-tag>
          </el-form-item>
          <el-form-item
            v-if="application.urgentSign"
            :label="t('productsShow.preparationEdit.urgentReason')"
            prop="urgentReason"
          >
            <el-input
              v-model="application.urgentReason"
              :readonly="true"
              :rows="4"
              type="textarea"
              :maxlength="1000"
              :show-word-limit="true"
              :autosize="{ minRows: 1, maxRows: 6 }"
              :placeholder="t('productsShow.preparationEdit.PleaseEnterUrgentReason')"
              clearable
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item :label="t('productsShow.preparationList.saleUserName')">
            <el-text>{{ application.saleUserName }}</el-text>
          </el-form-item>
          <el-form-item :label="t('productsShow.sampleCardEdit.saleUserTime')">
            <el-text class="mx-1" type="success">
              {{ dateFormat(application.saleUserTime) }}</el-text
            >
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item :label="t('productsShow.preparationEdit.saleLeader')"
            ><el-text>{{ application.leaderName }} </el-text>
          </el-form-item>
          <el-form-item :label="t('productsShow.preparationEdit.LeaderConfirmationTime')">
            <el-text class="mx-1" type="success"> {{ dateFormat(application.leaderTime) }}</el-text>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item :label="t('productsShow.preparationList.managerName')">
            <el-text>{{ application.managerName }} </el-text>
          </el-form-item>
          <el-form-item :label="t('productsShow.sampleCardEdit.managerTime')">
            <el-text class="mx-1" type="success">
              {{ dateFormat(application.managerTime) }}
            </el-text>
          </el-form-item>
        </el-row>

        <el-row>
          <el-form-item :label="t('productsShow.preparationEdit.ReviewComments')">
            <el-text style="color: red">{{ application.remark }}</el-text>
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 产品 -->
      <el-row>
        <el-col :span="12">
          <div class="header__row header__row_2">{{
            t('productsShow.preparationEdit.ProductList')
          }}</div>
        </el-col>
      </el-row>
      <el-table :data="products" border width="100%">
        <el-table-column
          prop="productName"
          :label="t('productsShow.preparationEdit.ProductName')"
          width="250"
          align="center"
        />
        <el-table-column
          prop="customerProductCode"
          :label="t('productsShow.preparationEdit.CustomerProductCode')"
          :min-width="ifEn ? 200 : 150"
          align="center"
        />
        <el-table-column
          prop="cardCode"
          :label="t('productsShow.preparationEdit.CardBaseNumber')"
          :min-width="ifEn ? 180 : 120"
          align="center"
        />
        <el-table-column
          prop="standbyType"
          :label="t('productsShow.preparationEdit.BackupType')"
          :min-width="ifEn ? 200 : 120"
          align="center"
        >
          <template #default="{ row }">
            <span
              class="badge"
              :style="{
                background:
                  row.standbyType === standbyTypeEnum.finish
                    ? 'rgb(252, 179, 34)'
                    : 'rgb(128, 117, 196)'
              }"
              >{{ standbyTypeMapper(row.standbyType) }}</span
            >
          </template>
        </el-table-column>

        <el-table-column
          prop="amount"
          :label="t('productsShow.preparationEdit.BackupQuantity')"
          :min-width="ifEn ? 180 : 120"
          align="center"
        />
        <el-table-column
          prop="branchInfo"
          :label="t('productsShow.preparationEdit.BranchMessage')"
          :min-width="ifEn ? 180 : 120"
          align="center"
        />
        <el-table-column
          prop="productType"
          :label="t('productsShow.preparationEdit.ProductType')"
          :min-width="ifEn ? 200 : 120"
          align="center"
        >
          <template #default="{ row }">
            <span
              class="badge"
              :style="{
                background:
                  row.productType === productTypeEnum.card ? 'rgb(255 75 75)' : 'rgb(131 130 120)'
              }"
              >{{ productTypeMapper(row.productType) }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          :label="t('productsShow.preparationEdit.Remarks')"
          width="150"
          align="center"
        />
      </el-table>
    </div>
  </ContentWrap>

  <div class="affix-container">
    <el-affix position="bottom" :offset="30">
      <el-button type="primary" @click="onGobackList">{{
        t('productsShow.preparationEdit.Back')
      }}</el-button>
    </el-affix>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'preparationView'
})

import { standbyTypeEnum, productTypeEnum } from '@/api/orderApplication/types/enum.d'
import { getStrDictOptions, DictDataType } from '@/utils/dict'
import { useOrderAppliactionService } from './hooks/useOrderApplicationService'
import { useOrderApplicationCommonService } from './hooks/useOrderApplicationCommonService'
import { useTagsViewStore } from '@/store/modules/tagsView'

const { t, ifEn } = useI18n()

const loading = ref<boolean>(false)

const route = useRoute()
const router = useRouter()

const orderApplicationService = useOrderAppliactionService()
const { application, products } = orderApplicationService

const commonService = useOrderApplicationCommonService()
const { dateFormat, standbyTypeMapper, productTypeMapper } = commonService
const deliveryTypeOptions = ref<DictDataType[]>(getStrDictOptions('mail_mode')) // 交付方式选项
const tagsViewStore = useTagsViewStore()

// const deliveryTypeMapper = computed<string>(() => {
//   const result = deliveryTypeOptions.value.filter(
//     (item) => item.value === application.value.deliveryType
//   )[0]
//   if (!result) return '-'
//   return (result as DictDataType).label
// })

async function onGobackList() {
  tagsViewStore.delCurView()
  router.push({
    name: `PreparationList`
  })
}

onMounted(() => {
  const applyId: string | undefined = route.query.id as string
  orderApplicationService.getApplication(applyId)
})
</script>

<style scoped lang="less">
.header__row {
  font-size: 20px;
  border-bottom: 1px dotted rgba(0, 0, 0, 0.2);
  padding: 15px 0px;
  text-transform: uppercase;
  color: #535351;
  font-weight: bold;
}

.header__row_2 {
  font-size: 17px !important;
  border-bottom: none !important;
  padding: 10px 0px !important;
}

.form__content {
  padding: 15px 0;
  .application_form {
    .el-form-item {
      min-width: 350px;
    }
  }
}

.form-item-h {
  :deep(.el-form-item__content) {
    align-items: flex-start;
  }
}

.affix-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  text-align: right;
  padding-right: 30px;
}

.tool-bar {
  float: right;
  margin-bottom: 10px;
}

.demandForm {
  padding: 20px 20px 0px 20px;
  border: 1px dotted rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999;
  border-radius: 10px;
}
</style>
