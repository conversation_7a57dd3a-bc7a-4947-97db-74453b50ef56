<template>
  <ContentWrap v-loading="loading">
    <div class="header__row">{{ t('productsShow.sampleCardEdit.ApplicationForm') }}</div>
    <div class="form__content">
      <!-- 申请单 -->
      <el-form
        ref="applicationFormRef"
        :rules="rules"
        :model="application"
        label-width="auto"
        class="application_form"
      >
        <el-form-item
          :label="t('productsShow.sampleCardEdit.ApplicationFormNo')"
          v-if="application.applyCode"
        >
          {{ application.applyCode }}
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.CustomerName')" prop="customerId">
          <el-select
            v-model="currCustomer"
            :disabled="readonly"
            value-key="id"
            :placeholder="t('productsShow.sampleCardEdit.PleaseEnterCustomerName')"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option v-for="item in customers" :key="item.id" :label="item.name" :value="item" />
          </el-select>
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.ApplicationType')" prop="type">
          <el-select
            v-model="application.type"
            :placeholder="t('productsShow.sampleCardEdit.PleaseSelect')"
            :disabled="readonly"
            style="width: 100%"
          >
            <el-option
              v-for="item in sampleCardApplicationTypeArray"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.deliveryDate')" prop="deliveryAt">
          <el-date-picker
            v-model="application.deliveryAt"
            :disabled="readonly"
            type="date"
            :placeholder="t('productsShow.sampleCardEdit.PleaseSelectDeliveryDate')"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%; min-width: 100%"
          />
        </el-form-item>

        <!--          <el-col :md="8" :lg="8" :xl="6">-->
        <!--            <el-form-item label="交付方式" prop="deliveryType">-->
        <!--              <el-select-->
        <!--                v-model="application.deliveryType"-->
        <!--                :disabled="readonly"-->
        <!--                placeholder="请选择交付方式"-->
        <!--                filterable-->
        <!--                clearable-->
        <!--                style="width: 100%; min-width: 100%"-->
        <!--              >-->
        <!--                <el-option-->
        <!--                  v-for="dict in deliveryTypeOptions"-->
        <!--                  :key="dict.value"-->
        <!--                  :label="dict.label"-->
        <!--                  :value="dict.value"-->
        <!--                />-->
        <!--              </el-select>-->
        <!--            </el-form-item>-->
        <!--          </el-col>-->

        <el-form-item :label="t('productsShow.sampleCardEdit.urgentSign')" prop="urgentSign">
          <el-checkbox
            v-model="application.urgentSign"
            :disabled="readonly"
            label=""
            size="default"
          />
        </el-form-item>

        <el-form-item
          v-if="application.urgentSign"
          :label="t('productsShow.sampleCardEdit.urgentReason')"
          prop="urgentReason"
        >
          <el-input
            v-model="application.urgentReason"
            :disabled="readonly"
            :rows="4"
            type="textarea"
            :maxlength="1000"
            :show-word-limit="true"
            :autosize="{ minRows: 1, maxRows: 6 }"
            :placeholder="t('productsShow.sampleCardEdit.PleaseEnterUrgentReason')"
            clearable
          />
        </el-form-item>

        <template v-if="hasSaleMan">
          <el-form-item :label="t('productsShow.sampleCardEdit.saleUserName')">
            <el-text>{{ application.saleUserName }}</el-text>
          </el-form-item>

          <el-form-item :label="t('productsShow.sampleCardEdit.managerTime')">
            <el-text type="success"> {{ dateFormat(application.saleUserTime) }}</el-text>
          </el-form-item>
        </template>
        <template v-if="hasSaleManager">
          <el-form-item :label="t('productsShow.sampleCardEdit.managerName')">
            <el-text>{{ application.managerName }} </el-text>
          </el-form-item>
          <el-form-item :label="t('productsShow.sampleCardEdit.managerTime')">
            <el-text type="success"> {{ dateFormat(application.managerTime) }}</el-text>
          </el-form-item>
        </template>
      </el-form>

      <!-- 产品 -->
      <el-row>
        <el-col :span="12">
          <div class="header__row header__row_2">{{
            t('productsShow.sampleCardEdit.ProductList')
          }}</div>
        </el-col>
        <el-col :span="12" v-if="!mangerAuditBtnShow">
          <div class="tool-bar mt-10px">
            <Upload
              ref="uploadExcelRef"
              @file-change="fileChange"
              :limit="1"
              :limitFormat="['xlsx', 'xls']"
              accept=".xlsx,.xls"
              style="margin-right: -220px; margin-left: 10px"
            >
              <template #btn>
                <el-button type="primary">
                  {{ t('productsShow.sampleCardEdit.BatchImport') }}
                </el-button>
              </template>
            </Upload>
          </div>
          <div class="tool-bar mt-10px">
            <el-button type="primary" @click="onAddProduct" v-if="!readonly">{{
              t('productsShow.sampleCardEdit.AddProducts')
            }}</el-button>
          </div>
          <div class="tool-bar mt-10px">
            <el-button
              type="primary"
              style="margin-right: 10px"
              @click="downloadTemplateSampleCardFile()"
              >{{ t('productsShow.sampleCardEdit.DownloadTemplate') }}</el-button
            >
          </div>
        </el-col>
      </el-row>
      <el-table :data="products" border width="100%">
        <el-table-column
          prop="productName"
          :label="t('productsShow.sampleCardEdit.ProductName')"
          width="250"
        />
        <el-table-column
          prop="customerProductCode"
          :label="t('productsShow.sampleCardEdit.CustomerProductCode')"
          :min-width="ifEn ? 200 : 150"
        />
        <el-table-column
          prop="cardCode"
          :label="t('productsShow.sampleCardEdit.CardBaseNumber')"
          :min-width="ifEn ? 180 : 120"
        />
        <el-table-column
          prop="amount"
          :label="t('productsShow.sampleCardEdit.ApplicationQuantity')"
          :min-width="ifEn ? 180 : 80"
        />
        <el-table-column
          prop="plan"
          :label="t('productsShow.sampleCardEdit.ProjectNumber')"
          :min-width="ifEn ? 150 : 80"
        />
        <el-table-column
          prop="remark"
          :label="t('productsShow.sampleCardEdit.Remarks')"
          min-width="100"
        />
        <el-table-column
          fixed="right"
          :label="t('productsShow.sampleCardEdit.Actions')"
          width="120"
          v-if="!readonly"
        >
          <template #default="{ row, $index }">
            <el-button type="primary" link @click="onEditProduct(row, $index)">{{
              t('productsShow.sampleCardEdit.edit')
            }}</el-button>
            <el-button type="primary" link @click="onDelProduct($index)">{{
              t('productsShow.sampleCardEdit.delete')
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-row>
        <el-col :span="12">
          <div class="header__row header__row_2">{{
            t('productsShow.sampleCardEdit.SampleCardElements')
          }}</div>
        </el-col>
      </el-row>
      <!-- 需求要素 -->

      <el-form :model="application" label-width="auto" :inline="false" class="demandForm">
        <el-form-item :label="t('productsShow.sampleCardEdit.SampleCardFunction')" v-if="demand">
          <demandItemCheckList
            :checkArray="purposeCheckArray"
            v-model:checked="demand.purpose.items"
            v-model:remark="demand.purpose.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item
          :label="t('productsShow.sampleCardEdit.CardFrontPrintingElements')"
          v-if="demand"
        >
          <demandItemCheckList
            :checkArray="frontCheckArray"
            v-model:checked="demand.front.items"
            v-model:remark="demand.front.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item
          :label="t('productsShow.sampleCardEdit.CardBackPrintingElements')"
          v-if="demand"
        >
          <demandItemCheckList
            :checkArray="backCheckArray"
            v-model:checked="demand.back.items"
            v-model:remark="demand.back.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.OtherElements')" v-if="demand">
          <demandItemCheckList
            :checkArray="otherCheckArray"
            v-model:checked="demand.other.items"
            v-model:remark="demand.other.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item
          :label="t('productsShow.sampleCardEdit.PersonalisationRequirements')"
          v-if="demand"
        >
          <demandItemCheckList
            :checkArray="personalCheckArray"
            v-model:checked="demand.personal.items"
            v-model:remark="demand.personal.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.PMTestingRequirement')" v-if="demand">
          <demandItemCheckList
            :checkArray="pmCheckArray"
            v-model:checked="demand.pm.items"
            v-model:remark="demand.pm.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item
          :label="t('productsShow.sampleCardEdit.SecurityMeasureRequirements')"
          v-if="demand"
        >
          <demandItemCheckList
            :checkArray="safetyCheckArray"
            v-model:checked="demand.safety.items"
            v-model:remark="demand.safety.remark"
            :readonly="readonly"
          />
        </el-form-item>
      </el-form>
    </div>
  </ContentWrap>

  <div class="affix-container">
    <el-affix position="bottom" :offset="30">
      <el-button type="primary" @click="onManagerDialog" v-if="mangerAuditBtnShow">{{
        t('productsShow.sampleCardEdit.Approva')
      }}</el-button>
      <el-button type="primary" @click="onSave" v-if="saveBtnsShow">{{
        t('productsShow.sampleCardEdit.Save')
      }}</el-button>
      <el-button type="primary" @click="onShowSaleDailog" v-if="submitBtnShow">{{
        t('productsShow.sampleCardEdit.Submit')
      }}</el-button>
      <el-button type="danger" @click="onCancel" v-if="cancelBtnShow">{{
        t('productsShow.sampleCardEdit.Cancel')
      }}</el-button>
      <el-button type="primary" @click="onGobackList">{{
        t('productsShow.sampleCardEdit.Back')
      }}</el-button>
    </el-affix>
  </div>

  <sampleProductDailog ref="sampleProductDailogRef" @save-product="onSaveProduct" />
  <appointSaleDialog ref="appointSaleDialogRef" @sale-man-selected="onSaleSelected" />
  <managerAuditDialog ref="managerAuditDialogRef" @submit="onManagerSubmit" />
</template>

<script setup lang="ts">
defineOptions({
  name: 'sampleCardEdit'
})

import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { getStrDictOptions, DictDataType } from '@/utils/dict'
import {
  orderApplicationReviewResultEnum,
  orderApplicationStatusEnum
} from '@/api/orderApplication/types/enum.d'
import {
  sampleCardApplicationTypeArray,
  purposeCheckArray,
  frontCheckArray,
  backCheckArray,
  otherCheckArray,
  personalCheckArray,
  pmCheckArray,
  safetyCheckArray
} from './types/data.d'
import { useOrderAppliactionService } from './hooks/useOrderApplicationService'
import { useOrderApplicationCommonService } from './hooks/useOrderApplicationCommonService'
import { useTagsViewStore } from '@/store/modules/tagsView'
// <!-- 组件 -->
import demandItemCheckList from './components/demandItemCheckList.vue'
import sampleProductDailog from './components/sampleProductDailog.vue'
import appointSaleDialog from './components/appointSaleDialog.vue'
import managerAuditDialog from './components/managerAuditDialog.vue'
import IOrderApplicationProduct from '@/api/orderApplication/types/orderApplicationProduct'
import { cloneDeep } from 'lodash-es'
import ISaleMan from './types/SaleMan'
import { useXlsx } from '@/hooks/web/useXlsx'
import Upload from './components/Upload.vue'
import { sampleCardHeaderInfo } from './common/lib'

const { t, ifEn } = useI18n()

//定义表单规则对象
interface RuleForm {
  customerId: string
  deliveryAt: Date
  // deliveryType: string
}

const loading = ref<boolean>(false)

const route = useRoute()
const router = useRouter()
const sampleProductDailogRef = ref()
const appointSaleDialogRef = ref()
const managerAuditDialogRef = ref()
const applicationFormRef = ref<FormInstance>() //表单对象

const commonService = useOrderApplicationCommonService()
const { customers, dateFormat } = commonService
const { analyzeExcel, exportExcel, getExcelHeader } = useXlsx()
// 导入模板 解析数据
const uploadExcelRef = ref()
const orderApplicationService = useOrderAppliactionService()
const { application, products, demand, currCustomer, hasSaleMan, hasSaleManager } =
  orderApplicationService
const tagsViewStore = useTagsViewStore()

const deliveryTypeOptions = ref<DictDataType[]>(getStrDictOptions('mail_mode')) // 交付方式选项
//表单校验规格
const rules = reactive<FormRules<RuleForm>>({
  customerId: [
    {
      required: true,
      message: t('productsShow.sampleCardEdit.PleaseEnterCustomerName'),
      trigger: ['blur', 'change']
    }
  ],
  deliveryAt: [
    {
      required: false,
      message: t('productsShow.sampleCardEdit.PleaseEnterDeliveryDate'),
      trigger: 'blur'
    }
  ]
  // deliveryType: [{ required: true, message: '请选择交付方式', trigger: ['blur', 'change'] }],
})
const cardDemandCheck = computed<boolean>(() => {
  return application.value.status == orderApplicationStatusEnum.SALE_AUDIT
})
const mangerAuditBtnShow = computed<boolean>(() => {
  return application.value.status == orderApplicationStatusEnum.MANAGE_AUDIT
})
const saveBtnsShow = computed<boolean>(() => {
  return (
    application.value.status == orderApplicationStatusEnum.WAIT_SUBMIT ||
    application.value.status == orderApplicationStatusEnum.CREATE
  )
})
const submitBtnShow = computed<boolean>(() => {
  return (
    application.value.status == orderApplicationStatusEnum.SALE_AUDIT ||
    application.value.status == orderApplicationStatusEnum.WAIT_SUBMIT ||
    application.value.status == orderApplicationStatusEnum.CREATE
  )
})

const cancelBtnShow = computed<boolean>(() => {
  return (
    application.value.status == orderApplicationStatusEnum.SALE_AUDIT ||
    application.value.status == orderApplicationStatusEnum.WAIT_SUBMIT
  )
})
//只读
const readonly = computed<boolean>(() => {
  const status: orderApplicationStatusEnum = application.value.status
  return (
    status == orderApplicationStatusEnum.MANAGE_AUDIT || status == orderApplicationStatusEnum.CANCEL
  )
})
const allFieldsEmpty = computed(() => {
  const demands = ['purpose', 'front', 'back', 'other', 'personal', 'pm', 'safety']
  // if (demand.value.demand[key].items.length === 0) {
  if (
    demand.value.purpose.items.length === 0 &&
    demand.value.front.items.length === 0 &&
    demand.value.back.items.length === 0 &&
    demand.value.other.items.length === 0 &&
    demand.value.personal.items.length === 0 &&
    demand.value.pm.items.length === 0 &&
    demand.value.safety.items.length === 0
  ) {
    return false
  }
  return true
})
async function onSaveProduct(product: IOrderApplicationProduct) {
  orderApplicationService.onSaveProduct(product)
}
async function onAddProduct() {
  sampleProductDailogRef.value?.show()
}
async function onEditProduct(data: IOrderApplicationProduct, index: any) {
  const editData = cloneDeep(toRaw(data))
  editData.index = index
  sampleProductDailogRef.value?.show(editData)
}
async function onDelProduct(index: number) {
  orderApplicationService.delProduct(index)
}

async function onShowSaleDailog() {
  //校验表单规则
  applicationFormRef.value?.validate((valid: boolean) => {
    if (cardDemandCheck.value) {
      console.log(application.value.deliveryAt)
      if (demand.value.purpose.items.length === 0) {
        ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectSampleCardFunction'))
      } else if (demand.value.front.items.length === 0) {
        ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectCardFrontPrintingElements'))
      } else if (demand.value.back.items.length === 0) {
        ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectCardBackPrintingElements'))
      } else if (demand.value.other.items.length === 0) {
        ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectOtherElements'))
      } else if (demand.value.personal.items.length === 0) {
        ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectPersonalisationRequirements'))
      } else if (demand.value.pm.items.length === 0) {
        ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectPMTestingRequirement'))
      } else if (demand.value.safety.items.length === 0) {
        ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectSecurityMeasureRequirements'))
      } else if (application.value.deliveryAt === null) {
        ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectDeliveryDate'))
      } else {
        if (valid) {
          appointSaleDialogRef.value?.show()
        } else {
          console.error('form valid error')
          return
        }
      }
    } else if (valid) {
      appointSaleDialogRef.value?.show()
    } else {
      console.error('form valid error')
      return
    }
  })
}

async function onSaleSelected(saleMan: ISaleMan) {
  submit(saleMan) //提交任务
}

async function submit(saleMan: ISaleMan) {
  try {
    loading.value = true
    const result = await orderApplicationService.submitApplication(saleMan)
    if (result) {
      ElMessage.success(t('productsShow.sampleCardEdit.SubmitSucess'))
      onGobackList()
    }
  } finally {
    loading.value = false
  }
}

async function onSave() {
  try {
    loading.value = true
    const result = await orderApplicationService.saveApplication()
    if (result) {
      ElMessage.success(t('productsShow.sampleCardEdit.SaveSucess'))
      onGobackList()
    }
  } finally {
    loading.value = false
  }
}

async function onManagerDialog() {
  managerAuditDialogRef.value?.show()
}

async function onManagerSubmit(reviewResult: orderApplicationReviewResultEnum, remark: string) {
  if (!reviewResult) {
    ElMessage.error(t('productsShow.sampleCardEdit.PleaseSelectApprovaResut'))
    throw t('productsShow.sampleCardEdit.PleaseSelectApprovaResut')
  }
  try {
    loading.value = true
    application.value.managerResult = reviewResult
    application.value.remark = remark
    const result = await orderApplicationService.managerAuditSubmit()
    if (result) {
      ElMessage.success(t('productsShow.sampleCardEdit.SubmitSucess'))
      onGobackList()
    }
  } finally {
    loading.value = false
  }
}

async function onCancel() {
  ElMessageBox.confirm(
    t('productsShow.sampleCardEdit.cancelApplication'),
    t('productsShow.sampleCardEdit.Tips'),
    {
      confirmButtonText: t('productsShow.sampleCardEdit.Submit2'),
      cancelButtonText: t('productsShow.sampleCardEdit.Cancel2'),
      type: 'warning'
    }
  )
    .then(async () => {
      await orderApplicationService.cancelApplication()
      ElMessage.success(t('productsShow.sampleCardEdit.CancelSuccess'))
      onGobackList()
    })
    .catch(() => {})
}

async function onGobackList() {
  await onClose()
  router.push({
    name: `SampleCardList`
  })
}

async function onClose() {
  tagsViewStore.delCurView()
}

provide('customer', currCustomer) //传递给子组件的客户对象

onMounted(() => {
  //判断是否存在卡产品需求信息
  const requirementInfoId: string | undefined = route.query.makeCardRequirementInfoId as string //卡产品需求ID
  if (requirementInfoId) {
    orderApplicationService.loadProductRequirement(requirementInfoId)
  } else {
    //非卡产品需求
    const applyId: string | undefined = route.query.id as string
    orderApplicationService.getApplication(applyId)
  }
  nextTick(() => {
    commonService.getCustomers()
  })
})

const downloadTemplateSampleCardFile = () => {
  exportExcel(sampleCardHeaderInfo, [], t('productsShow.sampleCardEdit.importTemplate'))
}

const fileChange = async () => {
  const res = await uploadExcelRef.value.submitFileBase64()
  resolveExcel(res)
}

// 解析excel数据
const resolveExcel = async (res) => {
  try {
    let headers = await getExcelHeader(res[0].raw)
    sampleCardHeaderInfo.forEach((item, index) => {
      if (item !== headers[index]) {
        throw Error(t('productsShow.sampleCardEdit.ImportFail'))
      }
    })
    let result = await analyzeExcel(res[0].raw)
    batchImportProduct(result)
  } catch (error) {
    ElMessage.error(t('productsShow.sampleCardEdit.ImportFail'))
  } finally {
    nextTick(() => {
      uploadExcelRef.value.clearFile()
    })
  }
}

// 产品最大数量
const ELEMENT_NUM = 9999900

// 批量导出创建产品
const batchImportProduct = (result) => {
  try {
    result.forEach((item) => {
      if (!item[sampleCardHeaderInfo[0]]) {
        throw new Error(`产品名称为必填项`)
      }
      const regexNum = /^\d+$/
      /*if (!regexNum.test(item[sampleCardHeaderInfo[3]])) {
        throw new Error(`申请数量只能为正整数`)
      }*/
      if (item[sampleCardHeaderInfo[3]] < 1 || item[sampleCardHeaderInfo[3]] > ELEMENT_NUM) {
        throw new Error(`请检查备库数量，范围1-${ELEMENT_NUM}`)
      }
    })
    let newProductList: any = []
    result.forEach((item) => {
      const newElement = {
        productName: item[sampleCardHeaderInfo[0]] || '',
        customerProductCode: item[sampleCardHeaderInfo[1]] || '',
        cardCode: item[sampleCardHeaderInfo[2]] || '',
        amount: item[sampleCardHeaderInfo[3]] || '',
        plan: item[sampleCardHeaderInfo[4]] || 0,
        remark: item[sampleCardHeaderInfo[5]] || 0
      }
      newProductList.push(newElement)
    })
    products.value.push(...newProductList)
  } catch (error: any) {
    ElMessage.error(error.message)
  } finally {
  }
}
</script>

<style lang="less" scoped>
.header__row {
  font-size: 20px;
  border-bottom: 1px dotted rgba(0, 0, 0, 0.2);
  padding: 15px 0px;
  text-transform: uppercase;
  color: #535351;
  font-weight: bold;
}

.header__row_2 {
  font-size: 17px !important;
  border-bottom: none !important;
  padding: 10px 0px !important;
}

.form__content {
  padding: 15px 0;

  .application_form {
    .el-form-item {
      max-width: 500px;
    }
  }
}

.affix-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  text-align: right;
  padding-right: 30px;

  .el-affix {
    min-width: 450px !important;
  }
}

.tool-bar {
  float: right;
  margin-bottom: 10px;
}

.demandForm {
  padding: 20px 20px 0px 20px;
  border: 1px dotted rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}
</style>
