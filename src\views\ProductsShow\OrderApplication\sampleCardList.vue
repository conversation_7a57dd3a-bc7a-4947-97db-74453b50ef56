<template>
  <ContentWrap ifTable>
    <!-- 查询 -->
    <template #search>
      <el-form ref="makeCardList" :model="params" :inline="false">
        <el-row :gutter="10">
          <el-col :md="8" :lg="8" :xl="6">
            <el-form-item :label="t('productsShow.sampleCardList.applyCode')">
              <el-input
                v-model="params.applyCode"
                :placeholder="t('productsShow.sampleCardList.applyCode')"
              />
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="6">
            <el-form-item :label="t('productsShow.sampleCardList.customerName')">
              <el-input
                v-model="params.customerName"
                :placeholder="t('productsShow.sampleCardList.customerNameEnter')"
              />
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="6">
            <el-form-item :label="t('productsShow.sampleCardList.sampleCardApplicationType')">
              <el-select
                v-model="params.type"
                :placeholder="t('productsShow.sampleCardList.sampleCardApplicationTypeSelect')"
                clearable
              >
                <el-option
                  v-for="item in sampleCardApplicationTypeArray"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8" :xl="6">
            <el-form-item :label="t('productsShow.sampleCardList.orderApplicationStatus')">
              <el-select
                v-model="params.status"
                :placeholder="t('productsShow.sampleCardList.orderApplicationStatusSelect')"
                clearable
              >
                <el-option
                  v-for="item in orderApplicationStatusArray"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :md="8" :lg="8" :xl="6">
            <!--查询 重置-->
            <el-form-item label-width="0px">
              <el-button type="primary" v-track:click.btn @click="onSearch">{{
                t('productsShow.sampleCardList.search')
              }}</el-button>
              <el-button type="warning" @click="onReset">{{
                t('productsShow.sampleCardList.reset')
              }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>

    <!-- Tool -->
    <div class="tool-bar">
      <el-button type="primary" v-track:click.btn @click="onAdd">{{
        t('productsShow.sampleCardList.new')
      }}</el-button>
    </div>
    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="datas"
      height="100%"
      :header-cell-style="{ background: '#F7F7F9', color: '#606266' }"
      style="width: 100%"
    >
      <el-table-column
        prop="applyCode"
        :label="t('productsShow.sampleCardList.applyCode2')"
        width="180"
      >
        <template #default="{ row }">
          <el-link type="primary" :underline="false" @click="onView(row)">{{
            row.applyCode
          }}</el-link>
          <el-link
            type="danger"
            :underline="false"
            title="t('productsShow.sampleCardList.orderDetial')"
            v-if="row.umvOrderId"
            @click="onGoOrder(row)"
          >
            <el-icon class="el-icon--right"><icon-view /></el-icon>
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="customerName" :label="t('productsShow.sampleCardList.customerName')" />
      <el-table-column
        prop="type"
        :label="t('productsShow.sampleCardList.sampleCardApplicationType')"
        width="180"
      >
        <template #default="{ row }">
          <span
            class="badge"
            :style="{
              background:
                row.type === orderApplicationTypeEnum.FreeSampleCard
                  ? 'rgb(252, 179, 34)'
                  : 'rgb(128, 117, 196)'
            }"
            >{{ applicationTypeMapper(row.type) }}</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="createName" :label="t('productsShow.sampleCardList.createName')" />
      <el-table-column
        prop="saleUserName"
        :label="t('productsShow.sampleCardList.saleUserName')"
        width="180"
      />
      <el-table-column
        prop="managerName"
        :label="t('productsShow.sampleCardList.managerName')"
        width="180"
      >
        <template #default="{ row }">
          {{ row.managerName }}
          <span class="badge" style="background: rgb(245, 108, 108)" v-if="isNG(row.managerResult)"
            >NG</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        :label="t('productsShow.sampleCardList.orderApplicationStatus')"
        width="180"
      >
        <template #default="{ row }">
          <span class="badge" :style="{ background: statusColorMapper(row.status) }">
            {{ dispalyStatus(row.status) }}</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="createDate" :label="t('productsShow.sampleCardList.createDate')" />
      <el-table-column fixed="right" :label="t('productsShow.sampleCardList.actions')" width="200">
        <template #default="{ row }">
          <el-button type="primary" link v-track:click.btn @click="onView(row)">{{
            t('productsShow.sampleCardList.views')
          }}</el-button>
          <el-button
            type="primary"
            link
            v-track:click.btn
            @click="onEdit(row)"
            v-if="isEditable(row.status)"
            >{{ t('productsShow.sampleCardList.edit') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->

    <template #pagination>
      <Pagination
        :total="total"
        v-model:page="pageIndex"
        v-model:limit="pageSize"
        @pagination="searchSamples"
      />
    </template>
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({
  name: 'sampleCardList'
})

import { View as IconView } from '@element-plus/icons-vue'
import IOrderApplication from '@/api/orderApplication/types/orderApplication'
import { sampleCardApplicationTypeArray, orderApplicationStatusArray } from './types/data.d'
import { useOrderAppliactionListService } from './hooks/useOrderApplicationListService'
import { useOrderApplicationCommonService } from './hooks/useOrderApplicationCommonService'
import {
  orderApplicationReviewResultEnum,
  orderApplicationStatusEnum,
  orderApplicationTypeEnum
} from '@/api/orderApplication/types/enum.d'
const { t } = useI18n()

const router = useRouter()
const orderApplicationService = useOrderAppliactionListService()
const { params, searchSamples, pageIndex, pageSize, resetParmas, datas, total } =
  orderApplicationService //查询条件

const commonService = useOrderApplicationCommonService()
const { applicationTypeMapper } = commonService

const loading = ref()

//判断是否可以编辑
function isEditable(status: orderApplicationStatusEnum): boolean {
  return status != orderApplicationStatusEnum.FINISH && status != orderApplicationStatusEnum.CANCEL
}

//显示申请类型名称
function dispalyStatus(value: orderApplicationStatusEnum): string {
  const result = orderApplicationStatusArray.filter((item) => item.value === value)[0]
  if (result) {
    return result.label
  }
  return ''
}
//显示申请状态对应的颜色
function statusColorMapper(value: orderApplicationStatusEnum): string {
  const result = orderApplicationStatusArray.filter((item) => item.value == value)[0]
  if (result) {
    return result.color
  }
  return ''
}
//评审是否NT
function isNG(value: orderApplicationReviewResultEnum) {
  return value == orderApplicationReviewResultEnum.NT
}

function onSearch() {
  pageIndex.value = 1
  searchSamples()
}
function onReset() {
  resetParmas()
}

function onAdd() {
  router.push({
    name: `SampleCardEdit`
  })
}

function onView(data: IOrderApplication) {
  const id: string = data.id
  router.push({
    name: `SampleCardView`,
    query: {
      id
    }
  })
}

function onEdit(data: IOrderApplication) {
  const id: string = data.id
  router.push({
    name: `SampleCardEdit`,
    query: {
      id
    }
  })
}

function onGoOrder(data: IOrderApplication) {
  if (!data.umvOrderId) {
    return
  }
  router.push({
    name: 'CardOrderDetails',
    query: { orderId: data.umvOrderId }
  })
}

onMounted(() => {
  nextTick(() => {
    onSearch()
  })
})

onActivated(() => searchSamples())
</script>

<style scoped lang="less">
.tool-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.bottom-view {
  margin-top: 10px;
  height: 50px;
  text-align: center;
  float: right;
}

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999;
  border-radius: 10px;
}
</style>
./types/data
