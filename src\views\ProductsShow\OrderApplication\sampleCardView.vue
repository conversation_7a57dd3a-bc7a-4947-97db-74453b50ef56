<template>
  <ContentWrap v-loading="loading">
    <div class="header__row">{{ t('productsShow.sampleCardEdit.ApplicationForm') }}</div>
    <div class="form__content">
      <!-- 申请单 -->
      <el-form
        ref="formRef"
        :model="application"
        label-width="auto"
        inline
        class="application_form"
      >
        <el-form-item :label="t('productsShow.sampleCardEdit.ApplicationFormNo')">
          {{ application.applyCode }}
        </el-form-item>
        <el-form-item :label="t('productsShow.sampleCardEdit.CustomerName')">
          {{ application.customerName }}
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.ApplicationType')" prop="type">
          {{ applicationTypeMapper(application.type) }}
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.deliveryDate')" prop="deliveryAt">
          {{ dateFormat(application.deliveryAt) }}
        </el-form-item>

        <el-form-item
          :label="t('cardProductBusiness.orderApproval.deliveryMode')"
          prop="deliveryType"
        >
          {{ deliveryTypeMapper }}
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.urgentSign')" prop="urgentSign">
          <el-tag type="danger" v-if="application.urgentSign">{{
            t('makeCard.common.yes')
          }}</el-tag>
          <el-tag type="success" v-else>{{ t('makeCard.common.no') }}</el-tag>
        </el-form-item>
        <el-form-item
          v-if="application.urgentSign"
          :label="t('productsShow.sampleCardEdit.urgentReason')"
          prop="urgentReason"
        >
          <el-input
            v-model="application.urgentReason"
            :readonly="true"
            :rows="4"
            type="textarea"
            :maxlength="1000"
            :show-word-limit="true"
            :autosize="{ minRows: 1, maxRows: 6 }"
            :placeholder="t('productsShow.sampleCardEdit.PleaseEnterUrgentReason')"
            clearable
          />
        </el-form-item>

        <el-row v-if="hasSaleMan">
          <el-form-item :label="t('productsShow.sampleCardList.saleUserName')">
            <el-text>{{ application.saleUserName }}</el-text>
          </el-form-item>
          <el-form-item :label="t('productsShow.sampleCardEdit.managerTime')">
            <el-text class="mx-1" type="success">
              {{ dateFormat(application.saleUserTime) }}</el-text
            >
          </el-form-item>
        </el-row>
        <el-row v-if="hasSaleManager">
          <el-form-item :label="t('productsShow.sampleCardList.managerName')"
            ><el-text>{{ application.managerName }} </el-text></el-form-item
          >
          <el-form-item :label="t('productsShow.sampleCardEdit.managerTime')">
            <el-text class="mx-1" type="success">
              {{ dateFormat(application.managerTime) }}</el-text
            >
          </el-form-item>
          <el-form-item :label="t('cardProductBusiness.orderApproval.auditRemark')">
            <el-text class="mx-1" style="color: red"> {{ application.remark }}</el-text>
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 产品 -->
      <el-row>
        <el-col :span="12">
          <div class="header__row header__row_2">{{
            t('productsShow.sampleCardEdit.ProductList')
          }}</div>
        </el-col>
      </el-row>
      <el-table :data="products" border width="100%">
        <el-table-column
          prop="productName"
          :label="t('productsShow.sampleCardEdit.ProductName')"
          width="250"
        />
        <el-table-column
          prop="customerProductCode"
          :label="t('productsShow.sampleCardEdit.CustomerProductCode')"
          :min-width="ifEn ? 200 : 150"
        />
        <el-table-column
          prop="cardCode"
          :label="t('productsShow.sampleCardEdit.CardBaseNumber')"
          :min-width="ifEn ? 180 : 120"
        />
        <el-table-column
          prop="amount"
          :label="t('productsShow.sampleCardEdit.ApplicationQuantity')"
          :min-width="ifEn ? 180 : 80"
        />
        <el-table-column
          prop="plan"
          :label="t('productsShow.sampleCardEdit.ProjectNumber')"
          :min-width="ifEn ? 150 : 80"
        />
        <el-table-column
          prop="remark"
          :label="t('productsShow.sampleCardEdit.Remarks')"
          min-width="100"
        />
      </el-table>

      <el-row>
        <el-col :span="12">
          <div class="header__row header__row_2">{{
            t('productsShow.sampleCardEdit.SampleCardElements')
          }}</div>
        </el-col>
      </el-row>
      <!-- 需求要素 -->
      <el-form :model="application" :label-width="auto" :inline="false" class="demandForm">
        <el-form-item :label="t('productsShow.sampleCardEdit.SampleCardFunction')" v-if="demand">
          <demandItemCheckList
            :checkArray="purposeCheckArray"
            v-model:checked="demand.purpose.items"
            v-model:remark="demand.purpose.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item
          :label="t('productsShow.sampleCardEdit.CardFrontPrintingElements')"
          v-if="demand"
        >
          <demandItemCheckList
            :checkArray="frontCheckArray"
            v-model:checked="demand.front.items"
            v-model:remark="demand.front.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item
          :label="t('productsShow.sampleCardEdit.CardBackPrintingElements')"
          v-if="demand"
        >
          <demandItemCheckList
            :checkArray="backCheckArray"
            v-model:checked="demand.back.items"
            v-model:remark="demand.back.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.OtherElements')" v-if="demand">
          <demandItemCheckList
            :checkArray="otherCheckArray"
            v-model:checked="demand.other.items"
            v-model:remark="demand.other.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item
          :label="t('productsShow.sampleCardEdit.PersonalisationRequirements')"
          v-if="demand"
        >
          <demandItemCheckList
            :checkArray="personalCheckArray"
            v-model:checked="demand.personal.items"
            v-model:remark="demand.personal.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item :label="t('productsShow.sampleCardEdit.PMTestingRequirement')" v-if="demand">
          <demandItemCheckList
            :checkArray="pmCheckArray"
            v-model:checked="demand.pm.items"
            v-model:remark="demand.pm.remark"
            :readonly="readonly"
          />
        </el-form-item>

        <el-form-item
          :label="t('productsShow.sampleCardEdit.SecurityMeasureRequirements')"
          v-if="demand"
        >
          <demandItemCheckList
            :checkArray="safetyCheckArray"
            v-model:checked="demand.safety.items"
            v-model:remark="demand.safety.remark"
            :readonly="readonly"
          />
        </el-form-item>
      </el-form>
    </div>
  </ContentWrap>

  <div class="affix-container">
    <el-affix position="bottom" :offset="30">
      <el-button type="primary" @click="onGobackList">{{
        t('productsShow.sampleCardEdit.Back')
      }}</el-button>
    </el-affix>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'sampleCardView'
})

import { getStrDictOptions, DictDataType } from '@/utils/dict'

import {
  purposeCheckArray,
  frontCheckArray,
  backCheckArray,
  otherCheckArray,
  personalCheckArray,
  pmCheckArray,
  safetyCheckArray
} from './types/data.d'
import { useOrderAppliactionService } from './hooks/useOrderApplicationService'
import { useOrderApplicationCommonService } from './hooks/useOrderApplicationCommonService'
import { useTagsViewStore } from '@/store/modules/tagsView'
// <!-- 组件 -->
import demandItemCheckList from './components/demandItemCheckList.vue'

const { t, ifEn } = useI18n()

const loading = ref<boolean>(false)

const route = useRoute()
const router = useRouter()

const commonService = useOrderApplicationCommonService()
const { applicationTypeMapper, dateFormat } = commonService
const orderApplicationService = useOrderAppliactionService()
const { application, products, demand, hasSaleMan, hasSaleManager } = orderApplicationService
const tagsViewStore = useTagsViewStore()

const deliveryTypeOptions = ref<DictDataType[]>(getStrDictOptions('mail_mode')) // 交付方式选项

const deliveryTypeMapper = computed<string>(() => {
  const result = deliveryTypeOptions.value.filter(
    (item) => item.value === application.value.deliveryType
  )[0]
  if (!result) return '-'
  return (result as DictDataType).label
})

//只读
const readonly = computed<boolean>(() => {
  return true
})

async function onGobackList() {
  await onClose()
  router.push({
    name: `SampleCardList`
  })
}

async function onClose() {
  tagsViewStore.delCurView()
}

onMounted(() => {
  const applyId: string | undefined = route.query.id as string
  orderApplicationService.getApplication(applyId)

  nextTick(() => {})
})
</script>

<style scoped>
.header__row {
  font-size: 20px;
  border-bottom: 1px dotted rgba(0, 0, 0, 0.2);
  padding: 15px 0px;
  text-transform: uppercase;
  color: #535351;
  font-weight: bold;
}

.header__row_2 {
  font-size: 17px !important;
  border-bottom: none !important;
  padding: 10px 0px !important;
}

.form__content {
  padding: 15px 0;
  .application_form {
    .el-form-item {
      min-width: 300px;
    }
  }
}

.affix-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  text-align: right;
  padding-right: 30px;
}

.tool-bar {
  float: right;
  margin-bottom: 10px;
}

.demandForm {
  padding: 20px 20px 0px 20px;
  border: 1px dotted rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}
</style>
