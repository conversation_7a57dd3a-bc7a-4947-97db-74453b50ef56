import {
  orderApplicationTypeEnum,
  orderApplicationStatusEnum,
  standbyTypeEnum,
  productTypeArray,
  productTypeEnum
} from '@/api/orderApplication/types/enum.d'

import { IDemandItem } from '@/api/orderApplication/types/orderApplicationDemand.d'
import { color } from 'echarts'
const { t } = useI18n()
/**
 * @description 样卡申请单枚举类型数组
 */
export const sampleCardApplicationTypeArray = [
  {
    value: orderApplicationTypeEnum.FreeSampleCard,
    label: t('productsShow.typeData.FreeSampleCard')
  },
  {
    value: orderApplicationTypeEnum.InboundSampleCard,
    label: t('productsShow.typeData.InboundSampleCard')
  },
  {
    value: orderApplicationTypeEnum.ChargeSampleCard,
    label: t('productsShow.typeData.ChargeSampleCard')
  }
]

/**
 * @description 产品备库申请枚举类型数组
 */
export const standbyTypeArray = [
  {
    value: standbyTypeEnum.finish,
    label: t('productsShow.typeData.finishedProductBackup')
  },
  {
    value: standbyTypeEnum.semiFinish,
    label: t('productsShow.typeData.semiProductBackup')
  }
]

/**
 * @description 产品枚举类型数组
 */
export const productTypeArray = [
  {
    value: productTypeEnum.card,
    label: t('productsShow.typeData.CardProduct')
  },
  {
    value: productTypeEnum.nonCard,
    label: t('productsShow.typeData.nonCardProduct')
  }
]

export const orderApplicationStatusArray = [
  {
    value: orderApplicationStatusEnum.CANCEL,
    label: t('productsShow.typeData.Cancelled'),
    color: '#F56C6C'
  },
  {
    value: orderApplicationStatusEnum.FINISH,
    label: t('productsShow.typeData.Completed'),
    color: '#67C23A'
  },
  {
    value: orderApplicationStatusEnum.LEADER_AUDIT,
    label: t('productsShow.typeData.LeaderApproval'),
    color: '#a77730'
  },
  {
    value: orderApplicationStatusEnum.MANAGE_AUDIT,
    label: t('productsShow.typeData.ManagementApproval'),
    color: '#E6A23C'
  },
  {
    value: orderApplicationStatusEnum.SALE_AUDIT,
    label: t('productsShow.typeData.SalesConfirmation'),
    color: '#a77730'
  },
  {
    value: orderApplicationStatusEnum.WAIT_SUBMIT,
    label: t('productsShow.typeData.Tobesubmitted'),
    color: '#909399'
  }
]

//样卡用途 元素数组
export const purposeCheckArray: IDemandItem[] = [
  { key: '1', value: t('productsShow.sampleCardEdit.None') },
  { key: '2', value: t('productsShow.sampleCardEdit.NewCardConfirmation') },
  { key: '3', value: t('productsShow.sampleCardEdit.TenderSubmission') },
  { key: '4', value: t('productsShow.sampleCardEdit.InspectionCertification') },
  { key: '5', value: t('productsShow.sampleCardEdit.SubmissionForApproval') },
  { key: '6', value: t('productsShow.sampleCardEdit.Other') }
]
//正面印和刷要素， 元素数组
export const frontCheckArray: IDemandItem[] = [
  { key: '1', value: t('productsShow.sampleCardEdit.None') },
  { key: '2', value: t('productsShow.sampleCardEdit.CardBaseImage') },
  { key: '3', value: t('productsShow.sampleCardEdit.ClientLogo') },
  { key: '4', value: t('productsShow.sampleCardEdit.CardSchemeLogo') },
  { key: '5', value: t('productsShow.sampleCardEdit.SpecialEffects') },
  { key: '6', value: t('productsShow.sampleCardEdit.Other') }
]
//背面印和刷要素， 元素数组
export const backCheckArray: IDemandItem[] = [
  { key: '1', value: t('productsShow.sampleCardEdit.None') },
  { key: '2', value: t('productsShow.sampleCardEdit.BackImage') },
  { key: '3', value: t('productsShow.sampleCardEdit.Text') },
  { key: '4', value: t('productsShow.sampleCardEdit.BackClientLogo') },
  { key: '5', value: t('productsShow.sampleCardEdit.BackCardSchemeLogo') },
  { key: '6', value: t('productsShow.sampleCardEdit.BackSpecialEffects') },
  { key: '7', value: t('productsShow.sampleCardEdit.BlankWhiteBack') },
  { key: '8', value: t('productsShow.sampleCardEdit.Other') }
]

//其他要素 元素数组
export const otherCheckArray: IDemandItem[] = [
  { key: '1', value: t('productsShow.sampleCardEdit.None') },
  { key: '2', value: t('productsShow.sampleCardEdit.SignaturePanel') },
  { key: '3', value: t('productsShow.sampleCardEdit.Hologram') },
  { key: '4', value: t('productsShow.sampleCardEdit.Magstripe') },
  { key: '5', value: t('productsShow.sampleCardEdit.MetalSticker') },
  { key: '6', value: t('productsShow.sampleCardEdit.HotStamping') },
  { key: '7', value: t('productsShow.sampleCardEdit.FunctionalEMVChip') },
  { key: '8', value: t('productsShow.sampleCardEdit.DummyEMVChip') },
  { key: '9', value: t('productsShow.sampleCardEdit.RealInlay') },
  { key: '10', value: t('productsShow.sampleCardEdit.Other') }
]
//个人化需求
export const personalCheckArray: IDemandItem[] = [
  { key: '1', value: t('productsShow.sampleCardEdit.None') },
  { key: '2', value: t('productsShow.sampleCardEdit.CardSurfacePersonalisation') },
  { key: '3', value: t('productsShow.sampleCardEdit.MagstripePersonalisation') },
  { key: '4', value: t('productsShow.sampleCardEdit.ChipPrePersonalisation') },
  { key: '5', value: t('productsShow.sampleCardEdit.ChipPersonalisation') },
  { key: '6', value: t('productsShow.sampleCardEdit.Other') }
]

//PM检测要求
export const pmCheckArray: IDemandItem[] = [
  { key: '1', value: t('productsShow.sampleCardEdit.None') },
  { key: '2', value: t('productsShow.sampleCardEdit.MagstripePhysicalProperties') },
  { key: '3', value: t('productsShow.sampleCardEdit.MagstripePersonalisationData') },
  { key: '4', value: t('productsShow.sampleCardEdit.ChipPhysicalProperties') },
  { key: '5', value: t('productsShow.sampleCardEdit.COSVersionChip') },
  { key: '6', value: t('productsShow.sampleCardEdit.ChipPersonalisationData') },
  { key: '7', value: t('productsShow.sampleCardEdit.Other') }
]
//安全处理要求
export const safetyCheckArray: IDemandItem[] = [
  { key: '1', value: t('productsShow.sampleCardEdit.None') },
  { key: '2', value: t('productsShow.sampleCardEdit.Holepunch') },
  { key: '3', value: t('productsShow.sampleCardEdit.VoidStamp') },
  { key: '4', value: t('productsShow.sampleCardEdit.ScratchedMagstripe') },
  { key: '5', value: t('productsShow.sampleCardEdit.NoTreatment') },
  { key: '6', value: t('productsShow.sampleCardEdit.Other') }
]
