/**
 * @description 产品信息
 * @export
 * @interface IProduct
 */
export default interface IProduct {
  /**
   * @description 产品主键id
   * @type {string}
   * @memberof IProduct
   */
  id: string

  /**
   * @description Product名
   * @type {string}
   * @memberof IProduct
   */
  name: string

  /**
   * @description 客户产品编码
   * @type {string}
   * @memberof IProduct
   */
  clientProductUniqueCode: string

  /**
   * @description 产品编号
   * @type {string}
   * @memberof IProduct
   */
  productCode: string
  /**
   * @description 卡款编号
   * @type {string}
   * @memberof IProduct
   */
  cardCode: string

  /**
   * @description 产品类型
   * @type {string}
   * @memberof IProduct
   */
  productType: string
}
