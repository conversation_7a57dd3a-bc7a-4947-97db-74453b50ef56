<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-02 14:27:40
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-12-26 15:07:40
 * @Description: 
-->
<template>
  <div :class="prefixCls" class="w-full flex justify-center flex-col">
    <div class="flex items-center flex-col">
      <CropperAvatar
        ref="cropperRef"
        :btnProps="{ preIcon: 'ant-design:cloud-upload-outlined' }"
        :showBtn="false"
        :value="avatar"
        width="120px"
        @change="handelUpload"
      />
    </div>

    <div class="flex justify-center flex-col">
      <el-row :gutter="20" justify="center">
        <el-col :span="6">{{ t('sys.AccountControl.username') }}</el-col>
        <el-col :span="6">{{
          accountInfo?.username ? accountInfo?.username : t('sys.AccountControl.nodata')
        }}</el-col>
      </el-row>
      <el-row :gutter="20" justify="center">
        <el-col :span="6">{{ t('sys.AccountControl.mobile') }}</el-col>
        <el-col :span="6">{{
          accountInfo?.mobile ? accountInfo?.mobile : t('sys.AccountControl.nodata')
        }}</el-col>
      </el-row>
      <el-row :gutter="20" justify="center">
        <el-col :span="6">{{ t('sys.AccountControl.email') }}</el-col>
        <el-col :span="6">{{
          accountInfo?.email ? accountInfo?.email : t('sys.AccountControl.nodata')
        }}</el-col>
      </el-row>
      <el-row :gutter="20" justify="center">
        <el-col :span="6">{{ t('sys.AccountControl.nickName') }}</el-col>
        <el-col :span="6">{{
          accountInfo?.nickname ? accountInfo?.nickname : t('sys.AccountControl.nodata')
        }}</el-col>
      </el-row>
      <el-row :gutter="20" justify="center">
        <el-col :span="6">{{ t('sys.AccountControl.deptName') }}</el-col>
        <el-col :span="6">{{
          accountInfo?.deptName ? accountInfo?.deptName : t('sys.AccountControl.nodata')
        }}</el-col>
      </el-row>
      <el-row :gutter="20" justify="center">
        <el-col :span="6">{{ t('sys.AccountControl.createTime') }}</el-col>
        <el-col :span="6">{{
          accountInfo?.createTime
            ? formatDate(accountInfo?.createTime)
            : t('sys.AccountControl.nodata')
        }}</el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'AccountInfo'
})

import { formatDate } from '@/utils/formatTime'

import * as UserApi from '@/api/user'
import { useDesign } from '@/hooks/web/useDesign'
const { t } = useI18n()

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('account-info')

//获取账号信息
import { useUserStoreWithOut } from '@/store/modules/user'
const userStore = useUserStoreWithOut()

let accountInfo = ref<UserApi.AccountType>(userStore.getAccountInfo)

//头像上传
import CropperAvatar from './Cropper/src/CropperAvatar.vue'
import { updateAvatar } from '@/api/user'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()
const avatar = computed(() => accountInfo.value.avatar)

const cropperRef = ref()
const handelUpload = async ({ data }) => {
  try {
    await updateAvatar({ avatarFile: data })
    cropperRef.value.close()
  } catch (error) {
  } finally {
    wsCache.delete(CACHE_KEY.ACCOUNT_INFO)
    wsCache.delete(CACHE_KEY.USER)
    userStore.setAccountInfoAction()
    userStore.setUserInfoAction()
  }
}
</script>
<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-account-info';

.@{prefix-cls} {
  .el-row {
    padding: 15px;
    .el-col:first-child {
      text-align: right;
      color: #666666;
    }
  }
}
</style>
