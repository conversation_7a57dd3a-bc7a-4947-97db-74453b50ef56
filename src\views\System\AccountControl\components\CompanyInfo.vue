<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-02 15:00:33
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-12-26 16:17:52
 * @Description: 
-->
<template>
  <div
    :class="prefixCls"
    class="w-full flex justify-center items-center flex-col"
    v-loading="loading"
  >
    <div class="mb-2">{{
      accountInfo?.customerName ?? t('sys.AccountControl.customerNameNodata')
    }}</div>
    <div class="mb-4 text-dark-50 text-14px">
      {{ t('sys.AccountControl.uscc') }}：{{ accountInfo?.uscc ?? t('sys.AccountControl.nodata') }}
    </div>
    <!-- 关联企业/机构 -->
    <div class="dot mb-2">{{ t('sys.AccountControl.cusRelated') }}</div>
    <div class="flex flex-row mb-4">
      <div
        v-for="item in accountInfo?.cusRelated"
        :key="item"
        class="border-dark-200 bg-light-600 p-2 px-4 mr-4 text-dark-50 text-14px"
      >
        {{ item }}
      </div>
      <div
        v-if="accountInfo?.cusRelated.length == 0"
        class="border-dark-200 bg-light-600 p-2 px-4 mr-4 text-dark-50 text-14px"
      >
        {{ t('sys.AccountControl.nodata') }}
      </div>
    </div>
    <!-- 部门关系 -->
    <div class="dot">{{ t('sys.AccountControl.deptRelated') }}</div>
    <vue3TreeOrg
      v-if="!isEmpty(orgData)"
      class="w-3/4 !min-h-[50px]"
      :data="orgData"
      :props="{
        id: 'id',
        pid: 'parentId',
        label: 'name',
        children: 'children'
      }"
      center
      :horizontal="false"
      :collapsable="false"
      :draggable="false"
      :scalable="false"
      :disabled="true"
      :toolBar="{ scale: false, restore: false, expand: false, zoom: false, fullscreen: false }"
      :label-style="{
        background: '#f6f6f6',
        color: 'black'
      }"
      :only-one-node="false"
      :clone-node-drag="false"
    />
    <span class="my-4 text-dark-50 text-14px" v-else>
      {{ t('sys.AccountControl.deptNodata') }}</span
    >

    <!-- 开通服务 -->
    <div class="dot mb-2">{{ t('sys.AccountControl.openServices') }}</div>
    <div class="flex flex-row w-2/3 justify-center mb-4">
      <div
        class="flex flex-col items-center mr-5"
        v-for="item in accountInfo?.openServices"
        :key="item"
      >
        <Icon
          icon="svg-icon:Account-service"
          :size="70"
          color="white"
          class="rounded-full p-10px"
        />
        <span class="text-dark-50">{{ item }}</span>
      </div>
      <!-- <div class="flex flex-col items-center">
        <Icon
          icon="svg-icon:Account-makeCard"
          :size="45"
          color="white"
          class="w-80px h-80px rounded-full p-10px bg-green-300"
        />
        <span class="mt-2 text-dark-50">制卡服务</span>
      </div>
      <div class="flex flex-col items-center">
        <Icon
          icon="svg-icon:Account-diy"
          :size="45"
          color="white"
          class="w-80px h-80px rounded-full p-10px bg-green-300"
        />
        <span class="mt-2 text-dark-50">DIY服务</span>
      </div>
      <div class="flex flex-col items-center">
        <Icon
          icon="svg-icon:Account-check"
          :size="45"
          color="white"
          class="w-80px h-80px rounded-full bg-green-300"
        />
        <span class="mt-2 text-dark-50">图审服务</span>
      </div>
      <div class="flex flex-col items-center">
        <Icon
          icon="svg-icon:Account-handle"
          :size="45"
          color="white"
          class="w-80px h-80px rounded-full p-1 bg-green-300"
        />
        <span class="mt-2 text-dark-50">图像处理服务</span>
      </div>
      <div class="flex flex-col items-center">
        <Icon
          icon="svg-icon:Account-call"
          :size="45"
          color="white"
          class="w-80px h-80px rounded-full p-10px bg-green-300"
        />
        <span class="mt-2 text-dark-50">外呼服务</span>
      </div> -->
    </div>
    <!-- 结算方式 -->
    <div class="dot">{{ t('sys.AccountControl.clearingForm') }}</div>
    <div class="mt-4 text-dark-50 text-14px"> {{ t('sys.AccountControl.nodata') }} </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'CompanyInfo'
})

import { isEmpty } from 'lodash-es'
import { useDesign } from '@/hooks/web/useDesign'
const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('company-info')

const { t } = useI18n()

//获取账号信息
import * as UserApi from '@/api/user'

import { useUserStoreWithOut } from '@/store/modules/user'
import { listToTree } from '@/utils/tree'
const userStore = useUserStoreWithOut()

let accountInfo = ref<UserApi.CompanyInfoType>()

let loading = ref(true)
// 组织架构
let orgData = ref({})
const getCompanyInfo = async () => {
  try {
    let res = await UserApi.getUserCompanyInfo(userStore.getUserId)
    accountInfo.value = res

    if (!res.deptInfo.length) return
    const configProps = {
      id: 'id',
      children: 'children',
      pid: 'parentId'
    }
    res.deptInfo.map((item) => {
      if (item.id === res.deptId) {
        item['style'] = { color: '#fff', background: '#108ffe' }
      }
      return item
    })
    let treeData = listToTree(res.deptInfo, configProps)
    console.log(treeData)

    if (treeData.length == 1) {
      orgData.value = treeData[0]
    } else {
      orgData.value = {
        name: t('sys.AccountControl.noTopDept'),
        children: treeData
      }
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getCompanyInfo()
})
</script>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-company-info';

.@{prefix-cls} {
  ::v-deep(.zoom-container) {
    min-height: 50px;
    min-width: 300px;
  }
  .dot {
    position: relative;
    padding-left: 20px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: var(--el-color-primary);
    }
  }
}
</style>
