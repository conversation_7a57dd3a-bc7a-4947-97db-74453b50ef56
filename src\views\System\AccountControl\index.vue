<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-08-02 13:51:34
 * @LastEditors: <PERSON>J<PERSON>
 * @LastEditTime: 2023-08-18 16:42:31
 * @Description: 
-->
<template>
  <div :class="prefixCls">
    <div class="w-full flex justify-center mt-4">
      <el-radio-group fill="#faf1e1" v-model="accountTyle">
        <el-radio fill="#faf1e1" :label="1" border>{{
          t('sys.AccountControl.accountInformation')
        }}</el-radio>
        <el-radio :label="2" border>{{ t('sys.AccountControl.enterpriseInformation') }}</el-radio>
      </el-radio-group>
    </div>
    <el-divider class="w-4/5" />
    <AccountInfo v-if="accountTyle === 1" />
    <CompanyInfo v-else />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'AccountControl'
})

import AccountInfo from './components/AccountInfo.vue'
import CompanyInfo from './components/CompanyInfo.vue'

const { t } = useI18n()

import { useDesign } from '@/hooks/web/useDesign'
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('account-control')

let accountTyle = ref<number>(1)
</script>
<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-account-control';

.@{prefix-cls} {
  :deep(.el-radio__input) {
    display: none;
  }

  .el-radio--default {
    border-radius: var(--el-border-radius-round);
  }
}
</style>
