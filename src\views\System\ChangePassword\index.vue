<template>
  <div class="change-password">
    <div class="title">修改密码</div>
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      class="form"
      label-width="120px"
      size="large"
    >
      <el-row>
        <el-col :span="20" style="padding-left: 10px; padding-right: 10px">
          <el-form-item prop="oldPassword" label="原密码">
            <el-input
              v-model="formData.oldPassword"
              type="password"
              show-password
              :placeholder="'请输入原密码'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20" style="padding-left: 10px; padding-right: 10px">
          <el-form-item prop="newPassword" label="新密码">
            <el-input
              v-model="formData.newPassword"
              type="password"
              show-password
              :placeholder="'请输入新密码'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="20" style="padding-left: 10px; padding-right: 10px">
          <el-form-item prop="confirmPassword" label="确认密码">
            <el-input
              v-model="formData.confirmPassword"
              type="password"
              show-password
              :placeholder="'请输入新密码'"
            />
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-col :span="20" style="padding-left: 10px; padding-right: 10px">
          <el-form-item prop="code" label="邮箱验证码">
            <el-input v-model="formData.code" :placeholder="'请输入邮箱验证码'" />
          </el-form-item>
        </el-col>
        <el-col :span="4"
          ><el-button class="ml-8px" @click="sendCodeFn" :disabled="countTxt !== codeText">{{
            countTxt
          }}</el-button></el-col
        >
      </el-row>
      <el-row>
        <el-col :span="20" style="padding-left: 10px; padding-right: 10px">
          <el-form-item label=" ">
            <el-button type="primary" class="mt-8px" @click="save" :loading="loading"
              >保存</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'ChangePassword'
})

import { sendUserCode, updatepasswordByCode } from '@/api/login'
import { useUserStoreWithOut } from '@/store/modules/user'
const userStore = useUserStoreWithOut()

function useFormValid<T extends Object = any>(formRef: Ref<any>) {
  async function validForm() {
    const form = unref(formRef)
    if (!form) return
    const data = await form.validate()
    return data as T
  }

  return {
    validForm
  }
}

const formData = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
  code: ''
})

const loading = ref(false)

const form = ref()

const { validForm } = useFormValid(form)

const codeText = ref('获取验证码')

const countTxt = computed(() => {
  if (pwdCount.value === 0) return codeText.value
  return pwdCount.value + 's 重新发送'
})

const pwdCount = ref(0)

const timer = ref()

const countDown = () => {
  pwdCount.value = pwdCount.value === 0 ? 300 : pwdCount.value
  timer.value = setInterval(() => {
    if (pwdCount.value > 0) {
      sessionStorage.setItem('pwdCount', pwdCount.value.toString())
      pwdCount.value--
    } else {
      sessionStorage.removeItem('pwdCount')
      clearInterval(timer.value)
    }
  }, 1000)
}

const save = async () => {
  const data = await validForm()
  if (!data) {
    return
  }
  try {
    loading.value = true
    const res = await updatepasswordByCode({
      oldPassword: formData.value.oldPassword,
      password: formData.value.newPassword,
      code: formData.value.code
    })
    if (res.code !== 0) return
    ElMessage.success('修改成功')
    formData.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: '',
      code: ''
    }
    clearInterval(timer.value)
    pwdCount.value = 0
    sessionStorage.removeItem('pwdCount')
    userStore.forceLoginOut()
  } finally {
    loading.value = false
  }
}

const sendCodeFn = async () => {
  try {
    const res = await sendUserCode({ scene: 3 })
    if (res.code !== 0) return
    ElMessage.success('发送成功')
    countDown()
  } catch {
    ElMessage.error('发送失败')
  }
}

const validPassword = (_rule, value, callback) => {
  // let reg =
  //   !/(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+-=_\[\]\{\}\|,:;'"<>?\/.~!@#$%^&*()]{6,18}$/.test(
  //     value
  //   )
  let reg = !/^[a-zA-Z0-9-*/+-=_\[\]\{\}\|,:;'"<>?\/.~!@#$%^&*()]{6,18}$/.test(value)
  if (!value) {
    callback('请输入密码')
  } else if (reg) {
    callback(new Error('请输入6-18位，数字、字母、符号（不含空格）的密码'))
  } else if (value === formData.value.oldPassword) {
    callback(new Error('新密码与旧密码不能相同，请重新输入'))
  } else {
    callback()
  }
}

const comfirmPassword = (_rule, value, callback) => {
  if (!value) {
    callback('请输入密码')
  } else if (value !== formData.value.newPassword) {
    callback(new Error('密码不一致，请重新输入'))
  } else {
    callback()
  }
}

const rules = {
  oldPassword: [{ required: true, message: '请输入原密码' }],
  newPassword: [{ required: true, validator: validPassword }],
  confirmPassword: [{ required: true, validator: comfirmPassword }],
  code: [{ required: true, message: '请输入验证码' }]
}

onMounted(() => {
  if (sessionStorage.getItem('pwdCount')) {
    pwdCount.value = parseInt(sessionStorage.getItem('pwdCount') as string)
    countDown()
  }
})
</script>
<style lang="less" scoped>
.title {
  font-size: 20px;
  color: #333333;
  width: 100%;
  text-align: center;
  padding: 26px 0 41px 26px;
  border-bottom: 1px #e6e6ea dashed;
}
.form {
  width: 560px;
  margin: 29px auto 0 auto;
}
</style>
