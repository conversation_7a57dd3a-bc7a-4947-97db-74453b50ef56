<!-- 合并了业务待办和流程待办
原路径AgencyManagement 和 Bpm 两个，应该用不到了 -->
<script setup lang="ts">
defineOptions({
  name: 'BusinessTodo'
})

import TodoList from './TodoList/Index.vue'
import TransferRecord from './TransferRecord/Index.vue'
const { t } = useI18n()

let tabName = ref<string>('TodoList')

let tabComponent = shallowRef<any>(TodoList) // 组件

const tabChange = (name) => {
  tabComponent.value = tabList.value.find((item) => item.name === name)?.component
}

const tabList = shallowRef<{ label: string; name: string; component: any }[]>([
  { label: t('todoManagement.businessTodo.todoList'), name: 'TodoList', component: TodoList },
  {
    label: t('todoManagement.businessTodo.transferRecord'),
    name: 'TransferRecord',
    component: TransferRecord
  }
])
</script>
<template>
  <ElTabs v-model="tabName" @tab-change="tabChange">
    <ElTabPane v-for="item in tabList" :key="item.label" :label="item.label" :name="item.name">
      <div></div>
    </ElTabPane>
  </ElTabs>
  <component :is="tabComponent" />
</template>

<style scoped lang="less"></style>
