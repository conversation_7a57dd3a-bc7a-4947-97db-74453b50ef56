<script setup lang="ts">
defineOptions({
  name: 'BusinessTodoList'
})

import { FormInstance } from 'element-plus'
import { findNoticeByPage } from '@/api/AgencyManagement/index'

const { t } = useI18n()
const router = useRouter()
let option = ref<string[]>([
  t('todoManagement.businessTodo.productApproval'),
  t('todoManagement.businessTodo.contractApproval'),
  t('todoManagement.businessTodo.imageApproval'),
  t('todoManagement.businessTodo.prodValidate'),
  t('todoManagement.businessTodo.markCardDemand'),
  t('todoManagement.businessTodo.designScheme'),
  t('todoManagement.businessTodo.designArchive'),
  t('todoManagement.businessTodo.draftScheme'),
  t('todoManagement.businessTodo.draftArchive')
])
let dialogVisible = ref<boolean>(false)

// 查询

const formRef = ref<FormInstance>()
let tableData = ref<object[]>([])
let total = ref<number>(0)
let pageNos = ref<number>(1)
let pageSizes = ref<number>(10)
let form = ref<{
  noticeTitle: string
  noticeTypeName: string
  noticeToLastDate: any
}>({
  noticeTitle: '',
  noticeTypeName: '',
  noticeToLastDate: []
})

const getList = async () => {
  try {
    const datas = {
      pageNo: pageNos.value,
      pageSize: pageSizes.value,
      noticeBusinessCode: 1, //业务待办：1，流程待办：2
      ...formdata()
    }

    const { data } = await findNoticeByPage(datas)
    tableData.value = data.list
    total.value = data.total
  } catch (error) {
    console.log(error, 'error')
  }
}

// 公共参数处理
const formdata = () => {
  let endDate = ''
  let startDate = ''
  if (form.value.noticeToLastDate && form.value.noticeToLastDate.length > 0) {
    startDate = form.value.noticeToLastDate[0] + ' 00:00:00'
    endDate = form.value.noticeToLastDate[1] + ' 23:59:59'
  } else {
    startDate = ''
    endDate = ''
  }
  const data = {
    noticeTitle: form.value.noticeTitle,
    noticeType: {
      noticeTypeName: form.value.noticeTypeName
    },
    startDate,
    endDate
  }
  return data
}
// 重置
const clearform = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

// 转办
import TransferDialog from '../../Components/TransferDialog.vue'

let forwardData = ref({})

let ifForward = ref(false) //是否流程转办
const Transfer = (item, ifForward2) => {
  ifForward.value = ifForward2
  forwardData.value = item
  dialogVisible.value = !dialogVisible.value
}

const indexMethod = (index: number): any => {
  let num: string | number = index + 1 + (pageNos.value - 1) * pageSizes.value
  num = num > 9 ? num.toString() : '0' + num
  return num
}

onMounted(() => {
  getList()
})
</script>
<template>
  <div style="padding: 20px">
    <ElForm label-width="70px" :model="form" ref="formRef">
      <ElRow>
        <ElCol :span="6">
          <ElFormItem :label="t('todoManagement.common.title')" prop="noticeTitle">
            <ElInput
              v-model="form.noticeTitle"
              clearable
              :placeholder="t('common.inputText')"
              maxlength="50"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6" :push="3">
          <ElFormItem :label="t('todoManagement.businessTodo.todoType')" prop="noticeTypeName">
            <ElSelect
              v-model="form.noticeTypeName"
              style="width: 100%"
              clearable
              :placeholder="t('common.selectText')"
            >
              <ElOption :value="item" :label="item" v-for="(item, index) in option" :key="index" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6" :push="6">
          <ElFormItem :label="t('todoManagement.common.time')" prop="noticeToLastDate">
            <ElDatePicker
              clearable
              v-model="form.noticeToLastDate"
              type="daterange"
              :start-placeholder="t('common.startTimeText')"
              :end-placeholder="t('common.endTimeText')"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElFormItem class="elformbutton">
        <div class="centers">
          <ElButton type="primary" @click="getList">{{ t('common.query') }}</ElButton>
          <ElButton color=" #e6a23c " @click="clearform(formRef)" class="button">{{
            t('common.reset')
          }}</ElButton>
        </div>
      </ElFormItem>
    </ElForm>
  </div>

  <div style="padding: 20px">
    <ElTable
      :data="tableData"
      style="width: 100%"
      :header-cell-style="{
        background: 'hsl(0deg 0% 96%)',
        color: 'hsl(0deg 0% 20%)',
        textAlign: 'center'
      }"
      max-height="600px"
      :cell-style="{ textAlign: 'center' }"
      border
    >
      <ElTableColumn
        :label="t('todoManagement.common.sortNum')"
        width="60"
        type="index"
        :index="indexMethod"
      />
      <ElTableColumn prop="noticeTitle" :label="t('todoManagement.common.title')" />
      <ElTableColumn
        prop="noticeType.noticeTypeName"
        :label="t('todoManagement.businessTodo.todoType')"
        width="150"
      />
      <ElTableColumn
        prop="createUser.name"
        :label="t('todoManagement.businessTodo.initiator')"
        width="200"
      />
      <ElTableColumn
        prop="noticeToLastDate"
        :label="t('todoManagement.businessTodo.receiveTime')"
        width="250"
      />
      <ElTableColumn :label="t('common.operate')" width="200">
        <template #default="{ row }">
          <div class="flexcenter">
            <ElLink type="primary" @click="router.push(row.pageUrl)">{{ t('common.see') }}</ElLink>
            <ElLink type="primary" @click="Transfer(row, false)">{{
              t('todoManagement.businessTodo.transfer')
            }}</ElLink>
          </div>
        </template>
      </ElTableColumn>
    </ElTable>
    <Pagination
      v-model:limit="pageSizes"
      v-model:page="pageNos"
      :total="total"
      @pagination="getList"
    />
  </div>
  <TransferDialog
    v-if="dialogVisible"
    v-model:dialogVisible="dialogVisible"
    :forwardData="forwardData"
    :ifForward="ifForward"
  />
</template>

<style scoped lang="less">
.flexcenter {
  display: flex;
  justify-content: space-evenly;
}
.marginTop {
  margin-top: 40px;
}
:deep(.el-pagination .el-select .el-input) {
  width: 100px;
}

:deep(.elformbutton > .el-form-item__content) {
  display: flex;
  justify-content: center;
}

.centers {
  display: flex;
  justify-content: center;
}

.rights {
  p {
    color: hsl(0deg 0% 60%);
    font-size: 12px;
    line-height: 32px;
    margin-left: 15%;
  }

  display: flex;
  justify-content: space-between;
  margin-top: 50px;
}

:deep(.el-tree) {
  background-color: #f2f2f2 !important;
}
:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: #f2f2f2 !important;
}
:deep(.el-tree-node__content:hover) {
  background-color: #f2f2f2 !important;
}
.button {
  background-color: #e6a23c;
  color: #fff;
}
.close {
  cursor: pointer;
}
.margina {
  padding: 20px;
}
.texts {
  color: #333333;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
.flex {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.background {
  background-color: #f2f2f2;
}
</style>
