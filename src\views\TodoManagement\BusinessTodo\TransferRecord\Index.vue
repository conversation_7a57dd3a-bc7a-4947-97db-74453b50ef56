<script setup lang="ts">
defineOptions({
  name: 'BusinessTodoTransferRecord'
})

import { FormInstance } from 'element-plus'
import { ref, onMounted } from 'vue'
import { findNoticeToByPage } from '@/api/AgencyManagement/index'
import MyTransferLog from '../../Components/MyTransferLog/Index.vue'
const { t } = useI18n()
let form = ref<any>({
  fromUser: {
    id: '',
    name: ''
  },
  TypeName: '',
  time: '',
  toUser: {
    id: '',
    name: ''
  }
})
const buttontitle = ref<any>([
  {
    title: t('todoManagement.businessTodo.myTransfer'),
    type: 'primary',
    numbel: '0'
  },
  {
    title: t('todoManagement.businessTodo.transferMy'),
    type: '',
    numbel: '1'
  }
])
let option = ref<string[]>([
  t('todoManagement.businessTodo.prodValidate'),
  t('todoManagement.businessTodo.markCardDemand'),
  t('todoManagement.businessTodo.designScheme'),
  t('todoManagement.businessTodo.designArchive'),
  t('todoManagement.businessTodo.draftScheme'),
  t('todoManagement.businessTodo.draftArchive'),
  t('todoManagement.businessTodo.productApproval'),
  t('todoManagement.businessTodo.contractApproval'),
  t('todoManagement.businessTodo.imageApproval')
])
let noticeBusinessName = ref<string>('我转办的')
let noticeId = ref<string>('')
let titlecolumn = ref<{ name: string; prop: string }[]>([
  { name: t('todoManagement.businessTodo.transferPerson'), prop: 'fromUser.name' },
  { name: t('todoManagement.businessTodo.transferedPerson'), prop: 'toUser.name' },
  { name: t('todoManagement.common.title'), prop: 'noticeToTitle' },
  { name: t('todoManagement.businessTodo.transferType'), prop: 'typeName' },
  { name: t('todoManagement.businessTodo.initiationTime'), prop: 'createDate' }
])
const formRef = ref<FormInstance>()
let tableData = ref<string[]>([])
let TransferredByPageNo = ref<number>(1)
let TransferredByPageSize = ref<number>(10)
let TransfereeByPageNo = ref<number>(1)
let TransfereeByPageSize = ref<number>(10)
let total = ref<number>(0)
let pageNos = ref<number>(1)
let pageSizes = ref<number>(10)
let dialogVisible = ref<boolean>(false)
let showrecvTime = ref(false)
// 重置
const clearform = (formEl: FormInstance | undefined, boolean: boolean) => {
  if (!formEl) return
  formEl.resetFields()
  if (boolean) {
    querydetail()
  }
}
// 0是我转办的  1是转办我的
const textsclick = async (item: { title: string; type: string; numbel: string }) => {
  clearform(formRef.value, false)
  buttontitle.value.forEach((item) => {
    item.type = ''
  })
  item.type = 'primary'
  if (item.numbel === '0') {
    pageNos.value = TransfereeByPageNo.value
    pageSizes.value = TransfereeByPageSize.value
    showrecvTime.value = false
  } else {
    pageNos.value = TransferredByPageNo.value
    pageSizes.value = TransferredByPageSize.value
    showrecvTime.value = true
  }
  noticeBusinessName.value = item.title
  const data: { noticeBusinessName?: string | undefined; pageNo: number; pageSize: number } = {
    pageNo: pageNos.value,
    pageSize: pageSizes.value,
    ...formdata()
  }
  await findNoticeByPages(data)
  pageNos.value = data.pageNo
  pageSizes.value = data.pageSize
  noticeBusinessName.value = item.title
}
const findNoticeByPages = async (datas: object) => {
  try {
    const { data, code, msg } = await findNoticeToByPage(datas)
    if (code == 0) {
      tableData.value = data.list
      total.value = data.total
      return { code, msg }
    }
  } catch (error) {}
}
// 改变页面显示数量
const sizechange = (size: number): void => {
  pageSizes.value = size
  let trans = buttontitle.value.find((item) => {
    return item.type === 'primary'
  }).numbel
  let pageSize = size
  if (trans === '0') {
    TransfereeByPageSize.value = pageSizes.value
  } else {
    TransferredByPageSize.value = pageSizes.value
  }
  const data = {
    pageNo: trans === '0' ? TransfereeByPageNo.value : TransferredByPageNo.value,
    pageSize,
    ...formdata()
  }
  findNoticeByPages(data)
}
// 改变页码
const currentpage = (page: number): void => {
  pageNos.value = page
  let trans = buttontitle.value.find((item) => {
    return item.type === 'primary'
  }).numbel
  let pageNo = page
  if (trans === '0') {
    TransfereeByPageNo.value = pageNos.value
  } else {
    TransferredByPageNo.value = pageNos.value
  }
  const data = {
    pageNo,
    pageSize: trans === '0' ? TransfereeByPageSize.value : TransferredByPageSize.value,
    ...formdata()
  }
  findNoticeByPages(data)
}
const listPaginationRef = ref()
onMounted(() => {
  const data = {
    pageNo: 1,
    pageSize: 10,
    ...formdata()
  }
  findNoticeByPages(data)
  listPaginationRef.value.$el.getElementsByTagName('span')[4].innerText = t(
    'todoManagement.common.jumpTo'
  )
})
// 公共参数处理
const formdata = () => {
  let endDate = ''
  let startDate = ''
  if (form.value.time) {
    if (form.value.time.length > 0) {
      startDate = form.value.time[0] + ' 00:00:00'
      endDate = form.value.time[1] + ' 23:59:59'
    }
  } else {
    form.value.time = []
  }
  let trans = buttontitle.value.find((item) => {
    return item.type === 'primary'
  }).numbel
  const data = {
    flag: trans === '0' ? '1' : '2',
    fromUser: form.value.fromUser,
    typeName: form.value.TypeName,
    endDate,
    startDate,
    toUser: form.value.toUser
  }
  if (trans === '0') {
    delete data.fromUser
  } else {
    delete data.toUser
  }
  return data
}
// 查询
const querydetail = async () => {
  const datas = {
    pageNo: pageNos.value,
    pageSize: pageSizes.value,
    ...formdata()
  }
  try {
    const data = await findNoticeByPages(datas)
    if (data?.code == 0) {
      ElMessage.success(data?.msg)
    }
  } catch (error) {}
}
const widthtitle = (item: string): string => {
  return item == t('todoManagement.common.title')
    ? '200'
    : item == t('todoManagement.businessTodo.initiationTime')
    ? '200'
    : ''
}
let numbes = ref<string>('')
const Log = (row) => {
  console.log(buttontitle.value, 'buttontitle.value')
  numbes.value = buttontitle.value.find((item) => {
    return item.type === 'primary'
  }).numbel
  dialogVisible.value = !dialogVisible.value
  noticeId.value = row.noticeId
}
const backName = (row: object, prop: string) => {
  let name = ''
  if (Object.keys(row).length === 0) return name
  let num = prop.indexOf('.')
  return (name =
    num > -1 && Object.keys(row).length !== 0
      ? row[`${prop.split('.')[0]}`][`${prop.split('.')[1]}`]
      : row[prop])
}
const indexMethod = (index: number) => {
  return index + 1 + (pageNos.value - 1) * pageSizes.value
}
watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      const data = {
        pageNo: 1,
        pageSize: 10,
        ...formdata()
      }
      findNoticeByPages(data)
      listPaginationRef.value.$el.getElementsByTagName('span')[4].innerText = t(
        'todoManagement.common.jumpTo'
      )
    }
  }
)
</script>
<template>
  <div style="padding: 20px">
    <ElForm label-width="70px" :model="form" ref="formRef">
      <ElRow>
        <ElCol :span="6">
          <ElFormItem
            :label="t('todoManagement.businessTodo.transferedPerson')"
            prop="toUser.name"
            v-if="!showrecvTime"
          >
            <ElInput
              v-model="form.toUser.name"
              clearable
              :placeholder="t('common.inputText')"
              maxlength="40"
            />
          </ElFormItem>
          <ElFormItem
            :label="t('todoManagement.businessTodo.transferPerson')"
            prop="fromUser.name"
            v-if="showrecvTime"
          >
            <ElInput
              v-model="form.fromUser.name"
              clearable
              :placeholder="t('common.inputText')"
              maxlength="40"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6" :push="3">
          <ElFormItem :label="t('todoManagement.businessTodo.transferType')" prop="TypeName">
            <ElSelect
              v-model="form.TypeName"
              style="width: 100%"
              clearable
              :placeholder="t('common.selectText')"
            >
              <ElOption :value="item" :label="item" v-for="(item, index) in option" :key="index" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6" :push="6">
          <ElFormItem :label="t('todoManagement.common.time')" prop="time">
            <ElDatePicker
              clearable
              v-model="form.time"
              type="daterange"
              :start-placeholder="t('common.startTimeText')"
              :end-placeholder="t('common.endTimeText')"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow>
        <ElCol :span="12" :push="3">
          <ElFormItem class="elformbutton">
            <div class="centers">
              <ElButton type="primary" @click="querydetail">{{ t('common.query') }}</ElButton>
              <ElButton color=" #e6a23c " @click="clearform(formRef, true)" class="button">{{
                t('common.reset')
              }}</ElButton>
            </div>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
  <div style="padding: 0 20px">
    <ElButton
      v-for="(item, index) in buttontitle"
      :key="index"
      :type="item.type"
      @click="textsclick(item)"
      >{{ item.title }}</ElButton
    >
  </div>
  <div style="padding: 20px">
    <ElTable
      border
      :data="tableData"
      style="width: 100%"
      :header-cell-style="{
        background: 'hsl(0deg 0% 96%)',
        color: 'hsl(0deg 0% 20%)',
        textAlign: 'center'
      }"
      max-height="600px"
      :cell-style="{ textAlign: 'center' }"
    >
      <ElTableColumn
        :label="t('todoManagement.common.sortNum')"
        type="index"
        :index="indexMethod"
        width="60"
        align="center"
      />
      <ElTableColumn
        :label="item.name"
        :min-width="widthtitle(item.name)"
        v-for="(item, index) in titlecolumn"
        :key="index"
      >
        <template #default="{ row }">
          <div>
            {{ backName(row, item.prop) }}
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn
        :label="t('todoManagement.businessTodo.receiveTime')"
        prop="recvTime"
        v-if="showrecvTime"
        min-width="200"
      />
      <ElTableColumn :label="t('common.operate')">
        <template #default="{ row }">
          <ElLink type="primary" @click="Log(row)">日志</ElLink>
        </template>
      </ElTableColumn>
    </ElTable>
    <div class="rights">
      <p
        >{{ t('todoManagement.businessTodo.total') }}{{ total
        }}{{ t('todoManagement.businessTodo.totalNum') }}
        {{ t('todoManagement.businessTodo.totalNo') }}{{ pageNos }}/{{ Math.ceil(total / pageSizes)
        }}{{ t('todoManagement.businessTodo.pageNo') }}</p
      >
      <ElPagination
        ref="listPaginationRef"
        layout="prev,pager,next,sizes,jumper"
        :total="total"
        v-model:current-page="pageNos"
        v-model:page-size="pageSizes"
        @size-change="sizechange"
        @current-change="currentpage"
      />
    </div>
  </div>
  <MyTransferLog v-model:dialogVisible="dialogVisible" :noticeId="noticeId" :numbes="numbes" />
</template>

<style scoped lang="less">
.flexcenter {
  display: flex;
  justify-content: space-evenly;
}
.marginTop {
  margin-top: 40px;
}
:deep(.el-pagination .el-select .el-input) {
  width: 100px;
}

:deep(.elformbutton > .el-form-item__content) {
  display: flex;
  justify-content: center;
}

.centers {
  display: flex;
  justify-content: center;
}

.rights {
  p {
    color: hsl(0deg 0% 60%);
    font-size: 12px;
    line-height: 32px;
    margin-left: 15%;
  }

  display: flex;
  justify-content: space-between;
  margin-top: 50px;
}

:deep(.el-tree) {
  background-color: #f2f2f2 !important;
}
:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: #f2f2f2 !important;
}
:deep(.el-tree-node__content:hover) {
  background-color: #f2f2f2 !important;
}
.button {
  background-color: #e6a23c;
  color: #fff;
}
.close {
  cursor: pointer;
}
.margina {
  padding: 20px;
}
.texts {
  color: #333333;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
.flex {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.background {
  background-color: #f2f2f2;
}
</style>
