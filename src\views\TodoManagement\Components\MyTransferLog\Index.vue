<script setup lang="ts">
const { t } = useI18n()
import { SuccessFilled } from '@element-plus/icons-vue'
import { findNoticeToById } from '@/api/AgencyManagement/index'
import { ref, watch, onMounted } from 'vue'
let log = ref<any>([])
let props = defineProps<{
  numbes: string
  dialogVisible: boolean
  noticeId: string
}>()
let dialogVisible = ref<boolean>(props.dialogVisible)
let emits = defineEmits(['update:dialogVisible'])
watch(
  () => props.dialogVisible,
  (val) => {
    dialogVisible.value = val
  }
)
watch(
  () => dialogVisible.value,
  (val) => {
    if (val) findNoticeToByIds(props.noticeId)
    emits('update:dialogVisible', val)
  }
)
const findNoticeToByIds = async (datas) => {
  try {
    const { data, code } = await findNoticeToById(datas)
    if (code == 0) {
      if (Object.keys(data).length !== 0) {
        log.value = data
      }
    }
  } catch (error) {}
}
onMounted(() => {})
</script>
<template>
  <ElDialog v-model="dialogVisible" width="50%">
    <template #header>
      <div class="texts">{{ t('todoManagement.components.transferLog') }}</div>
    </template>
    <div style="padding: 20px">
      <div v-for="(item, index) in log" :key="index">
        <div class="flex relative padding-left">
          <div class="rectangle"></div>
          <div class="truangular"></div>
          <div class="time absolute">{{ index }}</div>
        </div>
        <div
          class="flex space-between align-items min-hegiht relative"
          v-for="(it, ind) in item"
          :key="ind"
        >
          <div class="min-hegiht border absolute"></div>
          <div class="flex align-items">
            <ElIcon color="#409eff" size="20px"><SuccessFilled /></ElIcon>
            <div class="margin-left">
              <div v-if="props.numbes === '1'"
                >{{ t('todoManagement.businessTodo.transferPerson') }}: {{ it.fromUser.name }}</div
              >
              <div v-if="props.numbes === '0'"
                >{{ t('todoManagement.businessTodo.transferedPerson') }}: {{ it.toUser.name }}</div
              >
              <div
                >{{ t('todoManagement.components.staffId') }}:
                {{ props.numbes === '0' ? it.toUser.id : it.fromUser.id }}</div
              >
            </div>
          </div>
          <div>
            <div>{{ t('todoManagement.businessTodo.transferType') }}: {{ it.typeName }}</div>
          </div>
          <div class="flex">
            <div class="margin-right" v-show="it.recvTime"
              >{{ t('todoManagement.businessTodo.receiveTime') }}: {{ it.recvTime }}</div
            >
            <div v-show="it.updateDate"
              >{{ t('todoManagement.businessTodo.initiationTime') }}: {{ it.updateDate }}</div
            >
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <ElButton type="primary" @click="dialogVisible = false">{{ t('common.ok') }}</ElButton>
    </template>
  </ElDialog>
</template>

<style scoped lang="less">
.min-hegiht {
  min-height: 80px;
}
.align-items {
  align-items: center;
}
.padding-left {
  padding-left: 10px;
}
.relative {
  position: relative;
}
.margin-right {
  margin-right: 10px;
}
.border {
  border: 1px solid #e9e9e9;
  left: 10px;
}
.space-between {
  justify-content: space-between;
  .margin-left {
    margin-left: 20px;
  }
}
.flex {
  display: flex;
  .truangular {
    width: 13px;
    background-color: #409eff;
    border-right: 13px solid #fff !important;
    border-top: 13px solid transparent;
    border-bottom: 13px solid transparent;
  }
  .rectangle {
    width: 100px;
    min-height: 26px;
    background-color: #409eff;
  }
  .time {
    top: 15%;
    left: 2%;
    color: #fff;
  }
}
.absolute {
  position: absolute;
}
.texts {
  color: #333333;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
</style>
