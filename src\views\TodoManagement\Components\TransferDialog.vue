<script setup lang="ts">
defineOptions({
  name: 'TodoTransferDialog'
})

const { t } = useI18n()
import { Close } from '@element-plus/icons-vue'
import { addNoticeTo, listallsimple } from '@/api/AgencyManagement/index'
import { ElTree } from 'element-plus'
// import { loadNode, nodechildren, deptUserPages } from '@/utils/loadNode'
import { useUserStore } from '@/store/modules/user'
const message = useMessage()
let props = defineProps<{
  dialogVisible: boolean
  forwardData: any
  ifForward: boolean
}>()
const userStore = useUserStore()
let createBy = userStore.getUser.id
let dialogVisible = ref<boolean>(props.dialogVisible)
watch(
  () => props.dialogVisible,
  (val) => {
    dialogVisible.value = val
  }
)
let query = ref('')
const treedata = ref<any>()
let toUser = ref<any>({})
let Touser = ref<any>({})
let Name = ref('')
let toReason = ref<string>('')
let name = ref<string>('')

let emits = defineEmits(['update:dialogVisible', 'handleSucess'])

// onMounted(() => {
//   listallsimples()
// })
watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) toReason.value = ''
    emits('update:dialogVisible', val)
  }
)
// const listallsimples = async () => {
//   try {
//     const { data, code } = await listallsimple()
//     if (code == 0) {
//       if (data.length > 0) {
//         let tree = []
//         tree = loadNode(nodechildren(data))
//         await deptUserPages(tree)
//         treedata.value = tree
//       }
//     }
//   } catch (error) {
//     console.log('888999', error)
//   }
// }

//加载数据

//树形选择控件

const treeRef = ref<InstanceType<typeof ElTree>>()
import { useUserSelect } from '@/hooks/common/useUserSelect'
let { customLocationProps, loadNode } = useUserSelect()

//tree点击事件
const handleNodeClick = (data) => {
  console.log(data)
  //node.data.id
  if (data.disabled) return
  Touser.value = data
  Name.value = data.name
}
//选择成员按钮
const selectmembers = () => {
  toUser.value = Touser.value
  name.value = Name.value
}

const filterMethod = (value: string, data: any) => {
  if (!value) return true
  return data.name.includes(value)
}

const closetext = () => (name.value = '')
const closeDialog = () => {
  name.value = ''
  query.value = ''
}
import * as TaskApi from '@/api/bpm/task'

const Transfer = async () => {
  if (!Touser.value.id)
    return ElMessage.error(t('common.selectText') + t('todoManagement.businessTodo.transferPerson'))
  if (createBy === Touser.value.id)
    return ElMessage.error(t('todoManagement.components.canNotTransferTips'))

  try {
    //流程转办
    if (props.ifForward) {
      await TaskApi.transfer({
        taskId: props.forwardData.taskId,
        targetAssigneeUserId: Touser.value.id,
        transferReason: toReason.value
      })
      emits('handleSucess')
      dialogVisible.value = false
      return
    }
    //业务转办
    const datas = {
      noticeId: props.forwardData.noticeId,
      toUser: {
        id: Touser.value.id,
        name: Touser.value.name
      },
      toReason: toReason.value
    }
    const { code, msg } = await addNoticeTo(datas)

    if (code == 0) {
      emits('handleSucess')
      ElMessage.success(msg)
      dialogVisible.value = false
    }
  } catch (error) {}
}
watch(query, (val) => {
  treeRef.value!.filter(val)
})
</script>
<template>
  <ElDialog v-model="dialogVisible" width="55%" @close="closeDialog">
    <template #header>
      <div class="texts">{{ t('todoManagement.businessTodo.transfer') }}</div>
    </template>
    <div class="margina">
      <ElRow justify="space-around">
        <ElCol :span="11">
          <div>{{ t('todoManagement.common.select') }}：</div>
        </ElCol>
        <ElCol :span="11">
          <div>{{ t('todoManagement.common.selected') }}：</div>
        </ElCol>
      </ElRow>
      <ElRow justify="space-around">
        <ElCol :span="11">
          <div class="background">
            <div class="margina">
              <ElInput
                v-model="query"
                clearable
                :placeholder="t('common.selectText') + t('todoManagement.components.staffName')"
              />
              <ElButton type="primary" style="width: 100%; margin: 15px 0" @click="selectmembers">{{
                t('todoManagement.components.selectStaff')
              }}</ElButton>
              <ElScrollbar class="test">
                <ElTree
                  node-key="id"
                  ref="treeRef"
                  class="filter-tree"
                  :props="customLocationProps"
                  :load="loadNode"
                  lazy
                  @node-click="handleNodeClick"
                  :filter-node-method="filterMethod"
                  :highlight-current="true"
                />
              </ElScrollbar>
            </div>
          </div>
        </ElCol>
        <ElCol :span="11">
          <div class="background">
            <div class="flex">
              <div> {{ name }}</div>
              <ElIcon v-show="name" class="close" @click="closetext()"><Close /></ElIcon>
            </div>
          </div>
        </ElCol>
      </ElRow>
      <ElRow justify="center" class="marginTop">
        <ElCol :span="2">
          <div style="display: flex">
            <div>{{ t('todoManagement.components.transferReason') }}</div>
          </div>
        </ElCol>
        <ElCol :span="20">
          <ElInput :rows="5" type="textarea" v-model="toReason" />
        </ElCol>
      </ElRow>
    </div>
    <template #footer>
      <ElButton type="primary" @click="Transfer">{{ t('common.ok') }}</ElButton>
      <ElButton @click="dialogVisible = false">{{ t('common.cancel') }}</ElButton>
    </template>
  </ElDialog>
</template>
<style scoped lang="less">
.flexcenter {
  display: flex;
  justify-content: space-evenly;
}
.marginTop {
  margin-top: 40px;
}
:deep(.el-pagination .el-select .el-input) {
  width: 100px;
}

:deep(.elformbutton > .el-form-item__content) {
  display: flex;
  justify-content: center;
}
.centers {
  display: flex;
  justify-content: center;
}

.rights {
  p {
    color: hsl(0deg 0% 60%);
    font-size: 12px;
    line-height: 32px;
    margin-left: 15%;
  }

  display: flex;
  justify-content: space-between;
  margin-top: 50px;
}

:deep(.el-tree) {
  background-color: #f2f2f2 !important;
}
:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: white !important;
  color: #409eff !important;
}
:deep(.el-tree-node__content:hover) {
  background-color: white !important;
  color: #409eff !important;
}
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: white;
  color: #409eff !important;
}
.button {
  background-color: #e6a23c;
  color: #fff;
}
.close {
  cursor: pointer;
}
.margina {
  padding: 20px;
  .test {
    height: 200px;
  }
}
.texts {
  color: #333333;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
.flex {
  padding: 20px;
  height: 333px;
  display: flex;
  justify-content: space-between;
}
.background {
  background-color: #f2f2f2;
}
</style>
