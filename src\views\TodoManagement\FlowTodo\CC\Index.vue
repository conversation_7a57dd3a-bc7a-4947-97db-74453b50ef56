<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item :label="t('todoManagement.businessTodo.initiator')" prop="startUserNickname">
        <el-input
          v-model="queryParams.startUserNickname"
          class="!w-240px"
          clearable
          :placeholder="t('common.inputText') + t('todoManagement.businessTodo.initiator')"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('todoManagement.businessTodo.todoType')" prop="processDefinitionKey">
        <el-select
          v-model="processDefinitionKey"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in toDoTypeOptions"
            :key="dict.processDefinitionKey"
            :label="dict.processDefinitionName"
            :value="dict.processDefinitionKey"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('todoManagement.businessTodo.initiationTime')" prop="createTime">
        <el-date-picker
          v-model="createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          :end-placeholder="t('todoManagement.common.endDate')"
          :start-placeholder="t('todoManagement.common.startDate')"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item :label="t('todoManagement.flowTodo.flowStatus')" prop="processInstanceResult">
        <el-select
          v-model="queryParams.processInstanceResult"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in stateOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('todoManagement.flowTodo.copyTime')" prop="CCTime">
        <el-date-picker
          v-model="CCTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          :end-placeholder="t('todoManagement.common.endDate')"
          :start-placeholder="t('todoManagement.common.startDate')"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('common.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('common.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column
        align="center"
        :label="t('todoManagement.common.sortNum')"
        width="80px"
        type="index"
        :index="indexMethod"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.taskId')"
        prop="taskId"
        width="150px"
        show-overflow-tooltip
      />
      <el-table-column align="center" :label="t('todoManagement.common.title')" prop="taskName" />
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.todoType')"
        prop="category"
      >
        <template #default="{ row }">
          {{ getLabel(row?.processDefinitionKey) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.initiator')"
        prop="processInstanceStartUserNickname"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.initiationTime')"
        prop="processInstanceStartTime"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.copyTime')"
        prop="processCCTime"
      />
      <el-table-column align="center" :label="t('todoManagement.common.status')" prop="result">
        <!-- <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.taskResult" />
        </template> -->
        <template #default="{ row }">
          <span>{{
            getDictLabel('bpm_process_instance_result', String(row.processInstanceResult))
          }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="t('common.operate')">
        <template #default="scope">
          <el-button link type="primary" @click="handleAudit(scope.row)">{{
            t('common.see')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'TodoCcTask'
})

const { t } = useI18n()
// import { DICT_TYPE } from '@/utils/dict'
import * as TaskApi from '@/api/bpm/task'
import useToDoType from '../../common/useToDoType'
const { toDoTypeOptions, getLabel } = useToDoType()
const { push } = useRouter() // 路由
import { getDictOptions, getDictLabel } from '@/utils/dict'
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const processDefinitionKey = ref()
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  startUserNickname: '',
  processDefinitionKeys: null,
  status: '',
  beginStartTime: '',
  endStartTime: '',
  beginCCStartTime: '',
  endCCStartTime: ''
})
const queryFormRef = ref() // 搜索的表单
const stateOptions = reactive(getDictOptions('bpm_process_instance_result'))
/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  queryParams.processDefinitionKeys = processDefinitionKey.value
    ? [processDefinitionKey.value]
    : null
  try {
    const data = await TaskApi.getCcTaskPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
// 设置序号
const indexMethod = (index: number): number => {
  return (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  processDefinitionKey.value = ''
  queryParams.processDefinitionKeys = null
  queryParams.beginStartTime = ''
  queryParams.endStartTime = ''
  queryParams.beginCCStartTime = ''
  queryParams.endCCStartTime = ''
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理审批按钮 */
const handleAudit = (row) => {
  push({
    name: 'TodoProcessInstanceDetail',
    query: {
      id: row?.processInstanceId
    }
  })
}
const createTime = computed({
  get: () => {
    let time: [Date, Date] = [queryParams.beginStartTime as Date, queryParams.endStartTime as Date]
    return time
  },
  set: (value: any) => {
    if (value) {
      queryParams.beginStartTime = value[0]
      queryParams.endStartTime = value[1]
    } else {
      queryParams.beginStartTime = ''
      queryParams.endStartTime = ''
    }
  }
})
const CCTime = computed({
  get: () => {
    let time: [Date, Date] = [
      queryParams.beginCCStartTime as Date,
      queryParams.endCCStartTime as Date
    ]
    return time
  },
  set: (value: any) => {
    if (value) {
      queryParams.beginCCStartTime = value[0]
      queryParams.endCCStartTime = value[1]
    } else {
      queryParams.beginCCStartTime = ''
      queryParams.endCCStartTime = ''
    }
  }
})
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
