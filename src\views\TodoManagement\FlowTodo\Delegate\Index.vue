<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item :label="t('todoManagement.flowTodo.flowName')" prop="processInstanceName">
        <el-input
          v-model="queryParams.processInstanceName"
          class="!w-240px"
          clearable
          :placeholder="t('common.inputText') + t('todoManagement.flowTodo.flowName')"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item
        v-if="ifOwner"
        :label="t('todoManagement.flowTodo.taskAssigneeUserNickname')"
        prop="taskAssigneeUserNickname"
      >
        <el-input
          v-model="queryParams.taskAssigneeUserNickname"
          class="!w-240px"
          clearable
          :placeholder="
            t('common.inputText') + t('todoManagement.flowTodo.taskAssigneeUserNickname')
          "
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item
        v-if="!ifOwner"
        :label="t('todoManagement.flowTodo.taskOwnerUserNickname')"
        prop="taskOwnerUserNickname"
      >
        <el-input
          v-model="queryParams.taskOwnerUserNickname"
          class="!w-240px"
          clearable
          :placeholder="t('common.inputText') + t('todoManagement.flowTodo.taskOwnerUserNickname')"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('todoManagement.common.createTime')">
        <el-date-picker
          v-model="createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          :end-placeholder="t('todoManagement.common.endDate')"
          :start-placeholder="t('todoManagement.common.startDate')"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          {{ t('common.search') }}
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          {{ t('common.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-radio-group class="mb-4" v-model="ifOwner" @change="getList">
      <el-radio-button :label="true" border>{{
        t('todoManagement.flowTodo.myTaskAssignee')
      }}</el-radio-button>
      <el-radio-button :label="false" border>{{
        t('todoManagement.flowTodo.toTaskAssignee')
      }}</el-radio-button>
    </el-radio-group>

    <el-table v-loading="loading" :data="list" border>
      <el-table-column
        align="center"
        :label="t('todoManagement.common.sortNum')"
        width="80px"
        type="index"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.approvalId')"
        prop="processInstance.approvalKey"
        width="260px"
      />
      <el-table-column
        v-if="ifOwner"
        align="center"
        :label="t('todoManagement.flowTodo.taskAssigneeUserNickname')"
        width="120"
        prop="assignee.userNickname"
      />
      <el-table-column
        v-else
        align="center"
        :label="t('todoManagement.flowTodo.taskOwnerUserNickname')"
        width="120"
        prop="owner.userNickname"
      />

      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.flowTitle')"
        width="120"
        prop="processInstance.name"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.operateStep')"
        width="120"
        prop="taskInstance.taskName"
      />

      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.flowInitiator')"
        width="120"
        prop="processInstance.starter.userNickname"
      />

      <el-table-column align="center" :label="t('todoManagement.common.status')" prop="result">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.taskReason')"
        width="120"
        prop="delegateReason"
        show-overflow-tooltip
      />

      <el-table-column
        :formatter="dateFormatter"
        align="center"
        label="审批时间"
        prop="endTime"
        width="180"
      />

      <el-table-column align="center" :label="t('common.operate')" width="120" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleAudit(scope.row)">{{
            t('common.see')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>
<script setup lang="ts">
defineOptions({
  name: 'TodoDelegateTask'
})

const { t } = useI18n()
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'

const { push } = useRouter() // 路由

let ifOwner = ref<boolean>(true) //true我委派的  >>>>   false 我被委派的<<<

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据

const createTime = ref<any>([])
const queryParams = reactive<any>({
  pageNo: 1,
  pageSize: 10,
  delegateListTypeEnum: ifOwner.value ? 1 : 2, //委托列表类型
  taskOwnerUserNickname: '', //委托人昵称
  taskAssigneeUserNickname: '', //被委托人昵称
  processInstanceName: '', //流程名称
  beginApprovalTime: createTime.value?.length > 0 ? createTime.value[0] : '', //审批开始时间
  endApprovalTime: createTime.value?.length > 0 ? createTime.value[1] : '' //审批结束时间
})
const queryFormRef = ref() // 搜索的表单

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  queryParams.beginApprovalTime = createTime.value?.length > 0 ? createTime.value[0] : ''
  //审批开始时间
  queryParams.endApprovalTime = createTime.value?.length > 0 ? createTime.value[1] : ''
  //审批结束时间

  try {
    let data
    if (ifOwner.value) {
      data = await TaskApi.getDelegateOwnerList(queryParams)
    } else {
      data = await TaskApi.getDelegateAssigneeList(queryParams)
    }
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理审批按钮 */
const handleAudit = (row) => {
  push({
    name: 'TodoProcessInstanceDetail',
    query: {
      id: row.processInstance?.processInstanceId
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
