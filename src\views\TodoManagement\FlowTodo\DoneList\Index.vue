<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item
        :label="t('todoManagement.businessTodo.initiator')"
        prop="processInstanceStarterName"
      >
        <el-input
          v-model="queryParams.processInstanceStarterName"
          class="!w-240px"
          clearable
          :placeholder="t('common.inputText') + t('todoManagement.businessTodo.initiator')"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('todoManagement.businessTodo.todoType')" prop="processDefinitionKey">
        <el-select
          v-model="queryParams.processDefinitionKey"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in toDoTypeOptions"
            :key="dict.processDefinitionKey"
            :label="dict.processDefinitionName"
            :value="dict.processDefinitionKey"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('todoManagement.businessTodo.initiationTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          :end-placeholder="t('todoManagement.common.endDate')"
          :start-placeholder="t('todoManagement.common.startDate')"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item :label="t('todoManagement.flowTodo.flowStatus')" prop="processInstanceResult">
        <el-select
          v-model="queryParams.processInstanceResult"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in stateOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"> {{ t('common.query') }} </el-button>
        <el-button type="warning" @click="resetQuery"> {{ t('common.reset') }} </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-row :gutter="10" class="mb-20px">
      <el-col :span="1.5">
        <el-button @click="getList">{{ t('todoManagement.common.refresh') }}</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list">
      <el-table-column
        align="center"
        :label="t('todoManagement.common.sortNum')"
        width="80px"
        type="index"
        :index="indexMethod"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.approvalId')"
        prop="processInstance.approvalKey"
        width="260px"
      />
      <el-table-column align="center" :label="t('todoManagement.common.title')" prop="taskName" />
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.todoType')"
        prop="category"
      >
        <template #default="{ row }">
          {{ getLabel(row?.processInstance?.processDefinitionKey) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.initiator')"
        prop="processInstance.startUserNickname"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        :label="t('todoManagement.businessTodo.initiationTime')"
        prop="processInstance.startTime"
        width="180"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        :label="t('todoManagement.businessTodo.receiveTime')"
        prop="createTime"
        width="180"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        :label="t('todoManagement.flowTodo.dealTime')"
        prop="endTime"
        width="180"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.taskStatus')"
        prop="result"
      >
        <template #default="{ row }">
          <DictTag type="bpm_process_instance_result" :value="row.result" />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.flowStatus')"
        prop="result"
      >
        <template #default="{ row }">
          <DictTag type="bpm_process_instance_result" :value="row.processInstance.result" />
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="原因" prop="reason" /> -->

      <el-table-column align="center" :label="t('common.operate')">
        <template #default="scope">
          <!-- <el-button link type="primary" @click="openDetail(scope.row)">详情</el-button>
          <el-button link type="primary" @click="handleAudit(scope.row)">流程</el-button> -->
          <el-button link type="primary" @click="handleAudit(scope.row)">{{
            t('common.see')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="pageParams.pageSize"
      v-model:page="pageParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：详情 -->
  <TaskDetail ref="detailRef" @success="getList" />
</template>
<script setup lang="ts">
defineOptions({
  name: 'DoneList'
})

import { dateFormatter } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'
import TaskDetail from './TaskDetail.vue'
import { getDictOptions, getDictLabel } from '@/utils/dict'
import useToDoType from '../../common/useToDoType'
const { toDoTypeOptions, getLabel } = useToDoType()
const { push } = useRouter() // 路由
const { t } = useI18n()
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const pageParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const queryParams = reactive({
  processInstanceStarterName: '',
  createTime: [],
  beginCreateTime: '',
  endTime: '',
  processDefinitionKey: '',
  processInstanceResult: ''
})
const queryFormRef = ref() // 搜索的表单

const stateOptions = reactive(getDictOptions('bpm_process_instance_result'))

// 设置序号
const indexMethod = (index: number): number => {
  return (pageParams.pageNo - 1) * pageParams.pageSize + index + 1
}

/** 查询任务列表 */
const getList = async () => {
  queryParams.beginCreateTime = queryParams.createTime?.[0]
  queryParams.endTime = queryParams.createTime?.[1]
  loading.value = true
  try {
    const data = await TaskApi.getDoneTaskPage(pageParams, queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (row) => {
  detailRef.value.open(row)
}

/** 处理审批按钮 */
const handleAudit = (row) => {
  push({
    name: 'TodoProcessInstanceDetail',
    query: {
      id: row.processInstance?.processInstanceId,
      type: 'taskDone'
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
