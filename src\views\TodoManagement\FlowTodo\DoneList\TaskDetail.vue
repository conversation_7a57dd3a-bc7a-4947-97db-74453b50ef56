<template>
  <Dialog
    v-model="dialogVisible"
    :max-height="500"
    :scroll="true"
    :title="t('todoManagement.common.detail')"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item :label="t('todoManagement.flowTodo.taskId')" min-width="120">
        {{ detailData.id }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('todoManagement.flowTodo.taskName')">
        {{ detailData.name }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('todoManagement.flowTodo.flowBelong')">
        {{ detailData.processInstance.name }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('todoManagement.flowTodo.flowInitiator')">
        {{ detailData.processInstance.startUserNickname }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('todoManagement.common.status')">
        <!-- <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_RESULT" :value="detailData.result" /> -->
      </el-descriptions-item>
      <el-descriptions-item :label="t('todoManagement.common.reason')">
        {{ detailData.reason }}
      </el-descriptions-item>
      <el-descriptions-item :label="t('todoManagement.common.createTime')">
        {{ formatDate(detailData.createTime) }}
      </el-descriptions-item>
    </el-descriptions>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'TodoTaskDetail'
})

// import { DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
// import * as TaskApi from '@/api/bpm/task'

const { t } = useI18n()

const dialogVisible = ref(false) // 弹窗的是否展示
const detailLoading = ref(false) // 表单的加载中
const detailData = ref() // 详情数据

/** 打开弹窗 */
const open = async (data) => {
  dialogVisible.value = true
  // 设置数据
  detailLoading.value = true
  try {
    detailData.value = data
  } finally {
    detailLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
