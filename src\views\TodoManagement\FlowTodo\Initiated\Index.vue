<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item :label="t('todoManagement.businessTodo.todoType')" prop="processDefinitionKey">
        <el-select
          v-model="queryParams.processDefinitionKey"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in toDoTypeOptions"
            :key="dict.processDefinitionKey"
            :label="dict.processDefinitionName"
            :value="dict.processDefinitionKey"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('todoManagement.businessTodo.initiationTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          :end-placeholder="t('todoManagement.common.endDate')"
          :start-placeholder="t('todoManagement.common.startDate')"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item :label="t('todoManagement.flowTodo.flowStatus')" prop="result">
        <el-select
          v-model="queryParams.result"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in stateOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"> {{ t('common.query') }} </el-button>
        <el-button type="warning" @click="resetQuery"> {{ t('common.reset') }} </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-row :gutter="10" class="mb-20px">
      <el-col :span="1.5">
        <el-button @click="getList">{{ t('todoManagement.common.refresh') }}</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list">
      <el-table-column
        align="center"
        :label="t('todoManagement.common.sortNum')"
        width="80px"
        type="index"
        :index="indexMethod"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.approvalId')"
        prop="approvalKey"
        width="300px"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.common.title')"
        prop="processInstanceName"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.todoType')"
        prop="category"
      >
        <template #default="{ row }">
          {{ getLabel(row?.processDefinitionKey) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.initiator')"
        prop="starterName"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        :label="t('todoManagement.businessTodo.initiationTime')"
        prop="createTime"
        width="180"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.flowStatus')"
        prop="result"
      >
        <template #default="{ row }">
          <DictTag type="bpm_process_instance_result" :value="row.result" />

          <!-- <span>{{ getDictLabel('bpm_process_instance_result', String(row.result)) }}</span> -->
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="check(scope.row)">{{ t('common.see') }}</el-button>
          <!-- <el-button
            link
            type="primary"
            v-if="scope.row.result === 1"
            @click="showConfirm(scope.row, 1)"
            >撤回</el-button
          > -->
          <!-- <el-button
            link
            type="primary"
            v-if="scope.row.result === 3 || scope.row.result === 4"
            @click="showConfirm(scope.row, 2)"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="pageParams.pageSize"
      v-model:page="pageParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <ElDialog v-model="dialogVisible" :title="dialogTitle" width="30%">
    <div
      >{{ t('todoManagement.flowTodo.isNeed') }}{{ dialogTitle
      }}{{ t('todoManagement.flowTodo.application') }}？</div
    >
    <div></div>
    <template #footer>
      <span class="dialog-footer">
        <ElButton type="primary" @click="oprationFn">{{ t('common.ok') }}</ElButton>
        <ElButton @click="dialogVisible = false">{{ t('common.cancel') }}</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
defineOptions({
  name: 'Initiated'
})

const { t } = useI18n()
import { dateFormatter } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'
import { getDictOptions, getDictLabel } from '@/utils/dict'
import useToDoType from '../../common/useToDoType'
const { toDoTypeOptions, getLabel } = useToDoType()
const { push } = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const pageParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const queryParams = reactive({
  createTime: [],
  processDefinitionKey: '',
  result: ''
})
const queryFormRef = ref() // 搜索的表单

const stateOptions = reactive(getDictOptions('bpm_process_instance_result'))

let dialogVisible = ref(false)

let oprationRow = reactive({})

let oprationStatus = ref(1)

let dialogTitle = ref<string>('')

// 设置序号
const indexMethod = (index: number): number => {
  return (pageParams.pageNo - 1) * pageParams.pageSize + index + 1
}

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getInitiatedTaskPage(pageParams, queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查看按钮 */
const check = (row) => {
  push({
    name: 'TodoProcessInstanceDetail',
    query: {
      id: row?.processInstanceId
    }
  })
}

/** 撤回按钮 */
const withdraw = async (row) => {
  console.log('撤回', row)
}

/** 删除按钮 */
const del = async (row) => {
  console.log('删除', row)
}

const oprationFn = () => {
  const fn = oprationStatus.value === 1 ? withdraw : del
  fn(oprationRow)
}

// 打开确认弹窗
const showConfirm = (row, confirmStatus: number) => {
  dialogVisible.value = true
  dialogTitle.value = confirmStatus === 1 ? '撤回' : t('common.delete')
  oprationRow = row
  oprationStatus.value = confirmStatus
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
