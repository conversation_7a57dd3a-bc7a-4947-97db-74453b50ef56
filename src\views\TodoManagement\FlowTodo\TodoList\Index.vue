<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
    >
      <el-form-item :label="t('todoManagement.businessTodo.initiator')" prop="startUserNickname">
        <el-input
          v-model="queryParams.startUserNickname"
          class="!w-240px"
          clearable
          :maxlength="40"
          :placeholder="t('common.inputText') + t('todoManagement.businessTodo.initiator')"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="t('todoManagement.businessTodo.todoType')" prop="processDefinitionKey">
        <el-select
          v-model="queryParams.processDefinitionKey"
          :placeholder="t('common.selectText')"
          clearable
          style="width: 234px"
        >
          <el-option
            v-for="dict in toDoTypeOptions"
            :key="dict.processDefinitionKey"
            :label="dict.processDefinitionName"
            :value="dict.processDefinitionKey"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入任务名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item :label="t('todoManagement.businessTodo.initiationTime')" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
          :end-placeholder="t('todoManagement.common.endDate')"
          :start-placeholder="t('todoManagement.common.startDate')"
          type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery"> {{ t('common.search') }} </el-button>
        <el-button type="warning" @click="resetQuery"> {{ t('common.reset') }} </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-row :gutter="10" class="mb-20px">
      <el-col :span="1.5">
        <el-button @click="getList">{{ t('todoManagement.common.refresh') }}</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list">
      <el-table-column
        align="center"
        :label="t('todoManagement.common.sortNum')"
        width="80px"
        type="index"
        :index="indexMethod"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.approvalId')"
        prop="processInstance.approvalKey"
        width="300px"
      />
      <!-- <el-table-column align="center" label="任务编号" prop="id" width="300px" /> -->
      <el-table-column align="center" :label="t('todoManagement.common.title')" prop="taskName" />
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.todoType')"
        prop="category"
      >
        <template #default="{ row }">
          {{ getLabel(row?.processInstance?.processDefinitionKey) }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :label="t('todoManagement.flowTodo.flowBelong')"
        prop="processInstance.name"
      />
      <el-table-column
        align="center"
        :label="t('todoManagement.businessTodo.initiator')"
        prop="processInstance.startUserNickname"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        :label="t('todoManagement.businessTodo.initiationTime')"
        prop="processInstance.startTime"
        width="180"
      />
      <el-table-column
        :formatter="dateFormatter"
        align="center"
        :label="t('todoManagement.businessTodo.receiveTime')"
        prop="createTime"
        width="180"
      />
      <el-table-column
        :label="t('todoManagement.flowTodo.taskStatus')"
        align="center"
        prop="suspensionState"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.suspensionState === 1" type="success">{{
            t('todoManagement.flowTodo.activate')
          }}</el-tag>
          <el-tag v-if="scope.row.suspensionState === 2" type="warning">{{
            t('todoManagement.flowTodo.unActivate')
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="t('common.operate')">
        <template #default="scope">
          <el-button link type="primary" @click="handleAudit(scope.row)">{{
            t('todoManagement.common.deal')
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="pageParams.pageSize"
      v-model:page="pageParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
defineOptions({
  name: 'TodoList'
})

const { t } = useI18n()
import { dateFormatter } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'
import { ref } from 'vue'
import useToDoType from '../../common/useToDoType'
const { push } = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const pageParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const queryParams = reactive({
  startUserNickname: '',
  processDefinitionKey: '',
  createTime: []
})
const { toDoTypeOptions, getLabel } = useToDoType()
const queryFormRef = ref() // 搜索的表单

// 设置序号
const indexMethod = (index: number): number => {
  return (pageParams.pageNo - 1) * pageParams.pageSize + index + 1
}

/** 查询任务列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTodoTaskPage(pageParams, queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  pageParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理审批按钮 */
const handleAudit = (row) => {
  push({
    name: 'TodoProcessInstanceDetail',
    query: {
      id: row.processInstance?.processInstanceId
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
