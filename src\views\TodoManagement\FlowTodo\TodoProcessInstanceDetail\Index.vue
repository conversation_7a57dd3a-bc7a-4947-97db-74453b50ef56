<template>
  <ContentWrap>
    <!-- 审批信息 -->
    <el-tabs :tab-position="tabPosition" @tab-change="tabChange">
      <el-tab-pane :label="t('todoManagement.flowTodo.baseInfo')" />
      <el-tab-pane :label="t('todoManagement.flowTodo.flowLog')" />
      <el-tab-pane :label="t('todoManagement.flowTodo.flowChart')" />
      <!-- 申请信息 -->
      <el-card
        v-for="(item, index) in runningTasks"
        :key="index"
        v-loading="processInstanceLoading"
        class="box-card"
        v-show="tabValue === '0' && pageType !== 'taskDone'"
      >
        <template #header>
          <span class="el-icon-picture-outline"
            >{{ t('todoManagement.flowTodo.approvalTask') }}【{{ item.taskName }}】</span
          >
        </template>
        <el-col :offset="6" :span="16">
          <el-form
            :ref="'form' + index"
            :model="auditForms[index]"
            :rules="auditRule"
            label-width="100px"
          >
            <el-form-item
              v-if="processInstance && processInstance.name"
              :label="t('todoManagement.flowTodo.flowName2')"
            >
              {{ processInstance.name }}
            </el-form-item>
            <!-- 委派任务 -->
            <el-form-item
              v-if="getCurrentDelegateTask(item)"
              :label="t('todoManagement.flowTodo.taskTo')"
            >
              <span class="text-sm mr-4 p-1 px-2 border-1 border-[#d3d4d6] rounded">
                {{ getCurrentDelegateTask(item)?.ownerUserNickname }}
              </span>
              <el-tag>{{ t('todoManagement.flowTodo.taskToWho') }}</el-tag>
              <span class="text-sm ml-4 p-1 px-2 border-1 border-[#d3d4d6] rounded">
                {{ getCurrentDelegateTask(item)?.assigneeUserNickname }}
              </span>
            </el-form-item>
            <el-form-item
              v-if="processInstance && processInstance.startUser"
              :label="t('todoManagement.flowTodo.flowInitiator')"
            >
              {{ processInstance.startUser.nickname }}
              <el-tag size="small" type="info">{{ processInstance.startUser.deptName }}</el-tag>
            </el-form-item>
            <el-form-item :label="t('todoManagement.flowTodo.approvalSuggest')" prop="reason">
              <el-input
                v-model="auditForms[index].reason"
                :placeholder="t('common.inputText') + t('todoManagement.flowTodo.approvalSuggest')"
                type="textarea"
              />
            </el-form-item>
            <!-- 下节点审批人功能屏蔽 -->
            <!-- <el-form-item label="下节点审批人">
              <ElButton
                class="mr-5"
                type="primary"
                :icon="Plus"
                circle
                @click="
                  () => {
                    handleType = handleTypeEnum.CC
                    openTaskAssignRuleForm(index)
                  }
                "
              />
              <el-tag size="large" type="info" v-if="auditForms[index].assigneeType">
                {{ bpmTaskAssignRuleType[auditForms[index].assigneeType].zh }}
              </el-tag>
              <el-tag
                v-for="assignItem in auditForms[index].assigneeOptions"
                :key="assignItem.id"
                class="ml-2"
                size="large"
                closable
                @close="deleteAssignData(assignItem.id, index)"
                >{{ assignItem.name }}</el-tag
              >
            </el-form-item> -->
            <el-form-item :label="t('todoManagement.flowTodo.copyPerson')">
              <ElButton
                class="mr-5"
                type="primary"
                :icon="Plus"
                circle
                @click="
                  () => {
                    handleType = handleTypeEnum.CC
                    openTaskSelectUserForm(item.taskId, index)
                  }
                "
              />

              <el-tag
                v-for="userItem in auditForms[index].CCUserList"
                :key="userItem.id"
                class="mr-2"
                size="large"
                closable
                @close="deleteCCUser(userItem.id, index)"
                >{{ userItem.nickname }}</el-tag
              >
            </el-form-item>
          </el-form>
          <div style="margin-left: 10%; margin-bottom: 20px; font-size: 14px">
            <el-button type="success" @click="handleAudit(item, true)">
              <Icon icon="ep:select" />
              {{ t('todoManagement.common.pass') }}
            </el-button>
            <el-button type="danger" @click="handleAudit(item, false)">
              <Icon icon="ep:close" />
              {{ t('todoManagement.common.notPass') }}
            </el-button>
            <el-button
              type="primary"
              @click="
                () => {
                  handleType = handleTypeEnum.Forward
                  openTaskSelectUserForm(item.taskId, index)
                }
              "
            >
              <Icon icon="ep:edit" />
              {{ t('todoManagement.common.transfer') }}
            </el-button>
            <el-button
              type="primary"
              @click="
                () => {
                  handleType = handleTypeEnum.Delegate
                  openTaskSelectUserForm(item.taskId, index)
                }
              "
            >
              <Icon icon="ep:position" />
              {{ t('todoManagement.common.delegate') }}
            </el-button>
            <!-- <el-button type="warning" @click="handleBack(item)">
              <Icon icon="ep:back" />
              回退
            </el-button> -->
          </div>
        </el-col>
      </el-card>
      <el-card v-loading="processInstanceLoading" class="box-card" v-show="tabValue === '0'">
        <template #header>
          <span class="el-icon-document"
            >{{ t('todoManagement.flowTodo.applicateInfo') }}【{{ processInstance.name }}】</span
          >
        </template>
        <!-- 情况一：流程表单 -->
        <el-col v-if="processInstance?.processDefinition?.formType === 10" :offset="6" :span="16">
          <form-create
            ref="fApi"
            v-model="detailForm.value"
            :option="detailForm.option"
            :rule="detailForm.rule"
          />
        </el-col>
        <!-- 情况二：业务表单 -->
        <div v-if="processInstance?.processDefinition?.formType === 20">
          <div v-if="!processInstance?.processDefinition?.formCustomViewPath">{{
            t('todoManagement.flowTodo.flowBindTips')
          }}</div>
          <component :is="businessFormComponent" />
        </div>
        <div v-if="processInstance?.processDefinition?.formType === -10">{{
          t('todoManagement.flowTodo.notBindForm')
        }}</div>
      </el-card>
      <!-- 审批记录 -->
      <ProcessInstanceTaskListComponents
        :loading="tasksLoad"
        :tasks="tasks"
        v-show="tabValue === '1'"
      />
      <!-- 高亮流程图 -->
      <ProcessInstanceBpmnViewer
        :id="`${id}`"
        :bpmn-xml="bpmnXML"
        :loading="processInstanceLoading"
        :process-instance="processInstance"
        :processInstanceActivityList="processInstanceActivityList"
        :tasks="flowTasks"
        v-show="tabValue === '2'"
      />
    </el-tabs>

    <!-- 弹窗：转派审批人 -->
    <TaskSelectUserForm
      ref="taskSelectUserFormRef"
      :handleType="handleType"
      @success="getDetail"
      @handle-forward="handleForward"
      @handle-delegate="handleDelegate"
      @set-cc-user-list="setCcUserList"
    />
    <!-- 弹窗: 下节点审批人 -->
    <TaskAssignRuleForm ref="taskAssignRuleForm" @success="setAssignee" />
  </ContentWrap>
</template>
<script lang="ts" setup>
const { t } = useI18n()
import { useUserStore } from '@/store/modules/user'
import { setConfAndFields2 } from '@/utils/formCreate'
import type { ApiAttrs } from '@form-create/element-ui/types/config'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as TaskApi from '@/api/bpm/task'
import TaskSelectUserForm from './TaskSelectUserForm.vue'
import ProcessInstanceBpmnViewer from './ProcessInstanceBpmnViewer.vue'
import ProcessInstanceTaskListComponents from './ProcessInstanceTaskList.vue'
import { Plus } from '@element-plus/icons-vue'
const { query } = useRoute() // 查询参数
const message = useMessage() // 消息弹窗
const { proxy } = getCurrentInstance() as any

const userId = useUserStore().getUser.id // 当前登录的编号
const id = query.id as unknown as number // 流程实例的编号
const pageType = query?.type
const processInstanceLoading = ref(false) // 流程实例的加载中
const processInstance = ref<any>({}) // 流程实例
const processInstanceActivityList = ref<any>({}) // 流程实例
const processInstanceTaskList = ref<any>({}) // 流程实例
const bpmnXML = ref('') // BPMN XML
// const tempBpmnXML = ref('')
const tasksLoad = ref(false) // 任务的加载中
const tasks = ref<any[]>([]) // 任务列表
const flowTasks = ref<any[]>([]) // 暂放任务列表
const tabPosition = ref('top') //tab组件的位置
const tabValue = ref('0') //切换tab的值
// ========== 审批信息 ==========
const runningTasks = ref<any[]>([]) // 运行中的任务
const auditForms = ref<any[]>([]) // 审批任务的表单
const auditRule = reactive({
  reason: [
    { required: true, message: t('todoManagement.flowTodo.approvalSuggestTips'), trigger: 'blur' }
  ]
})
// ========== 申请信息 ==========
const fApi = ref<ApiAttrs>() //
const detailForm = ref({
  // 流程表单详情
  rule: [],
  option: {},
  value: {}
})

//审批人选择弹出框
enum handleTypeEnum {
  /** 转办*/
  Forward = 'Forward',
  /** 委派*/
  Delegate = 'Delegate',
  /**抄送3 */
  CC = 'CC'
}
let handleType = ref<string>('')

const deleteCCUser = (val, index) => {
  //删除抄送人
  auditForms.value[index].CCUserList = auditForms.value[index]?.CCUserList.filter(
    (item) => item.id != val
  )
  //子组件CCUserList数据同步
  taskSelectUserFormRef.value.CCUserList = taskSelectUserFormRef.value.CCUserList.filter(
    (item) => item.id != val
  )
  //子组件assigneeUserId数据同步
  taskSelectUserFormRef.value.formData.assigneeUserId =
    taskSelectUserFormRef.value.formData.assigneeUserId.filter((item) => item != val)
}

/** 处理审批通过和不通过的操作 */
const handleAudit = async (task, pass) => {
  // 1.1 获得对应表单
  const index = runningTasks.value.indexOf(task)
  const auditFormRef = proxy.$refs['form' + index][0]
  // 1.2 校验表单
  const elForm = unref(auditFormRef)
  if (!elForm) return
  const valid = await elForm.validate()
  if (!valid) return

  // 2.1 提交审批
  const data = {
    taskId: task.taskId,
    reason: auditForms.value[index].reason,
    ccUserIds: auditForms.value[index]?.CCUserList?.map((item) => item.id),
    assigneeType: auditForms.value[index]?.assigneeType,
    assigneeOptions: auditForms.value[index]?.assigneeOptions?.map((item) => item.id)
  }
  if (pass) {
    await TaskApi.approveTask(data)
    message.success(t('todoManagement.flowTodo.approvalPassSuccess'))
  } else {
    await TaskApi.rejectTask(data)
    message.success(t('todoManagement.flowTodo.approvalPassFailed'))
  }
  // 2.2 加载最新数据
  getDetail()
}

const tabChange = (name) => {
  tabValue.value = name
}

// 组件内无法检测到task的变化，因此在检测到标签切换的时候再进行赋值
watch(
  () => tabValue.value,
  (tabValues) => {
    if (tabValues === '2' && flowTasks.value.length === 0) {
      // 2. 获得流程任务列表（审批记录）
      getFlowTasks()
    }
  }
)
//审批人弹出框
const taskSelectUserFormRef = ref()

/**
 *打开弹窗
 * @param id  任务id
 * @param index  并行任务索引
 */
const openTaskSelectUserForm = (id: string, auditFormsIndex: number) => {
  taskSelectUserFormRef.value.open(id, auditFormsIndex)
}
/** 抄送 */
// const CCUserList = computed(() => taskSelectUserFormRef.value.CCUserList)
const setCcUserList = ({ CCUserList, auditFormsIndex }) => {
  console.log(CCUserList, auditFormsIndex)

  auditForms.value[auditFormsIndex].CCUserList = CCUserList
  console.log(auditForms.value)
}

/** 转办审批人 */
const handleForward = async () => {
  await TaskApi.transfer({
    taskId: taskSelectUserFormRef.value.formData?.taskId,
    targetAssigneeUserId: taskSelectUserFormRef.value.formData?.assigneeUserId,
    transferReason: taskSelectUserFormRef.value.formData?.transferReason
  })
  getDetail()
}

/** 处理委派操作 */
const handleDelegate = async () => {
  TaskApi.delegate({
    id: taskSelectUserFormRef.value.formData?.taskId,
    userId: taskSelectUserFormRef.value.formData?.assigneeUserId,
    delegateReason: taskSelectUserFormRef.value.formData?.transferReason
  })
  getDetail()
  // message.error('暂不支持【委派】功能，可以使用【转派】替代！')
}

/** 处理审批退回的操作 */
const handleBack = async (task) => {
  message.error('暂不支持【退回】功能！')
}

/** 获得详情 */
const getDetail = async () => {
  // 1. 获得流程实例相关
  await getProcessInstance()
  // 2. 获得流程任务列表（审批记录）
  getTaskList()
}
/** 初始化 */
onMounted(() => {
  getDetail()
})

/** 加载流程实例 */
let businessFormComponent = shallowRef<any>([]) // 异步组件
//依赖注入-流程变量
let processVariables = ref({})
provide('processVariables', processVariables)

const getProcessInstance = async () => {
  try {
    processInstanceLoading.value = true
    const data = await ProcessInstanceApi.getProcessInstance(id)
    if (!data) {
      message.error(`${t('todoManagement.flowTodo.searchFlowInfoNull')}！`)
      return
    }
    processInstance.value = data?.processInstance
    processInstanceActivityList.value = data?.processInstanceActivityList
    processInstanceTaskList.value = data?.processInstanceTaskList
    processVariables.value = data?.processInstance?.variables

    // 设置表单信息
    const processDefinition = processInstance.value.processDefinition
    if (processDefinition.formType === 10) {
      setConfAndFields2(
        detailForm,
        processDefinition.formConf,
        processDefinition.formFields,
        processInstance.value.variables
      )
      nextTick().then(() => {
        fApi.value?.fapi?.btn.show(false)
        fApi.value?.fapi?.resetBtn.show(false)
        fApi.value?.fapi?.disabled(true)
      })
    } else if (processDefinition.formType === 20) {
      // path示例 ../../../CustomList/Details/index.vue
      let path = `../../../${processInstance.value.processDefinition.formCustomViewPath}`
      console.log(path)

      const modules: any = import.meta.glob('../../../../views/**/*.vue')
      console.log(modules[path])
      if (!modules[path]) {
        message.notifyError(t('todoManagement.flowTodo.flowBindCompTips'))
        return
      }
      businessFormComponent.value = defineAsyncComponent(modules[path])
    }

    // 加载流程图
    bpmnXML.value = data?.processDefinitionBpmnXml
  } finally {
    processInstanceLoading.value = false
  }
}

/** 加载任务列表 */
const getTaskList = async () => {
  try {
    // 获得未取消的任务
    tasksLoad.value = true
    const data = processInstanceTaskList.value
    tasks.value = []
    // 1.1 移除已取消的审批
    data.forEach((task) => {
      if (task.result !== 4) {
        tasks.value.push(task)
      }
    })
    // 1.2 排序，将未完成的排在前面，已完成的排在后面；
    tasks.value.sort((a, b) => {
      // 有已完成的情况，按照完成时间倒序
      if (a.endTime && b.endTime) {
        return b.endTime - a.endTime
      } else if (a.endTime) {
        return 1
      } else if (b.endTime) {
        return -1
        // 都是未完成，按照创建时间倒序
      } else {
        return b.createTime - a.createTime
      }
    })

    // 获得需要自己审批的任务
    runningTasks.value = []
    auditForms.value = []
    tasks.value.forEach((task) => {
      // 2.1 只有待处理才需要
      if (task.result !== 1) {
        return
      }
      // 2.2 自己不是处理人
      if (!task.assigneeUser || task.assigneeUser.userId !== userId) {
        return
      }

      // 2.3 添加到处理任务
      runningTasks.value.push({ ...task })
      console.log('task', task)
      console.log('runningTasks', runningTasks.value)

      auditForms.value.push({
        reason: ''
      })
    })
  } finally {
    tasksLoad.value = false
  }
}

// 获取流程图的任务列表
const getFlowTasks = () => {
  flowTasks.value = tasks.value
  console.log(flowTasks.value, ' flowTasks.value')
}

//判断当前任务是否为委派任务并返回委派任务详情
const getCurrentDelegateTask = (task) => {
  if (!task.delegateList || task.delegateList?.length < 0) return
  //endTime有数据则已经审批过的要忽略
  let target = task.delegateList.find((item) => !item?.endTime)
  return target
}

/**下节点审批人 */
import { bpmTaskAssignRuleType } from '@/utils/constants'

import TaskAssignRuleForm from './TaskAssignRuleForm.vue'
const taskAssignRuleForm = ref()
/**
 * 打开弹出框
 * @param auditFormsIndex 并行任务索引
 */
const openTaskAssignRuleForm = (auditFormsIndex: number) => {
  taskAssignRuleForm.value.open(auditFormsIndex)
}

const setAssignee = ({ assigneeOptions, assigneeType, auditFormsIndex }) => {
  auditForms.value[auditFormsIndex].assigneeOptions = assigneeOptions
  auditForms.value[auditFormsIndex].assigneeType = assigneeType
}

const deleteAssignData = (val, auditFormsIndex) => {
  auditForms.value[auditFormsIndex].assigneeOptions = auditForms.value[
    auditFormsIndex
  ].assigneeOptions.filter((item) => item.id != val)
  if (!auditForms.value[auditFormsIndex].assigneeOptions.length) {
    auditForms.value[auditFormsIndex].assigneeType = undefined
  }
}
</script>
