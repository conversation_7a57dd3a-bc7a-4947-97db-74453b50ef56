<template>
  <el-card v-loading="loading" class="box-card">
    <template #header>
      <span class="el-icon-picture-outline">{{ t('todoManagement.flowTodo.flowChart') }}</span>
    </template>
    <MyProcessViewer
      key="designer"
      :activityData="activityList"
      :prefix="bpmnControlForm.prefix"
      :processInstanceData="processInstance"
      :taskData="tasks"
      :value="bpmnXml"
      v-bind="bpmnControlForm"
    />
  </el-card>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BpmProcessInstanceBpmnViewer'
})

const { t } = useI18n()
import { propTypes } from '@/utils/propTypes'
import { MyProcessViewer } from '@/components/BpmnProcessDesigner/package'
// import * as ActivityApi from '@/api/bpm/activity'

const props = defineProps({
  loading: propTypes.bool, // 是否加载中
  id: propTypes.string, // 流程实例的编号
  processInstance: propTypes.any, // 流程实例的信息
  processInstanceActivityList: propTypes.any,
  tasks: propTypes.array, // 流程任务的数组
  bpmnXml: propTypes.string // BPMN XML
})

const bpmnControlForm = ref({
  prefix: 'flowable'
})
const activityList = ref([]) // 任务列表
console.log(props.processInstance, props.tasks, props.bpmnXml, 'bpmnXml')

// const bpmnXML = computed(() => { // TODO 芋艿：不晓得为啊哈不能这么搞
//   if (!props.processInstance || !props.processInstance.processDefinition) {
//     return
//   }
//   return DefinitionApi.getProcessDefinitionBpmnXML(props.processInstance.processDefinition.id)
// })

/** 初始化 */
// onMounted(async () => {
//   if (props.id) {
//     activityList.value = await ActivityApi.getActivityList({
//       processInstanceId: props.id
//     })
//   }
// })
watch(
  () => props.processInstanceActivityList,
  (newValue) => {
    console.log(newValue, 'newValue')
    activityList.value = newValue
  }
)
</script>
<style scoped>
.box-card {
  width: 100%;
  margin-bottom: 20px;
}
</style>
