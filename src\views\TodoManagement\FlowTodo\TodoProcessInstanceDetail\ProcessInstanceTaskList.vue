<template>
  <el-card v-loading="loading" class="box-card">
    <template #header>
      <span class="el-icon-picture-outline">{{ t('todoManagement.flowTodo.approvalRecord') }}</span>
    </template>
    <el-col :offset="4" :span="16">
      <div class="block" v-if="false">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in tasks"
            :key="index"
            :icon="taskResultTpye[item.result].icon"
            :type="taskResultTpye[item.result].type"
          >
            <!-- <div>
              <label v-if="item.assigneeUser" style="font-weight: normal; margin-right: 30px">
                操作人员：{{ item.assigneeUser.nickname }}
                <el-tag size="small" type="info">{{ item.assigneeUser.deptName }}</el-tag>
              </label>
              <label v-if="item.createTime" style="font-weight: normal">操作动作：</label>
              <label style="color: #8a909c; font-weight: normal; margin-right: 30px">
                {{ formatDate(item?.createTime) }}
              </label>
              <label v-if="item.name" style="font-weight: normal">节点名称：</label>
              <label style="color: #8a909c; font-weight: normal">
                {{ item?.name }}
              </label>
              <label v-if="item.endTime" style="margin-left: 30px; font-weight: normal"> </label>
              <label v-if="item.endTime" style="color: #8a909c; font-weight: normal">
                {{ formatDate(item?.endTime) }}
              </label>
            </div>
            <div>
              <label v-if="item.endTime" style="font-weight: normal">员工编号：</label>
              <label style="color: #8a909c; font-weight: normal">
                {{ item?.endTime }}
              </label>
            </div> -->
            <p style="font-weight: 700">{{ t('todoManagement.common.task') }}：{{ item.name }}</p>
            <el-card :body-style="{ padding: '10px' }">
              <label v-if="item.assigneeUser" style="font-weight: normal; margin-right: 30px">
                {{ t('todoManagement.flowTodo.approvalUser') }}：{{ item.assigneeUser.nickname }}
                <el-tag size="small" type="info">{{ item.assigneeUser.deptName }}</el-tag>
              </label>
              <label v-if="item?.result" style="font-weight: normal; margin-right: 30px">
                {{ t('todoManagement.flowTodo.operateAction') }}：{{
                  taskResultTpye[item.result].text
                }}
              </label>
              <label v-if="item?.taskName" style="font-weight: normal; margin-right: 30px">
                {{ t('todoManagement.flowTodo.nodeName') }}：{{ item?.taskName }}
              </label>
              <label v-if="item.createTime" style="font-weight: normal"
                >{{ t('todoManagement.common.createTime') }}：</label
              >
              <label style="color: #8a909c; font-weight: normal">
                {{ formatDate(item?.createTime) }}
              </label>
              <label v-if="item.endTime" style="margin-left: 30px; font-weight: normal">
                {{ t('todoManagement.flowTodo.approvalTime') }}：
              </label>
              <label v-if="item.endTime" style="color: #8a909c; font-weight: normal">
                {{ formatDate(item?.endTime) }}
              </label>
              <label v-if="item.durationInMillis" style="margin-left: 30px; font-weight: normal">
                {{ t('todoManagement.flowTodo.useTime') }}：
              </label>
              <label v-if="item.durationInMillis" style="color: #8a909c; font-weight: normal">
                {{ formatPast2(item?.durationInMillis) }}
              </label>
              <p v-if="item.reason">
                <el-tag :type="taskResultTpye[item.result].type">{{ item.reason }}</el-tag>
              </p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
      <div style="padding: 20px">
        <div v-for="(item, index) in tasks" :key="index">
          <div class="flex relative padding-left">
            <div class="rectangle"></div>
            <div class="truangular"></div>
            <div class="time absolute">{{ formatDate(item.createTime, 'YYYY-MM-DD') }}</div>
          </div>
          <div class="flex align-items min-hegiht relative">
            <div class="min-hegiht border absolute h-[100%]"></div>
            <div class="flex align-items">
              <ElIcon color="#409eff" size="20px"><SuccessFilled /></ElIcon>
            </div>
            <div>
              <el-card :body-style="{ padding: '10px' }">
                <label v-if="item.assigneeUser" style="font-weight: normal; margin-right: 30px">
                  {{ t('todoManagement.flowTodo.operateUser') }}：{{ item.assigneeUser.nickname }}
                  <el-tag size="small" type="info">{{
                    item.assigneeUser.deptName ?? t('todoManagement.flowTodo.noDepartment')
                  }}</el-tag>
                </label>
                <label v-if="item?.result" style="font-weight: normal; margin-right: 30px">
                  {{ t('todoManagement.flowTodo.operateAction') }}：{{
                    taskResultTpye[item.result].text
                  }}
                </label>
                <label v-if="item?.taskName" style="font-weight: normal; margin-right: 30px">
                  {{ t('todoManagement.flowTodo.nodeName') }}：{{ item?.taskName }}
                </label>
                <label v-if="item.createTime" style="font-weight: normal"
                  >{{ t('todoManagement.common.createTime') }}：</label
                >
                <label style="color: #8a909c; font-weight: normal">
                  {{ formatDate(item?.createTime) }}
                </label>
                <label v-if="item.endTime" style="margin-left: 30px; font-weight: normal">
                  {{ t('todoManagement.flowTodo.approvalTime') }}：
                </label>
                <label v-if="item.endTime" style="color: #8a909c; font-weight: normal">
                  {{ formatDate(item?.endTime) }}
                </label>
                <label v-if="item.durationInMillis" style="margin-left: 30px; font-weight: normal">
                  {{ t('todoManagement.flowTodo.useTime') }}：
                </label>
                <label v-if="item.durationInMillis" style="color: #8a909c; font-weight: normal">
                  {{ formatPast2(item?.durationInMillis) }}
                </label>
                <p v-if="item.reason">
                  <el-tag :type="taskResultTpye[item.result].type">{{ item.reason }}</el-tag>
                </p>
              </el-card>

              <!-- 委派任务 -->
              <el-card
                :body-style="{ padding: '15px' }"
                v-for="(delegateListItem, delegateListIndex) in item.delegateList"
                :key="delegateListIndex"
                shadow="never"
              >
                <el-divider content-position="left">
                  <!-- 任务状态 -->
                  <el-tag :type="taskResultTpye[delegateListItem.result]?.type">
                    {{ taskResultTpye[delegateListItem.result]?.text }}
                  </el-tag>
                  <span
                    class="text-sm ml-3 p-1 px-2 border-1 border-[#d3d4d6] rounded text-[#909399]"
                  >
                    {{ formatDate(delegateListItem?.createTime) }}
                  </span>
                  -
                  <span
                    class="text-sm ml-3 p-1 px-2 border-1 border-[#d3d4d6] rounded text-[#909399]"
                  >
                    {{
                      delegateListItem?.endTime
                        ? formatDate(delegateListItem?.endTime)
                        : t('todoManagement.flowTodo.taskToDeal')
                    }}
                  </span>
                  <span class="ml-4" v-if="delegateListItem?.endTime">
                    {{ t('todoManagement.flowTodo.useTime') }}:
                    {{
                      formatPast2(delegateListItem?.endTime - delegateListItem?.createTime)
                    }}</span
                  >
                </el-divider>
                <div>
                  <span class="text-sm mr-4 p-1 px-2 border-1 border-[#d3d4d6] rounded">
                    {{ delegateListItem?.ownerUserNickname }}
                  </span>
                  <el-tag>{{ t('todoManagement.flowTodo.taskToWho') }}</el-tag>
                  <span class="text-sm ml-4 p-1 px-2 border-1 border-[#d3d4d6] rounded">
                    {{ delegateListItem?.assigneeUserNickname }}
                  </span>
                  <span class="ml-3 text-[#909399] text-sm">
                    :
                    {{
                      delegateListItem?.reason ?? t('todoManagement.flowTodo.approvalSuggestNull')
                    }}
                  </span>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-card>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BpmProcessInstanceTaskList'
})

import { SuccessFilled } from '@element-plus/icons-vue'
import { formatDate, formatPast2 } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
defineProps({
  loading: propTypes.bool, // 是否加载中
  tasks: Array<any> // 流程任务的数组
})
const { t } = useI18n()
/** 获得任务对应的 icon */
const taskResultTpye = {
  1: {
    icon: 'el-icon-time',
    type: 'primary',
    text: t('todoManagement.common.')
  },
  2: {
    icon: 'el-icon-check',
    type: 'success',
    text: t('todoManagement.common.pass')
  },
  3: {
    icon: 'el-icon-close',
    type: 'danger',
    text: t('todoManagement.common.notPass')
  },
  4: {
    icon: 'el-icon-remove-outline',
    type: 'info',
    text: t('common.cancel')
  }
}
</script>
<style scoped lang="less">
.min-hegiht {
  min-height: 80px;
}
.align-items {
  align-items: center;
}
.padding-left {
  padding-left: 10px;
}
.relative {
  position: relative;
}
.margin-right {
  margin-right: 10px;
}
.border {
  border: 1px solid #e9e9e9;
  left: 10px;
}
.margin-left {
  margin-left: 20px;
}
.flex {
  display: flex;
  .truangular {
    width: 13px;
    background-color: #409eff;
    border-right: 13px solid #fff !important;
    border-top: 13px solid transparent;
    border-bottom: 13px solid transparent;
  }
  .rectangle {
    width: 100px;
    min-height: 26px;
    background-color: #409eff;
  }
  .time {
    top: 15%;
    left: 2%;
    color: #fff;
  }
}
.absolute {
  position: absolute;
}
</style>
