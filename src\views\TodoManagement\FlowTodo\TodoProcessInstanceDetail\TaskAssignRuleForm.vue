<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJack
 * @LastEditTime: 2023-10-10 16:52:40
 * @Description:  修改任务规则 type:1/下节点审批人设置  type:2
-->

<template>
  <Dialog
    v-model="dialogVisible"
    :title="t('todoManagement.flowTodo.nextNodeApprovalUser')"
    width="600"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
      <el-form-item :label="t('todoManagement.flowTodo.ruleType')" prop="type">
        <el-select v-model="formData.type" clearable style="width: 100%">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_TASK_ASSIGN_RULE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formData.type === 10"
        :label="t('todoManagement.flowTodo.specifyRole')"
        prop="roleIds"
      >
        <el-select v-model="formData.roleIds" clearable multiple style="width: 100%">
          <el-option
            v-for="item in roleOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formData.type === 20 || formData.type === 21"
        :label="t('todoManagement.flowTodo.specifyDepartment')"
        prop="deptIds"
        span="24"
      >
        <el-tree-select
          ref="treeRef"
          v-model="formData.deptIds"
          :data="deptTreeOptions"
          :props="defaultProps"
          :empty-text="t('todoManagement.flowTodo.loadingWait')"
          multiple
          node-key="id"
          show-checkbox
        />
      </el-form-item>
      <el-form-item
        v-if="formData.type === 22"
        :label="t('todoManagement.flowTodo.specifyPost')"
        prop="postIds"
        span="24"
      >
        <el-select v-model="formData.postIds" clearable multiple style="width: 100%">
          <el-option
            v-for="item in postOptions"
            :key="parseInt(item.id)"
            :label="item.name"
            :value="parseInt(item.id)"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formData.type === 30 || formData.type === 31 || formData.type === 32"
        :label="t('todoManagement.flowTodo.specifyUser')"
        prop="userIds"
        span="24"
      >
        <el-select v-model="formData.userIds" clearable multiple style="width: 100%">
          <el-option
            v-for="item in userOptions"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formData.type === 40"
        :label="t('todoManagement.flowTodo.specifyUserGroup')"
        prop="userGroupIds"
      >
        <el-select v-model="formData.userGroupIds" clearable multiple style="width: 100%">
          <el-option
            v-for="item in userGroupOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="formData.type === 50"
        :label="t('todoManagement.flowTodo.specifyScript')"
        prop="scripts"
      >
        <el-select v-model="formData.scripts" clearable multiple style="width: 100%">
          <el-option
            v-for="dict in taskAssignScriptDictDatas"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <!-- 操作按钮 -->
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">{{
        t('common.ok')
      }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'BpmTaskAssignRuleForm'
})

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { defaultProps, handleTree, findNode } from '@/utils/tree'

import * as AssignApi from '@/api/bpm/assign/index'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<any>({
  type: Number(undefined),
  modelId: '',
  options: [],
  roleIds: [],
  deptIds: [],
  postIds: [],
  userIds: [],
  userGroupIds: [],
  scripts: []
})
const formRules = reactive({
  type: [{ required: true, message: t('todoManagement.flowTodo.ruleTypeTips'), trigger: 'change' }],
  roleIds: [
    { required: true, message: t('todoManagement.flowTodo.specifyRoleTips'), trigger: 'change' }
  ],
  deptIds: [
    {
      required: true,
      message: t('todoManagement.flowTodo.specifyDepartmentTips'),
      trigger: 'change'
    }
  ],
  postIds: [
    { required: true, message: t('todoManagement.flowTodo.specifyPostTips'), trigger: 'change' }
  ],
  userIds: [
    { required: true, message: t('todoManagement.flowTodo.specifyUserTips'), trigger: 'change' }
  ],
  userGroupIds: [
    {
      required: true,
      message: t('todoManagement.flowTodo.specifyUserGroupTips'),
      trigger: 'change'
    }
  ],
  scripts: [
    { required: true, message: t('todoManagement.flowTodo.specifyScriptTips'), trigger: 'change' }
  ]
})
const formRef = ref() // 表单 Ref
const roleOptions = ref<AssignApi.RoleVO[]>([]) // 角色列表
const deptOptions = ref<AssignApi.DeptVO[]>([]) // 部门列表
const deptTreeOptions = ref() // 部门树
const postOptions = ref<AssignApi.PostVO[]>([]) // 岗位列表
const userOptions = ref<AssignApi.UserVO[]>([]) // 用户列表
const userGroupOptions = ref<AssignApi.UserGroupVO[]>([]) // 用户组列表
const taskAssignScriptDictDatas = getIntDictOptions(DICT_TYPE.BPM_TASK_ASSIGN_SCRIPT)

let auditFormsIndex = ref<number>(0)
/**
 * 打开弹窗
 * @param index   并行任务索引
 */
const open = async (index) => {
  auditFormsIndex.value = index

  // 1. 先重置表单
  resetForm()
  // 2. 再设置表单
  formData.value = {
    options: [],
    roleIds: [],
    deptIds: [],
    postIds: [],
    userIds: [],
    userGroupIds: [],
    scripts: []
  }

  // 打开弹窗
  dialogVisible.value = true

  // 获得角色列表
  roleOptions.value = await AssignApi.getSimpleRoleList()
  // 获得部门列表
  deptOptions.value = await AssignApi.getSimpleDeptList()
  deptTreeOptions.value = handleTree(deptOptions.value, 'id')
  // 获得岗位列表
  postOptions.value = await AssignApi.getSimplePostList()
  // 获得用户列表
  userOptions.value = await AssignApi.getSimpleUserList()
  // 获得用户组列表
  userGroupOptions.value = await AssignApi.getSimpleUserGroupList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

//下节点审批人列表
const getAssignData = (form) => {
  if (form.type === 10) {
    return {
      assigneeType: form.type,
      assigneeOptions: form.roleIds.map((item) => {
        return {
          id: item,
          name: roleOptions.value.find((item2) => item2.id === item)?.name
        }
      })
    }
  } else if (form.type === 20 || form.type === 21) {
    return {
      assigneeType: form.type,
      assigneeOptions: form.deptIds.map((item) => {
        return {
          id: item,
          name: findNode(deptOptions.value, (node) => {
            return item === node.id
          })?.name
        }
      })
    }
  } else if (form.type === 22) {
    return {
      assigneeType: form.type,
      assigneeOptions: form.postIds.map((item) => {
        return {
          id: item,
          name: postOptions.value.find((item2) => item2.id === item)?.name
        }
      })
    }
  } else if (form.type === 30 || form.type === 31 || form.type === 32) {
    return {
      assigneeType: form.type,
      assigneeOptions: form.userIds.map((item) => {
        return {
          id: item,
          name: userOptions.value.find((item2) => item2.id === item)?.nickname
        }
      })
    }
  } else if (form.type === 40) {
    return {
      assigneeType: form.type,
      assigneeOptions: form.userGroupIds.map((item) => {
        return {
          id: item,
          name: userGroupOptions.value.find((item2) => item2.id === item)?.name
        }
      })
    }
  } else if (form.type === 50) {
    form.options = form.scripts
    return {
      assigneeType: form.type,
      assigneeOptions: form.scripts.map((item) => {
        return {
          id: item,
          name: taskAssignScriptDictDatas.find((item2) => item2.value === item)?.label
        }
      })
    }
  }
}

const submitForm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return

  // 构建表单
  const form = {
    ...formData.value,
    taskDefinitionName: undefined
  }
  // 将 roleIds 等选项赋值到 options 中
  let assignData = getAssignData(form)
  console.log(assignData)

  if (form.type === 10) {
    form.options = form.roleIds
  } else if (form.type === 20 || form.type === 21) {
    form.options = form.deptIds
  } else if (form.type === 22) {
    form.options = form.postIds
  } else if (form.type === 30 || form.type === 31 || form.type === 32) {
    form.options = form.userIds
  } else if (form.type === 40) {
    form.options = form.userGroupIds
  } else if (form.type === 50) {
    form.options = form.scripts
  }
  form.roleIds = undefined
  form.deptIds = undefined
  form.postIds = undefined
  form.userIds = undefined
  form.userGroupIds = undefined
  form.scripts = undefined

  // 提交请求
  formLoading.value = true
  try {
    // 发送操作成功的事件
    emit('success', { ...assignData, auditFormsIndex: auditFormsIndex.value })
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formRef.value?.resetFields()
}
</script>
