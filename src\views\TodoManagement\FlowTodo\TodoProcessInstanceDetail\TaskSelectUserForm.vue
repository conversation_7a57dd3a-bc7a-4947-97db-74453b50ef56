<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-06-30 15:21:16
 * @LastEditors: HoJ<PERSON>
 * @LastEditTime: 2023-12-04 15:09:56
 * @Description:   转办/委派/抄送 选人
-->
<template>
  <Dialog v-model="dialogVisible" :title="handleTypeEnum[props.handleType]?.title" width="500">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="110px"
    >
      <el-form-item :label="t('todoManagement.flowTodo.newApprovalUser')" prop="assigneeUserId">
        <ElTreeSelect
          v-model="formData.assigneeUserId"
          :props="customLocationProps"
          :load="loadNode"
          :multiple="handleTypeEnum[props.handleType]?.ifSelectMore"
          lazy
          clearable
        />
      </el-form-item>
      <el-form-item
        :label="t('todoManagement.common.reason')"
        prop="reason"
        v-if="props.handleType !== TaskApi.handleTypeEnum.CC"
      >
        <el-input
          v-model="formData.transferReason"
          :placeholder="t('common.inputText') + t('todoManagement.components.transferReason')"
          type="textarea"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">{{
        t('common.ok')
      }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
defineOptions({
  name: 'TaskSelectUserForm'
})

const { t } = useI18n()
import * as TaskApi from '@/api/bpm/task'

const emit = defineEmits(['handleForward', 'handleDelegate', 'success', 'setCcUserList']) // 定义 success 事件，用于操作成功后的回调

const handleTypeEnum = {
  Forward: { title: t('todoManagement.common.transfer'), ifSelectMore: false },
  Delegate: { title: t('todoManagement.common.delegate'), ifSelectMore: false },
  CC: { title: t('todoManagement.common.copy'), ifSelectMore: true }
}

const props = defineProps({
  handleType: {
    type: String,
    default: 'Forward'
  } //操作类型:  转办1 委派2 抄送3
})

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const formData = ref({
  taskId: '',
  assigneeUserId: '' as string | [],
  transferReason: null
})
const formRules = ref({
  assigneeUserId: [
    { required: true, message: t('todoManagement.flowTodo.newApprovalUserTips'), trigger: 'change' }
  ]
})

const formRef = ref() // 表单 Ref

/**
 * 打开弹窗
 * @param taskId  任务id
 * @param index   并行任务索引
 */

let auditFormsIndex = ref<number>(0)
const open = async (taskId: string, index: number) => {
  auditFormsIndex.value = index
  dialogVisible.value = true
  resetForm()
  formData.value.taskId = taskId
}

/** 提交表单 */
let CCUserList = ref<any>([]) //抄送用户列表

const submitForm = async () => {
  // 校验表单

  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  try {
    //抄送
    if (props.handleType === TaskApi.handleTypeEnum.CC) {
      CCUserList.value = userList.value.filter((item) =>
        (formData.value.assigneeUserId as [])?.find((item2) => item.id === item2)
      )
      emit('setCcUserList', {
        CCUserList: CCUserList.value,
        auditFormsIndex: auditFormsIndex.value
      })
    }
    //转办
    if (props.handleType === TaskApi.handleTypeEnum.Forward) {
      emit('handleForward', { auditFormsIndex })
      // 发送操作成功的事件
      // emit('success')
    }
    //委派
    if (props.handleType === TaskApi.handleTypeEnum.Delegate) {
      emit('handleDelegate', auditFormsIndex.value)
      // 发送操作成功的事件
      // emit('success')
    }
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    taskId: '',
    assigneeUserId: '',
    transferReason: null
  }
  userList.value = []
  formRef.value?.resetFields()
}
//加载用户下拉
import { useUserSelect } from '@/hooks/common/useUserSelect'
let { userList, customLocationProps, loadNode } = useUserSelect()

defineExpose({ open, formData, CCUserList }) // 提供 openModal 方法，用于打开弹窗
</script>
