<!-- 合并了业务待办和流程待办
原路径AgencyManagement 和 Bpm 两个，应该用不到了 -->
<script setup lang="ts">
defineOptions({
  name: 'TodoWorkbench'
})

import { useIntervalRequest } from '@/hooks/common/useIntervalRequest'

import MessageDetailDialog from '@/views/MessageCenter/components/MessageDetailDialog.vue' // 消息通知详情弹窗
import { useRouter } from 'vue-router'
let message = useMessage()
let router = useRouter()

const { t } = useI18n()

//消息查看
import * as messageCenterApi from '@/api/messageManagement/messageCenter/index'
import { useMessageStore } from '@/store/modules/message'
const messageStore = useMessageStore()
const messageDetailVisible = ref(false)
const messageDetailData = ref<any>(null)
const messageDetail = async (item) => {
  let messageItem = item.message
  if (messageItem.messageType.code == 'NOTICE') {
    messageDetailData.value = item
    messageDetailVisible.value = true
  } else if (messageItem.pageUrl) {
    let { path, query } = getQueryObject(messageItem.pageUrl)
    router.push({ path, query })
  }
  try {
    if (item.status.code === '0') {
      await messageCenterApi.readMessageTo({ batchToIds: [item.toId] })
      messageStore.getUnreadMessage()
      findMessage({ pageNo: 1, pageSize: 20 })
    }
  } catch (error) {}
}
const getQueryObject = (pageUrl) => {
  let url: string = decodeURIComponent(pageUrl)
  let query: any = new Object()
  let path = url
  if (url.indexOf('?') != -1) {
    path = url.substr(0, Number(url.indexOf('?')))
    let str = url.substr(Number(url.indexOf('?')) + 1)
    let strs = str.split('&')
    for (let i = 0; i < strs.length; i++) {
      query[strs[i].split('=')[0]] = strs[i].split('=')[1]
    }
  }
  return { path, query }
}
// 我的消息
import { findMessageToByPage } from '@/api/AgencyManagement/index'

let findMessages = ref<any>([])
const findMessagesLoading = ref(false)
const findMessage = async (messageobj) => {
  try {
    findMessagesLoading.value = true
    const { data, code } = await findMessageToByPage(messageobj)
    console.log(data, 'data')
    if (code == 0) {
      findMessages.value = data.list
    }
  } catch (error) {
  } finally {
    findMessagesLoading.value = false
  }
}

//定时刷新我的消息列表  60s
useIntervalRequest(() => {
  findMessage({ pageNo: 1, pageSize: 20 })
}, 60 * 1000)

//防止任务处理完,未刷新列表
onActivated(() => {
  findMessage({ pageNo: 1, pageSize: 20 })
})
//流程待办 numbel=2
import { findNoticeReadStatusByList } from '@/api/AgencyManagement/index'

import * as TaskApi from '@/api/bpm/task'
let taskList = ref<any>([])
const taskListLoading = ref(false)
const getTaskList = async () => {
  try {
    taskListLoading.value = true
    const data = await TaskApi.getTodoTaskPage({ pageNo: 1, pageSize: 10 }, {})
    let applyIdList = data.list.map((item) => item.taskId)
    let dataStaus = await findNoticeReadStatusByList({
      applyIdList,
      noticeBusiness: 2
    })
    taskList.value = data.list.map((item, index) => {
      item['readStatus'] = dataStaus.find((item2) => item2.applyId === item.taskId).readStatus
      //立项审批流程格式: [名称]-项目审批
      if (item.processInstance.name === '立项审批流程') {
        item['taskName'] = `[${item.taskName}]-项目审批`
      }
      return item
    })
  } catch (error) {
  } finally {
    taskListLoading.value = false
  }
}
//定时刷新流程待办列表  60
useIntervalRequest(getTaskList, 60 * 1000)

//防止任务处理完,未刷新列表
onActivated(() => {
  getTaskList()
})
//查看流程待办
import { addNoticeReadStatus } from '@/api/AgencyManagement/index'
const taskDetail = async (item) => {
  if (item?.processInstance?.processInstanceId) {
    router.push({
      name: 'TodoProcessInstanceDetail',
      query: {
        id: item.processInstance.processInstanceId
      }
    })
    //只有未读状态才调用接口
    if (item.readStatus === '1') {
      await addNoticeReadStatus(item.taskId)
      await getTaskList()
    }
    return
  }
  message.notifyError(t('todoManagement.flowTodo.flowCannotJump'))
}

//业务待办
import { findNoticeByPage } from '@/api/AgencyManagement/index'

let businessTaskList = ref<any>([])
const businessTaskListLoading = ref(false)
const getBusinessList = async () => {
  try {
    businessTaskListLoading.value = true
    // const { data } = await findNoticeByPage({
    //   noticeBusinessName: '流程待办',
    //   pageNo: 1,
    //   pageSize: 10
    // })
    // taskList.value = data.list
    // console.log(data)

    const { data: data2 } = await findNoticeByPage({
      // noticeBusinessName: '业务待办',
      noticeBusinessCode: 1, //业务待办：1，流程待办：2
      pageNo: 1,
      pageSize: 10
    })
    businessTaskList.value = data2.list
  } catch (error) {
  } finally {
    businessTaskListLoading.value = false
  }
}
//定时刷新业务待办列表  60s
useIntervalRequest(getBusinessList, 60 * 1000)

//防止任务处理完,未刷新列表
onActivated(() => {
  getBusinessList()
})
//查看业务待办
import { noticeReaded } from '@/api/AgencyManagement/index'
const businessDetail = async (item) => {
  if (item.pageUrl) {
    router.push(item.pageUrl)
    await noticeReaded(item.noticeId)
    await getBusinessList()
    return
  }
  message.notifyError(t('todoManagement.flowTodo.taskCannotJump'))
}
//转办弹出框
import TransferDialog from '../Components/TransferDialog.vue'
let dialogVisible = ref<boolean>(false)
let forwardData = ref({})

let ifForward = ref(false) //是否流程转办
const openTransferDialog = (item, ifForward2) => {
  ifForward.value = ifForward2
  forwardData.value = item
  dialogVisible.value = !dialogVisible.value
}

const colorStyle = (title: string): any => {
  let background = ''
  let color = ''
  if (title == '生产验证' || title == '通知公告') {
    background = '#edf5fe'
  } else if (title == '制卡需求') {
    background = '#eefbfe'
    color = '#00b0df'
  } else if (title == 'PM评审') {
    background = '#eefbfe'
    color = '#00b0df'
  } else if (title == '设计方案' || title == '图像审批') {
    background = '#fef5eb'
    color = '#ff8400'
  } else if (title == '设计归档' || title == '合同审批' || title == '业务提醒') {
    background = '#f0fef4'
    color = '#00cd3c'
  } else if (title == '稿样方案' || title == '预警提示') {
    background = '#fef0f0'
    color = '#fe4c5c'
  } else if (title == '稿样归档') {
    background = '#ecf2e9'
    color = '#3db809'
  } else {
    background = ''
  }
  return 'background-color:' + background + ';' + 'color:' + color
}
</script>
<template>
  <ElRow :gutter="10" justify="space-between" class="h-full text-16px">
    <ElCol :span="14" class="h-full flex left_notice-bar">
      <ElCard class="left_notice-bar-item" style="margin: 0" body-style="height:100%">
        <div class="flex justify-between">
          <div class="title">{{ t('todoManagement.businessTodo.todoList') }}</div>
          <div>
            <ElLink
              @click="
                () => {
                  router.push({
                    name: 'BusinessTodo',
                    query: { numbel: 1 }
                  })
                }
              "
              >{{ t('todoManagement.common.more') }}</ElLink
            >
          </div>
        </div>
        <div v-loading="businessTaskListLoading" class="pb-30px h-full">
          <el-scrollbar>
            <el-empty
              v-if="!businessTaskListLoading && businessTaskList.length == 0"
              :image-size="200"
              :description="t('todoManagement.flowTodo.noTodo')"
            />

            <div
              class="flex justify-between mt-20px"
              v-for="(item, index) in businessTaskList"
              :key="index"
            >
              <div class="flex flex-col justify-center">
                <!-- 业务名称 -->
                <div class="flex items-center text-15px" style="line-height: normal">
                  <div class="p-1 px-4" :style="colorStyle(item.noticeType.noticeTypeName)">
                    {{ item.noticeType.noticeTypeName }}
                  </div>
                  <!-- 业务名称 -->
                  <div class="ml-15px">{{ item.noticeTitle }}</div>
                </div>
                <div class="flex text-cool-gray-500 text-14px mt-10px">
                  <div class=""
                    >{{ t('todoManagement.businessTodo.initiator') }}:{{
                      item.updateUser.name
                    }}</div
                  >
                  <div class="ml-15px"
                    >{{ t('todoManagement.common.time') }}: {{ item.noticeToLastDate }}</div
                  >
                </div>
              </div>
              <div class="flex min-w-20" style="line-height: normal">
                <el-badge :value="item.readStatus === '1' ? t('todoManagement.common.new') : ''">
                  <ElLink class="mr-2" @click="businessDetail(item)" type="primary">{{
                    t('common.see')
                  }}</ElLink>
                </el-badge>
                <!-- 制卡需求 没有转办功能 -->
                <!-- <el-badge
                  class="ml-5"
                  v-if="item.noticeType.noticeTypeName !== '制卡需求'"
                  :value="''"
                >
                  <ElLink @click="openTransferDialog(item, false)" type="primary">转办</ElLink>
                </el-badge> -->
              </div>
            </div>
          </el-scrollbar>
        </div>
      </ElCard>
      <ElCard
        class="left_notice-bar-item"
        style="margin-top: 10px; margin-bottom: 0"
        body-style="height:100%"
      >
        <div class="flex justify-between">
          <div class="title">{{ t('todoManagement.flowTodo.flowTodo') }}</div>
          <div>
            <ElLink
              @click="
                () => {
                  router.push({
                    name: 'FlowTodo',
                    query: { numbel: 2 }
                  })
                }
              "
            >
              更多
            </ElLink>
          </div>
        </div>
        <div v-loading="taskListLoading" class="pb-30px h-full">
          <el-scrollbar>
            <el-empty
              v-if="!taskListLoading && taskList.length == 0"
              :image-size="200"
              :description="t('todoManagement.flowTodo.noTodo')"
            />

            <div
              class="flex justify-between mt-20px"
              v-for="(item, index) in taskList"
              :key="index"
            >
              <div class="flex flex-col justify-center">
                <!-- 流程名称 -->
                <div class="flex items-center text-15px" style="line-height: normal">
                  <div
                    class="p-1 px-4 bg-true-gray-100"
                    :style="colorStyle(item.processInstance.name)"
                    style="flex-shrink: 0"
                  >
                    {{ item.processInstance.name }}
                  </div>
                  <!-- 流程+任务名称 -->
                  <div class="ml-15px mr-15px">{{ item.taskName }}</div>
                </div>
                <div class="flex text-cool-gray-500 text-14px mt-10px">
                  <div class=""
                    >{{ t('todoManagement.businessTodo.initiator') }}:{{
                      item.processInstance.startUserNickname
                    }}</div
                  >
                  <div class="ml-15px"
                    >{{ t('todoManagement.common.time') }}: {{ item.createTime }}</div
                  >
                </div>
              </div>
              <div class="flex min-w-24 mr-3" style="line-height: normal">
                <el-badge :value="item.readStatus === '1' ? t('todoManagement.common.new') : ''">
                  <ElLink class="mr-2" @click="taskDetail(item)" type="primary">{{
                    t('common.see')
                  }}</ElLink>
                </el-badge>
                <el-badge class="ml-5" :value="''">
                  <ElLink @click="openTransferDialog(item, true)" type="primary">{{
                    t('todoManagement.common.transfer')
                  }}</ElLink>
                </el-badge>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </ElCard>
    </ElCol>
    <ElCol :span="10" class="h-full">
      <ElCard class="h-full" style="margin: 0" body-style="height:100%">
        <div class="flex justify-between">
          <div class="title">{{ t('todoManagement.flowTodo.myMessage') }}</div>
          <div>
            <ElLink @click="router.push({ name: 'MessageCenter' })">{{
              t('todoManagement.common.more')
            }}</ElLink>
          </div>
        </div>
        <div v-loading="findMessagesLoading" class="pb-30px h-full">
          <el-scrollbar>
            <el-empty
              v-if="!findMessagesLoading && findMessages.length == 0"
              :image-size="200"
              :description="t('todoManagement.flowTodo.noMessage')"
            />

            <div
              class="flex justify-between mt-20px"
              v-for="(item, index) in findMessages"
              :key="index"
            >
              <div class="flex flex-col justify-center">
                <div class="flex items-center text-15px" style="line-height: normal">
                  <div
                    class="p-1 px-4"
                    :style="colorStyle(item.message.messageType.name)"
                    style="flex-shrink: 0"
                    >{{ item.message.messageType.name }}</div
                  >
                  <div class="ml-15px mr-15px" :class="colorStyle(item)">{{
                    item.message.title.length > 30
                      ? item.message.title.substring(0, 30) + '......'
                      : item.message.title
                  }}</div>
                </div>
                <div class="flex text-cool-gray-500 text-14px mt-10px">
                  <div class="">{{ item.message.pageName }}</div>
                  <div class="ml-15px"
                    >{{ t('todoManagement.common.time') }}: {{ item.publishTime }}</div
                  >
                </div>
              </div>
              <div class="flex w-22 justify-end mr-5" style="line-height: normal">
                <ElBadge :value="item.status.code === '0' ? t('todoManagement.common.new') : ''">
                  <ElLink class="mr-2" @click="messageDetail(item)" type="primary">{{
                    t('common.see')
                  }}</ElLink>
                </ElBadge>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </ElCard>
    </ElCol>
  </ElRow>

  <TransferDialog
    v-if="dialogVisible"
    v-model:dialogVisible="dialogVisible"
    :forwardData="forwardData"
    :ifForward="ifForward"
    @handle-sucess="ifForward ? getTaskList() : getBusinessList()"
  />

  <MessageDetailDialog v-model="messageDetailVisible" :detail="messageDetailData" />
</template>
<style scoped lang="less">
.title {
  font-size: 16px;
  color: white;
  font-weight: 700;
  width: 100px;
  background-color: var(--el-color-primary);
  text-align: center;
  padding: 5px;
}

.left_notice-bar {
  display: flex !important;
  flex-direction: column;

  .left_notice-bar-item {
    flex: 1;
    flex-shrink: 0;
  }
}
</style>
