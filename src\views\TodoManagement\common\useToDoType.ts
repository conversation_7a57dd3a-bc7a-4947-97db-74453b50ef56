import { definition<PERSON>ey<PERSON><PERSON> } from '@/api/bpm/task'
function useToDoType() {
  const toDoTypeOptions = ref()
  const selectLoading = ref(false)
  onMounted(async () => {
    selectLoading.value = true
    try {
      const res = await definitionKey<PERSON>pi()
      toDoTypeOptions.value = res || []
    } finally {
      selectLoading.value = false
    }
  })
  const getLabel = (key) => {
    return (
      toDoTypeOptions.value &&
      toDoTypeOptions.value.find((item) => item.processDefinitionKey === key)?.processDefinitionName
    )
  }
  return {
    getLabel,
    toDoTypeOptions,
    selectLoading
  }
}
export default useToDoType
