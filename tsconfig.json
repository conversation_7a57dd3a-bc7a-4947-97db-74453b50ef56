{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,
    "strictFunctionTypes": false,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "skipLibCheck": true,
    "paths": {
      "@/*": ["src/*"]
    },
    // "typeRoots": ["./node_modules/@types/", "./types"],
    "types": [
      "@intlify/unplugin-vue-i18n/types",
      "vite/client",
      "element-plus/global",
      "@types/intro.js",
      "@types/qrcode",
      "./types/global.d.ts",
      "vite-plugin-svg-icons/client"
    ]
  },
  "include": [
    "src/**/*",
    "types/**/*.d.ts",
    "src/types/auto-imports.d.ts",
    "src/types/auto-components.d.ts",
    "mock/**/*.ts"
  ],
  "exclude": ["dist", "node_modules"]
}
