import type { CSSProperties } from 'vue'
const { t } = useI18n()
declare global {
  declare interface Fn<T = any> {
    (...arg: T[]): T
  }

  declare type Nullable<T> = T | null

  declare type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>

  declare type Recordable<T = any, K = string> = Record<K extends null | undefined ? string : K, T>

  declare type ComponentRef<T> = InstanceType<T>

  declare type LocaleType = 'zh-CN' | 'zh-TW' | 'en'

  declare type AxiosHeaders =
    | 'application/json'
    | 'application/x-www-form-urlencoded'
    | 'multipart/form-data'

  declare type AxiosMethod = 'get' | 'post' | 'delete' | 'put'

  declare type AxiosResponseType = 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream'

  declare interface AxiosConfig {
    params?: any
    data?: any
    url?: string
    method?: AxiosMethod
    headersType?: string
    responseType?: AxiosResponseType
  }

  declare interface IResponse<T = any> {
    code: string | number
    data: T extends any ? T : T & any
    msg?: string
  }
  //国际化函数t类型
  declare function t(key: string, params?: any): string
  // 声明全局变量类型
  declare const __APP_INFO__: {
    buildTime: string
    GIT_COMMIT_DATE: string
    GIT_HASH: string
    GIT_LAST_COMMIT_MESSAGE: string
    GIT_TAG: string
  }
}
