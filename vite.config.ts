/*
 * @Author: HoJ<PERSON>
 * @Date: 2023-06-13 16:01:29
 * @LastEditors: HoJack
 * @LastEditTime: 2023-12-19 18:42:48
 * @Description: 
 */
import { resolve } from 'path'
import { loadEnv } from 'vite'
import type { UserConfig, ConfigEnv } from 'vite'
import { createVitePlugins } from './build/vite'

import { include, exclude } from "./build/vite/optimize"

import { createBuildInfo } from './build/vite/buildInfo'


// https://vitejs.dev/config/
const root = process.cwd()

function pathResolve(dir: string) {
  return resolve(root, '.', dir)
}

/**
 * 根据环境变量和模式获取管理后台的URL
 *
 * @param env 环境变量对象
 * @param mode 模式，可选值为'dev'（开发环境）、'sit'（测试环境）、'uat'（预发布环境）、'prod'（生产环境）
 * @returns 返回对应模式的客户端后台URL
 */
export const getSaleUrlByMode = (env,mode) => {
  if (mode === 'dev') return env.VITE_SALE_DEV
  if (mode === 'sit') return env.VITE_SALE_SIT
  if (mode === 'uat') return env.VITE_SALE_UAT
  if (mode === 'pro') return env.VITE_SALE_PRO
}


export default ({ command, mode }: ConfigEnv): UserConfig => {
  let env = {} as any
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
  } else {
    env = loadEnv(mode, root)
  }
  return {
    base: env.VITE_BASE_PATH,
      // 项目使用的vite插件。 单独提取到build/vite/plugin中管理
      plugins: createVitePlugins(),

    css: {
      preprocessorOptions: {
        less: {
          additionalData: '@import "@/styles/variables.module.less";',
          javascriptEnabled: true
        }
      }
    },
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.less', '.css'],
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
        },
        {
          find: /\@\//,
          replacement: `${pathResolve('src')}/`
        }
      ]
    },
    build: {
      minify: 'terser',
      outDir: env.VITE_OUT_DIR || 'dist',
      sourcemap: env.VITE_SOURCEMAP === 'true' ? 'inline' : false,
      // brotliSize: false,
      terserOptions: {
        compress: {
          drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
          drop_console: env.VITE_DROP_CONSOLE === 'true'
        }
      },
      // commonjsOptions: {
      //   include: /node_modules|lib/  //这里记得把lib目录加进来，否则生产打包会报错！！
      // },
          /** 单个 chunk 文件的大小超过 512KB 时发出警告 */
      chunkSizeWarningLimit: 512,
      rollupOptions: {
        output: {
            chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
            entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
            assetFileNames: '[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
            // key自定义 value[] 插件同步package.json名称 或 src/相对路径下的指定文件 （自己鼠标点击进去可以看manualChunks ts类型）
            manualChunks: {
            // vue vue-router合并打包
            vue: ['vue', 'vue-router'],
            // echarts: ['echarts', 'echarts-wordcloud'],
            jsencrypt: ['jsencrypt'],
            // 将 Lodash 库的代码单独打包
            lodash: ['lodash-es'],
            // 将组件库的代码打包
            library: ['element-plus', '@element-plus/icons-vue'],
            view3D: ['vue-3d-model'],
            //图标(该json包有点大)
            epIconsJson: ['@iconify/json/json/ep.json'],
            faIconsJson: ['@iconify/json/json/fa.json'],
            faSolidIconsJson: ['@iconify/json/json/fa-solid.json'],
            i18n: ['vue-i18n'],
            wangeditor:['@wangeditor/editor','@wangeditor/editor-for-vue']
            }
        },
      },

    },
    server: {
      port: 4000,
      proxy: {
        // '/app-api/order/order/batch/export': {
        //   target: 'http://localhost:8089/',
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace('/app-api/order', '')
        // },


          '/app-api/admin-api': {
          target:getSaleUrlByMode(env,mode) ,
          changeOrigin: true,
          // rewrite: (path) => path.replace('/app-api', '')
         },
          '/admin-api': {
          target:getSaleUrlByMode(env,mode) ,
          changeOrigin: true,
        },
          '/app-api': {
          target:getSaleUrlByMode(env,mode) ,
          changeOrigin: true,
        },
        "/files/oss-dev": {
          target: "http://*************:8034/",
          changeOrigin: true,
        },
        "/files/umv-oss-sit": {
          target: "https://sit-sale.umvi.cn",
          changeOrigin: true,
        },
        "/files/oss-uat": {
          target: "https://uat-saleh5.umvcard.com",
          changeOrigin: true,
        }
      },
      hmr: {
        overlay: true
      },
      host: '0.0.0.0',
      open: true
    },
          //如果使用 unplugin-element-plus 并且只使用组件 API，你需要手动导入样式。 采用以下插件按需引入样式
    optimizeDeps: { include, exclude },
    define:createBuildInfo()
  }
}
